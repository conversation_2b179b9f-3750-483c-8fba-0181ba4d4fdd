"use client";

import { ReactNode } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

interface StatsCardProps {
  title: string;
  value: string;
  change: string;
  icon: ReactNode;
}

export function StatsCard({ title, value, change, icon }: StatsCardProps) {
  return (
    <Card className="p-6">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-base font-medium text-muted-foreground">{title}</h3>
        <div className="flex size-10 items-center justify-center rounded-full bg-muted">{icon}</div>
      </div>
      <div className="space-y-2">
        <p className="text-2xl font-bold">{value}</p>
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">{change} from last month</p>
          <Button variant="link" className="h-auto p-0 text-sm">
            View more
          </Button>
        </div>
      </div>
    </Card>
  );
}

import { Variant } from "@/features/variants/hooks/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

export const variantApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Variant>>(ENDPOINTS.VARIANT.LIST, {
      params,
    });
  },
  delete: async (id: string) => {
    const url = ENDPOINTS.VARIANT.DELETE.replace(":id", id);
    return await privateApi.delete(url);
  },
};

---
description: 
globs: 
alwaysApply: false
---
# Application Architecture

## Core Architecture
- Next.js App Router for routing and server components
- TypeScript for type safety and better developer experience
- Tailwind CSS for styling
- Jest and React Testing Library for testing

## Key Files and Entry Points
- [src/middleware.ts](mdc:src/middleware.ts): Request middleware and authentication
- App router pages in `src/app` directory
- Feature modules in `src/features`
- Shared components in `src/components`

## State Management
- React hooks for local state
- Custom hooks in `src/hooks` for shared logic
- Server components for data fetching
- API routes for backend communication

## Application Flow
1. Requests go through middleware for auth/validation
2. App router handles routing and layout
3. Pages render with server/client components
4. Components use hooks and utilities as needed
5. API calls handled through appropriate services

## Best Practices
- Use server components when possible
- Implement proper error boundaries
- Follow established patterns for data fetching
- Keep components small and focused
- Utilize TypeScript features for type safety

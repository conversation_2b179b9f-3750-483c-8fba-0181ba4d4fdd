"use client";

import { use<PERSON><PERSON><PERSON>, useEffect, useMemo, useState } from "react";
import { DragHandleDots2Icon } from "@radix-ui/react-icons";
import { Reorder, useDragControls } from "framer-motion";
import { Minus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button, Separator } from "@/components/ui";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Input } from "@/components/ui/input";
import { SaveFilterItem, SaveFilterItem as SaveFilterItemType } from "@/lib/apis/save-filter";

import { IconSelector } from "../components/icon-selector";
import { MAX_DISPLAY_TABS } from "../constant";

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  data: (Partial<SaveFilterItemType> & {
    id: string;
    name: string;
    type?: string;
    filters?: Record<string, unknown>;
    icon?: string;
  })[];
  selectedTabs: Record<string, boolean>;
  onTabSelect: (id: string, checked: boolean) => void;
  handleUpdateFilters: (filters: SaveFilterItemType[]) => Promise<SaveFilterItem[]>;
  onIconChange: (id: string, icon: string) => void;
  handleConfirmDelete: (id: string) => Promise<void>;
  isUpdating: boolean;
  isDeleting: boolean;
}

interface SortableItemProps {
  filter: SaveFilterItem;
  isLoading: boolean;
  listFilterIcons: string[];
  onNameChange?: (id: string, name: string) => void;
  handleConfirmDelete: (id: string) => Promise<void>;
  handleUpdateIconFilter: (id: string, icon: string) => void;
}

function SortableItem({
  filter,
  isLoading,
  listFilterIcons,
  onNameChange,
  handleConfirmDelete,
  handleUpdateIconFilter,
}: SortableItemProps) {
  const controls = useDragControls();
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const onUpdateIconFilter = (icon: string) => {
    handleUpdateIconFilter(filter.id, icon);
  };
  const handleDelete = async () => {
    try {
      setDeleteConfirmOpen(true);
      await handleConfirmDelete(filter.id);
    } finally {
      setDeleteConfirmOpen(false);
    }
  };
  return (
    <Reorder.Item
      value={filter}
      className="relative w-full select-none bg-card"
      dragListener={false}
      dragControls={controls}>
      <div className="flex items-center gap-2 py-2">
        {filter.type !== "default" && (
          <Button
            size="icon"
            variant={"ghost"}
            className="w-fit hover:bg-transparent"
            disabled={isLoading}>
            <DragHandleDots2Icon
              className="size-4 cursor-grab text-muted-foreground"
              onPointerDown={(event) => controls.start(event)}
            />
          </Button>
        )}

        <IconSelector
          disable={isLoading}
          disableIcons={listFilterIcons}
          value={filter.icon}
          onChange={(value) => onUpdateIconFilter(value)}
        />
        <Input
          defaultValue={filter.name}
          onChange={(e) => onNameChange?.(filter.id, e.target.value)}
          containerClassName="flex-1"
          placeholder="Enter tab name"
          disabled={isLoading}
        />
        <Button
          size={"icon"}
          variant={"ghost"}
          disabled={isLoading}
          className="flex "
          onClick={() => setDeleteConfirmOpen(true)}
          leftIcon={<Minus size={14} className="h-fit text-destructive " />}
        />
      </div>
      <ConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        variant="destructive"
        loading={isLoading}
        title="Delete Saved Filter"
        description="Are you sure you want to delete this saved filter? This action cannot be undone."
        cancelText="Cancel"
        isControl
        confirmText="Delete"
        onCancel={() => setDeleteConfirmOpen(false)}
        onConfirm={handleDelete}
      />
    </Reorder.Item>
  );
}

export function SettingsDialog({
  open,
  onOpenChange,
  data = [],
  isUpdating,
  isDeleting,
  handleConfirmDelete,
  handleUpdateFilters,
}: SettingsDialogProps) {
  const [filterItems, setFilterItems] = useState<SaveFilterItem[]>([]);

  const handleUpdateIconFilter = (id: string, icon: string) => {
    setFilterItems(filterItems.map((item) => (item.id === id ? { ...item, icon } : item)));
  };
  const handleChangeNameFilter = (id: string, name: string) => {
    setFilterItems(filterItems.map((item) => (item.id === id ? { ...item, name } : item)));
  };
  const { t } = useTranslation();
  useEffect(() => {
    if (open && data) {
      setFilterItems(data.filter((item) => item.id !== "all") as SaveFilterItem[]);
    }
  }, [open, data]);

  const handleReorder = (newOrder: SaveFilterItem[]) => {
    setFilterItems(newOrder);
  };
  const handleSave = useCallback(async () => {
    try {
      console.log("isUpdating", isUpdating);
      await handleUpdateFilters?.(filterItems);
      if (!isUpdating) onOpenChange(false);
    } catch (error) {
      console.error("Failed to save filters:", error);
    }
  }, [handleUpdateFilters, filterItems, isUpdating, onOpenChange]);
  const listFilterIcons = useMemo(() => {
    return filterItems.map((filter) => filter?.icon).filter((icon): icon is string => !!icon);
  }, [filterItems]);
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t("table.savedFilters.settings.title")}</AlertDialogTitle>
          <AlertDialogDescription>
            {t("table.savedFilters.settings.description")}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="my-4 max-h-[400px] space-y-4 overflow-y-auto px-2">
          <Reorder.Group axis="y" values={filterItems} onReorder={handleReorder}>
            {filterItems.map((filter, index) => (
              <div key={filter.id}>
                <SortableItem
                  filter={filter}
                  isLoading={isDeleting}
                  listFilterIcons={listFilterIcons || []}
                  onNameChange={handleChangeNameFilter}
                  handleConfirmDelete={handleConfirmDelete}
                  handleUpdateIconFilter={handleUpdateIconFilter}
                />
                {index === MAX_DISPLAY_TABS - 1 && filterItems.length > MAX_DISPLAY_TABS && (
                  <Separator className="my-4" />
                )}
              </div>
            ))}
          </Reorder.Group>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => onOpenChange(false)}>
            {t("common.cancel")}
          </AlertDialogCancel>
          <Button onClick={handleSave} loading={isUpdating}>
            {t("common.save")}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

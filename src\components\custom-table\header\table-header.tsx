/* eslint-disable @typescript-eslint/no-explicit-any */
import { memo, useState } from "react";
import { useTranslation } from "react-i18next";

import useDatatable from "@/components/custom-table/hooks/use-data-table";
import { FilterTableProps } from "@/components/data-table/types";
import { Button } from "@/components/ui";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { cn } from "@/lib/utils";

import SavedFilters from "../container/saved-filters";
import TableFilter from "./table-filter";

interface TableHeaderProps<TData> {
  title: string;
  description?: string;
  filterProps?: FilterTableProps;
  rightComponent?: React.ReactNode;
  filterType: string;
  isSaveFilters?: boolean;
  isExportable?: boolean;
  customRightFilter?: React.ReactNode;
  data: TData[];
}

export default memo(function TableHeader<TData>({
  title,
  filterProps,
  description,
  rightComponent,
  filterType,
  isSaveFilters = true,
  isExportable = true,
  data,
  customRightFilter,
}: TableHeaderProps<TData>) {
  const { handleParamSearch, handleExport } = useDatatable();
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const { t } = useTranslation();
  const onExport = () => {
    handleExport({ data, filename: "export" });
  };
  return (
    <div className={cn("flex flex-none flex-col gap-2", !isSaveFilters && "mb-2")}>
      <div className="flex flex-col justify-between gap-y-4 sm:flex-row">
        <div className="flex flex-col gap-1">
          <h1 className="text-base font-medium text-foreground">{t(title)}</h1>
          {description && (
            <span className="text-xs font-normal leading-4 text-muted-foreground">
              {t(description)}
            </span>
          )}
        </div>
        {rightComponent}
      </div>
      <div className={cn("flex", isSaveFilters ? "flex-col" : "flex-row justify-between")}>
        {filterProps && (
          <TableFilter {...filterProps}>
            <div className="flex items-center gap-2 self-center">
              {!isSaveFilters && isExportable && (
                <div className="self-end sm:self-center">
                  <Button variant="outline" size="sm" onClick={() => setIsExportDialogOpen(true)}>
                    {t("table.export.title")}
                  </Button>
                </div>
              )}
              {customRightFilter}
            </div>
          </TableFilter>
        )}
        <div
          className={cn(
            "flex max-w-full items-end gap-7 pl-2",
            isSaveFilters ? "justify-between " : "justify-end",
            isSaveFilters || isExportable ? "min-h-11" : "hidden"
          )}>
          {isSaveFilters && (
            <SavedFilters filterType={filterType} onFilterChange={handleParamSearch} />
          )}
          <div className="flex items-center gap-2 self-center">
            {isExportable && isSaveFilters && (
              <div className="self-center">
                <Button variant="outline" size="sm" onClick={() => setIsExportDialogOpen(true)}>
                  {t("table.export.title")}
                </Button>
              </div>
            )}
            {customRightFilter}
          </div>
        </div>
      </div>
      <ConfirmDialog
        open={isExportDialogOpen}
        onOpenChange={setIsExportDialogOpen}
        onConfirm={onExport}
        title={t("table.export.title")}
        description={t("table.export.description")}
        confirmText={t("table.export.confirm")}
        cancelText={t("table.export.cancel")}
      />
    </div>
  );
});

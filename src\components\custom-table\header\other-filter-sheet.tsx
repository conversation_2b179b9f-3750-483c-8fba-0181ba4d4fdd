"use client";

import { useEffect, useState } from "react";
import { DragHandleDots2Icon } from "@radix-ui/react-icons";
import { Reorder, useDragControls } from "framer-motion";
import { ArrowUpFromLine, MoreVertical } from "lucide-react";
import { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";

import { EFilterType, FilterType } from "@/components/data-table/types";
import { Checkbox, ScrollArea, Separator } from "@/components/ui";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import { NUMBER_OF_FILTERS } from "../constant";
import { dateOptions } from "./date-options";
import { OtherFilterGroup } from "./other-filter-group";

interface TableFilterSheetProps {
  listFilter: FilterType[];
  otherFilters: Record<string, string[]>;
  dateFilters: Record<
    string,
    {
      option: string | null;
      range: DateRange | undefined;
    }
  >;
  handleOtherFilterChange: (id: string, values: string[]) => void;
  handleDateOptionSelect: (id: string, value: string, type: "main" | "other") => void;
  handleDateRangeChange: (id: string, type: "main" | "other", range?: DateRange) => void;
  handleApplyFilters: ({ isSaveFilter }: { isSaveFilter?: boolean }) => Promise<void>;
  onReorderFilters?: (newOrder: FilterType[]) => void;
  isSavedFilterLoading: boolean;
}

interface SortableItemProps {
  filter: FilterType;
  children: React.ReactNode;
  index: number;
  onMoveToTop: (filter: FilterType) => void;
}

const SortableItem = ({ filter, children, index, onMoveToTop }: SortableItemProps) => {
  const controls = useDragControls();
  const showArrowTop = index >= NUMBER_OF_FILTERS;

  return (
    <Reorder.Item
      value={filter}
      className="relative w-full select-none bg-card"
      dragListener={false}
      dragControls={controls}>
      <div className="flex items-start gap-2 py-3">
        <div className="flex-1">{children}</div>
        <div className="absolute right-2 top-3 flex items-center gap-2">
          {showArrowTop && (
            <ArrowUpFromLine
              onClick={() => onMoveToTop(filter)}
              className="size-4 cursor-pointer opacity-80 hover:opacity-100 focus:opacity-100"
            />
          )}
          <DragHandleDots2Icon
            className="size-5 cursor-grab text-muted-foreground"
            onPointerDown={(event) => controls.start(event)}
          />
        </div>
      </div>
    </Reorder.Item>
  );
};

export default function TableFilterSheet({
  listFilter,
  dateFilters,
  otherFilters,
  handleOtherFilterChange,
  handleDateOptionSelect,
  handleDateRangeChange,
  handleApplyFilters,
  onReorderFilters,
  isSavedFilterLoading,
}: TableFilterSheetProps) {
  const { t } = useTranslation();
  const [filters, setFilters] = useState<FilterType[]>(listFilter);
  const [isSaveFilter, setIsSaveFilter] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  useEffect(() => {
    setFilters(listFilter);
  }, [listFilter]);

  const handleReorder = (newOrder: FilterType[]) => {
    setFilters(newOrder);
    onReorderFilters?.(newOrder);
  };

  const handleMoveToTop = (filter: FilterType) => {
    const newFilters = filters.filter((f) => f.id !== filter.id);
    newFilters.unshift(filter);
    setFilters(newFilters);
    onReorderFilters?.(newFilters);
  };
  const hanleSaveFilterClick = async () => {
    try {
      await handleApplyFilters({ isSaveFilter });
    } finally {
      setIsOpen(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon">
          <MoreVertical size={16} />
        </Button>
      </SheetTrigger>
      <SheetContent className="p-0" side="right">
        <div className="flex h-full max-h-screen flex-col">
          <div className="flex flex-auto flex-col overflow-y-hidden">
            <SheetHeader className="flex flex-col gap-[6px] p-6">
              <SheetTitle className="text-lg font-semibold">
                {t("pages.products.filters.otherFilters.title")}
              </SheetTitle>
              <SheetDescription className="text-sm text-muted-foreground">
                {t("pages.products.filters.otherFilters.description")}
              </SheetDescription>
            </SheetHeader>
            <ScrollArea className="flex-auto px-2 pt-0">
              <Reorder.Group axis="y" values={filters} onReorder={handleReorder}>
                {filters.map((filter, index) => (
                  <div className="px-2" key={filter.id}>
                    <SortableItem filter={filter} index={index} onMoveToTop={handleMoveToTop}>
                      {filter?.type === EFilterType.DATE ? (
                        <OtherFilterGroup
                          type={filter.type}
                          label={filter.title}
                          options={dateOptions}
                          dateSelectedOption={dateFilters[filter.id]?.option}
                          onDateOptionSelect={(value: string) =>
                            handleDateOptionSelect(filter.id, value, "other")
                          }
                          dateRange={dateFilters[filter.id]?.range}
                          onDateRangeChange={(range: DateRange | undefined) =>
                            handleDateRangeChange(filter.id, "other", range)
                          }
                        />
                      ) : (
                        <OtherFilterGroup
                          type={filter.type}
                          label={filter.title}
                          options={
                            filter.dataOption?.map((opt) => ({
                              label: opt.label,
                              value: String(opt.value),
                            })) || []
                          }
                          selectedOption={otherFilters[filter.id]}
                          onOptionSelect={(value: string[]) =>
                            handleOtherFilterChange(filter.id, value)
                          }
                          remote={filter.remote}
                          pathUrlLoad={filter.pathUrlLoad}
                          isChannelFetch={filter.isChannelFetch}
                        />
                      )}
                    </SortableItem>
                    {index === NUMBER_OF_FILTERS - 1 && <Separator className="my-4" />}
                  </div>
                ))}
              </Reorder.Group>
            </ScrollArea>
          </div>

          <div className="mt-auto flex justify-between gap-2 border-t p-6">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="save-filters"
                checked={isSaveFilter}
                onCheckedChange={(checked) => setIsSaveFilter(!!checked)}
              />
              <label htmlFor="save-filters" className="text-sm font-medium">
                {t("common.saveFilters")}
              </label>
            </div>
            <div className="flex items-center gap-2 ">
              <Button variant="outline" className="w-[80px]" onClick={() => setIsOpen(false)}>
                {t("common.cancel")}
              </Button>
              <Button
                className="w-[80px]"
                loading={isSavedFilterLoading}
                onClick={hanleSaveFilterClick}>
                {t("common.filter")}
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

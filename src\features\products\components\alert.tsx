"use client";

import { AnimatePresence, motion } from "framer-motion";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";

interface AlertProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  isLoading?: boolean;
}

export const Alert = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  isLoading,
}: AlertProps) => {
  const { t } = useTranslation();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{
              scale: 1,
              opacity: 1,
              transition: {
                type: "spring",
                duration: 0.5,
                bounce: 0.3,
              },
            }}
            exit={{
              scale: 0.95,
              opacity: 0,
              transition: {
                duration: 0.2,
              },
            }}
            className="relative mx-4 w-full max-w-md rounded-lg bg-white shadow-lg">
            {/* Alert Content */}
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <p className="mt-2 text-sm text-gray-500">{description}</p>
            </div>

            {/* Alert Actions */}
            <div className="flex justify-end gap-2 rounded-b-lg bg-gray-50 px-6 py-4">
              <Button variant="outline" onClick={onClose} disabled={isLoading}>
                {t("common.cancel")}
              </Button>
              <Button variant="destructive" onClick={onConfirm} loading={isLoading}>
                {t("common.delete")}
              </Button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

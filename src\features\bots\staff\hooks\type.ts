import { CreateImage, Image } from "@/features/products/hooks/types";

import { Knowledge } from "../../knowledge/types";

export enum Role {
  CUSTOMER_SUPPORT_AGENT = "CUSTOMER_SUPPORT_AGENT",
  SALES_ASSISTANT = "SALES_ASSISTANT",
  VIRTUAL_PERSONAL_ASSISTANT = "VIRTUAL_PERSONAL_ASSISTANT",
  TECHNICAL_SUPPORT_SPECIALIST = "TECHNICAL_SUPPORT_SPECIALIST",
  HR_RECRUITMENT_ASSISTANT = "HR_RECRUITMENT_ASSISTANT",
  MARKETING_ASSISTANT = "MARKETING_ASSISTANT",
  CONTENT_CREATOR = "CONTENT_CREATOR",
  DATA_ANALYST = "DATA_ANALYST",
  EDUCATIONAL_TUTOR = "EDUCATIONAL_TUTOR",
  SCHEDULING_ASSISTANT = "SCHEDULING_ASSISTANT",
  RESEARCH_ASSISTANT = "RESEARCH_ASSISTANT",
  FINANCIAL_ADVISOR = "FINANCIAL_ADVISOR",
  VIRTUAL_TRAVEL_AGENT = "VIRTUAL_TRAVEL_AGENT",
  LEGAL_ASSISTANT = "LEGAL_ASSISTANT",
  CODE_REVIEW_SPECIALIST = "CODE_REVIEW_SPECIALIST",
  HEALTHCARE_COACH = "HEALTHCARE_COACH",
  MENTAL_HEALTH_COMPANION = "MENTAL_HEALTH_COMPANION",
  VIRTUAL_EVENT_PLANNER = "VIRTUAL_EVENT_PLANNER",
  REAL_ESTATE_ADVISOR = "REAL_ESTATE_ADVISOR",
  SECURITY_ANALYST = "SECURITY_ANALYST",
  UX_UI_DESIGNER_AGENT = "UX_UI_DESIGNER_AGENT",
  PROJECT_MANAGEMENT_ASSISTANT = "PROJECT_MANAGEMENT_ASSISTANT",
  VIRTUAL_STAFF = "VIRTUAL_STAFF",
}

export interface PersonalTrait {
  formality?: number;
  detailed?: number;
  creativity?: number;
}

export interface LLMModel {
  provider?: string;
  model_name?: string;
  custom_url?: string | null;
}

export interface LLMSettings {
  llm_api_key?: string;
  llm_model?: LLMModel;
  default_llm_temperature?: number;
  max_tokens?: number;
  max_llm_call_retries?: number;
  other_kwargs?: Record<string, any>;
}

export interface MCPConfig {
  adapters_config?: any;
  enabled_adapters?: any;
  enabled?: boolean;
  server_url?: string | null;
}

export interface ToolsConfig {
  config?: any;
  enabled?: any;
}

export interface KnowledgeBaseConfig {
  context_template?: string | null;
  min_similarity_score?: number | null;
  knowledge_list?: Knowledge[] | null;
  enabled?: boolean;
  knowledge_ids?: string[];
  domain_expertise_ids?: string[] | null;
  max_context_tokens?: number | null;
}

export interface PersonalityConfig {
  language?: string;
  farewell?: string | null;
  personal_trait?: PersonalTrait | null;
  tone?: string | null;
  ethical_constraints?: boolean | null;
}

export interface ConversationConfig {
  max_history_length?: number | null;
  response_timeout?: number | null;
  fallback_message?: string | null;
  context_window?: number | null;
}

export interface Configuration {
  instruction?: string | null;
  llm_settings?: LLMSettings;
  personality?: PersonalityConfig;
  knowledge_base?: KnowledgeBaseConfig;
  tools?: ToolsConfig;
  mcp?: MCPConfig;
  conversation?: ConversationConfig;
}
export interface VirtualStaffModel {
  id: string;
  company_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  department_id: string;
  image?: Image | null;
  role: Role;
  skills?: string[] | null;
  greeting?: string | null;
  domain_expertise?: string[] | null;
  configuration?: Configuration;
  user?: any;
}
export type CreateStaffPayload = {
  id: string;
  company_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  department_id: string;
  image: CreateImage;
  role: Role;
  skills?: string[] | null;
  greeting?: string | null;
  domain_expertise?: string[] | null;
  configuration?: Configuration;
  user?: any;
};

import { removeCookie, set<PERSON>ookie } from "@/utils/cookie";

import { privateApi } from "./api_helper";
import { AuthUser } from "./apis/types/auth";

export const setAuthToken = (authData: AuthUser) => {
  try {
    // Lưu vào localStorage
    const stringifiedData = JSON.stringify(authData);
    localStorage.setItem("auth_user", stringifiedData);

    // Set cookie
    setCookie("auth_user", true);
  } catch (error) {
    console.error("Error saving auth data:", error);
  }
};

export const getAuthToken = () => {
  try {
    const authData = localStorage.getItem("auth_user");
    if (!authData) return null;

    // Parse data khi lấy ra
    return JSON.parse(authData) as AuthUser;
  } catch {
    return null;
  }
};

export const clearAuth = () => {
  // Clear localStorage
  localStorage.removeItem("auth_user");

  delete privateApi.defaults.headers.common.Authorization;

  // Clear cookie
  removeCookie("auth_user");
};

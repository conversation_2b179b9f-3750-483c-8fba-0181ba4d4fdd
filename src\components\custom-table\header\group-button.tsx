"use client";

import { ReactNode } from "react";
import Link from "next/link";
import { Loader2, LucideIcon, RefreshCw } from "lucide-react";

import { Button, ButtonProps } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

type ButtonVariant = NonNullable<ButtonProps["variant"]>;

interface BaseButtonConfig {
  type: "button" | "dropdown";
  title?: string;
  icon?: LucideIcon;
  customIcon?: ReactNode;
  variant?: ButtonVariant;
  onClick?: () => void;
  hide?: boolean;
  disabled?: boolean;
  isLoading?: boolean;
}

interface ButtonConfig extends BaseButtonConfig {
  type: "button";
}

interface DropdownConfig extends BaseButtonConfig {
  type: "dropdown";
  items: {
    type: string;
    title: string;
    icon?: LucideIcon;
    customIcon?: ReactNode;
    onClick?: () => void;
    href?: string;
    hide?: boolean;
  }[];
}

type ButtonGroupItem = ButtonConfig | DropdownConfig;

export interface GroupButtonProps {
  buttons?: ButtonGroupItem[];
  showRefresh?: boolean;
  isRefreshLoading?: boolean;
  onRefresh?: () => void;
}

const GroupButton = ({
  buttons = [],
  showRefresh = true,
  onRefresh,
  isRefreshLoading = false,
}: GroupButtonProps) => {
  if (buttons.length === 0 && !showRefresh) return null;

  const renderDropdownItem = (item: DropdownConfig["items"][0]) => {
    if (item.hide) return null;

    const Icon = item.icon;
    const content = (
      <span className="flex w-full items-center gap-2">
        {item.customIcon || (Icon && <Icon size={16} />)}
        {item.title}
      </span>
    );

    if (item.href) {
      return (
        <DropdownMenuItem key={item.type} asChild>
          <Link href={item.href as any} className="flex w-full cursor-pointer items-center gap-2">
            {content}
          </Link>
        </DropdownMenuItem>
      );
    }

    return (
      <DropdownMenuItem key={item.type} onClick={item.onClick}>
        <div className="cursor-pointer">{content}</div>
      </DropdownMenuItem>
    );
  };

  const renderButton = (button: ButtonGroupItem) => {
    if (button.hide) return null;

    const Icon = button.icon;
    const buttonIcon = button.isLoading ? (
      <Loader2 size={16} className="animate-spin" />
    ) : (
      button.customIcon || (Icon && <Icon size={16} />)
    );

    if (button.type === "dropdown") {
      return (
        <DropdownMenu key={button.title}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={button.variant || "default"}
              onClick={button.onClick}
              disabled={button.disabled}
              leftIcon={buttonIcon}>
              {button.title}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-fit">
            {button.items.map(renderDropdownItem)}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    return (
      <Button
        key={button.title}
        variant={button.variant || "default"}
        onClick={button.onClick}
        disabled={button.disabled}
        leftIcon={buttonIcon}>
        {button.title}
      </Button>
    );
  };

  return (
    <div className="flex gap-2">
      {buttons.map(renderButton)}
      {showRefresh && (
        <Button
          disabled={isRefreshLoading}
          variant="outline"
          size="icon"
          className="size-10"
          onClick={onRefresh}>
          <RefreshCw className={cn("size-4", isRefreshLoading ? "animate-spin" : "")} />
          <span className="sr-only">Refresh</span>
        </Button>
      )}
    </div>
  );
};

export default GroupButton;

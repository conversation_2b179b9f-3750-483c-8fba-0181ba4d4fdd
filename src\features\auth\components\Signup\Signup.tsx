"use client";

import { useRouter } from "next/navigation";
import { CircleAlert } from "lucide-react";
import { useTranslation } from "react-i18next";

import { BrandSection } from "@/features/auth/components/brand-section";
import { Footer } from "@/features/auth/components/public-footer";
import { useSignup } from "@/features/auth/hooks/useSignup";

import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

export const Signup = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const { signupSubmit, loading, form, error, handleFocus } = useSignup();

  return (
    <div className="min-h-screen w-screen bg-bg-secondary">
      <div className="grid min-h-screen w-screen lg:grid-cols-[1fr_2fr]">
        <BrandSection />
        <div className="relative flex flex-col justify-between p-8">
          <div className="flex flex-1 items-center justify-center">
            <div className="relative z-10 w-full max-w-[400px] space-y-6">
              <div className="space-y-1.5">
                <h1 className="text-2xl font-semibold tracking-tight">{t("auth.signUpTitle")}</h1>
                <p className="text-sm text-muted-foreground">{t("auth.signUpSubtitle")}</p>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(signupSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>{t("auth.email")}</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              autoComplete="email"
                              placeholder={t("auth.emailPlaceholderSignUp")}
                              disabled={loading}
                              error={fieldState.error?.message}
                              onFocus={handleFocus}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="username"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>{t("auth.username")}</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t("auth.usernamePlaceholder")}
                              disabled={loading}
                              error={fieldState.error?.message}
                              onFocus={handleFocus}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>{t("auth.password")}</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="password"
                              placeholder={t("auth.passwordPlaceholder")}
                              disabled={loading}
                              error={fieldState.error?.message}
                              onFocus={handleFocus}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>{t("auth.confirmPassword")}</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="password"
                              placeholder={t("auth.confirmPasswordPlaceholder")}
                              disabled={loading}
                              error={fieldState.error?.message}
                              onFocus={handleFocus}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    {error && (
                      <div className="flex items-center text-sm text-red-500">
                        <CircleAlert className="mr-2 size-4" />
                        {t(String(error))}
                      </div>
                    )}
                    <Button type="submit" disabled={loading} loading={loading} className="w-full">
                      {loading ? t("common.loading") : t("auth.signUpButton")}
                    </Button>
                  </div>
                </form>
              </Form>

              <div className="space-x-2 text-center text-sm">
                <span>{t("auth.alreadyHaveAccount")}</span>
                <Button
                  type="button"
                  variant="link"
                  className="h-fit p-0 text-sm font-normal leading-none"
                  onClick={() => router.push("/login")}
                  disabled={loading}>
                  {t("auth.login")}
                </Button>
              </div>
            </div>
          </div>

          <Footer />
        </div>
      </div>
    </div>
  );
};

/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TagInput } from "emblor";
import { X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

import {
  useAddProduct,
  useBrands,
  useCategories,
  usePriceGroups,
} from "@/features/products/hooks/product";
import {
  addQuickProductSchema,
  type AddQuickProductValues,
} from "@/features/products/utils/validators/add-quick";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Combobox } from "@/components/ui/combobox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { productApi } from "@/lib/apis/product";
import { createI18nResolver } from "@/lib/utils";
import { formatPrice, parsePrice } from "@/utils/helpers/price-formater";

import { productKeys } from "../hooks/keys";
import { useProducts } from "../hooks/product";
import { CreateProduct } from "../hooks/types";
import type { Image as UploadImage } from "../hooks/types";
import { AddBrandDialog } from "./dialogs/add-brand-dialog";
import { AddCategoryDialog } from "./dialogs/add-category-dialog";

interface AddQuickProductProps {
  onClose: () => void;
}

interface Tag {
  id: string;
  text: string;
}

export function AddQuickProduct({ onClose }: AddQuickProductProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [addBrandOpen, setAddBrandOpen] = useState(false);
  const [addCategoryOpen, setAddCategoryOpen] = useState(false);
  const [newItemName, setNewItemName] = useState("");
  const [tags, setTags] = useState<Tag[]>([]);
  const [activeTagIndex, setActiveTagIndex] = useState<number | null>(null);
  const queryClient = useQueryClient();
  const MAX_TAGS = 10;

  const form = useZodForm({
    schema: addQuickProductSchema,
    resolver: createI18nResolver(addQuickProductSchema, t),
    defaultValues: {
      name: "",
      sku: "",
      images: [],
      price: "",
      brand: "",
      category: "",
      publish: false,
      tags: null,
    },
    mode: "onTouched",
  });

  const {
    data: brandsData,
    fetchNextPage: fetchNextBrands,
    hasNextPage: hasNextBrands,
    isFetchingNextPage: isFetchingNextBrands,
  } = useBrands();

  const {
    data: categoriesData,
    fetchNextPage: fetchNextCategories,
    hasNextPage: hasNextCategories,
    isFetchingNextPage: isFetchingNextCategories,
  } = useCategories();

  const { data: priceGroupsData } = usePriceGroups();
  const { refetch } = useProducts({
    limit: 10,
  });

  const allBrands = useMemo(
    () => brandsData?.pages.flatMap((page) => page.items) ?? [],
    [brandsData]
  );

  const allCategories = useMemo(
    () => categoriesData?.pages.flatMap((page) => page.items) ?? [],
    [categoriesData]
  );

  const addProduct = useAddProduct({
    onSuccess: (response) => {
      toast(t("pages.products.addManual.success"), {
        description: t("pages.products.addManual.successDescription"),
      });
      refetch();
      onClose();
      router.push(`/products/${response.id}`);
    },
    onError: (error) => {
      console.error("Error creating product:", error);
      toast(t("pages.products.addManual.error"), {
        description: t("pages.products.addManual.errorDescription"),
      });
    },
  });

  // Create brand mutation
  const createBrandMutation = useMutation({
    mutationFn: async (data: { name: string; image: string | null }) => {
      return await productApi.createBrand({
        name: data.name,
        image: data.image ? { name: "image", image: data.image, prefix: "media" } : null,
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: productKeys.brands() });
      form.setValue("brand", data.id);
      setAddBrandOpen(false);
    },
  });

  // Create category mutation
  const createCategoryMutation = useMutation({
    mutationFn: async (data: {
      name: string;
      parent_category_id: string | null;
      image: string | null;
    }) => {
      return await productApi.createCategory({
        name: data.name,
        image: data.image ? { name: "image", image: data.image, prefix: "media" } : null,
        parent_category_id: data.parent_category_id,
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: productKeys.categories() });
      form.setValue("category", data.id);
      setAddCategoryOpen(false);
    },
  });

  const handleBrandSubmit = async (data: { name: string; image: UploadImage | null }) => {
    const result = await createBrandMutation.mutateAsync({
      name: data.name,
      image: data.image?.url || null,
    });
    return result;
  };

  const handleCategorySubmit = async (data: {
    name: string;
    parent_category_id: string | null;
    image: UploadImage | null;
  }) => {
    const result = await createCategoryMutation.mutateAsync({
      name: data.name,
      parent_category_id: data.parent_category_id,
      image: data.image?.url || null,
    });
    return result;
  };

  const onSubmit = async (data: AddQuickProductValues) => {
    try {
      setLoading(true);
      // Create price entries for all price groups
      const prices =
        priceGroupsData?.items.map((group) => ({
          price: parsePrice(data.price),
          price_group: {
            id: group.id,
            name: group.name,
          },
        })) || [];

      const productData: CreateProduct = {
        name: data.name,
        sku: data.sku,
        description: "",
        shortDescription: "",
        brand: data.brand ? { id: data.brand, name: "" } : null,
        category: data.category ? { id: data.category, name: "" } : null,
        tags: tags.map((tag) => tag.text).join(","),
        images: data.images || [],
        options: [],
        prices: prices,
        publish: data.publish,
        variants: [
          {
            id: uuidv4(),
            name: data.name,
            sku: data.sku,
            barcode: null,
            prices: prices,
            images: data.images && data.images.length > 0 ? [data.images[0]] : [],
            option1: "",
            option2: "",
            option3: "",
            measurements: {
              weight_value: 0,
              weight_unit: "g",
              height_value: 0,
              height_unit: "cm",
              width_value: 0,
              width_unit: "cm",
              length_value: 0,
              length_unit: "cm",
            },
          },
        ],
        measurements: {
          weight_value: 0,
          weight_unit: "g",
          height_value: 0,
          height_unit: "cm",
          width_value: 0,
          width_unit: "cm",
          length_value: 0,
          length_unit: "cm",
        },
      };

      await addProduct.mutateAsync(productData);
    } catch (error) {
      console.error("Error creating product:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (base64: string | null) => {
    if (!base64) {
      return;
    }

    try {
      const images = JSON.parse(base64) as string[];
      const processedImages = images.map((image) => {
        const nameMatch = image.match(/;name=(.*?)(;|$)/);
        const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : "image.jpg";
        return {
          name: filename,
          image: image,
        };
      });

      form.setValue("images", processedImages);
    } catch (error) {
      console.error("Error processing images:", error);
      toast.error("Failed to process images");
    }
  };

  const hasUnsavedChanges = () => {
    return Object.keys(form.formState.dirtyFields).length > 0;
  };

  const handleCancel = () => {
    if (hasUnsavedChanges()) {
      setShowCancelDialog(true);
    } else {
      onClose();
    }
  };

  const handleTagsChange = (value: Tag[] | ((prev: Tag[]) => Tag[])) => {
    const newTags = typeof value === "function" ? value(tags) : value;
    if (newTags.length <= MAX_TAGS) {
      setTags(newTags);
      form.setValue("tags", newTags.map((tag) => tag.text).join(","));
    } else {
      toast.error(`Maximum ${MAX_TAGS} tags allowed`);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="flex max-h-[90vh] w-full max-w-3xl flex-col rounded-lg bg-card">
        <div className="flex items-center justify-between p-6">
          <h2 className="text-xl font-semibold">Add Quick Product</h2>
          <Button variant="ghost" size="icon" onClick={handleCancel}>
            <X className="size-4" />
          </Button>
        </div>

        <Form {...form}>
          <div className="flex-1 overflow-y-auto px-6 pb-6 pt-2">
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>
                      {t("pages.products.addManual.basicInfo.name")}{" "}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="space-y-1">
                        <Input
                          {...field}
                          placeholder="Enter product name"
                          error={fieldState.error?.message}
                        />
                        <p className="text-xs text-muted-foreground">{field.value.length}/250</p>
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sku"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>
                      {t("pages.products.addManual.basicInfo.sku")}{" "}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter product SKU"
                        error={fieldState.error?.message}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="images"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("pages.products.addManual.basicInfo.images")}</FormLabel>
                    <FormControl>
                      <ImageUpload
                        value={
                          field.value && field.value.length > 0
                            ? JSON.stringify(field.value.map((img: { image: string }) => img.image))
                            : null
                        }
                        onChange={handleImageUpload}
                        multiple
                        className="size-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>
                      {t("pages.products.addManual.basicInfo.price")}{" "}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        error={fieldState.error?.message}
                        type="text"
                        placeholder="Enter price"
                        value={formatPrice(field.value)}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (/^[0-9,]*$/.test(value)) {
                            field.onChange(parsePrice(value).toString());
                          }
                        }}
                        suffix="đ"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="brand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("pages.products.addManual.basicInfo.brand")}</FormLabel>
                    <FormControl>
                      <Combobox
                        value={field.value}
                        onValueChange={field.onChange}
                        items={allBrands.map((brand) => ({
                          id: brand.id,
                          name: brand.name,
                          displayValue: brand.name,
                        }))}
                        placeholder="Select brand"
                        searchPlaceholder="Search brand..."
                        emptyText="No brands found."
                        onCreateNew={(name) => {
                          setNewItemName(name);
                          setAddBrandOpen(true);
                        }}
                        onLoadMore={fetchNextBrands}
                        hasNextPage={hasNextBrands}
                        isLoadingMore={isFetchingNextBrands}
                        onClear={() => field.onChange("")}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("pages.products.addManual.basicInfo.category")}</FormLabel>
                    <FormControl>
                      <Combobox
                        value={field.value}
                        onValueChange={field.onChange}
                        items={allCategories.map((category) => ({
                          id: category.id,
                          name: category.name,
                          displayValue: category.name,
                        }))}
                        placeholder="Select category"
                        searchPlaceholder="Search category..."
                        emptyText="No categories found."
                        onCreateNew={(name) => {
                          setNewItemName(name);
                          setAddCategoryOpen(true);
                        }}
                        onLoadMore={fetchNextCategories}
                        hasNextPage={hasNextCategories}
                        isLoadingMore={isFetchingNextCategories}
                        onClear={() => field.onChange("")}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <label className="mb-1.5 block text-sm font-medium">
                  {t("pages.products.addManual.basicInfo.tags")}
                </label>
                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => (
                    <TagInput
                      {...field}
                      placeholder="Enter tags"
                      tags={tags}
                      setTags={handleTagsChange}
                      activeTagIndex={activeTagIndex}
                      setActiveTagIndex={setActiveTagIndex}
                      styleClasses={{
                        tag: {
                          body: "bg-muted text-bg-background py-0.5",
                        },
                        input: "bg-transparent p-0 ps-0 shadow-none",
                        inlineTagsContainer: "bg-transparent rounded-md px-3 py-2 text-sm gap-1",
                      }}
                      truncate={20}
                      maxLength={30}
                      maxTags={MAX_TAGS}
                      shape={"rounded"}
                    />
                  )}
                />
                <span className="text-xs text-muted-foreground">
                  {tags.length}/{MAX_TAGS}
                </span>
              </div>
            </form>
          </div>

          <div className="flex items-center justify-between rounded-b-lg border-t bg-card p-6">
            <div className="flex items-center gap-2">
              <FormField
                control={form.control}
                name="publish"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel className="!mt-0">Publish</FormLabel>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex gap-2">
              <Button
                className="inline-flex min-w-20 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
                type="button"
                variant="outline"
                onClick={handleCancel}>
                {t("pages.products.addManual.buttons.cancel")}
              </Button>
              <Button
                className="inline-flex min-w-20 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                type="submit"
                disabled={loading}
                loading={loading}
                onClick={() => form.handleSubmit(onSubmit)()}>
                {t("pages.products.addManual.buttons.add")}
              </Button>
            </div>
          </div>
        </Form>

        <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                {t("pages.products.addManual.dialogs.leaveDesc")}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setShowCancelDialog(false)}>
                {t("pages.products.addManual.buttons.stay")}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  setShowCancelDialog(false);
                  onClose();
                }}>
                {t("pages.products.addManual.buttons.leave")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <AddBrandDialog
          open={addBrandOpen}
          onOpenChange={setAddBrandOpen}
          onSubmit={handleBrandSubmit}
          initialName={newItemName}
        />

        <AddCategoryDialog
          open={addCategoryOpen}
          onOpenChange={setAddCategoryOpen}
          onSubmit={handleCategorySubmit}
          categories={categoriesData?.pages[0]?.items || []}
          initialName={newItemName}
        />
      </div>
    </div>
  );
}

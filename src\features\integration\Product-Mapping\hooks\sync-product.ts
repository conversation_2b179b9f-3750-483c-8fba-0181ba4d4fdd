import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { syncProductAPI } from "@/lib/apis/connection";

import { productMappingKeys, syncProductKeys } from "./keys";

interface SyncProductPayload {
  sync_record_ids?: string[];
}

export const useSyncProduct = (connectionId: string) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [isSyncing, setIsSyncing] = useState(false);

  const syncMutation = useMutation({
    mutationKey: syncProductKeys.sync(connectionId),
    mutationFn: async (productIds?: string[]) => {
      setIsSyncing(true);
      try {
        const payload: SyncProductPayload = {};

        // Only include sync_record_ids if productIds are provided
        if (productIds && productIds.length > 0) {
          payload.sync_record_ids = productIds;
        }

        return await syncProductAPI.sync(connectionId, payload);
      } finally {
        setIsSyncing(false);
      }
    },
    onSuccess: () => {
      toast.success(t("pages.productMappingList.syncSuccess"));

      // Invalidate queries to refresh the list after sync
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: productMappingKeys.lists() });
      }, 5000);
    },
    onError: (error) => {
      console.error("Sync error:", error);
      toast.error(t("pages.productMappingList.syncFail"));
    },
  });

  const handleSync = async (productIds?: string[]) => {
    if (!connectionId) {
      toast.error(t("pages.productMappingList.noConnection"));
      return;
    }

    return syncMutation.mutateAsync(productIds);
  };

  return {
    handleSync,
    isSyncing: isSyncing || syncMutation.isPending,
    isSuccess: syncMutation.isSuccess,
  };
};

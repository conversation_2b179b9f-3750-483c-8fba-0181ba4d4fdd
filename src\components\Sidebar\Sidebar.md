# Sidebar Component

## Description
The Sidebar is a main navigation component of the application, providing access to various features and functionalities. This component supports internationalization, dark mode, and can be collapsed/expanded.

## Props
```typescript
interface SidebarProps {
  className?: string;                    // Custom CSS classes
  defaultCollapsed?: boolean;            // Default collapsed state
  user?: {                              // User information
    name: string;
    email: string;
    image?: string;
  };
  currentBranch?: string;               // Current branch
  branches?: Branch[];                  // List of branches
  onBranchChange?: (branch: string) => void;  // Callback when branch changes
  onLogout?: () => void;                // Callback when logging out
}
```

## Features

### 1. Navigation
- Main navigation menu with items like Dashboard, Products, Orders, etc.
- Support for sub-items in complex menu items
- Active state display for current page
- Menu grouping with group titles

### 2. Branch Management
- Switch between branches
- Display icons and counts for each branch
- Keyboard shortcuts: Alt + [1-9] for quick branch switching
- Add new branch functionality

### 3. Profile Menu
- Display user information
- Toggle dark/light mode
- Change language (English/Vietnamese)
- Logout functionality

### 4. Responsive Design
- Collapsible sidebar on desktop
- Drawer menu with overlay on mobile
- Automatic layout adjustment based on screen size

### 5. Accessibility
- Keyboard navigation support
- Proper ARIA labels and roles
- Focus management

### 6. Internationalization
- Multi-language support through i18next
- Automatic content translation based on selected language

## Keyboard Shortcuts
- `Alt + [1-9]`: Quick switch between branches
- `Ctrl/Cmd + K`: Open branch switch menu
- `Ctrl/Cmd + B`: Open branch selection menu

## Usage

```tsx
import { Sidebar } from '@/components/Sidebar/Sidebar'

// Basic usage
<Sidebar />

// With all props
<Sidebar
  defaultCollapsed={false}
  user={{
    name: "John Doe",
    email: "<EMAIL>",
    image: "/avatar.png"
  }}
  currentBranch="Main Branch"
  onBranchChange={(branch) => console.log(`Switched to ${branch}`)}
  onLogout={() => console.log('Logged out')}
/>
```

## Dependencies
- next/image
- next/link
- next/navigation
- next-themes
- next-i18next
- lucide-react
- tailwind-merge
- class-variance-authority
- @radix-ui/react-dropdown-menu

## Styling
- Uses Tailwind CSS
- Dark mode compatible
- Custom styling via className prop
- Smooth animations and transitions

## Best Practices
1. Always provide alt text for images
2. Use aria-labels for accessibility
3. Handle all states (loading, error, etc.)
4. Optimize performance with Next.js Image
5. Responsive design for all screen sizes 
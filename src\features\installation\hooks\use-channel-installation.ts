import { useEffect, useRef, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { channelApi } from "@/lib/apis/channel";
import { setAuthToken } from "@/lib/auth";
import { transformUserInfo } from "@/lib/transforms";
import { COOKIES } from "@/utils/constants/cookies";
import { setCookie } from "@/utils/cookie";

type InstallStatus = "loading" | "error";

interface UseChannelInstallationReturn {
  status: InstallStatus;
  errorMessage: string;
}

interface UseChannelInstallationProps {
  sourceChannel: string | undefined | null;
  destinationChannel?: string | undefined | null;
}

/**
 * Hook để xử lý quá trình cài đặt/kết nối giữa các kênh
 * Tự động lấy tham số từ URL và gọi API để cài đặt kết nối
 *
 * @param sourceChannel - Loại kênh nguồn (ví dụ: "shopify_oauth")
 * @param destinationChannel - <PERSON><PERSON><PERSON> kênh đích (ví dụ: "tiktok_shop")
 */
export const useChannelInstallation = ({
  sourceChannel,
  destinationChannel,
}: UseChannelInstallationProps): UseChannelInstallationReturn => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<InstallStatus>("loading");
  const [errorMessage, setErrorMessage] = useState<string>("");

  // Sử dụng ref để theo dõi xem API đã được gọi chưa
  const apiCalledRef = useRef(false);

  useEffect(() => {
    const handleInstallation = async () => {
      try {
        // Đảm bảo API chỉ được gọi một lần
        if (apiCalledRef.current) return;

        // Kiểm tra sourceChannel
        if (!sourceChannel) {
          setStatus("error");
          setErrorMessage("install.missingSourceChannel");
          return;
        }

        // Đánh dấu rằng API đã được gọi
        apiCalledRef.current = true;

        // Lấy các params từ URL
        const shop = searchParams.get("shop");
        const host = searchParams.get("host");
        const hmac = searchParams.get("hmac");
        const timestamp = searchParams.get("timestamp");

        if (!shop || !host || !hmac || !timestamp) {
          throw new Error("Missing required parameters");
        }

        // Gọi API để hoàn tất quá trình cài đặt
        const response = await channelApi.installConnection({
          shop,
          host,
          hmac,
          timestamp,
          source_channel: sourceChannel,
          ...(destinationChannel && { destination_channel: destinationChannel }),
        });

        if (response?.exists) {
          router.push("/channels");
        }

        // Save token and user attributes
        if (response?.token && response?.user_attributes) {
          // Transform data và lưu token
          const transformedData = transformUserInfo(response.user_attributes, response.token);
          setAuthToken(transformedData);
        }
        if (response?.destination_id && response?.source_id) {
          const cookieData = {
            destination_id: response.destination_id,
            source_id: response.source_id,
            status: COOKIES.INSTALLATION_STATUS.INSTALLED,
          };
          setCookie(COOKIES.INSTALLATION_DATA, cookieData);
        }

        const authorization_url = response?.authorization_url;
        if (authorization_url) {
          // Chuyển hướng trong cùng tab hiện tại
          window.location.href = authorization_url;
        }
      } catch (error) {
        console.error("Installation error:", error);
        setStatus("error");
        setErrorMessage(error instanceof Error ? error.message : String(error));
      }
    };

    handleInstallation();
  }, [router, searchParams, sourceChannel, destinationChannel]);

  return { status, errorMessage };
};

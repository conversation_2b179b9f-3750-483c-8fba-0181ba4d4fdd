import { useMutation, useQuery } from "@tanstack/react-query";

import { transformationAPI } from "@/lib/apis/connection";

import { TRANSFORMATION_KEYS } from "./keys";
import { TransformationPayload } from "./types";

export const useTransformations = () => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: TRANSFORMATION_KEYS.list(),
    queryFn: async () => {
      const response = await transformationAPI.list();
      return response.data;
    },
  });

  return {
    // The array of transformations is in the data property of the response
    transformations: data || [],
    isLoading,
    isError,
    error,
    refetch,
  };
};

export const useHandleTransformation = () => {
  const mutation = useMutation({
    mutationKey: TRANSFORMATION_KEYS.handleTransformation(),
    mutationFn: async (data: TransformationPayload) => {
      const response = await transformationAPI.handleTransformation(data);
      return response.data;
    },
  });

  return {
    executeTransformation: mutation.mutateAsync,
    data: mutation.data,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
    reset: mutation.reset,
  };
};

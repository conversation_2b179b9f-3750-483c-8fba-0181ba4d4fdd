"use client";

import Image from "next/image";
import { ClipboardList, Package, RefreshCw, Warehouse } from "lucide-react";

import { PlatformIntegrationProps } from "@/features/installation/hooks/type";

// Component hiển thị nền tảng
const Platform = ({
  name,
  logo,
  logoAlt,
}: {
  name: string;
  logo: any;
  logoAlt: string;
  logoBackground?: string;
}) => (
  <div className="flex flex-col items-center">
    <div className={`mb-2 flex size-20 items-center justify-center`}>
      <Image src={logo} alt={logoAlt || `${name} Logo`} width={80} height={80} />
    </div>
    <span className="font-medium">{name}</span>
  </div>
);

export const PlatformIntegration: React.FC<PlatformIntegrationProps> = ({
  leftPlatform,
  rightPlatform,
  syncSettings,
}) => {
  return (
    <div className="flex items-center justify-between bg-card p-8 pb-6">
      {/* Left Platform */}
      <Platform name={leftPlatform.name} logo={leftPlatform.logo} logoAlt={leftPlatform.logoAlt} />

      {/* Connection Icon */}
      <div className="flex flex-col items-center">
        <div className="mb-4 flex size-10 items-center justify-center">
          <RefreshCw className="size-8 text-primary" />
        </div>
        <div className="mt-2 flex gap-6 text-muted-foreground">
          <Package
            className={`size-6 transition-all duration-700 ease-in-out ${
              syncSettings.products && "text-sematic-info"
            }`}
          />
          <Warehouse
            className={`size-6 transition-all duration-700 ease-in-out ${
              syncSettings.inventory && "text-sematic-warning"
            }`}
          />
          <ClipboardList
            className={`size-6 transition-all duration-700 ease-in-out ${
              syncSettings.orders && "text-sematic-success"
            }`}
          />
        </div>
      </div>

      {/* Right Platform */}
      <Platform
        name={rightPlatform.name}
        logo={rightPlatform.logo}
        logoAlt={rightPlatform.logoAlt}
      />
    </div>
  );
};

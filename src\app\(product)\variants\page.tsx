"use client";

import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/variants/components/VariantList/column";
import { useVariants } from "@/features/variants/hooks/variant";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";

export default function VariantsPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );
  const { variants, total, isLoading, isFetching, refetch, useDeleteVariantMutation } =
    useVariants(options);

  const isTableLoading = isLoading || isFetching;
  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "variants",
      searchPlaceHolder: t("pages.products.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "product_id",
          type: "selectBox",
          title: t("pages.products.filters.product"),
          defaultValue: getInitialParams["product_id"],
          pathUrlLoad: "product/products",
          remote: true,
        },
        {
          id: "category.id",
          type: "selectBox",
          title: t("pages.products.filters.category"),
          remote: true,
          pathUrlLoad: "product/categories",
          defaultValue: getInitialParams["category.id"],
        },
        {
          id: "brand.id",
          type: "selectBox",
          title: t("pages.products.filters.brand"),
          remote: true,
          pathUrlLoad: "product/brands",
          defaultValue: getInitialParams["brand.id"],
        },
        {
          id: "created_at",
          type: "date",
          title: t("pages.products.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: "date",
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);
  const groupButtonConfig: GroupButtonProps = {
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.variants.title")}
        filterType="variants"
        data={variants || []}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(useDeleteVariantMutation, isFetching, t)}
        data={variants || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
      />
    </TableCard>
  );
}

import { Variant } from "@/features/products/hooks/types";

export interface SyncRecordDetail {
  id: string;
  company_id: string;
  channels: string;
  record_type: string;
  connection_id: string;
  is_source: boolean;
  standard_source_data: StandardSourceData;
}

export interface StandardSourceData {
  id: string;
  status: string;
  connection_id: string;
  created_at: string;
  fetch_event_id: string;
  images: string[];
  sku: string;
  title: string;
  updated_at: string;
  variants: Variant[];
}

import { TokenData, UserInfoResponse } from "./auth";

export interface Channel {
  key: string;
  name: string;
  channel_type: string;
  description: string;
  logo: string;
  installed?: boolean;
}

export interface ChannelResponse {
  connection_types: Channel[];
}

export interface ConnectionPayload {
  connection_type: string;
  settings: Record<string, any>;
}

export interface ConnectionResponse {
  connection_id?: string;
  authorization_url?: string;
  message?: string;
}

export interface ConnectionDetail {
  user: any;
  settings: Record<string, any>;
  company_id: string;
  dynamic_settings: Record<string, any>;
  action_groups: Record<string, any>;
  name: string;
  status: string;
  token_type: string;
  webhook_settings: Record<string, any>;
  auth_type: string;
  external_id: string;
  id: string;
  access_token: string;
  created_at: string;
  logo: string;
  updated_at: string;
  expires_at: string;
  channel_name: string;
  token_data: Record<string, any>;
  type: string;
  description: string;
  channel_type: string;
  refresh_token: string;
}

export interface InstallConnectionResponse {
  authorization_url?: string;
  message?: string;
  company_id?: string;
  username?: string;
  user_attributes?: UserInfoResponse;
  token?: TokenData;
  exists?: boolean;
  destination_id?: string;
  source_id?: string;
}

export interface InstallConnectionParams {
  shop: string;
  host: string;
  hmac: string;
  timestamp: string;
  destination_channel?: string;
  source_channel?: string;
}

export interface SetupFieldsResponse {
  setup_fields: Array<{
    key: string;
    label: string;
    required: boolean;
    type: string;
    options?: Array<{
      label: string;
      value: string;
    }>;
    default?: string;
  }>;
  description?: string;
}

export interface SyncSettingsPayload {
  source: string;
  destination: string;
  actions: Record<string, any>;
}

export interface SyncSettingsResponse {
  message: string;
  authorization_url?: string;
  connection_id?: string;
}

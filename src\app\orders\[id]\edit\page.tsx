"use client";

import { notFound, usePara<PERSON>, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { AddOrderManual } from "@/features/orders/components/AddOrder/add_order_manual";
import { OrderDetailSkeleton } from "@/features/orders/components/skeleton/order_detail_skeleton";
import { useOrderDetail, useUpdateOrder } from "@/features/orders/hooks/order";

import { variantApi } from "@/lib/apis/product";

export default function EditOrderPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const { t } = useTranslation();
  const { order, isLoading: orderLoading } = useOrderDetail(id as string);
  const { mutateAsync: updateOrder, isPending: isUpdating } = useUpdateOrder();
  // Get all SKUs from order items that are not services
  const productSkus =
    order?.order_line_items.filter((item) => !item.custom).map((item) => item.sku) || [];

  // Fetch variants data
  const { data: variantsData, isLoading: variantsLoading } = useQuery({
    queryKey: ["variants", productSkus],
    queryFn: async () => {
      if (productSkus.length === 0) return [];
      const variants = await Promise.all(
        productSkus.map(async (sku) => {
          const response = await variantApi.list({ query: sku });
          return response.items?.[0];
        })
      );
      return variants.filter(Boolean);
    },
    enabled: productSkus.length > 0 && Boolean(order),
  });

  // Show loading state while either order or variants are loading
  if (orderLoading || (productSkus.length > 0 && variantsLoading)) {
    return <OrderDetailSkeleton />;
  }

  if (!order) {
    notFound();
  }

  // Create new order items from variants data
  const orderItems =
    variantsData?.map((variant) => {
      // Find matching order line item to get quantity and note
      const matchingOrderItem = order.order_line_items.find((item) => item.sku === variant.sku);

      const matchingInventory = variant.inventories?.find(
        (inv: any) => inv.location_id === order.location.id
      );

      return {
        variant_id: variant.id,
        name: variant.name,
        sku: variant.sku,
        image: variant.images?.[0]?.url || "",
        quantity: Number(matchingOrderItem?.quantity) || 1,
        price: variant.price || 0,
        total: (variant.price || 0) * (Number(matchingOrderItem?.quantity) || 0),
        note: matchingOrderItem?.note || "",
        custom: false,
        discount: Number(matchingOrderItem?.discount || 0),
        variant: {
          ...variant,
          inventories: [
            {
              location_id: order.location.id,
              available: matchingInventory?.available || 0,
              on_hand: matchingInventory?.on_hand || 0,
              cost: matchingInventory?.cost || 0,
              company_id: matchingInventory?.company_id || "",
            },
          ],
        },
      };
    }) || [];

  // Add service items
  const serviceItems = order.order_line_items
    .filter((item) => item.custom)
    .map((item) => ({
      variant_id: item.sku,
      name: item.name,
      sku: item.sku,
      image: "",
      quantity: Number(item.quantity),
      price: Number.parseFloat(item.unit_price),
      unit_price: Number.parseFloat(item.unit_price),
      sale_price: Number.parseFloat(item.sale_price || item.unit_price),
      total: Number.parseFloat(item.sale_price || item.unit_price) * Number(item.quantity),
      discount: Number.parseFloat(item.discount || "0"),
      custom: true,
      variant: {
        id: item.sku,
        name: item.name,
        sku: item.sku,
        custom: true,
        price: Number.parseFloat(item.unit_price),
      },
    }));

  const allOrderItems = [...(orderItems || []), ...serviceItems];

  const handleSubmit = async (values: any) => {
    try {
      // Check for empty service names
      const hasEmptyService = values.order_line_items.some(
        (item: any) => item.custom && !item.name?.trim()
      );
      if (hasEmptyService) {
        // toast.error(t("pages.orders.emptyServiceName"));
        return;
      }
      // Create a deep copy of values to avoid mutating the original
      const payload = JSON.parse(JSON.stringify(values));

      // const defaultBillingAddress = payload.customer.addresses?.find(
      //   (addr: any) => addr.default_billing === true
      // );
      // const defaultShippingAddress = payload.customer.addresses?.find(
      //   (addr: any) => addr.default_shipping === true
      // );

      // payload.billing_address = defaultBillingAddress || payload.customer.billing_address || null;

      // // Set shipping address (prioritize default shipping address)
      // payload.shipping_address =
      //   defaultShippingAddress || payload.customer.shipping_address || null;

      // // Important: Always use the billing_address from customer directly
      // if (payload.customer.selected_billing_address) {
      //   payload.billing_address = payload.customer.selected_billing_address;
      // }
      // if (payload.customer.selected_shipping_address) {
      //   payload.shipping_address = payload.customer.selected_shipping_address;
      // }

      // Format order line items similar to add order mode
      payload.order_line_items = (payload.order_line_items || [])
        .filter((item: any) => item.quantity > 0)
        .map((item: any) => {
          const { variant, total, ...res } = item;

          // Ensure location in each order item only has id and name
          if (res.location) {
            res.location = {
              id: res.location.id,
              name: res.location.name,
            };
          }

          // Simplify brand object to only include id and name
          if (res.brand) {
            res.brand = {
              id: res.brand.id,
              name: res.brand.name,
            };
          }

          // Simplify category object to only include id and name
          if (res.category) {
            res.category = {
              id: res.category.id,
              name: res.category.name,
            };
          }

          // Check if this is a service item (no product_id or has custom flag)
          if (!res.product_id || res.custom) {
            res.custom = true;

            // Remove variant-related fields from service items
            delete res.variant_id;
            delete res.variant_name;
            delete res.product_id;

            // Set image_url to null for service items
            res.image_url = null;
          }

          return res;
        });

      // Add the order ID to the payload
      payload.id = id;

      // Call the update mutation
      await updateOrder(payload);

      toast.success("Order updated successfully");
      router.push(`/orders/${id}`);
    } catch (error) {
      console.error("Error updating order:", error);
      toast.error("Failed to update order");
    }
  };

  const handleCancel = () => {
    router.push(`/orders/${id}`);
  };

  return (
    <div className="min-h-screen bg-background">
      <AddOrderManual
        order={order}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        initialOrderItems={allOrderItems}
        selectedLocationId={order.location.id}
        isEditMode={true}
        initialShippingAddress={order.shipping_address}
        initialBillingAddress={order.billing_address}
        isUpdating={isUpdating}
      />
    </div>
  );
}

{"nav": {"overview": "Overview", "patientManagement": "Patient Management", "doctorManagement": "Doctor Management", "medicalSupplies": "Medical Supplies", "invoicesPayments": "Invoices & Payments", "report": "Report", "administration": "Administration", "product": "Product", "productList": "Product List", "newProduct": "Add manual product", "editProduct": "Edit product", "variantsList": "Variant List", "brandList": "Brand List", "categoryList": "Category List", "order": "Order", "orderList": "Order List", "orderDetail": "Order Detail", "orderEdit": "Edit Order", "orderProcess": "Order Process", "returnOrderList": "Return Order List", "packageList": "Package List", "integration": "Integration", "fetchEvent": "Fetch Event", "detailFetchEvent": "Fetch Event Detail", "syncRecords": "Sync Records", "channel": "Channel", "supportedChannels": "Channel List", "installChannel": "Install new channel", "logistics": "Logistics", "shippingProviderList": "Shipping Provider List", "purchaseOrder": "Purchase Order", "purchaseOrderList": "Purchase Order List", "supplierList": "Supplier List", "customers": "Customers", "customerDashboard": "Dashboard", "customerList": "Customer List", "customerDetail": "Customer Detail", "customerGroupList": "Customer Group List", "loyaltyProgram": "Loyalty Program", "rewardProgram": "Reward Program", "finance": "Finance", "account": "Account", "paymentMethod": "Payment Method", "transaction": "Transaction", "inventory": "Inventory", "locationList": "Location List", "inventoryList": "Inventory List", "stockAdjustmentList": "Stock Adjustment List", "stockRelocateList": "Stock Relocate List", "promotion": "Promotion", "discountList": "Discount List", "voucherList": "Voucher List", "import": "Import", "importList": "Import List", "recordList": "Record List", "website": "Website", "blogCategory": "Blog Category", "blogList": "Blog List", "notification": "Notification", "notificationList": "Notification List", "loyaltyApp": "Loyalty App", "pos": "POS", "terminalList": "Terminal List", "shiftList": "Shift List", "posFnB": "POS F&B", "settings": "Settings", "dashboard": "Dashboard", "productReport": "Product Report", "productDetail": "Product Details", "orderManual": "Add Order", "productMapping": "Product Mapping", "productMappingDetail": "Mapping Detail", "productMappingAttribute": "Product Mapping Attribute", "staff": "Staff", "staffList": "Staff List", "department": "Department", "knowledge": "Knowledge", "task": "Task", "conversation": "Conversation", "interact": "Interact", "editStaff": "Edit Staff"}, "product": {"image": "Product Image", "title": "Product Title", "description": "Description", "price": "Price", "sku": "SKU", "brand": "Brand", "category": "Category", "inventory": "Inventory", "notMapped": "Not mapped to destination"}, "productMapping": {"lastSynced": "Last synced", "errorLoading": "Error loading product mapping details", "manualRetry": "Manual Retry", "cancelledMessage": "Product mapping cancelled", "mappingStatus": "Mapping status", "variant": "Variants"}, "groups": {"operations": "Operations", "virtual_staff": "Virtual Staff"}, "branch": {"title": "Select location", "branch": "Branch", "all": "All Branches", "addBranch": "Add New Branch", "shortcuts": {"alt": "Alt", "plus": "+"}, "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "annually": "Annually", "refresh": "Refresh"}, "profile": {"profile": "Profile", "settings": "Settings", "darkMode": "Dark mode", "on": "On", "off": "Off", "language": "Language", "english": "English", "vietnamese": "Vietnamese", "logout": "Log out", "founder": "Founder"}, "auth": {"username": "Username", "usernamePlaceholder": "Enter username", "login": "Log in", "register": "Register", "forgotPasswordDescription": "Enter your email and instructions will be sent to you!", "forgotPasswordTitle": "Forgot Password?", "forgotPasswordSubtitle": "Enter your email to receive a password reset instruction", "resetPassword": "Reset password", "resetPasswordTitle": "Reset password", "resetPasswordDescription": "Enter the verification code and new password", "resetPasswordSubtitle": "Enter the verification code and new password", "resetPasswordButton": "Reset password", "resetPasswordSuccess": "Password reset successfully", "resetPasswordSuccessDescription": "Now you can log in with your new password", "resetPasswordError": "Failed to reset password", "resetPasswordLoading": "Resetting...", "confirmPassword": "Confirm password", "confirmPasswordPlaceholder": "Confirm password", "backToLogin": "Back to log in", "backToForgotPassword": "Back to forgot password", "loginTitle": "<PERSON><PERSON>", "loginSubtitle": "Enter your username or email to log in to your account", "email": "Email", "emailPlaceholder": "m@example", "emailPlaceholderSignUp": "Enter email", "verifyEmail": "Verify email", "verifyEmailButton": "Verify email", "verifyEmailSuccess": "Email has been verified successfully", "verifyEmailError": "Failed to verify email", "verifyEmailLoading": "Verifying email...", "verifyEmailCode": "Enter the code sent to your email", "verifyEmailCodePlaceholder": "Enter the code", "verifyEmailCodeButton": "Verify code", "verifyEmailCodeSuccess": "Code has been verified successfully", "verifyEmailCodeError": "Failed to verify code", "verifyEmailCodeLoading": "Verifying code...", "newPassword": "New password", "newPasswordPlaceholder": "Enter new password", "verificationCode": "Verification code", "verificationCodePlaceholder": "Enter the code", "verificationCodeButton": "Verify", "verificationCodeSuccess": "Verification successful", "verificationCodeError": "Verification failed", "verificationCodeDescription": "We have sent a code to {{username}}. Enter it below.", "sendInstructions": "Send instructions", "sending": "Sending...", "resetting": "Resetting...", "password": "Password", "passwordPlaceholder": "Enter password", "rememberMe": "Remember me", "loginButton": "<PERSON><PERSON>", "loginWithGoogle": "Login with Google", "loginWithGithub": "Login with <PERSON><PERSON><PERSON>", "noAccount": "Don't have an account?", "signUp": "Sign up", "signUpTitle": "Sign up", "signUpSubtitle": "Sign up to log in to your admin panel.", "signUpButton": "Sign up", "signUpSuccess": "Sign up successful! Please verify your email", "signUpError": "Sign up failed", "signUpLoading": "Signing up...", "alreadyHaveAccount": "Already have an account?", "sendNewCode": "Resend", "resendCodeSuccess": "New code has been sent", "resendCodeError": "Failed to send new code", "usernameOrEmail": "Username or email", "usernameOrEmailPlaceholder": "Enter username or email", "forgot": "Forgot?", "or": "Or", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "loginLoading": "Logging in...", "usernameRequired": "Please enter username", "emailRequired": "Please enter email", "passwordRequired": "Please enter password", "confirmPasswordRequired": "Please confirm password", "invalidPassword": "Password must be at least 8 characters", "forgotPasswordSuccess": "Password reset instructions have been sent to your email", "forgotPasswordError": "Failed to send password reset instructions", "newPasswordRequired": "New password is required", "passwordsDoNotMatch": "Passwords do not match", "passwordMustBeAtLeast8Characters": "Password must be at least 8 characters", "codeRequired": "Please confirm the code", "resendCodeCountdown": "Resend in {{seconds}}s"}, "common": {"start": "Start", "back": "Back", "words": "words", "confirm": "Confirm", "apply": "Apply", "totalSize": "Total size", "search": "Search", "filter": "Filter", "reset": "Reset", "setAsDefault": "Set as default", "saveFilters": "Save filters", "sort": "Sort", "view": "View", "add": "Add", "update": "Update", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "close": "Close", "clear": "Clear", "loading": "Loading...", "loadingMore": "Loading more...", "deleting": "Deleting...", "noData": "No data available", "error": "An error occurred", "success": "Success", "uploadImage": "Drag an image here or", "upload": "Upload", "uploading": "Uploading...", "fileSizeError": "File size must be less than 5MB", "uploadError": "Failed to upload image", "imageUploadError": "Failed to upload staff image", "areYouSure": "Are you absolutely sure?", "leaveDesc": "Any unsaved changes will be lost.", "deleteProductConfirmation": "This action cannot be undone. This product will be permanently deleted.", "deleteListProductConfirmation": "This action cannot be undone.This {{count}} products will be permanently deleted.", "install": "Install", "configure": "Configure", "deleteSuccess": "Product deleted successfully", "deleteError": "Failed to delete product", "deleteSuccessDescription": "Product has been deleted successfully", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "saveChanges": "Save changes", "unsavedChanges": "Unsaved changes", "leaveWithoutSavingDescription": "You have unsaved changes. Are you sure you want to leave without saving?", "leaveWithoutSaving": "Leave without saving", "leave": "Leave", "stay": "Stay", "knowledgeUpdated": "Knowledge updated successfully", "areYouSureDescription": "Are you sure you want to delete this knowledge?", "staffUpdated": "Staff updated successfully", "updateStaffError": "Failed to update staff", "areYouSureConfirm": "Confirm", "areYouSureCancel": "Cancel", "updateAttribute": "Update attribute", "time": {"month": "Month", "timeAgo": {"seconds": "{{count}} second ago", "seconds_plural": "{{count}} seconds ago", "minutes": "{{count}} minute ago", "minutes_plural": "{{count}} minutes ago", "hours": "{{count}} hour ago", "hours_plural": "{{count}} hours ago", "days": "{{count}} day ago", "days_plural": "{{count}} days ago", "months": "{{count}} month ago", "months_plural": "{{count}} months ago", "years": "{{count}} year ago", "years_plural": "{{count}} years ago"}}, "empty": {"title": "There's nothing here!", "description": "No matching result found."}, "create": "Create", "noFileSelected": "No file selected", "uploadSuccess": "Upload successful", "fileTooLarge": "File exceeds maximum size of {{max}}MB"}, "pages": {"overview": {"title": "Overview", "filters": {"period": "Time Period", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "selectLocation": "Select location", "refresh": "Refresh"}, "stats": {"totalFacilities": "Total Facilities", "totalPatients": "Total Patients", "averageOccupancy": "Average Occupancy", "totalRevenue": "Total Revenue", "viewMore": "View more", "fromLastMonth": "from last month"}, "patientStats": {"title": "Patient Statistics", "outpatient": "Outpatient", "inpatient": "Inpatient"}, "topTwenty": {"title": "Top 20", "icdDiagnoses": "ICD Diagnoses", "prescribedMedications": "Prescribed Medications"}, "costs": {"averageTreatmentCosts": "Average Treatment Costs", "insurancePayments": "Insurance Payments", "insurance": "Insurance", "service": "Service", "specialCare": "Special Care"}, "treatmentOutcomes": {"title": "Treatment Outcomes", "recovered": "Recovered", "improved": "Improved", "unchanged": "Unchanged", "deteriorated": "Deteriorated", "deceased": "Deceased", "left": "Left"}}, "customers": {"title": "Customer List", "filters": {"search": {"placeholder": "Search by name"}, "group": "Group", "createdAt": "Created At", "updatedAt": "Updated At"}, "orderHistory": "Order History", "name": "Name", "phone": "Phone", "email": "Email", "address": "Address", "group": "Group", "createdAt": "Created At", "updatedAt": "Updated At"}, "orders": {"filters": {"shift": "Shift", "status": "Order status", "paymentStatus": "Payment status", "createdAt": "Created Date", "updatedAt": "Updated Date"}, "orderHistory": "Order History", "amount": "Amount", "redeemPoints": "Redeem Points", "loyalPoints": "Loyalty Points", "updatedAt": "Updated At", "title": "Order List", "searchPriceGroup": "Search price group", "noPriceGroupsFound": "No price groups found", "searchBranch": "Search branch", "noBranchFound": "No branches found", "emptyServiceName": "Please fill in all service names", "updateOrder": "Update", "confirm": "Are you sure?", "confirmCancel": "Confirm", "confirmDelete": "Delete", "cancelWarning": "This action cannot be undone. Can<PERSON> changes?", "cancelDelete": "This action cannot be undone. Delete item?", "selectGender": "Select gender", "tax": "Tax", "shipping": "Shipping fee", "addCustomer": "Add customer", "save": "Save", "defaultShippingAddress": "Default Shipping Address", "defaultBillingAddress": "De<PERSON><PERSON> Billing Address", "noAddressesFound": "No addresses found", "edit": "Edit", "removeRecipientInfo": "Remove recipient information", "addRecipientInfo": "Add recipient information", "address": "Address", "province": "Province", "district": "District", "ward": "Ward", "enterName": "Enter name", "enterAddress": "Enter address", "searchWard": "Search ward", "searchDistrict": "Search district", "selectWard": "Select ward", "selectDistrict": "Select district", "selectProvince": "Select province", "searchProvince": "Search province", "enterCompanyName": "Enter company name", "enterPhoneNumber": "Enter phone number", "noProvincesFound": "No provinces found", "noDistrictsFound": "No districts found", "noWardsFound": "No wards found", "shippingDefault": "Shipping Default", "billingDefault": "Billing De<PERSON>", "addAddress": "Add address", "editCustomer": "Edit customer", "Name": "Full Name", "phoneNumber": "Phone number", "email": "Email", "gender": "Gender", "enterEmail": "Enter email", "birthday": "Birthday", "pickADate": "Pick a date", "customerGroup": "Customer group", "selectCustomerGroup": "Select customer group", "companyName": "Company name", "addresses": "Addresses", "submit": "Submit", "accumulatedPoints": "Accumulated Points", "groupName": "Group name", "placeholder": "Search orders...", "quantity": "Quantity", "price": "Price", "total": "Total", "noProductsFound": "No products found", "addService": "Add Service (F9)", "loadingMore": "Loading more...", "addProduct": "Add Product", "available": "Available", "onHand": "On Hand", "note": "Note", "maximumAvailableQuantity": "Maximum available quantity", "branch": "Branch", "loadingCustomerDetails": "Loading customer details...", "customer": "Customer", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "noCustomersFound": "No customers found", "loading": "Loading...", "searchCustomer": "Search customer", "payment": "Payment", "addPromotion": "Add Promotion", "products": "Products", "subtotal": "Subtotal", "discount": "Discount", "voucher": "Voucher", "fees": "Fees", "promotions": "Promotions", "notePlaceholder": "Enter note", "tags": "Tags", "tagsPlaceholder": "Enter tags", "noProductsInOrder": "No products in the order.", "cancel": "Cancel", "addOrder": "Add order", "success": "Order created successfully!", "error": "Order could not be processed", "adjustPrice": "Adjust price", "adjustPriceSuccess": "Price adjusted successfully!", "adjustPriceError": "Failed to adjust price", "adjustPriceDescription": "Adjust the price of the selected product", "adjustPricePlaceholder": "Enter new price", "adjustPriceButton": "Adjust Price", "adjustPriceCancel": "Cancel", "setNewPrice": "Set new price", "value": "Value", "percent": "Percent", "addProductToOrderWarning": "Please add products to the order", "selectCustomer": "Please select a customer", "addVoucher": "Add voucher", "voucherCode": "Voucher", "voucherCodePlaceholder": "Enter voucher code", "voucherCodeButton": "Add voucher", "voucherCodeSuccess": "Voucher added successfully", "voucherCodeError": "Failed to add voucher"}, "variants": {"title": "Variants", "filters": {"search": {"placeholder": "Search by name, sku, barcode"}}}, "products": {"title": "Products", "filters": {"search": {"placeholder": "Search by name, sku, barcode", "placeholderBrand": "Search brand..."}, "product": "Product", "source": "Source", "category": "Category", "brand": "Brand", "createdAt": "Created At", "updatedAt": "Updated At", "otherFilters": {"title": "Other filters", "all": "All", "description": "The first two filters will be prioritized for display on the main page. Customize them based on your needs."}, "deletionFailed": "Variant deletion failed", "deletedSuccessfully": "<PERSON><PERSON><PERSON> deleted successfully", "dateOptions": {"allTime": "All time", "today": "Today", "yesterday": "Yesterday", "lastWeek": "Last week", "thisWeek": "This week", "lastMonth": "Last month", "thisMonth": "This month", "customize": "Customize"}}, "headers": {"productInfo": "Product Information", "category": "Category", "brand": "Brand", "updatedAt": "Updated At", "createdAt": "Created At", "available": "Available", "variant": "<PERSON><PERSON><PERSON>"}, "actions": {"addProduct": "Add Product", "addManual": {"title": "Add product", "onThisPage": "On This Page", "sections": {"basicInfo": "Basic Information", "options": "Product Options", "units": "Packing Units", "prices": "Product Prices", "measurements": "Measurements"}}, "addQuick": "Add quick product", "addBulk": "Add bulk product", "refresh": "Refresh", "saveFilters": "Save filters", "reset": "Reset", "filter": "Filter"}, "descriptionDeleteOption": "This action cannot be undone. Data related to Package Unit, Product Prices and Measurements will be permanently deleted.", "descriptionDeleteValueOption": "This action cannot be undone. Data related to Product Prices and Measurements will be permanently deleted.", "name": "Name", "sku": "SKU", "barcode": "Barcode", "option1": "Option 1", "option2": "Option 2", "option3": "Option 3", "unit": "Unit", "weight": "Weight", "height": "Height", "width": "<PERSON><PERSON><PERSON>", "length": "Length", "source": "Source", "category": "Category", "brand": "Brand", "createdAt": "Created At", "description": "Description", "viewLess": "View less", "viewMore": "View more", "variantDetails": "<PERSON><PERSON><PERSON>", "variants": "Variants", "noPricesAvailable": "No prices available", "prices": "Prices", "inventory": {"noMatchResult": "No matching result found", "title": "Inventory", "branch": "Branch", "history": "History", "allBranches": "All Branches", "inventory": "Inventory", "packing": "Packing", "shipping": "Shipping", "minValue": "Min value", "maxValue": "Max value", "staff": "Staff", "transactionType": "Transaction Type", "change": "Change", "quantity": "Quantity", "reference": "Reference", "available": "Available", "incoming": "Incoming", "onHand": "On Hand"}, "addManual": {"title": "Add Manual Product", "onThisPage": "On This Page", "publish": "Publish", "sections": {"addVariant": "Create variant when having more than one choice, such as about sizes or colors.", "variant": "<PERSON><PERSON><PERSON>", "all": "All", "apply": "Apply", "unitPlaceholder": "Select Unit", "unitSearchPlaceholder": "Search unit", "variantSearchPlaceholder": "Search variant", "unitEmptyText": "No units found.", "variantPlaceholder": "Select Variant", "usedByAllVariants": " Used by all variants", "usedByThis": " Used by this", "addType": "Add type", "addUnit": "Add unit", "basicInfo": "Basic Information", "option": "option", "options": "Product Options", "units": "Packing Units", "prices": "Product Prices", "measurements": "Measurements", "selectImages": "Select Images", "submit": "Submit", "cancel": "Cancel", "add": "Add", "edit": "Edit", "save": "Save", "stay": "Stay", "leave": "Leave", "values": "Values", "valuesPlaceholder": "Value 1, Value 2, Value 3", "optionsPlaceholder": "Enter option name", "addOption": "Add option", "optionName": "Option name", "addValue": "Add value", "remove": "Remove", "valuesPlaceholderInput": "Enter value", "duplicateValue": "This value is duplicated", "createVariant": "Create variant when having more than one choice, such as about sizes or colors."}, "basicInfo": {"brandPlaceholder": "Select brand", "brandSearchPlaceholder": "Search brand", "brandEmptyText": "No brands found", "categoryPlaceholder": "Select category", "categorySearchPlaceholder": "Search category", "categoryEmptyText": "No categories found", "tagsPlaceholder": "Enter tags", "images": "Images", "name": "Name", "description": "Description", "shortDescription": "Short Description", "brand": "Brand", "category": "Category", "sku": "SKU", "tags": "Tags", "price": "Price", "uploadImage": "Upload image", "optimize": "Optimize", "required": "This field is required", "imageRequired": "At least one image is required", "nameRequired": "Product name is required", "nameWarning": "Product name is required for optimization", "descriptionWarning": "Description is required for optimization"}, "options": {"addOption": "Add option", "optionName": "Option name", "values": "Values", "addValue": "Add value", "remove": "Remove"}, "units": {"title": "Packing Units", "addUnit": "Add unit", "unitName": "Unit name", "ratio": "<PERSON><PERSON>", "remove": "Remove"}, "prices": {"title": "Product Prices", "addGroup": "Add New Price Group", "groupName": "Group name", "price": "Price", "apply": "Apply", "applyAll": "Apply to all"}, "measurements": {"weight": "Weight", "height": "Height", "width": "<PERSON><PERSON><PERSON>", "length": "Length", "apply": "Apply", "applyAll": "Apply to all"}, "buttons": {"cancel": "Cancel", "add": "Add", "edit": "Update", "save": "Save", "stay": "Stay", "leave": "Leave"}, "dialogs": {"leaveTitle": "Are you sure you want to leave?", "leaveDesc": "Any unsaved changes will be lost."}, "validation": {"hasErrors": "Validation Error", "checkFields": "Please check all required fields and try again"}, "success": "Product Created", "successUpdate": "Product Updated", "successDescription": "Product has been created successfully", "successDescriptionUpdate": "Product has been updated successfully", "error": "Error", "errorDescription": "Failed to create product. Please try again.", "errorDescriptionUpdate": "Failed to update product. Please try again."}}, "choosePlan": {"forIndividuals": "For Individuals", "forCompanies": "For Companies", "monthly": "Monthly", "annually": "Annually", "chooseAPlan": "Choose a plan", "planDescription": {"firstSection": "Choose a plan that best fits your business needs.", "middleSection": "You can always upgrade or downgrade later.", "secondSection": "All plans include basic features."}, "planData": {"Up to 50 variants": "Up to 50 variants, overall statistics.", "Real-time inventory syncing": "Real-time inventory syncing.", "Ideal for startups (1,000 items)": "Ideal for startups (1,000 items).", "Analytics dashboard": "Analytics dashboard.", "User-friendly interface": "User-friendly interface.", "Support for multiple currencies and languages": "Support for multiple currencies and languages.", "Real time inventory": "Real time inventory."}, "planNames": {"Free": "Free", "Starter": "Starter", "Pro": "Pro", "Agency": "Agency"}, "mostPopular": "Most Popular", "numberIntegrations": "Number of Integrations", "explainNoIntegrations": "No integrations available", "getStarted": "Get Started"}, "synchronization": {"platforms": {"source": "Source", "destination": "Destination"}, "title": {"success": "Connect {{source}} to {{destination}}", "error": "Synchronize configuration"}, "error": {"missingConnection": "Missing connection", "connectionError": "Connection error", "sourceNotFound": "Source not found", "destinationNotFound": "Destination not found"}, "success": {"completeTitle": "Connection Complete!", "gotoDashboard": "Go to dashboard"}, "description": "Select a niche that aligns with your interests and target audience.", "syncSetting": {"title": "Sync Settings", "product": {"title": "Products", "description": "Sync products from {{source}} to {{destination}}"}, "inventory": {"title": "Inventory", "description": "Keep inventory levels synchronized"}, "order": {"title": "Orders", "description": "Import {{destination}} orders to {{source}}"}, "buttonTitle": "Connect to {{destination}}"}}, "syncRecords": {"title": "Sync Records List", "filters": {"search": {"placeholder": "Search return orders..."}, "status": "Status", "recordType": "Record Type", "channel": "Channel", "connectionId": "Connection ID", "fetchEventId": "Fetch Event ID"}, "columns": {"channel": "Channel", "header": "Record Type", "fetchEventId": "Fetch Event ID", "connectionId": "Connection ID", "lastUpdated": "Last Updated", "fetchedAt": "Fetched At", "finishedAt": "Finished At", "publishedAt": "Published At", "transformedAt": "Transformed At"}}, "fetchEvents": {"title": "Fetch Event", "filters": {"search": {"placeholder": "Search fetch event..."}, "status": "Status", "actionType": "Action Type", "actionGroup": "Action Group", "eventTime": "Event Time", "eventSource": "Event Source", "fetchEventId": "Fetch Event ID"}, "headers": {"channel": "Channel", "actionType": "Action Type", "actionGroup": "Action Group", "eventSource": "Event Source", "eventTime": "Event Time", "status": "Status", "actions": "Actions"}, "columns": {"channel": "Channel", "header": "Record Type", "fetchEventId": "Fetch Event ID", "connectionId": "Connection ID", "lastUpdated": "Last Updated", "fetchedAt": "Fetched At", "finishedAt": "Finished At", "publishedAt": "Published At", "transformedAt": "Transformed At"}}, "fetchEventDetail": {"title": "Fetch Event Details of", "actionGroup": "Action Group", "connectionId": "Connection ID", "actionType": "Action Type", "eventSource": "Event Source", "retryCount": "Retry Count", "status": "Status", "continuationToken": "Continuation Token", "objectId": "Object ID", "eventTime": "Event Time", "createdAt": "Created At", "updatedAt": "Updated At", "eventNumber": "{{number}}. No ID"}, "channel": {"title": "Connection List", "filters": {"search": {"placeholder": "Search connections..."}, "status": "Status"}, "headers": {"channel": "Channel", "status": "Status", "url": "URL", "createdAt": "Created At", "lastUpdated": "Updated At", "actions": "Actions"}, "actions": {"install": "Install new channel", "configure": "Configure", "activate": "Activate", "deactivate": "Deactivate"}}, "supportedChannels": {"title": "Channel List", "filters": {"search": {"placeholder": "Search channel..."}}}, "settings": {"themeSetting": "Theme Setting", "saveSuccess": "Colors have been saved", "saveError": "Failed to save colors", "colorSetting": "Color Setting", "logoSetting": "Logo Setting", "lightMode": "Light Mode", "darkMode": "Dark Mode", "logo": {"lightModeLogo": "Light Mode Logo", "darkModeLogo": "Dark Mode Logo", "lightModeIcon": "Light Mode Icon", "darkModeIcon": "Dark Mode Icon", "favicon": "Favicon", "lightModeLogoDescription": "Upload logo for light mode (recommended size: 150x48px)", "darkModeLogoDescription": "Upload logo for dark mode (recommended size: 150x48px)", "lightModeIconDescription": "Upload icon for light mode (recommended size: 48x48px)", "darkModeIconDescription": "Upload icon for dark mode (recommended size: 48x48px)", "faviconDescription": "Upload favicon for the website (recommended size: 32x32px)", "saveSuccess": "Logo has been saved", "saveError": "Failed to save logo", "noChangesToSave": "No changes to save", "resetSuccess": "Logo has been reset", "resetError": "Failed to reset logo"}, "color": {"saveSuccess": "Colors have been saved", "saveError": "Failed to save colors", "resetSuccess": "Colors have been reset", "resetError": "Failed to reset colors"}}, "installChannel": {"title": "Install Channel"}, "productMappingList": {"syncSuccess": "Receive process mapping request successful", "syncFail": "Receive process mapping request failed", "noConnection": "No connection", "title": "Product Mapping", "description": "Map your Shopify products to TikTok Shop.", "filters": {"search": {"placeholder": "Search"}, "status": {"all": "All Products", "synced": "Synced", "mapped": "Mapped", "unMapped": "Unmaped", "errors": "Errors"}}, "alert": {"title": "Confirm Product Synchronization?", "description": "You are about to synchronize all products between the two platforms. This process may take some time to complete.", "note": "Note:", "noteDescription": "If you only want to sync specific products, please select them from the table before proceeding.", "confirm": "Sync", "cancel": "Cancel", "areYouSure": "Are you sure you want to continue?", "unmapSuccess": "Product unmapped successfully", "unmapFail": "Fail to unmap product"}, "groupButton": {"settingButton": "Setting", "syncButton": "Sync product"}, "status": {"synced": "Synced", "mapped": "Mapped", "unmapped": "Unmaped", "error": "Errors"}, "actions": {"unmap": "Unmap", "map": "Map", "fix": "Fix Attributes"}, "headers": {"product": "{{product}} Product", "price": "Price", "last_synced": "Last Synced", "status": "Status", "actions": "Actions"}, "nomap": "Not mapped to {{destination}}"}, "productMapping": {"advancedMapping": {"title": "Advanced Mapping", "description": "Configure advanced mapping rules for your products.", "sourceField": "Source Field", "transformationType": "Transformation Type", "addTransformation": "Add Transformation", "removeTransformation": "Remove Transformation", "ruleConfiguration": "Rule Configuration", "outputPreview": "Output Preview", "finalOutput": "Final Output", "applyTransformations": "Apply Transformations", "transformationChain": "Transformation Chain", "sampleData": "Sample Data", "preview": "Preview", "output": "Output", "singleValue": "Single Value", "transformationForm": "Transformation", "exampleUsage": "Example Usage", "selectFieldsPlaceholder": "Select a field", "searchFieldsPlaceholder": "Search fields...", "source": "Source", "searchTransformationTypes": "Search transformation types...", "selectTransformationTypes": "Select transformation types..."}}, "staff": {"title": "Staff list", "filters": {"department": "Department", "role": "Role", "search": {"placeholder": "Search staff..."}}, "actionButton": {"create": "Create staff"}, "columns": {"staff": "Staff", "role": "Role", "skills": "Skills", "task": "Task", "actions": "Actions"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "maxCharactersReached": "Maximum 50 characters reached", "online": "Online", "noStaff": "No staff found.", "loading": "Loading...", "interact": "Interact", "createStaff": "Create Staff", "staffName": "Staff Name", "enterStaffName": "Enter Staff name", "staffNameRequired": "Staff Name is required", "maxCharacters": "Max 250 characters", "selectDepartment": "Select a department", "searchDepartments": "Search departments...", "selectRole": "Select a role", "searchRoles": "Search roles...", "creating": "Creating", "update": "Update", "role": "Role", "score": "Score", "knowledgeWarning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance.", "avatar": {"title": "Avatar", "xbotAvatar": "XBot avatar", "image": "Image", "selectedAvatar": "Selected avatar", "avatar": "Avatar"}, "knowledge": {"tab": "Knowledge", "baby": "Baby", "warning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance."}, "interactionStyle": {"tab": "Interaction Style", "description": "Configure how the staff interacts with users", "communicationTone": "Communication Tone", "languagePreferences": "Language Preferences", "responseLength": "Response Length", "personalityTraits": "Personality Traits", "formal": "Formal", "casual": "Casual", "detailed": "Detailed", "concise": "Concise", "creative": "Creative", "analytical": "Analytical", "ethicalConstraints": "Ethical Constraints", "contentFiltering": "Enable content filtering and safety checks"}, "skills": {"tab": "Data Access Configuration", "description": "Configure which data sources the virtual staff can access", "products": "Products", "orders": "Orders", "inventory": "Inventory"}, "staffInfo": {"tab": "Staff Information", "description": "Manage staff basic information"}, "task": {"tab": "Task", "description": "Manage staff tasks and assignments", "noTasks": "No tasks available"}, "editStaff": {"title": "Edit staff", "staffName": "Staff Name", "rolePurpose": "Role/Purpose", "department": "Department", "domainExpertise": "Domain Expertise", "customExpertisePlaceholder": "Enter custom domain expertise and press enter", "noRoleFound": "No role found", "noDepartmentFound": "No department found", "selectRole": "Select role", "selectDepartment": "Select department", "searchRoles": "Search roles...", "searchDepartments": "Search departments...", "namePhoneRequirement": "Name, Phone number requirement", "roles": {"contentWriter": "Content Writer", "seoSpecialist": "SEO Specialist", "socialMediaManager": "Social Media Manager", "marketingSpecialist": "Marketing Specialist"}, "knowledge": {"knowledgeWarning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance."}, "departments": {"sales": "Sales", "marketing": "Marketing", "engineering": "Engineering", "support": "Support"}, "expertise": {"contentWriting": "Content Writing", "seo": "SEO", "socialMedia": "Social Media Management", "emailMarketing": "Email Marketing", "graphicDesign": "Graphic Design", "dataAnalysis": "Data Analysis", "projectManagement": "Project Management", "customerSupport": "Customer Support"}}, "embed": {"instructions": {"title": "Embedding Instructions", "step1Title": "Step 1: Add the Script", "step1Description": "Copy the script tag and paste it in your website's HTML, preferably in the <head> section or just before the closing </body> tag.", "step2Title": "Step 2: Add the Widget Container", "step2Description": "Copy the div element and paste it where you want the virtual staff widget to appear on your page. The widget will automatically initialize in this location.", "step3Title": "Step 3: Customize the Widget (Optional)", "step3Description": "You can customize the widget's appearance by adding CSS to your website. The widget container has the ID xbot-container.", "step4Title": "Step 4: Test the Integration", "step4Description": "After adding the code, refresh your page and verify that the virtual staff widget appears correctly. The widget should show the staff member's information and allow visitors to interact with them.", "troubleshootingTitle": "Troubleshooting", "troubleshooting1": "Make sure the script URL is accessible from your website.", "troubleshooting2": "Check your browser's console for any error messages.", "troubleshooting3": "Verify that the staff ID and name are correct.", "troubleshooting4": "Ensure your website allows loading external scripts."}, "script": {"title": "Embed Code", "copy": "Copy", "copied": "Copied!", "containerInstructions": "1. Add this container where you want the widget to appear", "scriptInstructions": "2. Add this script to your HTML"}}}, "knowledge": {"title": "Knowledge", "headers": {"file": "File", "status": "Status", "size": "Size", "updatedAt": "Last updated"}, "filters": {"search": {"placeholder": "Search file..."}, "fileType": "File type", "status": "Status"}, "status": {"pending": "Pending", "processing": "Processing", "ready": "Ready", "error": "Error"}, "actions": {"upload": "Upload new file"}, "upload": {"dragAndDrop": "Drag and drop here or", "uploadButton": "Upload", "supportedFiles": "PDF, DOCX, TXT, or CSV files", "totalSize": "Total size", "noFileSelected": "No file selected", "uploadSuccess": "Upload successful", "fileTooLarge": "File exceeds maximum size of {{max}}MB", "file": "File", "url": "URL Import", "text": "Direct Text Input", "title": "Upload Knowledge", "uploaded": "Knowledge Uploaded", "invalidUrl": "Invalid URL", "urlAlreadyAdded": "URL already added", "noUrlsToUpload": "Please enter at least one URL", "uploadError": "Upload error", "knowledgeNameRequired": "Knowledge name is required", "knowledgeNameTooLong": "Knowledge name must be less than 250 characters", "textRequired": "Text is required", "textTooLong": "Text must be less than 20000 characters", "search": "Search knowledge", "textTitle": "Text Knowledge", "fileTitle": "File Knowledge", "urlTitle": "URL Knowledge", "allTitle": "All Knowledge", "deleteTitle": "Delete Knowledge", "deleteDescription": "Are you sure you want to delete this knowledge? This action cannot be undone.", "deleteSuccess": "Knowledge deleted successfully", "deleteError": "Failed to delete knowledge", "deleteConfirm": "Delete", "deleteCancel": "Cancel", "leaveTitle": "Leave without saving?", "leaveDescription": "Any unsaved changes will be lost.", "leaveConfirm": "Leave", "leaveCancel": "Stay", "textInput": "Direct Text Input", "textInputPlaceholder": "Paste your knowledge base text here...", "textInputTitle": "Text Knowledge", "textTitlePlaceholder": "Enter knowledge title", "pleaseSelectAtLeastOneFile": "Please select at least one file", "pleaseEnterAtLeastOneURL": "Please enter at least one URL", "pleaseEnterAtLeastOneTextFile": "Please enter at least one text file", "pleaseEnterAllFields": "Please enter all fields", "newest": "Newest", "oldest": "Oldest", "noKnowledge": "No knowledge found", "urlImport": "URL Import", "urlImportDescription": "Import knowledge from web pages", "deleteKnowledge": "Delete Knowledge", "deleteKnowledgeDescription": "Are you sure you want to delete this knowledge? This action cannot be undone.", "deleteKnowledgeSuccess": "Knowledge deleted successfully", "deleteKnowledgeError": "Failed to delete knowledge", "deleteKnowledgeConfirm": "Delete", "deleteKnowledgeCancel": "Cancel", "deleteKnowledgeTitle": "Delete Knowledge"}}, "department": {"title": "Departments", "createDepartment": "Create Department", "createStaff": "Create Staff", "departmentName": "Department Name", "enterDepartmentName": "Enter Department Name", "description": "Description", "enterDescription": "Enter description...", "departmentNameRequired": "Department Name is required", "viewStaff": "View Staff", "staffCount": "{{count}} staff", "interact": "Interact", "upload": "Upload", "knowledgeWarning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance.", "deleteKnowledge": "Delete Knowledge", "deleteKnowledgeDescription": "Are you sure you want to delete this knowledge? This action cannot be undone.", "deleteKnowledgeSuccess": "Knowledge deleted successfully", "deleteKnowledgeError": "Failed to delete knowledge", "deleteKnowledgeConfirm": "Delete", "deleteKnowledgeCancel": "Cancel", "knowledge": {"tab": "Knowledge", "baby": "Baby", "warning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance.", "status": {"error": "Error", "pending": "Pending", "success": "Success"}}}, "customer": {"details": {"customerDetails": "Customer Details", "name": "Name", "birthday": "Birthday", "gender": "Gender", "phone": "Phone", "email": "Email", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "groupName": "Group name", "totalLoyalPoints": "Total loyal points", "totalRedeemPoints": "Total redeem points", "tags": "Tags", "noTags": "---"}, "purchase": {"purchaseInfo": "Purchase information", "totalSpent": "Total spent", "totalProductsPurchased": "Total products purchased", "purchasedOrder": "Purchased order", "totalProductsReturned": "Total products returned", "lastOrderAt": "Last order at"}, "sales": {"suggestionInfo": "Sales suggestion information", "defaultPriceGroup": "Default price group", "defaultPaymentMethod": "Default payment method", "discountPercent": "Discount percent"}, "order": {"orderHistory": "Order History"}}}, "table": {"pagination": {"rowsPerPage": "Rows per page", "description": "{{start}} to {{end}} row(s) of a total of {{total}}", "next": "Next", "previous": "Previous"}, "selected": {"title": "Selected", "delete": "Delete {{count}} selected"}, "export": {"title": "Export", "description": "Export data", "confirm": "Export", "cancel": "Cancel"}, "filter": {"clearFilter": "Clear filter", "loadMore": "Load more"}, "savedFilters": {"settings": {"settings": "Settings", "title": "Tabs settings", "description": "The first six tabs will be prioritized for display on the main page. Customize them based on your needs."}}}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidUsername": "Username must be at least 3 characters", "minLength": "Must be at least {{count}} characters", "maxLength": "Must be less than {{count}} characters", "passwordMismatch": "Passwords do not match", "emailRequired": "Please enter email", "usernameRequired": "Please enter username", "passwordRequired": "Please enter password", "confirmPasswordRequired": "Please confirm password", "invalidPassword": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "verificationCodeRequired": "Please enter verification code", "verificationCodeLength": "Verification code must be 6 characters", "sessionRequired": "Session is required", "usernameSpecialCharacters": "Username can only contain letters, numbers, and underscores", "skuFormat": "The SKU can only contain letters, numbers, and underscores", "skuRequired": "SKU is required", "nameRequired": "Product name is required", "nameTooLong": "Product name must be less than 250 characters", "priceRequired": "Price required", "imageRequired": "At least one image is required", "imageFormat": "Invalid image format", "priceMustBePositive": "Please enter a valid price", "invalidPrice": "Invalid price", "wrongUsernameOrPassword": "Incorrect username or password", "phoneNumberAlreadyExists": "Phone number already exists"}, "footer": {"crafted": "Crafted with", "by": "by", "team": "OneXAPIs team", "heart": "heart"}, "install": {"installing": "Installing...", "pleaseWait": "Please wait a moment", "error": {"backToHome": "Back to home", "notFound": "Looks like you've ventured into the unknown digital realm.", "installationFailed": "Installation Failed", "missingSourceChannel": "Missing source channel", "authorizeDestination": "Please authorize the destination channel"}}, "error": {"notFound": "Not found", "notFoundDescription": "The page you are looking for does not exist.", "backToHome": "Back to home"}}
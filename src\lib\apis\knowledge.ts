import axios from "axios";

import {
  CreateKnowledgeByFilePayload,
  CreateKnowledgeByTextPayload,
  GetUploadUrlsPayload,
  Knowledge,
  KnowledgeFileResponse,
  KnowledgeTextResponse,
  KnowledgeUploadUrl,
  KnowledgeURLResponse,
} from "@/features/bots/knowledge/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";

export const knowledgeApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<{ items: Knowledge[]; total: number; page: number; limit: number }>(
      ENDPOINTS.KNOWLEDGE.LIST,
      {
        params,
      }
    );
  },
  update: async (id: string, data: Partial<Knowledge>) => {
    const url = ENDPOINTS.KNOWLEDGE.UPDATE(id);
    return await privateApi.put<Knowledge>(url, data);
  },
  getById: async (id: string) => {
    const url = ENDPOINTS.KNOWLEDGE.GET_BY_ID(id);
    return await privateApi.get<Knowledge>(url);
  },
  create: async (data: Partial<Knowledge>) => {
    return await privateApi.post<Knowledge>(ENDPOINTS.KNOWLEDGE.CREATE, data);
  },
  delete: async (id: string) => {
    const url = ENDPOINTS.KNOWLEDGE.DELETE(id);
    return await privateApi.delete(url);
  },
  createByUrl: async (data: string[]) => {
    return await privateApi.post<KnowledgeURLResponse>(
      ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE_BY_URL,
      data
    );
  },
  createByFile: async (data: CreateKnowledgeByFilePayload) => {
    return await privateApi.post<KnowledgeFileResponse>(
      ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE_BY_FILE,
      data
    );
  },
  createByText: async (data: CreateKnowledgeByTextPayload[]) => {
    return await privateApi.post<KnowledgeTextResponse>(
      ENDPOINTS.KNOWLEDGE.CREATE_KNOWLEDGE_BY_TEXT,
      data
    );
  },
  getUploadUrls: async (data: GetUploadUrlsPayload) => {
    return await privateApi.post<KnowledgeUploadUrl[]>(ENDPOINTS.KNOWLEDGE.GET_UPLOAD_URLS, data);
  },

  putUploadFileWithFileUrl: async (
    url: string,
    file: File,
    onProgress: (progress: number) => void
  ) => {
    return await axios.put(url, file, {
      headers: {
        "Content-Type": file.type,
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        }
      },
    });
  },
};

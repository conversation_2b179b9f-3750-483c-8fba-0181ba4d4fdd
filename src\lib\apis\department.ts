import { Department } from "@/features/bots/department/hooks/types";

import { DEPARTMENT_ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

export const departmentApi = {
  list: async (params?: Record<string, unknown>) => {
    const response = await privateApi.get<ResponseList<Department>>(DEPARTMENT_ENDPOINTS.LIST, {
      params,
    });
    return response;
  },

  getById: async (id: string) => {
    const response = await privateApi.get(DEPARTMENT_ENDPOINTS.GET_BY_ID.replace(":id", id));
    return response.data as Department;
  },

  create: async (data: Partial<Department>) => {
    const response = await privateApi.post<Department>(DEPARTMENT_ENDPOINTS.CREATE, data);
    return response as Department;
  },

  update: async (id: string, data: Partial<Department>) => {
    const response = await privateApi.put<Department>(
      DEPARTMENT_ENDPOINTS.UPDATE.replace(":id", id),
      data
    );
    return response as Department;
  },
};

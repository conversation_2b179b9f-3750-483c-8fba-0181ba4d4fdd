name: 🚀 Vercel Preview Deployment

on:
  pull_request:
    types: [opened, synchronize, reopened]

permissions:
  issues: write
  pull-requests: write
  contents: write

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  deploy-preview:
    runs-on: ubuntu-latest
    environment: "Development"

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🛠️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "yarn"

      - name: 📦 Install Yarn
        run: npm install -g yarn

      - name: 🔧 Install Vercel CLI
        run: yarn global add vercel@latest

      - name: 📚 Install Dependencies
        run: yarn install

      - name: ⚡ Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}

      - name: 🏗️ Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}

      - name: 🚀 Deploy to Vercel
        id: deploy
        run: |
          set +e
          DEPLOYMENT_URL=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
          DEPLOY_EXIT_CODE=$?
          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV
          echo "DEPLOY_EXIT_CODE=$DEPLOY_EXIT_CODE" >> $GITHUB_ENV
          exit $DEPLOY_EXIT_CODE

      - name: 💬 Comment Success on PR
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const deploymentUrl = process.env.DEPLOYMENT_URL;
            const timestamp = new Date().toISOString();
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🚀 Preview Deployment Status

              | Status  | Preview                           | Updated      |
              | ------- | --------------------------------- | ------------ |
              | ✅ Ready | [Visit Preview](${deploymentUrl}) | ${timestamp} |
              `
            });

      - name: 📢 Send Success Notification to Slack
        if: success()
        run: |
          # Format PR title to include Jira links
          PR_TITLE="${{ github.event.pull_request.title }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"
          FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')
          # Convert comma-separated user IDs to Slack mentions
          MENTIONS=$(echo "${{ vars.SLACK_DEV_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')
          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "✅ PR Preview Deployment Successful!",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Pull Request:*\n<${{ github.event.pull_request.html_url }}|#${{ github.event.pull_request.number }}> - '"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Author:*\n${{ github.event.pull_request.user.login }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Preview:*\n<'"$DEPLOYMENT_URL"'|View Deployment> 🔗"
                  }
                ]
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "👀 *Please review the code changes and provide feedback!*\ncc: '"$MENTIONS"'"
                  }
                ]
              },
              {
                "type": "divider"
              }
            ]
          }' ${{ vars.REVIEW_WEBHOOK_URL }}

      - name: 📢 Send Failure Notification to Slack
        if: failure()
        run: |
          # Format PR title to include Jira links
          PR_TITLE="${{ github.event.pull_request.title }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"
          FORMATTED_TITLE=$(echo "$PR_TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')
          # Convert comma-separated user IDs to Slack mentions
          MENTIONS=$(echo "${{ vars.SLACK_DEV_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')
          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "❌ PR Preview Deployment Failed!",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Pull Request:*\n<${{ github.event.pull_request.html_url }}|#${{ github.event.pull_request.number }}> - '"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Author:*\n${{ github.event.pull_request.user.login }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Details:*\n<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run> 🔍"
                  }
                ]
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "🚨 *Action Required:* Please check the workflow logs for details.\ncc: '"$MENTIONS"'"
                  }
                ]
              },
              {
                "type": "divider"
              }
            ]
          }' ${{ vars.REVIEW_WEBHOOK_URL }}

      - name: ⚠️ Comment Failure on PR
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const timestamp = new Date().toISOString();
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## ❌ Preview Deployment Failed

              | Status  | Details                          | Updated      |
              | ------- | -------------------------------- | ------------ |
              | Failed  | Deployment failed. Please check the [workflow logs](${context.serverUrl}/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId}) for more details. | ${timestamp} |
              `
            });

"use client";

import React, { useState } from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import CreateDepartmentDialog from "@/features/bots/department/components/create-department-dialog";
import DepartmentCard from "@/features/bots/department/components/department-card";
import { DepartmentSkeleton } from "@/features/bots/department/components/department-skeleton";
import { useDepartments } from "@/features/bots/department/hooks/department";
import { Department } from "@/features/bots/department/hooks/types";
import { CreateStaffDialog } from "@/features/bots/staff/components/create-staff/create-staff-dialog";

import { But<PERSON>, Card } from "@/components/ui";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function DepartmentPage() {
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const [showAddStaff, setShowAddStaff] = useState(false);
  const { departments, isLoading, onCreate, isCreating } = useDepartments();

  return (
    <Card className="mx-4 h-[calc(100vh-80px)] p-4">
      <div className="flex w-full flex-wrap justify-between">
        <div className="mb-4">
          <span className="text-base font-medium">{t("pages.department.title")}</span>
        </div>
        <div className="mb-4 flex gap-4">
          <Button variant={"outline"} onClick={() => setOpen(true)}>
            <Plus className="size-4" />
            {t("pages.department.createDepartment")}
          </Button>
          <CreateDepartmentDialog
            onCreate={(data) =>
              onCreate({
                name: data.departmentName,
                description: data.description,
              })
            }
            isPending={isCreating}
            open={open}
            onCancel={() => setOpen(false)}
          />
          <Button
            variant={"default"}
            onClick={() => {
              setShowAddStaff(true);
            }}>
            <Plus className="size-4" />
            {t("pages.department.createStaff")}
          </Button>
        </div>
      </div>

      {isLoading ? (
        <DepartmentSkeleton />
      ) : (
        <ScrollArea className="h-[calc(100vh-170px)]">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {departments.map((department: Department) => (
              <div key={department.id}>
                <DepartmentCard department={department} />
              </div>
            ))}
          </div>
        </ScrollArea>
      )}

      {showAddStaff && (
        <CreateStaffDialog
          open={showAddStaff}
          onOpenChange={setShowAddStaff}
          onClose={() => setShowAddStaff(false)}
        />
      )}
    </Card>
  );
}

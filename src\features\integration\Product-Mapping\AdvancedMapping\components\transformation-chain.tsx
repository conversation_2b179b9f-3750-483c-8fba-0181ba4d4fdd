import { capitalize } from "lodash";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";

import { Transformation } from "../hooks/types";

interface TransformationChainProps {
  transformation: Transformation;
  index: number;
  sourceValue: string;
  transformedValue: string;
  isLastTransformation: boolean;
  previousOutput?: string;
}

export function TransformationChain({
  transformation,
  index,
  sourceValue,
  transformedValue,
  previousOutput,
}: TransformationChainProps) {
  const { t } = useTranslation();

  const currentSourceValue = index === 0 ? sourceValue : previousOutput;
  console.log("TransformationValue", transformedValue);

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <h4 className="text-sm font-medium">
          {t("pages.productMapping.advancedMapping.transformationChain")}
        </h4>
        <Badge variant="default" className=" border-none px-2">
          {index + 1}
        </Badge>
        {transformation.type && (
          <Badge variant="sematic_default" className="border-none bg-muted px-2">
            {capitalize(transformation.type)}
          </Badge>
        )}
      </div>
      <div className="space-y-4  text-sm  ">
        <div className="space-y-1 rounded-md border border-border bg-muted p-2">
          <div className="text-muted-foreground">
            {t("pages.productMapping.advancedMapping.source")}
          </div>
          <div className="break-all">{currentSourceValue || "null"}</div>
        </div>
        <div className="space-y-1 rounded-md  border border-border bg-muted p-2">
          <div className=" text-muted-foreground ">
            {t("pages.productMapping.advancedMapping.output")}
          </div>
          <div className="break-all">{transformedValue || "null"}</div>
        </div>
      </div>
    </div>
  );
}

import { useEffect, useState } from "react";

interface UseEmbeddedProps {
  botId?: string;
  containerSelector?: string;
  defaultOpen?: boolean;
}

export const useEmbedded = ({
  botId,
  containerSelector = "#chat-container",
  defaultOpen = false,
}: UseEmbeddedProps = {}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const toggleChat = () => {
    setIsOpen((prev) => !prev);
  };

  useEffect(() => {
    if (!botId) {
      setError("Bot ID is required");
      return;
    }

    const container = document.querySelector(containerSelector);
    if (!container) {
      setError(`Container with selector "${containerSelector}" not found`);
      return;
    }

    try {
      // Injecting the chat container and script
      const chatFrame = document.createElement("div");
      chatFrame.id = `xbot-chat-frame-${botId}`;
      chatFrame.className = "xbot-embedded-chat";
      chatFrame.style.display = isOpen ? "block" : "none";
      chatFrame.style.position = "fixed";
      chatFrame.style.bottom = "80px";
      chatFrame.style.right = "20px";
      chatFrame.style.width = "350px";
      chatFrame.style.height = "500px";
      chatFrame.style.borderRadius = "10px";
      chatFrame.style.boxShadow = "0 5px 40px rgba(0, 0, 0, 0.16)";
      chatFrame.style.zIndex = "9999";
      chatFrame.style.overflow = "hidden";

      // Create iframe for chat
      const iframe = document.createElement("iframe");
      iframe.src = `/virtual-staff/${botId}?embedded=true`;
      iframe.style.width = "100%";
      iframe.style.height = "100%";
      iframe.style.border = "none";

      chatFrame.appendChild(iframe);
      container.appendChild(chatFrame);

      // Create toggle button
      const toggleButton = document.createElement("button");
      toggleButton.id = `xbot-toggle-${botId}`;
      toggleButton.className = "xbot-chat-toggle";
      toggleButton.innerHTML = "-";
      toggleButton.style.position = "fixed";
      toggleButton.style.bottom = "20px";
      toggleButton.style.right = "20px";
      toggleButton.style.width = "50px";
      toggleButton.style.height = "50px";
      toggleButton.style.borderRadius = "50%";
      toggleButton.style.backgroundColor = "#0070f3";
      toggleButton.style.color = "white";
      toggleButton.style.border = "none";
      toggleButton.style.fontSize = "24px";
      toggleButton.style.cursor = "pointer";
      toggleButton.style.zIndex = "9999";
      toggleButton.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";

      toggleButton.addEventListener("click", () => {
        chatFrame.style.display = chatFrame.style.display === "none" ? "block" : "none";
        setIsOpen(chatFrame.style.display === "block");
      });

      container.appendChild(toggleButton);

      setIsLoaded(true);

      // Cleanup function to remove added elements
      return () => {
        chatFrame.remove();
        toggleButton.remove();
      };
    } catch (err) {
      setError(`Failed to load embedded chat: ${err instanceof Error ? err.message : String(err)}`);
    }
  }, [botId, containerSelector]);

  return {
    isOpen,
    isLoaded,
    error,
    toggleChat,
  };
};

export default useEmbedded;

"use client";

import { useMemo, useState } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@radix-ui/react-popover";
import { ChevronsUpDown, icons, Search } from "lucide-react";

import { Input } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface IconSelectorProps {
  value?: string;
  disable?: boolean;
  onChange?: (value: string) => void;
  disableIcons?: string[];
}

export function IconSelector({
  value,
  onChange,
  disable = false,
  disableIcons = [],
}: IconSelectorProps) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const IconComponent = value ? icons[value as keyof typeof icons] : icons.Circle;

  const allIcons = Object.keys(icons);
  const filteredIcons = useMemo(() => {
    if (!search) return allIcons.slice(0, 100);
    return allIcons.filter((name) => name.toLowerCase().includes(search.toLowerCase()));
  }, [allIcons, search]);

  return (
    <Popover modal open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" className="border-dashed px-3" disabled={disable}>
          <div className="flex items-center gap-2">
            <IconComponent size={16} className={value ? "opacity-100" : "opacity-50"} />
            <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className=" z-50 my-1 rounded-md border bg-card p-0 shadow-md !w-fit flex flex-col"
        align="start">
        {/* <div className="flex flex-1 items-center rounded-t-md border-b bg-card px-3">
          <Search className="mr-2 size-4 shrink-0 opacity-50" />
          <Input
            placeholder="Search icons..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="h-10  flex-1 border-0 bg-card pl-0 outline-none placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div> */}
        <div className="flex items-center bg-card px-3 border-b overflow-hidden w-[200px] rounded-t-md">
          <Search className="mr-2 size-4 flex-none opacity-50" />
          <Input
            placeholder="Search icons..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className=" h-10  flex-1 border-0 bg-card pl-0 outline-none placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0"
            containerClassName="flex-auto"
          />
        </div>
        <div className="h-[300px] overflow-auto w-fit">
          <div className="grid grid-cols-4 gap-2 p-2 pr-1 w-fit">
            {filteredIcons.length === 0 ? (
              <div className="col-span-4 text-center text-sm text-muted-foreground">
                No icons found.
              </div>
            ) : (
              filteredIcons.map((iconName) => {
                const Icon = icons[iconName as keyof typeof icons];
                const isSelected = value === iconName;
                return (
                  <Button
                    key={iconName}
                    disabled={disableIcons?.includes(iconName)}
                    variant={isSelected ? "secondary" : "outline"}
                    size="icon"
                    className={cn(
                      "h-10 w-10 rounded-md",
                      isSelected && "ring-2 ring-primary ring-offset-2"
                    )}
                    onClick={() => {
                      onChange?.(iconName);
                      setOpen(false);
                      setSearch("");
                    }}>
                    <div>
                      <Icon size={20} />
                    </div>
                  </Button>
                );
              })
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

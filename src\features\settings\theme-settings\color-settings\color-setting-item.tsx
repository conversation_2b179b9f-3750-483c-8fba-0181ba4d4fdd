import { useState } from "react";
import { ChevronRight } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

import { ColorPickerInput } from "./color-picker";

interface ColorSettingItemProps {
  keyColor: string;
  value: string;
  description: string;
  onChange: (key: string, color: string) => void;
  subColors?: Array<{ key: string; value: string; description: string }>;
  mode: "light" | "dark";
}

export function ColorSettingItem({
  keyColor,
  value,
  description,
  onChange,
  subColors,
  // mode,
}: ColorSettingItemProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { t } = useTranslation();

  return (
    <div className={cn("space-y-4", subColors && "xl:col-span-2")}>
      <div className="grid grid-cols-2 gap-2">
        <div className=" flex items-center gap-1">
          {subColors && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className={cn("transition-transform", isExpanded && "rotate-90")}>
              <ChevronRight className="size-4" />
            </Button>
          )}
          <span className="min-w-16 text-sm">{t(description)}</span>
        </div>
        <ColorPickerInput value={value} onChange={(color) => onChange(keyColor, color)} />
      </div>

      {isExpanded && subColors && (
        <div className="ml-4 space-y-4 border-l pl-4">
          {subColors.map((subColor) => (
            <div key={subColor.key} className="grid grid-cols-2 gap-2">
              <span className="min-w-16 text-sm text-muted-foreground">
                {t(subColor.description)}
              </span>
              <ColorPickerInput
                value={subColor.value}
                onChange={(color) => onChange(subColor.key, color)}
                disabled
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

import { useState } from "react";
import { AlertTriangle, Info } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

import { MappingCategory, MappingField } from "./mocks/mapping-attribute";
import { MappingFieldsTable } from "./product-mapping-table";

interface ProductMappingListProps {
  categories: MappingCategory[];
  onFieldChange?: (categoryId: string, fieldIndex: number, updatedField: MappingField) => void;
  onFieldToggle?: (categoryId: string, fieldIndex: number, enabled: boolean) => void;
  onAdvancedMapping?: (categoryId: string, fieldIndex: number, field: MappingField) => void;
  onTabChange?: (categoryId: string) => void;
  attributeData?: any;
  attributeDetail?: any;
}

export function ProductMappingList({
  categories,
  onFieldChange,
  onFieldToggle,
  onAdvancedMapping,
  onTabChange,
  attributeData,
  attributeDetail,
}: ProductMappingListProps) {
  const [activeTab, setActiveTab] = useState(categories[0]?.id || "");

  const handleFieldChange = (
    categoryId: string,
    fieldIndex: number,
    updatedField: MappingField
  ) => {
    if (onFieldChange) {
      onFieldChange(categoryId, fieldIndex, updatedField);
    }
  };

  const handleFieldToggle = (categoryId: string, fieldIndex: number, enabled: boolean) => {
    if (onFieldToggle) {
      onFieldToggle(categoryId, fieldIndex, enabled);
    }
  };

  const handleAdvancedMapping = (categoryId: string, fieldIndex: number, field: MappingField) => {
    if (onAdvancedMapping) {
      onAdvancedMapping(categoryId, fieldIndex, field);
    }
  };

  const handleTabChange = (categoryId: string) => {
    setActiveTab(categoryId);
    if (onTabChange) {
      onTabChange(categoryId);
    }
  };

  const activeCategory = categories.find((c) => c.id === activeTab);

  return (
    <div className="grid w-full grid-cols-1 gap-4 lg:grid-cols-4">
      {/* Left Column - Sidebar Categories */}
      <div className="hide-scrollbar h-fit max-h-[calc(100vh-200px)] overflow-y-auto lg:sticky">
        <div className="flex h-full flex-col">
          <div className="min-h-0 flex-1">
            <div className="space-y-2">
              {categories.map((category) => (
                <div
                  key={category.id}
                  className={cn(
                    "flex cursor-pointer flex-col rounded-lg p-3 transition-colors",
                    activeTab === category.id ? "bg-muted" : "bg-card hover:bg-muted/50"
                  )}
                  onClick={() => handleTabChange(category.id)}>
                  <div className="flex items-center gap-2">
                    <Label className="font-medium">{category.label}</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="size-4 cursor-pointer text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Information about {category.label}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>

                  <div className="mt-2 flex items-center gap-2">
                    {category.errors > 0 && (
                      <span className="flex items-center text-sm">
                        <Badge variant="destructive" className="text-xs font-normal">
                          <AlertTriangle className="mr-1 size-3" />
                          {category.errors} errors
                        </Badge>
                      </span>
                    )}
                    <span className="text-sm text-muted-foreground">
                      <Badge variant="default" className="text-xs font-normal">
                        {category.mapped}/{category.total} mapped
                      </Badge>
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Right Column - Mapping Fields */}
      <div className="lg:col-span-3">
        <Card className="overflow-x-auto">
          <CardContent className="p-0">
            {activeCategory && activeCategory.fields.length > 0 ? (
              <MappingFieldsTable
                fields={activeCategory.fields}
                onFieldChange={(index, updatedField) =>
                  handleFieldChange(activeTab, index, updatedField)
                }
                onFieldToggle={(index, enabled) => handleFieldToggle(activeTab, index, enabled)}
                onAdvancedMapping={(index, field) => handleAdvancedMapping(activeTab, index, field)}
                attributeData={attributeData}
                attributeDetail={attributeDetail}
              />
            ) : (
              <div className="py-8 text-center text-muted-foreground">
                <Label>Select a category to view mapping fields</Label>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export type { MappingCategory, MappingField };

import { FilterOption } from "@/components/data-table/types";

export const RecordTypeOptions: FilterOption[] = [
  { value: "product", label: "Product" },
  { value: "stock_adjustment", label: "Stock adjustment" },
  { value: "order", label: "Order" },
  { value: "return_order", label: "Return Order" },
  { value: "purchase_order", label: "Purchase order" },
  { value: "customer", label: "Customer" },
  { value: "inventory", label: "Inventory" },
];

export const SyncRecordStatusOptions: FilterOption[] = [
  { value: "FETCHED", label: "Fetched" },
  { value: "TRANSFORMED", label: "Transformed" },
  { value: "PUBLISHED", label: "Published" },
  { value: "ERROR", label: "Error" },
  { value: "SKIPPED", label: "Skipped" },
  { value: "COMPLETED", label: "Completed" },
];

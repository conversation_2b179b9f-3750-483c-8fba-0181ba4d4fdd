import React from "react";
import { icons } from "lucide-react";

interface LucideIconProps extends React.HTMLAttributes<SVGElement> {
  iconName: string;
  size?: number;
  className?: string;
}

const LucideIcon = ({ iconName, size = 16, className, ...props }: LucideIconProps) => {
  const Icon = icons[iconName as keyof typeof icons];
  return <Icon size={size} className={className} {...props} />;
};

export default LucideIcon;

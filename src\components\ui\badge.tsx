import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary/10 text-primary",
        primary: "border-transparent rounded-lg bg-primary text-primary-foreground",
        secondary: "border-transparent bg-muted text-secondary-foreground",
        destructive: "border-transparent bg-destructive/10 text-destructive shadow",
        outline: "text-foreground",
        sky: "bg-sematic-info text-sematic-foreground",
        blue: "border-none bg-sematic-blue/10 text-sematic-blue",
        green: "border-none bg-sematic-success/20 text-sematic-success",
        yellow: "border-none bg-sematic-warning/10 text-sematic-warning",
        orange: "border-none bg-[rgba(249,115,22,0.1)] text-orange-500",
        sematic_info: "bg-[rgba(14,165,233,0.1)] font-semibold text-sematic-info",
        sematic_warning: "bg-[rgba(245,158,11,0.1)] font-semibold text-sematic-warning",
        sematic_success: "bg-[rgba(22,163,74,0.1)] font-semibold text-sematic-success",
        sematic_error: "bg-[rgba(220,38,38,0.1)] font-semibold text-destructive",
        sematic_destructive: "border-none bg-[rgba(220,38,38,0.1)] font-semibold text-destructive",
        sematic_default: "bg-muted font-semibold text-secondary-foreground",
        gradient: "bg-gradient-to-r from-[#9747FF] to-[#FF7B00] text-white",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant }), className)} {...props} />;
}

export { Badge, badgeVariants };

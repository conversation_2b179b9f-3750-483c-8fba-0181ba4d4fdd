import { useState } from "react";
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { departmentApi } from "@/lib/apis/department";

import { departmentKeys, IGetDepartmentsParams } from "./keys";
import { Department, DepartmentPayload } from "./types";

interface UseDepartmentsOptions extends Partial<IGetDepartmentsParams> {
  enabled?: boolean;
}

export function useDepartments(options: UseDepartmentsOptions = {}) {
  const { limit = 999, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();
  const [isTimeoutLoading, setIsTimeoutLoading] = useState(false);

  const query = useInfiniteQuery({
    queryKey: departmentKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      departmentApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const departments = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const createDepartment = useMutation({
    mutationFn: async (data: DepartmentPayload) => {
      const response = await departmentApi.create(data);
      return response;
    },
    onError: (error: any) => {
      setIsTimeoutLoading(false);
      toast.error(
        error.response?.data?.message || "Failed to create department. Please try again."
      );
    },
    onMutate: () => {
      setIsTimeoutLoading(true);
    },
    onSuccess: (newDepartment) => {
      toast.success("Department created successfully");

      // Update the query data with the new department
      queryClient.setQueryData(departmentKeys.list({ limit, ...restOptions }), (oldData: any) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          pages: oldData.pages.map((page: any, index: number) => {
            if (index === 0) {
              return {
                ...page,
                items: [newDepartment, ...page.items],
                total: page.total + 1,
              };
            }
            return page;
          }),
        };
      });
      setTimeout(() => {
        setIsTimeoutLoading(false);
      }, 2000);
    },
  });

  return {
    ...query,
    departments,
    total,
    onCreate: createDepartment.mutateAsync,
    isCreating: createDepartment.isPending || isTimeoutLoading,
  };
}

export function useDepartmentDetail(id: string) {
  const query = useQuery({
    queryKey: departmentKeys.detail(id),
    queryFn: () => departmentApi.getById(id),
  });

  const department = query.data;

  return { ...query, department };
}

export function useUpdateDepartment() {
  return useMutation({
    mutationFn: async (data: Partial<Department>) => {
      const response = await departmentApi.update(data.id as string, data);
      return response;
    },
    onError: (error: any) => {
      console.error("Error updating department:", error);
      toast.error(
        error.response?.data?.message || "Failed to update department. Please try again."
      );
    },
    onSuccess: (data) => {
      console.log("Department updated successfully:", data);
    },
  });
}

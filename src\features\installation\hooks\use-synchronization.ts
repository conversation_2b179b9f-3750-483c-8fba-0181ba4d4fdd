import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { useConnection } from "@/features/integration/hooks/use-channel";

import { COOKIES } from "@/utils/constants/cookies";
import { getCookie } from "@/utils/cookie";

import { useSyncSettings } from "./use-sync-settings";

export const useSynchronization = () => {
  const { t } = useTranslation();

  const [sourceId, setSourceId] = useState<string>("");
  const [destinationId, setDestinationId] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [connectionChecked, setConnectionChecked] = useState(false);

  // Lấy thông tin source và destination connection
  const { data: sourceConnection, isLoading: isLoadingSource } = useConnection(sourceId);
  const { data: destinationConnection, isLoading: isLoadingDestination } =
    useConnection(destinationId);

  // Đ<PERSON>nh nghĩa syncSettingsConfig dựa trên loại kết nối
  const syncSettingsConfig = [
    {
      key: "products" as const,
      title: t("pages.synchronization.syncSetting.product.title", {
        source: sourceConnection?.name,
        destination: destinationConnection?.name,
      }),
      description: t("pages.synchronization.syncSetting.product.description", {
        source: sourceConnection?.name,
        destination: destinationConnection?.name,
      }),
    },
    {
      key: "inventory" as const,
      title: t("pages.synchronization.syncSetting.inventory.title", {
        source: sourceConnection?.name,
        destination: destinationConnection?.name,
      }),
      description: t("pages.synchronization.syncSetting.inventory.description", {
        source: sourceConnection?.name,
        destination: destinationConnection?.name,
      }),
    },
    {
      key: "orders" as const,
      title: t("pages.synchronization.syncSetting.order.title", {
        source: sourceConnection?.name,
        destination: destinationConnection?.name,
      }),
      description: t("pages.synchronization.syncSetting.order.description", {
        source: sourceConnection?.name,
        destination: destinationConnection?.name,
      }),
    },
  ];

  // State to track sync settings
  const {
    syncSettingsConfig: settingsConfig,
    syncSettings,
    handleSwitchToggle,
    onSyncSetting,
    isSuccess,
    message,
    isConnecting,
  } = useSyncSettings({
    syncSettingsConfig,
    initialSettings: {
      products: !!(
        sourceConnection?.action_groups?.product?.fetch_actions?.get_product &&
        destinationConnection?.action_groups?.product?.publish_actions?.sync_product
      ),
      inventory: !!(
        sourceConnection?.action_groups?.inventory?.fetch_actions?.get_inventory &&
        destinationConnection?.action_groups?.inventory?.publish_actions?.sync_inventory
      ),
      orders: !!(
        sourceConnection?.action_groups?.order?.fetch_actions?.get_order &&
        destinationConnection?.action_groups?.order?.publish_actions?.sync_order
      ),
    },
  });

  // Lấy source và destination ID từ cookie
  useEffect(() => {
    const installationData = getCookie(COOKIES.INSTALLATION_DATA);
    const sourceId = installationData?.source_id;
    const destinationId = installationData?.destination_id;

    if (!sourceId || !destinationId) {
      setError(t("pages.synchronization.error.missingConnection"));
      setConnectionChecked(true);
      return;
    }

    setSourceId(sourceId);
    setDestinationId(destinationId);

    // Reset error state when we have both IDs
    setError(null);
  }, []);

  // Kiểm tra kết quả trả về từ API và hiển thị lỗi nếu cần
  useEffect(() => {
    if (!connectionChecked || isLoadingSource || isLoadingDestination) return;

    // Chỉ kiểm tra khi đã có sourceId và destinationId
    if (sourceId && destinationId) {
      if (!sourceConnection) {
        setError("pages.synchronization.error.sourceNotFound");
      } else if (!destinationConnection) {
        setError("pages.synchronization.error.destinationNotFound");
      } else {
        setError(null);
      }
      setConnectionChecked(true);
    }
  }, [
    sourceConnection,
    destinationConnection,
    isLoadingSource,
    isLoadingDestination,
    sourceId,
    destinationId,
    connectionChecked,
  ]);

  return {
    // States
    sourceId,
    destinationId,
    error,
    connectionChecked,
    isLoadingSource,
    isLoadingDestination,
    sourceConnection,
    destinationConnection,
    settingsConfig,
    syncSettings,
    isSuccess,
    message,
    isConnecting,

    // Handlers
    handleSwitchToggle,
    onSyncSetting,
  };
};

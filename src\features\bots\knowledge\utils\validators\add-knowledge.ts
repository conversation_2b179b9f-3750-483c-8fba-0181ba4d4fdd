import * as z from "zod";

export const addKnowledgeURLSchema = z.object({
  url: z
    .string()
    .min(1, "pages.knowledge.upload.urlRequired")
    .url("pages.knowledge.upload.invalidUrl"),
});

export type AddKnowledgeURLValues = z.infer<typeof addKnowledgeURLSchema>;

export const addKnowledgeTextSchema = z.object({
  knowledge_name: z
    .string()
    .min(1, "pages.knowledge.upload.knowledgeNameRequired")
    .max(250, "pages.knowledge.upload.knowledgeNameTooLong"),
  content: z
    .string()
    .min(1, "pages.knowledge.upload.textRequired")
    .max(20000, "pages.knowledge.upload.textTooLong"),
});

export type AddKnowledgeTextValues = z.infer<typeof addKnowledgeTextSchema>;

import { useCallback } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import { Customer } from "@/features/customer/hooks/type";

import ChannelLogo, {
  DateColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Badge } from "@/components/ui/badge";

export const columns = (
  t: any,
  useDeleteCustomerMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  onEdit?: (customer: Customer) => void
): CustomColumn<Customer>[] => [
  {
    id: "name",
    accessorKey: "name",
    header: t("pages.customers.name"),
    sorter: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Customer> }) => {
      const customer = row.original;
      return (
        <Link href={`/customers/${customer.id}`}>
          <TextColumn
            text={`${customer?.first_name}`}
            className="cursor-pointer text-sm text-primary hover:underline"
          />
        </Link>
      );
    },
  },
  {
    id: "source",
    accessorKey: "source",
    header: t("pages.products.source"),
    sorter: true,
    sortKey: "source.id",
    cell: ({ row }: { row: Row<Customer> }) => (
      <ChannelLogo channelKey={row?.original?.source?.channel_name || ""} href={""} />
    ),
  },
  {
    id: "phone",
    accessorKey: "phone",
    header: t("pages.customers.phone"),
    sorter: true,
    sortKey: "phone",
    cell: ({ row }: { row: Row<Customer> }) => (
      <TextColumn text={row?.original?.phone || ""} className="text-sm" />
    ),
  },
  {
    id: "customer_group",
    accessorKey: "customer_group",
    header: t("pages.customers.group"),
    sorter: true,
    sortKey: "customer_group.id",
    cell: ({ row }: { row: Row<Customer> }) => (
      <Badge variant="yellow">{row?.original?.customer_group?.name || "--"}</Badge>
    ),
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.customers.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Customer> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: t("common.actions"),
    cell: ({ row }: { row: Row<Customer> }) => (
      <ActionCell
        row={row}
        onEdit={onEdit}
        useDeleteCustomerMutation={useDeleteCustomerMutation}
        isDeleting={isDeleting}
      />
    ),
  },
];

const ActionCell = ({
  row,
  onEdit,
  useDeleteCustomerMutation,
  isDeleting,
}: {
  row: Row<Customer>;
  onEdit?: (customer: Customer) => void;
  useDeleteCustomerMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
}) => {
  const router = useRouter();
  const customer = row.original;

  const handleView = useCallback(() => {
    router.push(`/customers/${customer.id}` as any);
  }, [router, customer.id]);

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(customer);
    } else {
      router.push(`/customers/${customer.id}/edit` as any);
    }
  }, [router, customer, onEdit]);

  const handleDelete = useCallback(async () => {
    return useDeleteCustomerMutation.mutateAsync(customer.id);
  }, [useDeleteCustomerMutation, customer.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
        },
      ]}
    />
  );
};

import React from "react";
import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface PurchaseInfoProps {
  totalSpent: number | string;
  totalProductsPurchased: number;
  purchasedOrders: number;
  totalProductsReturned: number;
  lastOrderAt: string;
}

export const PurchaseInfo: React.FC<PurchaseInfoProps> = ({
  totalSpent,
  totalProductsPurchased,
  purchasedOrders,
  totalProductsReturned,
  lastOrderAt,
}) => {
  const { t } = useTranslation();

  return (
    <Card className="w-full">
      <CardHeader className="p-4">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {t("pages.customer.purchase.purchaseInfo")}
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4">
        <div className="w-full">
          <div className="grid grid-cols-2">
            <div className="flex border-b py-2">
              <div className="text-sm font-medium">{t("pages.customer.purchase.totalSpent")}</div>
              <div className="ml-auto text-sm">{totalSpent} đ</div>
            </div>
            <div className="ml-4 flex border-b py-2">
              <div className="text-sm font-medium">
                {t("pages.customer.purchase.totalProductsPurchased")}
              </div>
              <div className="ml-auto text-sm">{totalProductsPurchased}</div>
            </div>
          </div>

          <div className="grid grid-cols-2">
            <div className="flex border-b py-2">
              <div className="text-sm font-medium">
                {t("pages.customer.purchase.purchasedOrder")}
              </div>
              <div className="ml-auto text-sm">{purchasedOrders}</div>
            </div>
            <div className="ml-4 flex border-b py-2">
              <div className="text-sm font-medium">
                {t("pages.customer.purchase.totalProductsReturned")}
              </div>
              <div className="ml-auto text-sm">{totalProductsReturned}</div>
            </div>
          </div>

          <div className="grid grid-cols-2">
            <div className="flex border-b py-2">
              <div className="text-sm font-medium">{t("pages.customer.purchase.lastOrderAt")}</div>
              <div className="ml-auto text-sm">{lastOrderAt || "--"}</div>
            </div>
            <div className="ml-4 flex border-b py-2"></div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PurchaseInfo;

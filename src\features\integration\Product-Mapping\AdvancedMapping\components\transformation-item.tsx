"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { Info, Loader2, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Separator } from "@/components/ui";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PortalCombobox } from "@/components/ui/portal-combobox";
import { cn } from "@/lib/utils";

import { useTransformations } from "../hooks/transformation";
import { TransformationDirection } from "./transformation-direction";

interface TransformationItemProps {
  transformation: {
    id?: string;
    type?: string;
    config?: Record<string, any>;
  };
  index: number;
  onTransformationTypeChange: (id: string, type: string) => void;
  onConfigChange: (id: string, config: any) => void;
  onRemoveTransformation: (id: string) => void;
}

export function TransformationItem({
  transformation,
  index,
  onTransformationTypeChange,
  onConfig<PERSON>hange,
  onRemoveTransformation,
}: TransformationItemProps) {
  const { t } = useTranslation();
  const [isDirectionOpen, setIsDirectionOpen] = useState(false);
  const { transformations: availableTransformations, isLoading } = useTransformations();

  // Local state for config values to make UI responsive
  const [localConfig, setLocalConfig] = useState<Record<string, any>>(transformation.config || {});

  // Timer reference for debouncing
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Update local config when transformation.config changes
  useEffect(() => {
    setLocalConfig(transformation.config || {});
  }, [transformation.config]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Find the selected transformation from available transformations
  const selectedTransformation = availableTransformations.find(
    (transform) => transform.type === transformation.type
  );

  // Debounced handler for text/number inputs
  const handleInputChange = useCallback(
    (fieldKey: string, value: any) => {
      // Update local state immediately for responsive UI
      setLocalConfig((prevConfig) => ({
        ...prevConfig,
        [fieldKey]: value,
      }));

      // Clear any existing timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Set a new timer
      debounceTimerRef.current = setTimeout(() => {
        // Only notify parent after debounce period
        onConfigChange(transformation.id || "", {
          ...localConfig,
          [fieldKey]: value,
        });
        debounceTimerRef.current = null;
      }, 1000);
    },
    [transformation.id, onConfigChange, localConfig]
  );

  // Immediate handler for select/checkbox inputs (no debounce needed)
  const handleImmediateChange = useCallback(
    (fieldKey: string, value: any) => {
      // Update local state
      setLocalConfig((prevConfig) => ({
        ...prevConfig,
        [fieldKey]: value,
      }));

      // Notify parent immediately
      onConfigChange(transformation.id || "", {
        ...localConfig,
        [fieldKey]: value,
      });
    },
    [transformation.id, onConfigChange, localConfig]
  );

  const renderConfigFields = () => {
    if (!selectedTransformation?.config_fields) return null;

    return (
      <div className="flex flex-col items-center justify-center gap-2">
        <Separator orientation="horizontal" />
        {selectedTransformation.config_fields.map((field) => {
          switch (field.type) {
            case "text":
              return (
                <Input
                  key={field.key}
                  containerClassName="w-full bg-card"
                  placeholder={field.placeholder}
                  value={localConfig[field.key] || ""}
                  onChange={(e) => handleInputChange(field.key, e.target.value)}
                />
              );
            case "number":
              return (
                <Input
                  key={field.key}
                  type="number"
                  containerClassName="w-full bg-card"
                  placeholder={field.placeholder}
                  value={localConfig[field.key] || ""}
                  onChange={(e) => handleInputChange(field.key, e.target.value)}
                />
              );
            case "select":
              return (
                <PortalCombobox
                  key={field.key}
                  value={localConfig[field.key] || ""}
                  onValueChange={(value) => handleImmediateChange(field.key, value)}
                  items={
                    field.options?.map((option) => ({
                      id: typeof option === "string" ? option : option.value,
                      name: typeof option === "string" ? option : option.label,
                    })) || []
                  }
                  placeholder={field.placeholder || "Select an option"}
                  searchPlaceholder="Search options..."
                />
              );
            case "boolean":
              return (
                <div key={field.key} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={localConfig[field.key] || false}
                    onChange={(e) => handleImmediateChange(field.key, e.target.checked)}
                  />
                  <label>{field.label}</label>
                </div>
              );
            default:
              return null;
          }
        })}
      </div>
    );
  };

  // Filter out transformations with undefined type or label and format for combobox
  const transformationItems = availableTransformations
    .filter((transform) => transform.type && transform.label)
    .map((transform) => ({
      id: transform.type as string,
      name: transform.label as string,
    }));

  console.log("TransformationItem - Formatted items for combobox:", transformationItems);

  return (
    <div className="flex flex-col gap-2 rounded-lg border border-border bg-background p-2">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium">
          {t("pages.productMapping.advancedMapping.transformationType", { index: index + 1 })}
        </label>
        <div className="flex items-center gap-2">
          {transformation.type && (
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "flex size-5 items-center justify-center",
                  isDirectionOpen && "bg-neutral-300 text-accent-foreground"
                )}
                onClick={() => setIsDirectionOpen(!isDirectionOpen)}>
                <Info className="size-4" />
              </Button>
            </div>
          )}
          {transformation.type && <Separator className="h-4" orientation="vertical" />}
          <Button
            variant="ghost"
            size="icon"
            className="flex size-5 items-center justify-center text-destructive hover:text-destructive-hover"
            onClick={() => onRemoveTransformation(transformation?.id || "")}>
            <Trash2 size={16} />
          </Button>
        </div>
      </div>
      {isDirectionOpen && transformation.type && (
        <div className="relative">
          <div className="absolute -top-1 right-[42.5px] size-0 border-x-[5px] border-b-[5px] border-x-transparent border-b-border" />
          <TransformationDirection type={transformation.type} />
        </div>
      )}
      <div className="bg-card">
        {isLoading ? (
          <div className="flex h-10 items-center justify-center">
            <Loader2 className="size-5 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <PortalCombobox
            value={transformation.type || ""}
            onValueChange={(value) => {
              console.log("TransformationItem - Type selected:", value);
              onTransformationTypeChange(transformation.id || "", value);
              setIsDirectionOpen(false);
            }}
            items={transformationItems}
            placeholder={t("pages.productMapping.advancedMapping.selectTransformationTypes")}
            searchPlaceholder={t("pages.productMapping.advancedMapping.searchTransformationTypes")}
          />
        )}
      </div>

      {renderConfigFields()}
    </div>
  );
}

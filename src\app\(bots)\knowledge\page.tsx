"use client";

import { useMemo, useState } from "react";
import { Upload } from "lucide-react";
import { useTranslation } from "react-i18next";

import AddKnowledgeDialog from "@/features/bots/knowledge/components/knowledge-dialog/add-knowledge";
import { columns } from "@/features/bots/knowledge/components/KnowledgeList/column";
import { useKnowledge } from "@/features/bots/knowledge/hooks/knowledge";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";

const KNOWLEDGE_FILTERS: FilterType[] = [
  {
    id: "source",
    type: EFilterType.SELECT_BOX,
    title: "pages.knowledge.filters.fileType",
    remote: false,
    dataOption: [
      { label: "URL", value: "URL" },
      { label: "File", value: "FILE" },
      { label: "Text", value: "DIRECT_TEXT" },
    ],
  },
  {
    id: "status",
    type: EFilterType.SELECT_BOX,
    title: "pages.knowledge.filters.status",
    remote: false,
    dataOption: [
      { label: "pages.knowledge.status.pending", value: "PENDING" },
      { label: "pages.knowledge.status.processing", value: "PROCESSING" },
      { label: "pages.knowledge.status.ready", value: "READY" },
      { label: "pages.knowledge.status.error", value: "ERROR" },
    ],
  },
];

export default function KnowledgePage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const [showAdd, setShowAdd] = useState(false);

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const {
    knowledge,
    total,
    isLoading,
    isFetching,
    refetch,
    useDeleteKnowledgeMutation,
    useDeleteListKnowledgeMutation,
  } = useKnowledge(options);

  const isTableLoading = isLoading || isFetching;

  const filterConfig: FilterTableProps = useMemo(
    () => ({
      showSearch: true,
      filterType: "knowledge",
      searchPlaceHolder: t("pages.knowledge.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: KNOWLEDGE_FILTERS.map((filter) => ({
        ...filter,
        title: t(filter.title),
        dataOption: filter.dataOption?.map((option) => ({
          ...option,
          label: t(option.label),
        })),
        defaultValue: getInitialParams[filter.id] as string | number | undefined,
      })),
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [handleParamSearch, isTableLoading, t, getInitialParams]
  );

  const groupButtonConfig: GroupButtonProps = useMemo(
    () => ({
      buttons: [
        {
          type: "button" as const,
          title: t("common.upload"),
          icon: Upload,
          onClick: () => setShowAdd(true),
        },
      ],
      onRefresh: () => refetch(),
      isRefreshLoading: isFetching,
    }),
    [t, refetch, isFetching]
  );

  const handleDelete = async (listIndexId: number[], handleRestRows: () => void) => {
    const newList = listIndexId.map((row: any) => knowledge?.[row]?.id);
    useDeleteListKnowledgeMutation.mutate({
      listId: newList,
      handleRestRows,
    });
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.knowledge.title")}
        filterType="knowledge"
        data={knowledge || []}
        filterProps={filterConfig}
        rightComponent={<GroupButton {...groupButtonConfig} />}
        isExportable={false}
      />
      <TableContainer
        columns={columns(useDeleteKnowledgeMutation, isFetching, t)}
        data={knowledge || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={handleDelete}
      />
      {showAdd && (
        <AddKnowledgeDialog
          open={showAdd}
          onOpenChange={setShowAdd}
          onClose={() => setShowAdd(false)}
        />
      )}
    </TableCard>
  );
}

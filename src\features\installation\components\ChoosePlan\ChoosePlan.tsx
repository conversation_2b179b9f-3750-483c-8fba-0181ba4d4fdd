"use client";

import React, { useEffect, useState } from "react";
import * as ToggleGroup from "@radix-ui/react-toggle-group";
import { motion } from "framer-motion";
import { Building2, User } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslation } from "react-i18next";

import { COOKIES } from "@/utils/constants/cookies";
import { getCookie } from "@/utils/cookie";

import { InstallationData } from "../../hooks/type";
import { usePlan } from "../../hooks/use-plan";
import { PricingPlanCard } from "../PricingPlanCard";
import { ShopifyMenu } from "../ShopifyMenu";

export type PlanType = "monthly" | "annually";
export type PricingTier = "free" | "starter" | "pro" | "agency";

export const ChoosePlan: React.FC = () => {
  const { t } = useTranslation();
  const [connectionId, setConnectionId] = useState<string>("");

  const {
    planType,
    setPlanType,
    integrations,
    selectedPlan,
    handleIncrement,
    handleDecrement,
    getUndiscountPrice,
    getPrice,
    setSelectedPlan,
    handleSubscribePlan,
    plans,
    isLoading,
    subscribingPlanId,
  } = usePlan(connectionId);
  const [mounted, setMounted] = useState(false);
  const { theme, systemTheme } = useTheme();

  useEffect(() => {
    const installationData = getCookie(COOKIES.INSTALLATION_DATA) as InstallationData;
    setConnectionId(installationData?.source_id);
    setMounted(true);
  }, []);

  const currentMode = theme === "system" ? systemTheme : theme;
  const isDark = mounted && currentMode === "dark";

  const SectionContainer: React.FC<{
    title: string;
    icon: React.ReactNode;
    children: React.ReactNode;
    className?: string;
  }> = ({ title, icon, children, className }) => (
    <div className={`w-full ${className}`}>
      <div className="h-full rounded-3xl bg-gradient-to-b from-[#FFF4E7] to-transparent p-3 transition-all duration-300 dark:from-[#1A0F01]">
        <div className="mb-2 flex w-full items-center justify-center gap-1 text-muted-foreground">
          {icon}
          <span className="text-sm text-foreground">{title}</span>
        </div>
        {children}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-background p-4 sm:p-10 sm:pt-6">
      <div className="mx-auto">
        <div className="flex w-full justify-center">
          <ShopifyMenu className="p-2 sm:p-0" />
        </div>
        <div className="flex w-full flex-col items-center bg-transparent">
          {/* Header section with responsive design */}
          <div className="w-full">
            {/* Desktop header */}
            <div className="mb-4 flex items-center justify-center">
              <h1 className="text-center text-3xl font-bold text-foreground">
                {t("pages.choosePlan.chooseAPlan")}
              </h1>
            </div>
            <p className="mx-auto max-w-2xl text-center text-muted-foreground">
              {t("pages.choosePlan.planDescription.firstSection")}{" "}
              <span className="font-semibold text-foreground">
                {t("pages.choosePlan.planDescription.middleSection")}
              </span>{" "}
              {t("pages.choosePlan.planDescription.secondSection")}
            </p>
          </div>

          {/* Toggle Group */}
          <div className="mt-2 w-fit flex-1 flex-row text-right">
            <div className="flex w-full items-center justify-center">
              <ToggleGroup.Root
                type="single"
                value={planType}
                defaultValue="annually"
                className="relative grid h-10 grid-cols-2 rounded-lg bg-muted p-1">
                <motion.div
                  className="absolute top-1 h-8 rounded-md bg-bg-secondary"
                  initial={false}
                  animate={{
                    x: planType === "monthly" ? 2 : "calc(105% - 4px)",
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 500,
                    damping: 30,
                    mass: 0.7,
                  }}
                  style={{ width: "calc(50% - 4px)", marginLeft: "2px" }}
                />
                <ToggleGroup.Item
                  value="monthly"
                  onClick={() => setPlanType("monthly")}
                  className="z-10 h-full rounded-md px-2 text-sm font-medium text-muted-foreground transition data-[state=on]:text-foreground">
                  {t("branch.monthly")}
                </ToggleGroup.Item>
                <ToggleGroup.Item
                  value="annually"
                  onClick={() => setPlanType("annually")}
                  className="z-10 h-full rounded-md px-2 text-sm font-medium text-muted-foreground transition data-[state=on]:text-foreground">
                  {t("branch.annually")}
                </ToggleGroup.Item>
              </ToggleGroup.Root>
            </div>
            {/* Discount Label */}
            <motion.div
              className="flex justify-end pr-1 text-xs text-muted-foreground"
              initial={{ opacity: 0 }}
              animate={{
                opacity: planType === "annually" ? 1 : 0,
                y: planType === "annually" ? 0 : -5,
              }}
              transition={{ duration: 0.2 }}>
              10% off
            </motion.div>
          </div>
        </div>
        <div className="mx-auto mt-4 flex w-full flex-col items-center">
          <div className="flex w-full flex-wrap justify-center gap-4">
            {/* For Individuals Section */}
            <SectionContainer
              title={t("pages.choosePlan.forIndividuals")}
              icon={<User className="size-4 text-foreground" />}
              className="lg:flex-[3]">
              <div className="grid size-full grid-cols-1 gap-4 md:grid-cols-3">
                {Array.isArray(plans) &&
                  plans.slice(0, -1).map((plan) => (
                    <div key={plan.id} className="size-full">
                      <PricingPlanCard
                        plan={plan}
                        integrations={integrations}
                        handleIncrement={handleIncrement}
                        handleDecrement={handleDecrement}
                        getUndiscountPrice={getUndiscountPrice}
                        getPrice={getPrice}
                        planType={planType}
                        isPopular={!!plan?.is_popular}
                        onClick={handleSubscribePlan}
                        isLoading={subscribingPlanId === plan.id}
                        disabled={subscribingPlanId !== null && subscribingPlanId !== plan.id}
                        isCompany={false}
                      />
                    </div>
                  ))}
              </div>
            </SectionContainer>

            {/* For Companies Section */}
            {Array.isArray(plans) && plans.length > 0 && (
              <SectionContainer
                title={t("pages.choosePlan.forCompanies")}
                icon={<Building2 className="size-4 text-foreground" />}
                className="mt-6 lg:mt-0 lg:flex-1">
                <div className="size-full">
                  <PricingPlanCard
                    plan={plans[plans.length - 1]}
                    integrations={integrations}
                    handleIncrement={handleIncrement}
                    handleDecrement={handleDecrement}
                    getUndiscountPrice={getUndiscountPrice}
                    getPrice={getPrice}
                    planType={planType}
                    isPopular={!!plans[plans.length - 1]?.is_popular}
                    onClick={handleSubscribePlan}
                    isLoading={subscribingPlanId === plans[plans.length - 1].id}
                    isCompany={true}
                    disabled={
                      subscribingPlanId !== null && subscribingPlanId !== plans[plans.length - 1].id
                    }
                  />
                </div>
              </SectionContainer>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

import { MESSAGE as MessageType } from "@/features/xbots/hooks/types";

import { CustomImage } from "@/components/ui/image";
import { Markdown } from "@/components/ui/markdown";

interface MessageProps {
  message: MessageType;
  isSending?: boolean;
  staffAvatar?: string;
  isSender?: boolean;
  isStream?: boolean;
}

export function Message({
  message,
  isSending,
  staffAvatar,
  isSender,
  isStream = true,
}: MessageProps) {
  const isExternalUserAvatar = message.role === "EXTERNAL_USER";
  const isStaff = message.role === "VIRTUAL_STAFF";
  if (!message?.content) return null;
  return (
    <div className={`flex w-full flex-col ${isSender ? "items-end" : "items-start"}`}>
      <div
        className={`flex w-full items-start gap-2 ${isSender ? "justify-end" : "justify-start"}`}>
        {/* Staff avatar on left if staff is receiver */}
        {!isSender && isStaff && (
          <div className="relative size-8 shrink-0 overflow-hidden rounded-full border border-border">
            <CustomImage
              src={staffAvatar || "/avatars/default.png"}
              alt="Staff Avatar"
              fill
              className="object-cover"
            />
          </div>
        )}
        {/* External user avatar (always left) */}
        {isExternalUserAvatar && !isSender && (
          <div className="relative size-8 shrink-0 overflow-hidden rounded-full border border-border">
            <CustomImage
              src={"/avatars/default.png"}
              alt="Staff Avatar"
              fill
              className="object-cover"
            />
          </div>
        )}
        <div
          className={`flex flex-col ${isSender ? "items-end" : "items-start"} w-fit max-w-[80%]`}>
          <div
            className={`${
              isSender
                ? " bg-primary text-left "
                : " border border-bg-secondary bg-bg-primary text-left  "
            } w-fit max-w-full overflow-hidden break-words rounded-2xl p-3 text-sm leading-5 `}>
            <div className="w-fit whitespace-pre-wrap break-words">
              <Markdown>{message.content}</Markdown>
              {/* {isStream && isSending && !message.content && <LoadingThreeDotsJumping />} */}
            </div>
          </div>
          <p className="mt-1 w-full text-xs" style={{ textAlign: isSender ? "right" : "left" }}>
            {new Date(message?.created_at || "").toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </p>
        </div>
        {/* Staff avatar on right if staff is sender */}
        {isSender && isStaff && (
          <div className="relative size-8 shrink-0 overflow-hidden rounded-full border border-border">
            <CustomImage
              src={staffAvatar || "/avatars/default.png"}
              alt="Staff Avatar"
              fill
              className="object-cover"
            />
          </div>
        )}
      </div>
    </div>
  );
}

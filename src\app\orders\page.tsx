"use client";

import { useMemo } from "react";
import { useRouter } from "next/navigation";
import { Row } from "@tanstack/react-table";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import OrderRowExpand from "@/features/orders/components/order-row-expand";
import { columns } from "@/features/orders/components/OrderList/column";
import { useOrders } from "@/features/orders/hooks/order";
import { Order } from "@/features/orders/hooks/types";
import {
  OrderStatusFilterOptions,
  PaymentStatusFilterOptions,
} from "@/features/products/utils/constants/fitler_options";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function OrdersPage() {
  return <OrdersContent />;
}

function OrdersContent() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );
  const { orders, total, isLoading, isFetching } = useOrders(options);

  const isTableLoading = isLoading || isFetching;
  const router = useRouter();
  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "orders",
      searchPlaceHolder: t("pages.orders.placeholder"),
      numberOfFilters: 2,
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "source.channel_name",
          type: EFilterType.SELECT_BOX,
          title: t("pages.products.filters.source"),
          defaultValue: getInitialParams["source.channel_name"],
          isChannelFetch: true,
          remote: true,
        },
        {
          id: "status",
          type: EFilterType.SELECT_BOX,
          title: t("pages.orders.filters.status"),
          defaultValue: getInitialParams["status"],
          dataOption: OrderStatusFilterOptions,
        },
        {
          id: "payment_status",
          type: EFilterType.SELECT_BOX,
          title: t("pages.orders.filters.paymentStatus"),
          defaultValue: getInitialParams["payment_status"],
          dataOption: PaymentStatusFilterOptions,
        },
        {
          id: "shift_id",
          type: EFilterType.DATE,
          title: t("pages.orders.filters.shift"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "created_at",
          type: EFilterType.DATE,
          title: t("pages.orders.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: EFilterType.DATE,
          title: t("pages.orders.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [getInitialParams, t, handleParamSearch, isTableLoading]);
  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("common.add"),
        icon: PlusIcon,
        onClick: () => {
          router.push(authProtectedPaths.ORDERS_NEW as any);
        },
      },
    ],
    showRefresh: true,
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.orders.title")}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
        data={orders || []}
        filterType="orders"
      />
      <TableContainer
        columns={columns(t)}
        data={orders || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        expandable={{
          enabled: true,
          content: (row: Row<Order>) => <OrderRowExpand row={row} />,
        }}
      />
    </TableCard>
  );
}

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { KnowledgeTab, useKnowledgeUpload } from "../../hooks/useKnowledgeUpload";
import { Knowledge } from "../../types";
import { FileUploader } from "./file-uploader";
import KnowledgeUploadedTab from "./knowledge-uploaded";
import TextUploader from "./text-uploader";
import URLUploader from "./url-uploader";

interface AddKnowledgeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onClose: () => void;
  showKnowledgeSection?: boolean;
  selectedKnowledge?: Knowledge[];
  onSave?: (selectedKnowledge: Knowledge[]) => void;
  lastDeletedId?: string;
  isStaffContext?: boolean;
}

export default function AddKnowledgeDialog({
  open,
  onOpenChange,
  onClose,
  showKnowledgeSection = false,
  selectedKnowledge = [],
  onSave,
  lastDeletedId,
  isStaffContext = false,
}: AddKnowledgeDialogProps) {
  const [tab, setTab] = useState<KnowledgeTab>("FILE");
  const { t } = useTranslation();
  const [isSaving, setIsSaving] = useState(false);
  const {
    fileList,
    setFileList,
    urls,
    setUrls,
    textForm,
    urlForm,
    isUploading,
    uploadKnowledge,
    listText,
    setListText,
  } = useKnowledgeUpload();

  const [tempSelectedKnowledge, setTempSelectedKnowledge] = useState<Knowledge[]>(
    isStaffContext ? selectedKnowledge : []
  );

  useEffect(() => {
    if (isStaffContext) {
      setTempSelectedKnowledge(selectedKnowledge);
    }
  }, [selectedKnowledge, isStaffContext]);

  const handleSubmit = async () => {
    if (tab === "LIST") {
      setIsSaving(true);
      try {
        if (isStaffContext) {
          const finalSelectedKnowledge = tempSelectedKnowledge.filter(
            (k) => !lastDeletedId || k.id !== lastDeletedId
          );
          onSave?.(finalSelectedKnowledge);
        } else {
          onClose();
        }
        setIsSaving(false);
        onClose();
      } catch (error) {
        console.error("Error saving knowledge:", error);
        setIsSaving(false);
      }
    } else {
      await uploadKnowledge(tab);
      onClose();
    }
  };

  const TABS = showKnowledgeSection
    ? [
        { label: t("pages.knowledge.upload.file"), value: "FILE" },
        { label: t("pages.knowledge.upload.url"), value: "URL" },
        { label: t("pages.knowledge.upload.text"), value: "TEXT" },
        { label: t("pages.knowledge.upload.uploaded"), value: "LIST" },
      ]
    : [
        { label: t("pages.knowledge.upload.file"), value: "FILE" },
        { label: t("pages.knowledge.upload.url"), value: "URL" },
        { label: t("pages.knowledge.upload.text"), value: "TEXT" },
      ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex h-[90vh] max-h-[90vh] max-w-[800px] flex-col gap-0 overflow-hidden rounded-lg p-0">
        <DialogHeader className="flex-none shrink-0 p-6 text-left">
          <DialogTitle>{t("pages.knowledge.upload.title")}</DialogTitle>
        </DialogHeader>
        <Tabs
          value={tab}
          onValueChange={(v) => setTab(v as KnowledgeTab)}
          className="flex flex-auto flex-col gap-4 overflow-hidden px-6 py-2">
          <TabsList className="flex-none shrink-0 items-end justify-start rounded-none border-b border-transparent bg-transparent p-0">
            {TABS.map((tab) => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="group relative h-9 items-end rounded-none border-b-2 border-transparent px-5 text-sm font-medium data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-foreground data-[state=active]:shadow-none">
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
          <div className="flex flex-auto flex-col overflow-hidden">
            {TABS.map((item) => (
              <TabsContent
                key={item.value}
                value={item.value}
                className="flex flex-1 flex-col overflow-hidden data-[state=inactive]:size-0 data-[state=inactive]:flex-none">
                {item.value === "FILE" ? (
                  <FileUploader
                    fileList={fileList}
                    setFileList={setFileList}
                    isUploading={isUploading}
                    multiple={true}
                    accept={".pdf,.docx,.txt,.csv"}
                    className="mb-4 h-full"
                  />
                ) : item.value === "URL" ? (
                  <URLUploader
                    urls={urls}
                    setUrls={setUrls}
                    form={urlForm}
                    isUploading={isUploading}
                  />
                ) : item.value === "TEXT" ? (
                  <TextUploader
                    listText={listText}
                    setListText={setListText}
                    form={textForm}
                    isUploading={isUploading}
                  />
                ) : (
                  <KnowledgeUploadedTab
                    onClose={() => {}}
                    selectedKnowledge={tempSelectedKnowledge}
                    onSelectedKnowledgeChange={setTempSelectedKnowledge}
                    isSaving={isSaving}
                    setIsSaving={setIsSaving}
                    lastDeletedId={lastDeletedId}
                    isStaffContext={isStaffContext}
                  />
                )}
              </TabsContent>
            ))}
          </div>
        </Tabs>
        <DialogFooter className="flex-none gap-2 border-t border-border p-6">
          <Button
            variant="outline"
            onClick={onClose}
            className="min-w-[80px]"
            disabled={isUploading || isSaving}>
            {t("common.cancel")}
          </Button>
          <Button
            className="min-w-[80px]"
            onClick={handleSubmit}
            loading={isUploading || (tab === "LIST" && isSaving)}
            disabled={isUploading || isSaving}>
            {t(tab === "LIST" ? "common.save" : "common.upload")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

---
description: 
globs: 
alwaysApply: false
---
# API Integration Standards

## Query Key Structure
1. Key Organization:
   ```typescript
   // entityKeys.ts
   export const entityKeys = {
     // Base key for the entity
     all: () => ["entityName"] as const,
     
     // List operations
     lists: () => [...entityKeys.all(), "list"] as const,
     list: (params: IGetEntityParams) => [...entityKeys.lists(), params] as const,
     
     // Detail operations
     details: () => [...entityKeys.all(), "detail"] as const,
     detail: (id: string) => [...entityKeys.details(), id] as const,
     
     // Additional entity-specific operations
     specificFeature: () => [...entityKeys.all(), "specificFeature"] as const,
   };

   // Shared query keys
   export const QUERY_KEYS = {
     ENTITY_ONE: ["entityOne"] as const,
     ENTITY_TWO: ["entityTwo"] as const,
     // ... other global keys
   } as const;
   ```

2. Parameter Interface:
   ```typescript
   export interface IGetEntityParams {
     page?: number;
     limit?: number;
     query?: string;
     [key: string]: unknown; // For additional filters
   }
   ```

## Hook Structure
1. List Query with Infinite Loading:
   ```typescript
   interface UseEntityOptions extends Partial<IGetEntityParams> {
     enabled?: boolean;
   }

   export function useEntities(options: UseEntityOptions = {}) {
     const { limit = 20, enabled = true, ...restOptions } = options;
     const queryClient = useQueryClient();
     
     const query = useInfiniteQuery({
       queryKey: entityKeys.list({ limit, ...restOptions }),
       queryFn: ({ pageParam = 0 }) => 
         entityApi.list({
           page: pageParam as number,
           limit,
           ...restOptions,
         }),
       getNextPageParam: (lastPage) => {
         const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
         if (lastPage.page < totalPages) {
           return lastPage.page + 1;
         }
         return undefined;
       },
       initialPageParam: 0,
       enabled,
     });

     const items = query.data?.pages.flatMap((page) => page.items) ?? [];
     const total = query.data?.pages[0]?.total ?? 0;

     return {
       ...query,
       items,
       total,
     };
   }
   ```

2. Detail Query:
   ```typescript
   export function useEntity(id: string) {
     return useQuery({
       queryKey: entityKeys.detail(id),
       queryFn: async () => {
         const response = await entityApi.getById(id);
         return response;
       },
       enabled: !!id,
     });
   }
   ```

3. Create Mutation:
   ```typescript
   interface UseAddEntityOptions {
     onSuccess?: (data: Entity) => void;
     onError?: (error: Error) => void;
   }

   export function useAddEntity(options: UseAddEntityOptions = {}) {
     const { onSuccess, onError } = options;
     const queryClient = useQueryClient();

     return useMutation<Entity, Error, CreateEntity>({
       mutationFn: async (data: CreateEntity) => {
         const response = await entityApi.create(data);
         return response;
       },
       onSuccess: (data) => {
         queryClient.invalidateQueries({ queryKey: entityKeys.lists() });
         onSuccess?.(data);
       },
       onError,
     });
   }
   ```

4. Update Mutation:
   ```typescript
   interface UpdateEntityPayload {
     id: string;
     data: Partial<Entity>;
   }

   export function useUpdateEntity(options: UseUpdateEntityOptions = {}) {
     const { onSuccess, onError } = options;
     const queryClient = useQueryClient();

     return useMutation({
       mutationFn: async ({ id, data }: UpdateEntityPayload) => {
         const response = await entityApi.update(id, data);
         return response;
       },
       onSuccess: (updatedEntity) => {
         // Update both list and detail cache
         queryClient.setQueryData<InfiniteData<ResponseList<Entity>>>(
           entityKeys.lists(),
           (oldData) => {
             if (!oldData) return oldData;
             const newPages = oldData.pages.map((page) => ({
               ...page,
               items: page.items.map((item) =>
                 item.id === updatedEntity.id ? updatedEntity : item
               ),
             }));
             return { ...oldData, pages: newPages };
           }
         );
         queryClient.setQueryData(
           entityKeys.detail(updatedEntity.id),
           updatedEntity
         );
         onSuccess?.(updatedEntity);
       },
       onError,
     });
   }
   ```

5. Delete Mutation:
   ```typescript
   export function useDeleteEntity(options: UseDeleteEntityOptions = {}) {
     const { onSuccess, onError } = options;
     const queryClient = useQueryClient();

     return useMutation({
       mutationFn: async (id: string) => {
         return entityApi.delete(id);
       },
       onSuccess: (_, deletedId) => {
         queryClient.setQueryData<InfiniteData<ResponseList<Entity>>>(
           entityKeys.lists(),
           (oldData) => {
             if (!oldData) return oldData;
             const newPages = oldData.pages.map((page) => ({
               ...page,
               items: page.items.filter((item) => item.id !== deletedId),
               total: page.total - 1,
             }));
             return { ...oldData, pages: newPages };
           }
         );
         onSuccess?.();
       },
       onError,
     });
   }
   ```

## Cache Management Best Practices
1. Invalidation:
   ```typescript
   // Invalidate all queries for an entity
   queryClient.invalidateQueries({ queryKey: entityKeys.all() });

   // Invalidate only list queries
   queryClient.invalidateQueries({ queryKey: entityKeys.lists() });

   // Invalidate specific detail
   queryClient.invalidateQueries({ queryKey: entityKeys.detail(id) });
   ```

2. Optimistic Updates:
   ```typescript
   // Example in update mutation
   {
     onMutate: async (newData) => {
       // Cancel outgoing fetches
       await queryClient.cancelQueries({ queryKey: entityKeys.detail(id) });

       // Save previous value
       const previousData = queryClient.getQueryData(entityKeys.detail(id));

       // Optimistically update
       queryClient.setQueryData(entityKeys.detail(id), newData);

       // Return context with previous value
       return { previousData };
     },
     onError: (err, newData, context) => {
       // Rollback on error
       queryClient.setQueryData(
         entityKeys.detail(id),
         context?.previousData
       );
     }
   }
   ```

## Error Handling
```typescript
// Global error handler
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
      onError: (error) => {
        // Handle global query errors
        toast.error("An error occurred while fetching data");
      },
    },
    mutations: {
      onError: (error) => {
        // Handle global mutation errors
        toast.error("An error occurred while updating data");
      },
    },
  },
});
```

## Response Types
```typescript
// Common response interface
interface ResponseList<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
}

// Entity type definition
interface Entity {
  id: string;
  // ... other fields
}

// Create/Update payload types
interface CreateEntity {
  // ... fields for creation
}

interface UpdateEntity {
  // ... fields for update
}
```

## Common Response Types
The following response types are defined in `src/lib/apis/types/common.ts` and should be used consistently across all API endpoints:

1. `ResponseList<T>` - For paginated data responses
2. `MessageResponse` - For simple message responses
3. `ResponseAxiosDetail<T>` - For detail responses with success flag
4. `SessionResponse` - For session-related responses

These types should be imported from the common types file rather than redefined in individual files.

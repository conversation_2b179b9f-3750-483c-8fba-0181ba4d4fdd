import { createContext, useContext } from "react";
import { DragHandleDots2Icon } from "@radix-ui/react-icons";
import { Table } from "@tanstack/react-table";
import { Column } from "@tanstack/table-core";
import { Reorder } from "framer-motion";
import { ArrowUpFromLine, Search } from "lucide-react";

import { ScrollArea } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";

import { useColumnView } from "../custom-table/hooks/use-column-view";

interface DataTableViewDialogProps<TData> {
  table: Table<TData>;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface SortableItemProps {
  value: string;
  children: React.ReactNode;
}

const SortableItem = ({ value, children }: SortableItemProps) => {
  return (
    <Reorder.Item value={value} className="w-full">
      <div className="flex items-center justify-between px-2 py-3">
        {children}
        <div className="flex items-center gap-1">
          <MoveToTopButton columnId={value} />
          <DragHandleDots2Icon className="size-5 cursor-grab text-muted-foreground" />
        </div>
      </div>
    </Reorder.Item>
  );
};

interface MoveToTopButtonProps {
  columnId: string;
}

const MoveToTopButton = ({ columnId }: MoveToTopButtonProps) => {
  const context = useContext(DataTableViewContext);
  if (!context) return null;

  const { moveColumnToTop } = context;

  return (
    <Button
      variant="ghost"
      size="icon"
      className="size-8 rounded-full hover:bg-muted"
      onClick={(e) => {
        e.stopPropagation();
        moveColumnToTop(columnId);
      }}>
      <ArrowUpFromLine className="size-4 text-muted-foreground" />
    </Button>
  );
};

interface DataTableViewContextType {
  moveColumnToTop: (columnId: string) => void;
}

const DataTableViewContext = createContext<DataTableViewContextType | null>(null);

export function DataTableViewDialog<TData>({
  table,
  open,
  onOpenChange,
}: DataTableViewDialogProps<TData>) {
  const {
    searchQuery,
    setSearchQuery,
    setColumnOrder,
    tempVisibility,
    displayColumnOrder,
    moveColumnToTop,
    handleToggleAll,
    handleToggleColumn,
    handleSave,
    handleCancel,
    specialStartColumns,
    specialEndColumns,
  } = useColumnView(table);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      handleCancel();
    }
    onOpenChange(open);
  };

  const handleSaveClick = () => {
    if (handleSave()) {
      onOpenChange(false);
    }
  };

  const columns = displayColumnOrder
    .map((id) => table.getColumn(id))
    .filter((column): column is Column<TData> => column !== undefined && column.getCanHide())
    .filter((column) => {
      if (!searchQuery) return true;
      const headerValue = column.columnDef.header;
      const headerText = typeof headerValue === "string" ? headerValue : column.id;
      return (
        headerText.toLowerCase().includes(searchQuery.toLowerCase()) ||
        column.id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    });

  const isAllVisible = columns.every((column) => tempVisibility[column.id] ?? true);

  return (
    <DataTableViewContext.Provider value={{ moveColumnToTop }}>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Display Customizer</DialogTitle>
            <DialogDescription>
              Customize the display of content columns according to your preferences.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search size={16} />}
            />
            <ScrollArea className="h-[320px]">
              <div className="flex items-center space-x-2 border-b px-2 py-3">
                <Checkbox
                  checked={isAllVisible}
                  onCheckedChange={(value) => handleToggleAll(!!value)}
                  id="all-columns"
                />
                <label htmlFor="all-columns" className="text-sm font-medium">
                  All
                </label>
              </div>
              <Reorder.Group
                axis="y"
                values={columns.map((column) => column.id)}
                onReorder={(newOrder) => {
                  const updatedOrder = [...displayColumnOrder];
                  newOrder.forEach((id, index) => {
                    const currentIndex = updatedOrder.indexOf(id);
                    if (currentIndex !== -1) {
                      updatedOrder.splice(currentIndex, 1);
                      updatedOrder.splice(index, 0, id);
                    }
                  });
                  setColumnOrder([...specialStartColumns, ...updatedOrder, ...specialEndColumns]);
                }}>
                {columns.map((column) => {
                  const headerValue = column.columnDef.header;
                  const headerText = typeof headerValue === "string" ? headerValue : column.id;

                  return (
                    <SortableItem key={column.id} value={column.id}>
                      <div className="flex flex-1 items-center space-x-2">
                        <Checkbox
                          checked={tempVisibility[column.id] ?? true}
                          onCheckedChange={(value) => handleToggleColumn(column.id, !!value)}
                          id={column.id}
                        />
                        <label htmlFor={column.id} className="text-sm font-medium">
                          {headerText}
                        </label>
                      </div>
                    </SortableItem>
                  );
                })}
              </Reorder.Group>
            </ScrollArea>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveClick}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DataTableViewContext.Provider>
  );
}

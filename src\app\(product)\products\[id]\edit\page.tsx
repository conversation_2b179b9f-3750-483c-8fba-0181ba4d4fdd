"use client";

import { notFound, useParams } from "next/navigation";

import { AddManual } from "@/features/products/components/AddManual/add-manual";
import { ProductDetailSkeleton } from "@/features/products/components/ProductDetails/product-detail-skeleton";
import { useProduct } from "@/features/products/hooks/product";

export default function EditProductPage() {
  const { id } = useParams();
  const { data: product, isLoading } = useProduct(id as string);

  if (isLoading) {
    return <ProductDetailSkeleton />;
  }

  if (!product) {
    notFound();
  }

  return <AddManual isEditing={true} productData={product} />;
}

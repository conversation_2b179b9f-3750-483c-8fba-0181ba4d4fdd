"use client";

import { useEffect, useState } from "react";
import { notFound, useParams, useRouter } from "next/navigation";
import { Banknote, Copy, Package2, PackageCheck, Printer, X } from "lucide-react";

import { AddOrder } from "@/features/orders/components/add_order";
import { Customer } from "@/features/orders/components/customer";
import { Note } from "@/features/orders/components/note";
import { Payment } from "@/features/orders/components/payment";
import { OrderDetailSkeleton } from "@/features/orders/components/skeleton/order_detail_skeleton";
import { useOrderDetail } from "@/features/orders/hooks/order";

import { Button } from "@/components/ui";

export default function DetailProductPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  return <OrderDetailContent id={id as string} />;
}

type ButtonAction =
  | "print"
  | "confirmOrder"
  | "cancelOrder"
  | "edit"
  | "copyOrder"
  | "pay"
  | "pack";

interface StatusToShowButton {
  DRAFT: ButtonAction[];
  PENDING: ButtonAction[];
  CONFIRMED: ButtonAction[];
  SUBMITTED: ButtonAction[];
  PATRIAL_PACKAGE: ButtonAction[];
  AWAIT_PACKING: ButtonAction[];
  IN_CANCEL: ButtonAction[];
  REJECT_CANCEL: ButtonAction[];
  CANCELLED: ButtonAction[];
  PACKING: ButtonAction[];
  PATRIAL_READY: ButtonAction[];
  READY: ButtonAction[];
  PATRIAL_SHIPPING: ButtonAction[];
  SHIPPING: ButtonAction[];
  PATRIAL_DELIVERED: ButtonAction[];
  DELIVERED: ButtonAction[];
  PATRIAL_RETURNING: ButtonAction[];
  RETURNING: ButtonAction[];
  PATRIAL_RETURNED: ButtonAction[];
  RETURNED: ButtonAction[];
  COMPLETED: ButtonAction[];
}

const statusToShowButton = {
  DRAFT: ["confirmOrder", "cancelOrder", "edit", "print", "copyOrder"], // draft -> show as 'pending'
  PENDING: ["pay", "edit", "print", "copyOrder"], // pending -> show as 'confirmed'
  CONFIRMED: ["pack", "edit", "print", "copyOrder"],
  SUBMITTED: ["pack", "edit", "print", "copyOrder"],
  PATRIAL_PACKAGE: ["edit", "pack", "print", "copyOrder"],
  AWAIT_PACKING: ["edit", "print", "copyOrder"],
  IN_CANCEL: ["edit", "print", "copyOrder"],
  REJECT_CANCEL: ["edit", "print", "copyOrder", "confirmOrder", "cancelOrder"],
  CANCELLED: ["print", "copyOrder"],
  PACKING: ["edit", "print", "copyOrder"],
  PATRIAL_READY: ["edit", "print", "copyOrder"],
  READY: ["edit", "print", "copyOrder"],
  PATRIAL_SHIPPING: ["edit", "print", "copyOrder"],
  SHIPPING: ["edit", "print", "copyOrder"],
  PATRIAL_DELIVERED: ["edit", "print", "copyOrder"],
  DELIVERED: ["edit", "print", "copyOrder"],
  PATRIAL_RETURNING: ["edit", "print", "copyOrder"],
  RETURNING: ["edit", "print", "copyOrder"],
  PATRIAL_RETURNED: ["edit", "print", "copyOrder"],
  RETURNED: ["edit", "print", "copyOrder"],
  COMPLETED: ["edit", "print", "copyOrder"],
} as StatusToShowButton;

function OrderDetailContent({ id }: { id: string }) {
  const { order, isLoading } = useOrderDetail(id);
  const router = useRouter();
  const [subtotalPrice, setSubtotalPrice] = useState(0);

  // Use sub_total from order data
  useEffect(() => {
    if (order?.sub_total) {
      setSubtotalPrice(order.sub_total);
    }
  }, [order]);

  if (isLoading) {
    return <OrderDetailSkeleton />;
  }
  if (!order) {
    notFound();
  }

  // Calculate total items - ensure we're getting numbers
  const totalItems = order.order_line_items.reduce((sum, item) => {
    const quantity =
      typeof item.quantity === "number" ? item.quantity : parseInt(item.quantity) || 0;
    return sum + quantity;
  }, 0);

  // Convert order line items to the format expected by AddOrder
  const orderItems = order.order_line_items.map((item) => ({
    variant_id: item.custom ? item.sku : item.variant_id || "",
    name: item.name,
    sku: item.sku,
    image: item.image_url || "",
    price: Number.parseFloat(item.unit_price),
    unit_price: Number.parseFloat(item.unit_price),
    sale_price: Number.parseFloat(item.sale_price || item.unit_price),
    quantity: Number(item.quantity),
    total: Number.parseFloat(item.sale_price || item.unit_price) * Number(item.quantity),
    custom: item.custom || false,
    note: item.note || "",
    discount: Number.parseFloat(item.discount || "0"),
    variant: {
      id: item.custom ? item.sku : item.variant_id || "",
      name: item.name,
      sku: item.sku,
      custom: item.custom || false,
      price: Number.parseFloat(item.unit_price),
      unit: {
        id: "default",
        name: "Default",
      },
      // Add other required fields for non-service items
      ...(item.custom
        ? {}
        : {
            images: item.image_url ? [{ url: item.image_url }] : [],
            inventories: [
              {
                location_id: "",
                available: 0,
                on_hand: 0,
                cost: 0,
                company_id: "",
              },
            ],
            prices: [
              {
                price: Number.parseFloat(item.unit_price),
                price_group: {
                  id: "",
                  name: "",
                },
              },
            ],
          }),
    },
  }));

  return (
    <div className="flex min-h-screen w-full flex-col">
      <div className="min-h-screen">
        <div className="flex flex-col p-4">
          <div className="flex-1">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {/* Left Column - Order Items and Payment */}
              <div className="space-y-4 md:col-span-2">
                {/* AddOrder component */}
                <div className="rounded-lg border bg-card p-4">
                  <AddOrder
                    readOnly={true}
                    initialOrderItems={orderItems}
                    showAddServiceButton={false}
                    disabled={true}
                    orderNumber={order?.number}
                    orderStatus={order?.status}
                  />
                </div>

                {/* Payment Section */}
                <div className="rounded-lg border bg-card p-4">
                  <Payment
                    subtotal={subtotalPrice}
                    totalItems={totalItems}
                    disabled={true}
                    readOnly={true}
                    isPaid={order?.payment_status === "PAID"}
                    discount={order?.discount}
                    tax={order?.tax}
                    shippingFee={order?.shipping_fee}
                    voucherTotal={order?.sub_voucher_total}
                    total={order?.total}
                    otherFees={order?.other_fees}
                  />
                </div>
              </div>

              {/* Right Column - Order Details */}
              <div className="order-first space-y-4 md:order-last">
                {/* Customer Information */}
                <div className="rounded-lg border bg-card p-4">
                  <Customer
                    initialSelectedCustomer={order?.customer}
                    initialShippingAddress={order?.shipping_address}
                    initialBillingAddress={order?.billing_address}
                    disabled={true}
                    readOnly={true}
                  />
                </div>

                {/* Note Section with Additional Information */}
                <div className="rounded-lg border bg-card p-4">
                  <Note
                    initialNote={order?.note || ""}
                    initialTags={order?.tags ? [order.tags] : []}
                    disabled={true}
                    readOnly={true}
                    additionalInfo={{
                      staff: order?.staff,
                      branch: order?.location,
                      updated_at: order?.updated_at,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="sticky bottom-0 w-full gap-2 border-t border-border bg-card p-4 pb-[24px] text-foreground">
        <div className="flex flex-wrap gap-2">
          <div className="flex gap-2">
            {order?.status &&
              statusToShowButton[order.status as keyof StatusToShowButton].includes("print") && (
                <Button variant="outline" size="md" className="flex items-center gap-2">
                  <Printer size={16} />
                  <span>Print</span>
                </Button>
              )}
            {order?.status &&
              statusToShowButton[order.status as keyof StatusToShowButton].includes(
                "copyOrder"
              ) && (
                <Button variant="outline" size="md" className="flex items-center gap-2">
                  <Copy size={16} />
                  <span>Copy order</span>
                </Button>
              )}
          </div>

          <div className="flex grow flex-wrap justify-end gap-2">
            <div className="flex flex-wrap justify-end gap-2 border-r-2 border-border px-2">
              <div className="flex">
                {order?.status &&
                  statusToShowButton[order.status as keyof StatusToShowButton].includes(
                    "cancelOrder"
                  ) && (
                    <Button
                      size="md"
                      className="flex items-center gap-2 border border-border-destructive-50 bg-card text-destructive hover:bg-bg-destructive-10">
                      <X size={16} />
                      <span>Cancel order</span>
                    </Button>
                  )}
                {order?.status &&
                  statusToShowButton[order.status as keyof StatusToShowButton].includes("pack") && (
                    <Button variant="outline" size="md" className="flex items-center gap-2">
                      <Package2 size={16} />
                      <span>Pack</span>
                    </Button>
                  )}
              </div>

              <div className="flex gap-2">
                {order?.status &&
                  statusToShowButton[order.status as keyof StatusToShowButton].includes(
                    "confirmOrder"
                  ) && (
                    <Button variant="outline" size="md" className="flex items-center gap-2">
                      <PackageCheck size={16} />
                      <span>Confirm order</span>
                    </Button>
                  )}

                {order?.status &&
                  statusToShowButton[order.status as keyof StatusToShowButton].includes("pay") && (
                    <Button variant="outline" size="md" className="flex items-center gap-2">
                      <Banknote size={16} />
                      <span>Pay</span>
                    </Button>
                  )}
              </div>
            </div>

            <div>
              {order?.status &&
                statusToShowButton[order.status as keyof StatusToShowButton].includes("edit") && (
                  <Button
                    variant="default"
                    size="md"
                    className="ml-auto"
                    onClick={() => router.push(`/orders/${order.id}/edit`)}>
                    Edit
                  </Button>
                )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

import { publicPaths } from "./constants/paths";
import { IS_ONE_X_BOT } from "./utils/constants/common";

export function middleware(request: NextRequest) {
  const isAuthenticated = request.cookies.has("auth_user");
  const { pathname } = request.nextUrl;
  // Bỏ qua các static files và api routes
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api") ||
    pathname.startsWith("/images") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  const onexbotsPaths = [
    "/department",
    "/conversation",
    "/knowledge",
    "/staff",
    "/task",
    "/chat-bots",
    "/virtual-staff",
  ];
  const isOneXBotsPath = onexbotsPaths.some((path) => pathname.includes(path));

  if (!IS_ONE_X_BOT && isOneXBotsPath) {
    return NextResponse.redirect(new URL("/not-found", request.url));
  }

  // Check if the current path matches any public path pattern
  const isPublicPath = Object.values(publicPaths).some((path) => {
    // Convert route pattern (like "/virtual-staff/:id") to regex
    const pattern = path.replace(/:[^/]+/g, "[^/]+");
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(pathname);
  });

  // Auth logic
  // Nếu đang ở public path và đã auth -> redirect to home
  if (isPublicPath && isAuthenticated) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  // Nếu không ở public path và chưa auth -> redirect to login
  if (!isPublicPath && !isAuthenticated) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: "/((?!api|_next/static|_next/image|favicon.ico).*)",
};

"use client";

import { Suspense, useMemo } from "react";
import { useTranslation } from "react-i18next";

import { useSyncRecords } from "@/features/integration/hooks/sync-record";
import { columns } from "@/features/integration/SyncRecords/column";
import {
  RecordTypeOptions,
  SyncRecordStatusOptions,
} from "@/features/integration/SyncRecords/filter-options";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";

export default function SyncRecordPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SyncRecordContent />
    </Suspense>
  );
}

function SyncRecordContent() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const { syncRecords, total, isLoading, isFetching, refetch } = useSyncRecords({
    limit: Number(getInitialParams.limit),
    ...getInitialParams,
  });

  const isTableLoading = isLoading || isFetching;

  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "syncRecords",
      searchPlaceHolder: t("pages.syncRecords.filters.search.placeholder"),
      numberOfFilters: 2,
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "record_type",
          type: EFilterType.SELECT_BOX,
          title: t("pages.syncRecords.filters.recordType"),
          defaultValue: getInitialParams["channel"],
          dataOption: RecordTypeOptions,
        },
        {
          id: "status",
          type: EFilterType.SELECT_BOX,
          title: t("pages.syncRecords.filters.status"),
          defaultValue: getInitialParams["status"],
          dataOption: SyncRecordStatusOptions,
        },
        {
          id: "connection_id",
          type: EFilterType.SELECT_BOX,
          title: t("pages.syncRecords.filters.connectionId"),
          defaultValue: getInitialParams["connection_id"],
          remote: true,
          pathUrlLoad: "/connections/list",
        },
        {
          id: "fetch_event_id",
          type: EFilterType.SELECT_BOX,
          title: t("pages.syncRecords.filters.fetchEventId"),
          remote: true,
          pathUrlLoad: "/flows/connections/list_fetch_events",
          defaultValue: getInitialParams["fetch_event_id"],
        },

        {
          id: "created_at",
          type: EFilterType.DATE,
          title: t("pages.products.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: EFilterType.DATE,
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);
  const groupButtonConfig: GroupButtonProps = {
    onRefresh: () => refetch(),
  };
  return (
    <TableCard>
      <TableHeader
        title={t("pages.syncRecords.title")}
        filterType="syncRecords"
        data={syncRecords || []}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(t)}
        data={syncRecords || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
      />
    </TableCard>
  );
}

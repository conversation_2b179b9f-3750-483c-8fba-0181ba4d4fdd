import { Fragment, ReactNode, useState } from "react";
import Link from "next/link";
import { Eye, Loader2, MoreHorizontal, <PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import toNavUrl from "@/utils/helpers/nav-url-formater";

interface Action {
  type: "view" | "edit" | "delete" | "updateAttribute" | string;
  title?: string;
  onClick?: () => Promise<void> | void;
  href?: string;
  hide?: boolean;
  customIcon?: ReactNode;
  shortcut?: string;
  loading?: boolean;
}

interface ActionGroupProps {
  actions: Action[];
}

const defaultIcons = {
  view: <Eye className="size-4" />,
  edit: <Pencil className="size-4" />,
  delete: <Trash className="size-4" />,
  updateAttribute: <Pencil className="size-4" />,
};

const defaultShortcuts = {
  delete: "⌘⌫",
};

export default function ActionGroup({ actions }: ActionGroupProps) {
  const { t } = useTranslation();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);

  const handleOpenChange = (open: boolean) => {
    if (!alertOpen) {
      setDropdownOpen(open);
    }
  };

  const handleAlertClose = () => {
    setAlertOpen(false);
    setDropdownOpen(false);
  };

  const getIcon = (action: Action) => {
    if (action.customIcon) return action.customIcon;
    return defaultIcons[action.type as keyof typeof defaultIcons];
  };

  const getShortcut = (action: Action) => {
    if (action.shortcut) return action.shortcut;
    return defaultShortcuts[action.type as keyof typeof defaultShortcuts];
  };
  const handleImplementAction = async (action: Action) => {
    try {
      console.log(action.loading);
      await action.onClick?.();
    } finally {
      handleAlertClose();
    }
  };

  const renderActionItem = (action: Action, index: number) => {
    if (action.hide) return null;

    const icon = getIcon(action);
    const title = action.title ? t(action.title) : action.type ? t(`common.${action.type}`) : "";
    const isDelete = action.type === "delete";
    const shortcut = getShortcut(action);
    const isLastItem = index === actions.length - 1;

    if (isDelete) {
      return (
        <Fragment key={index}>
          {isLastItem && <div className="my-1 border-t" />}
          <DropdownMenuItem
            className="cursor-pointer gap-2 text-destructive"
            onSelect={(e) => {
              e.preventDefault();
              setAlertOpen(true);
            }}>
            {action.loading ? <Loader2 className="size-4 animate-spin" /> : icon}
            <span className="text-nowrap ">{title}</span>
            {shortcut && <DropdownMenuShortcut>{shortcut}</DropdownMenuShortcut>}
          </DropdownMenuItem>
          <ConfirmDialog
            open={alertOpen}
            onOpenChange={setAlertOpen}
            title={t("common.areYouSure")}
            description={t("common.deleteProductConfirmation")}
            cancelText={t("common.cancel")}
            confirmText={action.loading ? t("common.deleting") : t("common.delete")}
            variant="destructive"
            loading={action.loading}
            isControl
            onCancel={handleAlertClose}
            onConfirm={async () => await handleImplementAction(action)}
          />
          {!isLastItem && <DropdownMenuSeparator />}
        </Fragment>
      );
    }

    return (
      <Fragment key={index}>
        {isLastItem && <div className="my-1 border-t" />}
        {action.href ? (
          <Link href={toNavUrl(action.href)} passHref>
            <DropdownMenuItem
              asChild
              className="cursor-pointer gap-2"
              onSelect={() => setDropdownOpen(false)}>
              <div className="flex items-center gap-2 hover:text-destructive">
                {action.loading ? <Loader2 className="size-4 animate-spin" /> : icon}
                <span>{title}</span>
                {shortcut && <DropdownMenuShortcut>{shortcut}</DropdownMenuShortcut>}
              </div>
            </DropdownMenuItem>
          </Link>
        ) : (
          <DropdownMenuItem
            onClick={() => {
              if (action.onClick) {
                action.onClick();
              }
              setDropdownOpen(false);
            }}
            className="cursor-pointer gap-2"
            disabled={action.loading}>
            {action.loading ? <Loader2 className="size-4 animate-spin" /> : icon}
            <span>{title}</span>
            {shortcut && <DropdownMenuShortcut>{shortcut}</DropdownMenuShortcut>}
          </DropdownMenuItem>
        )}
        {!isLastItem && <DropdownMenuSeparator />}
      </Fragment>
    );
  };

  return (
    <DropdownMenu open={dropdownOpen} onOpenChange={handleOpenChange} modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="size-8 p-0">
          <MoreHorizontal className="size-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-fit">
        {actions.map(renderActionItem)}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

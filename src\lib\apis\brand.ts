import { Brand, BrandPayload } from "@/features/brand/hooks/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

export const brandApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Brand>>(ENDPOINTS.BRAND.LIST, { params });
  },
  create: async (brand: BrandPayload) => {
    return await privateApi.post<Brand>(ENDPOINTS.BRAND.CREATE, brand);
  },
  update: async (id: string, brand: Brand) => {
    return await privateApi.put<Brand>(ENDPOINTS.BRAND.UPDATE.replace(":id", id), brand);
  },
  delete: async (id: string) => {
    const url = ENDPOINTS.BRAND.DELETE.replace(":id", id);
    return await privateApi.delete(url);
  },
};

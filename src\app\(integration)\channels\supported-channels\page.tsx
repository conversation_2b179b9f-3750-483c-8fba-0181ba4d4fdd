"use client";

import { t } from "i18next";

import { ChannelCard, ChannelCardSkeleton } from "@/features/integration/Channel/channel-card";
import { CHANNEL_TYPE_OPTIONS } from "@/features/integration/Channel/type-options";
import { useSupportedChannels } from "@/features/integration/hooks/supported-channel";

import { Card, CardTitle, Input } from "@/components/ui";
import { CardContent } from "@/components/ui/card";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

export default function SupportedChannelsPage() {
  const {
    filteredChannels,
    selectedTypes,
    searchQuery,
    isLoading,
    handleTypeChange,
    handleSearchChange,
    handleInstall,
    handleConfigure,
  } = useSupportedChannels();

  return (
    <div className="flex size-full overflow-hidden p-4 pt-0">
      <Card className="flex w-full flex-col space-y-6 p-6">
        <CardTitle className="flex-none text-base font-medium">
          {t("pages.supportedChannels.title")}
        </CardTitle>

        <div className="flex-none space-y-4">
          <Input
            className="w-full bg-background text-muted-foreground"
            placeholder={t("pages.supportedChannels.filters.search.placeholder")}
            value={searchQuery}
            onChange={handleSearchChange}
            disabled={isLoading}
          />

          <ToggleGroup
            type="multiple"
            value={selectedTypes}
            onValueChange={handleTypeChange}
            className="flex flex-none flex-wrap gap-2"
            disabled={isLoading}>
            {CHANNEL_TYPE_OPTIONS.map((type) => (
              <ToggleGroupItem key={type.id} value={type.id} variant="outline" className="text-sm">
                {type.name}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </div>

        <div className="flex-auto overflow-y-auto">
          <CardContent className="grid grid-cols-1 gap-4 p-0 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {isLoading ? (
              <>
                {[...Array(8)].map((_, index) => (
                  <ChannelCardSkeleton key={index} />
                ))}
              </>
            ) : filteredChannels?.length ? (
              filteredChannels.map((channel) => (
                <ChannelCard
                  key={channel.key}
                  channel={channel}
                  onInstall={handleInstall}
                  onConfigure={handleConfigure}
                />
              ))
            ) : (
              <div className="col-span-full text-center text-muted-foreground">No Result</div>
            )}
          </CardContent>
        </div>
      </Card>
    </div>
  );
}

/** @type {import('prettier').Config} */
module.exports = {
  endOfLine: "lf",
  semi: true,
  bracketSameLine: true,
  printWidth: 100,
  singleQuote: false,
  trailingComma: "es5",
  tabWidth: 2,
  arrowParens: "always",
  plugins: ["@ianvs/prettier-plugin-sort-imports"],
  importOrder: [
    "^(react/(.*)$)|^(react$)",
    "^(next/(.*)$)|^(next$)",
    "<THIRD_PARTY_MODULES>",
    "",
    "^@/features/(.*)$",
    "",
    "^@/assets$",
    "^@/assets/(.*)$",
    "^@/components$",
    "^@/components/(.*)$",
    "^@/config$",
    "^@/config/(.*)$",
    "^@/constants$",
    "^@/constants/(.*)$",
    "^@/hooks$",
    "^@/hooks/(.*)$",
    "^@/i18n$",
    "^@/i18n/(.*)$",
    "^@/lib$",
    "^@/lib/(.*)$",
    "^@/store$",
    "^@/store/(.*)$",
    "^@/utils$",
    "^@/utils/(.*)$",
    "",
    "^[./]",
  ],
  importOrderParserPlugins: ["typescript", "jsx", "decorators-legacy"],
};

import { fireEvent, render, screen, waitFor } from "@testing-library/react";

import "@testing-library/jest-dom";

import { I18nextProvider } from "react-i18next";

import i18n from "@/i18n";

import { Login } from "./Login";

jest.mock("@/assets/images/vcare-text.svg", () => "mock-svg");
jest.mock("@/assets/images/logo.png", () => "mock-logo");
jest.mock("@/assets/images/bg.png", () => "mock-bg");
jest.mock("@/assets/images/logo-bg.png", () => "mock-logo-bg");

// ✅ Mock Next.js Router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(), // Mock navigation
    replace: jest.fn(),
    prefetch: jest.fn(),
    pathname: "/login",
  }),
}));

describe("Component", () => {
  it("renders correctly", () => {
    const { container } = render(
      <I18nextProvider i18n={i18n}>
        <Login />
      </I18nextProvider>
    );

    // Find the input by its name attribute
    const nameInput = container.querySelector('input[name="email"]');
    expect(nameInput).toBeInTheDocument();

    const passwordInput = container.querySelector('input[name="password"]');
    expect(passwordInput).toBeInTheDocument();

    expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
  });
});

describe("Validate", () => {
  it("shows validation errors for empty fields", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <Login />
    //   </Provider>
    // );

    fireEvent.click(screen.getByRole("button", { name: "Login" }));

    await waitFor(() => {
      expect(screen.getByText("Username or email is required")).toBeInTheDocument();
      expect(screen.getByText("Password is required")).toBeInTheDocument();
    });
  });
});

// npm test -- features/auth/components/Login/Login.test.tsx

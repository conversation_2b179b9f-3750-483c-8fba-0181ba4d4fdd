import { fireEvent, screen, waitFor } from "@testing-library/react";

import "@testing-library/jest-dom";

import { toast } from "sonner";

jest.mock("@/assets/images/vcare-text.svg", () => "mock-svg");
jest.mock("@/assets/images/logo.png", () => "mock-logo");
jest.mock("@/assets/images/bg.png", () => "mock-bg");

// ✅ Mock Next.js Router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(), // Mock navigation
    replace: jest.fn(),
    prefetch: jest.fn(),
    pathname: "verify-confirmation-code",
  }),

  useSearchParams: () => ({
    get: (key: string) => {
      if (key === "username") return "testuser"; // Mock query param
      return null;
    },
  }),
}));

jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

describe("Component", () => {
  it("render correctly", () => {
    // const { container } = render(
    //   <I18nextProvider i18n={i18n}>
    //     <VerifyCode />
    //   </I18nextProvider>
    // );

    expect(screen.getByPlaceholderText("Enter the code")).toBeInTheDocument();
    expect(screen.getByText("Verify code")).toBeInTheDocument();
  });
});

// auth.verificationCodeRequired
describe("Validate", () => {
  it("validate for empty code", async () => {
    // const { container } = render(<VerifyCode />);

    fireEvent.click(screen.getByRole("button", { name: "Verify code" }));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("auth.verificationCodeRequired");
    });
  });
});

// npm test -- features/auth/components/VerifyCode/VerifyCode.test.tsx

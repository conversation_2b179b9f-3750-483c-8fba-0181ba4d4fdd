export type LogoType = "lightLogo" | "darkLogo" | "lightIcon" | "darkIcon" | "favicon";

interface LogoConfig {
  type: LogoType;
  titleKey: string;
  descriptionKey: string;
}

export const logoConfigs: LogoConfig[] = [
  {
    type: "lightLogo",
    titleKey: "pages.settings.logo.lightModeLogo",
    descriptionKey: "pages.settings.logo.lightModeLogoDescription",
  },
  {
    type: "darkLogo",
    titleKey: "pages.settings.logo.darkModeLogo",
    descriptionKey: "pages.settings.logo.darkModeLogoDescription",
  },
  {
    type: "lightIcon",
    titleKey: "pages.settings.logo.lightModeIcon",
    descriptionKey: "pages.settings.logo.lightModeIconDescription",
  },
  {
    type: "darkIcon",
    titleKey: "pages.settings.logo.darkModeIcon",
    descriptionKey: "pages.settings.logo.darkModeIconDescription",
  },
  {
    type: "favicon",
    titleKey: "pages.settings.logo.favicon",
    descriptionKey: "pages.settings.logo.faviconDescription",
  },
];

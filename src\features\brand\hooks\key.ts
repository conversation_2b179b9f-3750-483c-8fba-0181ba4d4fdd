export interface IGetBrandParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}

export const brandKeys = {
  all: () => ["brand"] as const,
  lists: () => [...brandKeys.all(), "list"] as const,
  list: (params: IGetBrandParams) => [...brandKeys.lists(), params] as const,
  create: () => [...brandKeys.all(), "create"] as const,
  update: (id: string) => [...brandKeys.all(), "update", id] as const,
  delete: (id: string) => [...brandKeys.all(), "delete", id] as const,
};

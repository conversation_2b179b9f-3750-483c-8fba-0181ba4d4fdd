import {
  BotMessageSquare,
  ClipboardList,
  CopyCheck,
  GalleryVerticalEnd,
  Landmark,
  LayoutDashboard,
  LibraryBig,
  MessagesSquare,
  Package,
  School,
  Users,
  Webhook,
} from "lucide-react";

import { authProtectedPaths } from "@/constants/paths";
import { IS_ONE_X_BOT } from "@/utils/constants/common";

export const data = {
  branches: [
    {
      name: "All",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "<PERSON>",
      logo: School,
      plan: "Startup",
    },
    {
      name: "<PERSON> <PERSON><PERSON>",
      logo: Landmark,
      plan: "Free",
    },
  ],
  overview: [
    {
      title: "nav.dashboard",
      url: authProtectedPaths.DASHBOARD,
      icon: LayoutDashboard,
    },
  ],
  navMain: [
    {
      title: "nav.product",
      icon: Package,
      items: [
        {
          title: "nav.productList",
          url: authProtectedPaths.PRODUCTS,
        },
        {
          title: "nav.variantsList",
          url: authProtectedPaths.VARIANTS,
        },
        {
          title: "nav.brandList",
          url: authProtectedPaths.BRANDS,
        },
        // {
        //   title: "nav.categoryList",
        //   url: authProtectedPaths.CATEGORIES,
        // },
      ],
    },
    {
      title: "nav.order",
      icon: ClipboardList,
      items: [
        {
          title: "nav.orderList",
          url: authProtectedPaths.ORDERS,
        },
        // {
        //   title: "nav.orderProcess",
        //   url: authProtectedPaths.ORDER_PROCESS,
        // },
        // {
        //   title: "nav.returnOrderList",
        //   url: authProtectedPaths.RETURN_ORDERS,
        // },
        // {
        //   title: "nav.packageList",
        //   url: authProtectedPaths.PACKAGES,
        // },
      ],
    },
    // {
    //   title: "nav.purchaseOrder",
    //   icon: ReceiptText,
    //   items: [
    //     {
    //       title: "nav.purchaseOrderList",
    //       url: authProtectedPaths.PURCHASE_ORDERS,
    //     },
    //     {
    //       title: "nav.supplierList",
    //       url: authProtectedPaths.SUPPLIERS,
    //     },
    //   ],
    // },
    // {
    //   title: "nav.logistics",
    //   icon: Truck,
    //   items: [
    //     {
    //       title: "nav.shippingProviderList",
    //       url: authProtectedPaths.SHIPPING_PROVIDERS,
    //     },
    //   ],
    // },
    {
      title: "nav.customers",
      icon: Users,
      items: [
        // {
        //   title: "nav.customerDashboard",
        //   url: authProtectedPaths.CUSTOMER_DASHBOARD,
        // },
        {
          title: "nav.customerList",
          url: authProtectedPaths.CUSTOMERS,
        },
        // {
        //   title: "nav.customerGroupList",
        //   url: authProtectedPaths.CUSTOMER_GROUPS,
        // },
        // {
        //   title: "nav.loyaltyProgram",
        //   url: authProtectedPaths.LOYALTY_PROGRAMS,
        // },
        // {
        //   title: "nav.rewardProgram",
        //   url: authProtectedPaths.REWARD_PROGRAMS,
        // },
      ],
    },
    // {
    //   title: "nav.inventory",
    //   icon: Warehouse,
    //   items: [
    //     {
    //       title: "nav.locationList",
    //       url: authProtectedPaths.LOCATIONS,
    //     },
    //     {
    //       title: "nav.inventoryList",
    //       url: authProtectedPaths.INVENTORY_ITEMS,
    //     },
    //     {
    //       title: "nav.stockAdjustmentList",
    //       url: authProtectedPaths.STOCK_ADJUSTMENTS,
    //     },
    //     {
    //       title: "nav.stockRelocateList",
    //       url: authProtectedPaths.STOCK_RELOCATES,
    //     },
    //   ],
    // },
    // {
    //   title: "nav.promotion",
    //   icon: TicketPercent,
    //   items: [
    //     {
    //       title: "nav.discountList",
    //       url: authProtectedPaths.DISCOUNTS,
    //     },
    //     {
    //       title: "nav.voucherList",
    //       url: authProtectedPaths.VOUCHERS,
    //     },
    //   ],
    // },
    // {
    //   title: "nav.import",
    //   icon: Contact,
    //   items: [
    //     {
    //       title: "nav.importList",
    //       url: authProtectedPaths.IMPORTS,
    //     },
    //     {
    //       title: "nav.recordList",
    //       url: authProtectedPaths.RECORDS,
    //     },
    //   ],
    // },
  ],
  operations: [
    // {
    //   title: "nav.report",
    //   icon: ChartColumnBig,
    //   items: [
    //     {
    //       title: "nav.productReport",
    //       url: authProtectedPaths.REPORT_PRODUCTS,
    //     },
    //   ],
    // },
    // {
    //   title: "nav.finance",
    //   icon: HandCoins,
    //   items: [
    //     {
    //       title: "nav.account",
    //       url: authProtectedPaths.ACCOUNTS,
    //     },
    //     {
    //       title: "nav.paymentMethod",
    //       url: authProtectedPaths.PAYMENT_METHODS,
    //     },
    //     {
    //       title: "nav.transaction",
    //       url: authProtectedPaths.TRANSACTIONS,
    //     },
    //   ],
    // },
    {
      title: "nav.integration",
      icon: Webhook,
      items: [
        {
          title: "nav.fetchEvent",
          url: authProtectedPaths.FETCH_EVENTS,
        },
        {
          title: "nav.syncRecords",
          url: authProtectedPaths.SYNC_RECORDS,
        },
        {
          title: "nav.channel",
          url: authProtectedPaths.CHANNELS,
        },
        {
          title: "nav.productMapping",
          url: authProtectedPaths.PRODUCT_MAPPING,
        },
      ],
    },
    // {
    //   title: "nav.website",
    //   icon: Contact,
    //   items: [
    //     {
    //       title: "nav.blogCategory",
    //       url: authProtectedPaths.BLOG_CATEGORIES,
    //     },
    //     {
    //       title: "nav.blogList",
    //       url: authProtectedPaths.BLOGS,
    //     },
    //   ],
    // },
    // {
    //   title: "nav.notification",
    //   icon: Megaphone,
    //   items: [
    //     {
    //       title: "nav.notificationList",
    //       url: authProtectedPaths.NOTIFICATIONS,
    //     },
    //   ],
    // },
    // {
    //   title: "nav.loyaltyApp",
    //   icon: Star,
    //   url: authProtectedPaths.LOYALTY_APP,
    // },
    // {
    //   title: "nav.pos",
    //   icon: Contact,
    //   items: [
    //     {
    //       title: "nav.terminalList",
    //       url: authProtectedPaths.TERMINALS,
    //     },
    //     {
    //       title: "nav.shiftList",
    //       url: authProtectedPaths.SHIFTS,
    //     },
    //     {
    //       title: "nav.pos",
    //       url: authProtectedPaths.POS,
    //     },
    //     {
    //       title: "nav.posFnB",
    //       url: authProtectedPaths.POS_FNB,
    //     },
    //   ],
    // },
    // {
    //   title: "nav.administration",
    //   icon: UserCog,
    //   url: authProtectedPaths.SETTINGS,
    // },
  ],
  ...(IS_ONE_X_BOT && {
    virtual_staff: [
      {
        title: "nav.staff",
        icon: BotMessageSquare,
        items: [
          {
            title: "nav.department",
            url: authProtectedPaths.DEPARTMENT,
          },
          {
            title: "nav.staffList",
            url: authProtectedPaths.STAFF,
          },
        ],
      },
      {
        title: "nav.knowledge",
        icon: LibraryBig,
        url: authProtectedPaths.KNOWLEDGE,
      },
      {
        title: "nav.task",
        icon: CopyCheck,
        url: authProtectedPaths.TASK,
      },
      {
        title: "nav.conversation",
        icon: MessagesSquare,
        url: authProtectedPaths.CONVERSATION,
      },
    ],
  }),
};

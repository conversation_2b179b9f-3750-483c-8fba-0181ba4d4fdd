// Define the XBot interface for TypeScript
interface XBotInterface {
  open: () => void;
  close: () => void;
  toggle: () => void;
  sendMessage: (message: string) => void;
}
// Extend Window interface
export {}; // Convert file to module
declare global {
  interface Window {
    XBot: XBotInterface;
  }
}

// This script will be injected into external websites to enable the chat embed
(function () {
  // Get the bot ID from the script data attribute
  const scripts = document.querySelectorAll("script[data-bot-id]");
  const currentScript = scripts[scripts.length - 1] as HTMLScriptElement;
  const botId = currentScript.getAttribute("data-bot-id");
  const staffImage = currentScript.getAttribute("data-image");

  if (!botId) {
    console.error("XBot: Bot ID is required");
    return;
  }

  // Get the container element
  const container = document.getElementById("xbot-container");
  if (!container) {
    console.error('XBot: Container with ID "xbot-container" not found');
    return;
  }

  // Add animation keyframes to document
  const addAnimationStyles = () => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes xbot-bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-20px); }
        60% { transform: translateY(-10px); }
      }
      
      @keyframes xbot-pulse {
        0% { transform: scale(1); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); }
        50% { transform: scale(1.1); box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3); }
        100% { transform: scale(1); box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); }
      }
       @keyframes xbot-dropdown-in {
        0% { 
          opacity: 0; 
          transform: translateY(-20px) scale(0.9);
          transform-origin: bottom right;
        }
        100% { 
          opacity: 1; 
          transform: translateY(0) scale(1);
          transform-origin: bottom right;
        }
      }
      
      @keyframes xbot-dropdown-out {
        0% { 
          opacity: 1; 
          transform: translateY(0) scale(1);
          transform-origin: bottom right;
        }
        100% { 
          opacity: 0; 
          transform: translateY(-20px) scale(0.9);
          transform-origin: bottom right;
        }
      }
      
      @keyframes xbot-fade-in {
        0% { opacity: 0; transform: translateY(20px); }
        100% { opacity: 1; transform: translateY(0); }
      }
      
      @keyframes xbot-fade-out {
        0% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(20px); }
      }
      
      @keyframes xbot-wiggle {
        0% { transform: rotate(0deg); }
        25% { transform: rotate(-10deg); }
        50% { transform: rotate(0deg); }
        75% { transform: rotate(10deg); }
        100% { transform: rotate(0deg); }
      }
      
      @keyframes xbot-click {
        0% { transform: scale(1); }
        50% { transform: scale(0.9); }
        100% { transform: scale(1); }
      }
      
      .xbot-animate-bounce {
        animation: xbot-bounce 1s ease;
      }
      
      .xbot-animate-pulse {
        animation: xbot-pulse 1.5s infinite;
      }
      
      .xbot-animate-fade-in {
        animation: xbot-fade-in 0.5s ease-out;
      }
      
      .xbot-animate-fade-out {
        animation: xbot-fade-out 0.5s ease-in forwards;
      }
      
      .xbot-animate-wiggle {
        animation: xbot-wiggle 0.5s ease-in-out;
      }
      
      .xbot-animate-click {
        animation: xbot-click 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
      
      .xbot-chat-toggle {
        transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1), box-shadow 0.4s ease;
        will-change: transform, box-shadow;
      }
      
      .xbot-chat-toggle:hover {
        transform: scale(1.15);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25);
      }
      
      .xbot-chat-toggle:active {
        transform: scale(0.95);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        transition: transform 0.1s ease, box-shadow 0.1s ease;
      }
      
      .xbot-icon-chat {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    `;
    document.head.appendChild(style);
  };

  // Create the chat frame
  const createChatFrame = () => {
    const chatFrame = document.createElement("div");
    chatFrame.id = `xbot-chat-frame-${botId}`;
    chatFrame.className = "xbot-embedded-chat xbot-animate-dropdown-in";
    chatFrame.style.display = "none";
    chatFrame.style.position = "fixed";
    chatFrame.style.bottom = "80px";
    chatFrame.style.right = "20px";
    chatFrame.style.width = "350px";
    chatFrame.style.height = "500px";
    chatFrame.style.borderRadius = "10px";
    chatFrame.style.boxShadow = "0 5px 40px rgba(0, 0, 0, 0.16)";
    chatFrame.style.zIndex = "9999";
    chatFrame.style.overflow = "hidden";
    chatFrame.style.transition = "box-shadow 0.3s ease";

    // Create close button for the frame
    const closeButton = document.createElement("button");
    closeButton.className = "xbot-frame-close";
    closeButton.style.position = "absolute";
    closeButton.style.top = "20px";
    closeButton.style.right = "20px";
    closeButton.style.width = "28px";
    closeButton.style.height = "28px";
    closeButton.style.borderRadius = "50%";
    closeButton.style.backgroundColor = "rgba(0, 0, 0, 0.1)";
    closeButton.style.border = "none";
    closeButton.style.cursor = "pointer";
    closeButton.style.display = "flex";
    closeButton.style.alignItems = "center";
    closeButton.style.justifyContent = "center";
    closeButton.style.zIndex = "10000";
    closeButton.innerHTML = "−";
    closeButton.style.fontSize = "24px";
    closeButton.style.color = "white";
    closeButton.style.transition = "background-color 0.2s";

    closeButton.addEventListener("mouseover", () => {
      closeButton.style.backgroundColor = "rgba(0, 0, 0, 0.3)";
    });

    closeButton.addEventListener("mouseout", () => {
      closeButton.style.backgroundColor = "rgba(0, 0, 0, 0.1)";
    });

    closeButton.addEventListener("click", () => {
      // Fade out animation
      chatFrame.classList.remove("xbot-animate-dropdown-in");
      chatFrame.classList.add("xbot-animate-dropdown-out");

      // Toggle icon back to chat icon
      toggleButton.classList.remove("open");

      // Wait for animation to complete before hiding
      setTimeout(() => {
        chatFrame.style.display = "none";
        chatFrame.classList.remove("xbot-animate-fade-out");
      }, 500);
    });

    // Create the iframe
    const iframe = document.createElement("iframe");
    iframe.src = `${currentScript.src.split("/xbot-embed.js")[0]}/virtual-staff/${botId}?embedded=true`;
    iframe.style.width = "100%";
    iframe.style.height = "100%";
    iframe.style.border = "none";

    chatFrame.appendChild(closeButton);
    chatFrame.appendChild(iframe);
    return chatFrame;
  };

  // Create hello message box above the toggle button
  const createHelloBox = () => {
    const helloBox = document.createElement("div");
    helloBox.id = `xbot-hello-box-${botId}`;
    helloBox.className = "xbot-hello-box xbot-animate-fade-in";
    helloBox.style.position = "fixed";
    helloBox.style.bottom = "90px";
    helloBox.style.right = "20px";
    helloBox.style.padding = "10px 15px";
    helloBox.style.backgroundColor = "#222";
    helloBox.style.color = "white";
    helloBox.style.borderRadius = "10px";
    helloBox.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
    helloBox.style.zIndex = "9998";
    helloBox.style.maxWidth = "200px";
    helloBox.style.fontFamily = "Arial, sans-serif";
    helloBox.style.fontSize = "14px";
    helloBox.style.opacity = "0";
    helloBox.style.transition = "opacity 0.5s ease-out";
    helloBox.innerText = "Hello my dear!";

    // Add message bubble triangle
    const triangle = document.createElement("div");
    triangle.style.position = "absolute";
    triangle.style.bottom = "-8px";
    triangle.style.right = "20px";
    triangle.style.width = "0";
    triangle.style.height = "0";
    triangle.style.borderLeft = "8px solid transparent";
    triangle.style.borderRight = "8px solid transparent";
    triangle.style.borderTop = "8px solid #222";

    helloBox.appendChild(triangle);

    // Fade in the hello box after a short delay
    setTimeout(() => {
      helloBox.style.opacity = "1";
    }, 1000);

    return helloBox;
  };

  // Create the toggle button
  const createToggleButton = () => {
    const toggleButton = document.createElement("button");
    toggleButton.id = `xbot-toggle-${botId}`;
    toggleButton.className = "xbot-chat-toggle";
    toggleButton.style.position = "fixed";
    toggleButton.style.bottom = "20px";
    toggleButton.style.right = "20px";
    toggleButton.style.width = "56px";
    toggleButton.style.height = "56px";
    toggleButton.style.borderRadius = "100%";
    toggleButton.style.backgroundColor = "transparent";
    toggleButton.style.color = "white";
    toggleButton.style.border = "1px solid #f97316";
    toggleButton.style.cursor = "pointer";
    toggleButton.style.zIndex = "9999";
    toggleButton.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
    toggleButton.style.display = "flex";
    toggleButton.style.alignItems = "center";
    toggleButton.style.justifyContent = "center";
    toggleButton.style.padding = "2px";
    toggleButton.style.overflow = "hidden";
    toggleButton.style.opacity = "0";

    // Create chat icon (image)
    const chatIcon = document.createElement("div");
    chatIcon.className = "xbot-icon-chat";

    // Add image to chat icon
    const img = document.createElement("img");
    // Use staff image if available, otherwise use default staff icon
    img.src = staffImage || `${currentScript.src.split("/xbot-embed.js")[0]}/staff-icon.png`;
    img.alt = "Chat";
    img.style.width = "100%";
    img.style.height = "100%";
    img.style.objectFit = "cover";

    // Add icon to button
    chatIcon.appendChild(img);
    toggleButton.appendChild(chatIcon);

    // Fade in the button initially without using animation class
    setTimeout(() => {
      toggleButton.style.transition = "opacity 0.5s ease-out";
      toggleButton.style.opacity = "1";

      // Add initial bounce animation after fade in
      setTimeout(() => {
        toggleButton.classList.add("xbot-animate-bounce");
        setTimeout(() => {
          toggleButton.classList.remove("xbot-animate-bounce");
          toggleButton.classList.add("xbot-animate-pulse");
        }, 1000);
      }, 500);
    }, 100);

    // Set up wiggle animation every 3 seconds
    setInterval(() => {
      // Only wiggle if the chat is not open
      const chatFrame = document.getElementById(`xbot-chat-frame-${botId}`);
      if (chatFrame && chatFrame.style.display !== "block") {
        // Remove and re-add the wiggle class to trigger the animation
        toggleButton.classList.remove("xbot-animate-wiggle");
        void toggleButton.offsetWidth; // Force reflow
        toggleButton.classList.add("xbot-animate-wiggle");
      }
    }, 3000);

    return toggleButton;
  };

  // Inject the elements
  addAnimationStyles();
  const chatFrame = createChatFrame();
  const toggleButton = createToggleButton();
  const helloBox = createHelloBox();

  container.appendChild(chatFrame);
  container.appendChild(helloBox);
  container.appendChild(toggleButton);

  // Add toggle functionality
  toggleButton.addEventListener("click", () => {
    // Play click animation
    toggleButton.classList.add("xbot-animate-click");
    setTimeout(() => {
      toggleButton.classList.remove("xbot-animate-click");
    }, 300);

    // Remove pulse and wiggle animations when clicked
    toggleButton.classList.remove("xbot-animate-pulse");
    toggleButton.classList.remove("xbot-animate-wiggle");

    const isVisible = chatFrame.style.display === "block";

    // Hide hello box when chat is opened
    helloBox.classList.remove("xbot-animate-fade-in");
    helloBox.classList.add("xbot-animate-fade-out");
    setTimeout(() => {
      helloBox.style.display = "none";
      helloBox.classList.remove("xbot-animate-fade-out");
    }, 500);

    if (isVisible) {
      // Fade out animation
      chatFrame.classList.remove("xbot-animate-fade-in");
      chatFrame.classList.add("xbot-animate-fade-out");

      // Wait for animation to complete before hiding
      setTimeout(() => {
        chatFrame.style.display = "none";
        chatFrame.classList.remove("xbot-animate-fade-out");
      }, 500); // Match the animation duration
    } else {
      // Show and fade in
      chatFrame.classList.remove("xbot-animate-fade-out");
      chatFrame.classList.add("xbot-animate-fade-in");
      chatFrame.style.display = "block";
    }
  });

  // Add window messaging capability
  window.addEventListener("message", (event) => {
    // Verify origin for security
    if (event.origin !== window.location.origin) {
      return;
    }

    if (event.data && event.data.type === "XBOT_CLOSE") {
      // Fade out animation
      chatFrame.classList.remove("xbot-animate-fade-in");
      chatFrame.classList.add("xbot-animate-fade-out");

      // Toggle icon back to chat icon
      toggleButton.classList.remove("open");

      // Wait for animation to complete before hiding
      setTimeout(() => {
        chatFrame.style.display = "none";
        chatFrame.classList.remove("xbot-animate-fade-out");
      }, 500);
    }
  });

  // Expose the API
  (window as any).XBot = {
    open: () => {
      // Stop wiggle and pulse animations
      toggleButton.classList.remove("xbot-animate-wiggle");
      toggleButton.classList.remove("xbot-animate-pulse");

      // Hide hello box
      helloBox.classList.remove("xbot-animate-fade-in");
      helloBox.classList.add("xbot-animate-fade-out");
      setTimeout(() => {
        helloBox.style.display = "none";
        helloBox.classList.remove("xbot-animate-fade-out");
      }, 500);

      // Fade in animation for chat only
      chatFrame.classList.remove("xbot-animate-fade-out");
      chatFrame.classList.add("xbot-animate-fade-in");
      chatFrame.style.display = "block";

      // Toggle icon to X
      toggleButton.classList.add("open");
    },
    close: () => {
      // Fade out animation
      chatFrame.classList.add("xbot-animate-fade-out");

      // Toggle icon back to chat icon
      toggleButton.classList.remove("open");

      // Wait for animation to complete before hiding
      setTimeout(() => {
        chatFrame.style.display = "none";
        chatFrame.classList.remove("xbot-animate-fade-out");
      }, 500);
    },
    toggle: () => {
      // Stop wiggle and pulse animations
      toggleButton.classList.remove("xbot-animate-wiggle");
      toggleButton.classList.remove("xbot-animate-pulse");

      // Hide hello box
      helloBox.classList.remove("xbot-animate-fade-in");
      helloBox.classList.add("xbot-animate-fade-out");
      setTimeout(() => {
        helloBox.style.display = "none";
        helloBox.classList.remove("xbot-animate-fade-out");
      }, 500);

      const isVisible = chatFrame.style.display === "block";

      if (isVisible) {
        // Fade out animation
        chatFrame.classList.remove("xbot-animate-fade-in");
        chatFrame.classList.add("xbot-animate-fade-out");

        // Toggle icon back to chat icon
        toggleButton.classList.remove("open");

        // Wait for animation to complete before hiding
        setTimeout(() => {
          chatFrame.style.display = "none";
          chatFrame.classList.remove("xbot-animate-fade-out");
        }, 500);
      } else {
        // Fade in animation for chat only
        chatFrame.classList.remove("xbot-animate-fade-out");
        chatFrame.classList.add("xbot-animate-fade-in");
        chatFrame.style.display = "block";

        // Toggle icon to X
        toggleButton.classList.add("open");
      }
    },
    sendMessage: (message: string) => {
      const iframeEl = chatFrame.querySelector("iframe");
      if (iframeEl && iframeEl.contentWindow) {
        iframeEl.contentWindow.postMessage({ type: "XBOT_MESSAGE", content: message }, "*");
      }
    },
  };
})();

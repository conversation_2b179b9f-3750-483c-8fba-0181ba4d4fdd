"use client";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Customer as CustomerType } from "@/features/customer/hooks/customer";
import { AddOrder, AddOrderRef } from "@/features/orders/components/add_order";
import { useCreateOrder } from "@/features/orders/hooks/order";
import { OrderItem } from "@/features/orders/hooks/types";
import { orderFormSchema, OrderFormValues } from "@/features/orders/utils/validators/order";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Location } from "@/lib/apis/location";

import { Branch } from "../branch";
import { Customer } from "../customer";
import { Note } from "../note";
import { Payment } from "../payment";

interface AddOrderManualProps {
  order?: any;
  cloneOrderId?: string;
  userProfile?: {
    UserAttributes?: { sub?: string };
    Username?: string;
  };
  onSubmit?: (values: any) => void;
  selectedPriceGroup?: any;
  initialOrderItems?: OrderItem[];
  selectedLocationId?: string;
  onCancel?: () => void;
  isEditMode?: boolean;
  initialShippingAddress?: any;
  initialBillingAddress?: any;
  isUpdating?: boolean;
}

export function AddOrderManual({
  order,
  cloneOrderId,
  onSubmit,
  selectedPriceGroup,
  selectedLocationId,
  onCancel,
  initialOrderItems,
  isEditMode = false,
  initialShippingAddress,
  initialBillingAddress,
  isUpdating = false,
}: AddOrderManualProps) {
  const router = useRouter();
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    order?.location || null
  );
  const [orderTotal, setOrderTotal] = useState(order?.total || 0);
  const [orderItemsCount, setOrderItemsCount] = useState(0);
  const [currentCustomer, setCurrentCustomer] = useState<CustomerType | null>(
    order?.customer || null
  );
  const addOrderRef = useRef<AddOrderRef>(null);
  const [initialItemsLoaded, setInitialItemsLoaded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [currentShippingAddress, setCurrentShippingAddress] = useState<any>(initialShippingAddress);
  const [currentBillingAddress, setCurrentBillingAddress] = useState<any>(initialBillingAddress);
  const [parsedUser, setParsedUser] = useState<any>(null);

  // Use the create order mutation
  const { mutateAsync: createOrder, isPending } = useCreateOrder();
  const { t } = useTranslation();
  // Move localStorage access to useEffect
  useEffect(() => {
    const userInfo = localStorage.getItem("auth_user");
    setParsedUser(userInfo ? JSON.parse(userInfo) : null);
  }, []);

  // Initialize the form with default values
  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      customer: order?.customer || null,
      billing_address: order?.billing_address || null,
      location: order?.location || null,
      staff: {
        id: parsedUser?.UserAttributes?.sub,
        name: parsedUser?.Username,
      },
      source: order?.source || null,
      shipping_address: order?.shipping_address || null,
      order_line_items: (order?.order_line_items || []).map((item: any) => {
        if (cloneOrderId) {
          delete item.id;
        }
        return item;
      }),
      note: order?.note || "",
      tags: order?.tags || "",
      total: order?.total || 0,
      sub_total: order?.sub_total || 0,
      discount: order?.discount || 0,
      payments: cloneOrderId ? null : order?.payments || null,
      other_fees: order?.other_fees || null,
      shipping_fee: order?.shipping_fee || 0,
      tax: order?.tax || 0,
      discount_id: order?.discount_id || null,
      discount_by_customer_group: order?.discount_by_customer_group || null,
      redeem_point: order?.redeem_point || null,
      reward_program: order?.reward_program || null,
    },
  });
  // Update staff values when parsedUser changes
  useEffect(() => {
    if (parsedUser) {
      form.setValue("staff", {
        id: parsedUser?.UserAttributes?.sub,
        name: parsedUser?.Username,
      });
    }
  }, [parsedUser, form]);

  // Update orderItemsCount whenever items change
  const updateOrderItemsCount = () => {
    if (addOrderRef.current) {
      const count = addOrderRef.current.getOrderItemCount();
      setOrderItemsCount(count);
    }
  };

  // Handle updating order line items when they change in the AddOrder component
  const updateOrderLineItems = (orderItems: OrderItem[]) => {
    const orderLineItems = orderItems.map((item) => ({
      unit_price: item.price,
      sale_price: item.sale_price,
      sku: item.sku,
      name: item.name,
      variant_name: item.variant.name,
      location: selectedLocation,
      image_url: item.image,
      product_id: (item.variant as any).product_id || "",
      variant_id: item.variant_id,
      category: (item.variant as any).category || null,
      brand: (item.variant as any).brand || null,
      quantity: item.quantity,
      note: item.note,
      custom: item.custom,
    }));
    form.setValue("order_line_items", orderLineItems);

    // Update count whenever items change
    updateOrderItemsCount();
  };

  // Update form values when total changes
  useEffect(() => {
    form.setValue("total", orderTotal);
    form.setValue("sub_total", orderTotal);
  }, [orderTotal, form]);

  // Load initial order items into the AddOrder component
  useEffect(() => {
    if (addOrderRef.current && !initialItemsLoaded) {
      if (initialOrderItems && initialOrderItems.length > 0) {
        addOrderRef.current.setOrderItems(initialOrderItems);
        setInitialItemsLoaded(true);
      }
    }
  }, [
    addOrderRef,
    initialItemsLoaded,
    initialOrderItems,
    selectedLocation,
    selectedPriceGroup,
    selectedLocationId,
  ]);

  const handleBranchSelect = (branch: Location) => {
    setSelectedLocation(branch);
    form.setValue("location", branch);
  };

  const handleCustomerSelect = (customer: CustomerType | null) => {
    // Update current customer state
    // setCurrentCustomer(customer);
    // console.log("customer", customer);
    // console.log("Order", order);
    // if (order) {
    //   order.billing_address = customer?.selected_billing_address;
    //   order.shipping_address = customer?.selected_shipping_address;
    // }
    // console.log("order.billing_address", order?.billing_address);
    // console.log("order.shipping_address", order?.shipping_address);
    // form.setValue("billing_address", order?.billing_address);
    // form.setValue("shipping_address", selectedShippingAddress);
    // Set the complete customer object in the form
    form.setValue("customer", customer);
  };

  const handleNoteChange = (note: string) => {
    form.setValue("note", note);
  };

  const handleTagsChange = (tags: string[]) => {
    form.setValue("tags", tags.join(","));
  };

  // Add these handlers
  const handleShippingAddressChange = (address: any) => {
    setCurrentShippingAddress(address);
    form.setValue("shipping_address", address);
  };

  const handleBillingAddressChange = (address: any) => {
    setCurrentBillingAddress(address);
    form.setValue("billing_address", address);
  };

  // Validate the form data
  const validateFormData = (): boolean => {
    // Get current customer value from the form
    const customerValue = form.getValues("customer");

    // Check if customer has required fields
    if (!customerValue || !customerValue.id) {
      toast.error(t("pages.orders.selectCustomer"));
      return false;
    }

    // Check for order items
    const orderItems = form.getValues("order_line_items");
    if (!orderItems || orderItems.length === 0) {
      toast.error(t("pages.orders.addProductToOrderWarning"));
      return false;
    }

    const hasEmptyService = form
      .getValues("order_line_items")
      .some((item: any) => item.custom && !item.name?.trim());
    if (hasEmptyService) {
      // toast.error(t("pages.orders.emptyServiceName"));
      return false;
    }

    return true;
  };

  // Add function to check if there are any changes
  const hasChanges = () => {
    // Check if there are order items
    if (addOrderRef.current?.getOrderItemCount() && addOrderRef.current?.getOrderItemCount() > 0)
      return true;

    // Check if there's a selected customer
    if (currentCustomer) return true;

    // Check if there's a note or tags
    const note = form.getValues("note");
    const tags = form.getValues("tags");
    if (note || tags) return true;

    return false;
  };

  // Modify the handleCancel function
  const handleCancel = () => {
    if (hasChanges()) {
      setShowCancelDialog(true);
    } else {
      if (onCancel) {
        onCancel();
      } else {
        router.push("/orders");
      }
    }
  };

  // Add function to handle confirmed cancel
  const handleConfirmedCancel = () => {
    setShowCancelDialog(false);
    if (onCancel) {
      onCancel();
    } else {
      toast.info("Order creation cancelled");
      router.push("/orders");
    }
  };

  // Modify the handleSubmit function to properly handle service items
  const handleSubmit = async (values: OrderFormValues) => {
    setIsSubmitting(true);

    try {
      // If there's no custom onSubmit, use the default implementation
      if (onSubmit) {
        onSubmit(values);
        setIsSubmitting(false);
        return;
      }

      const payload = JSON.parse(JSON.stringify(values)); // Deep copy

      // Ensure customer object is included
      if (!payload.customer || !payload.customer.id) {
        toast.error("Required customer");
        setIsSubmitting(false);
        return;
      }

      // const defaultBillingAddress = payload.customer.addresses?.find(
      //   (addr: any) => addr.default_billing === true
      // );
      // const defaultShippingAddress = payload.customer.addresses?.find(
      //   (addr: any) => addr.default_shipping === true
      // );

      // payload.billing_address = defaultBillingAddress || payload.customer.billing_address || null;

      // // Set shipping address (prioritize default shipping address)
      // payload.shipping_address =
      //   defaultShippingAddress || payload.customer.shipping_address || null;

      // // Important: Always use the billing_address from customer directly
      // if (payload.customer.selected_billing_address) {
      //   payload.billing_address = payload.customer.selected_billing_address;
      // }
      // if (payload.customer.selected_shipping_address) {
      //   payload.shipping_address = payload.customer.selected_shipping_address;
      // }

      if (!selectedLocation) {
        toast.error("Required branch");
        setIsSubmitting(false);
        return;
      }

      // Ensure location only contains id and name fields
      payload.location = {
        id: selectedLocation.id,
        name: selectedLocation.name,
      };

      // Process order line items - ensure location objects are also simplified
      payload.order_line_items = (payload.order_line_items || [])
        .filter((item: any) => item.quantity > 0)
        .map((item: any) => {
          const { ...res } = item;

          // Ensure location in each order item only has id and name
          if (res.location) {
            res.location = {
              id: res.location.id,
              name: res.location.name,
            };
          }

          // Simplify brand object to only include id and name
          if (res.brand) {
            res.brand = {
              id: res.brand.id,
              name: res.brand.name,
            };
          }

          // Simplify category object to only include id and name
          if (res.category) {
            res.category = {
              id: res.category.id,
              name: res.category.name,
            };
          }

          // Check if this is a service item (no product_id or has custom flag)
          // and add the custom: true flag to service items
          if (!res.product_id || res.product_id === "") {
            res.custom = true;

            // Remove variant_id and variant_name from service items
            delete res.variant_id;
            delete res.variant_name;
            delete res.product_id;

            // Set image_url to null for service items
            res.image_url = null;
          }

          return res;
        });
      console.log("values", values);
      console.log("payload", payload);

      const result = await createOrder(payload);

      // // Reset form state or navigate
      if (result?.id) {
        toast.success(t("pages.orders.success"));
        // Navigate to the order detail page
        router.push(`/orders/${result.id}`);
      } else {
        toast.info(t("pages.orders.error"));
      }
    } catch (error: any) {
      toast.error(error.message || t("pages.orders.error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get the current values of note and tags for the Note component
  const currentNote = form.watch("note") || "";
  const currentTags = form.watch("tags") || "";
  const tagsArray = currentTags ? currentTags.split(",").filter(Boolean) : [];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <div>
          {/* Main Content */}
          <div className="flex min-h-screen flex-col p-4">
            <div className="flex-1">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Left Column - Order Items */}
                <div className="space-y-4 md:col-span-2">
                  {/* AddOrder component */}
                  <Card>
                    <CardContent className="p-4">
                      <FormField
                        control={form.control}
                        name="order_line_items"
                        render={() => (
                          <FormItem>
                            <AddOrder
                              ref={addOrderRef}
                              selectedLocationId={selectedLocation?.id}
                              onTotalChange={setOrderTotal}
                              onOrderItemsChange={updateOrderLineItems}
                              selectedPriceGroupId={selectedPriceGroup?.id}
                              disabled={isPending || isSubmitting}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>

                  {/* Payment Section */}
                  <Card className="mt-6">
                    <CardContent className="p-4">
                      <FormField
                        control={form.control}
                        name="total"
                        render={() => (
                          <FormItem>
                            <Payment
                              subtotal={orderTotal}
                              totalItems={orderItemsCount}
                              disabled={isPending || isSubmitting}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </div>

                {/* Right Column - Order Details */}
                <div className="space-y-4">
                  {/* Branch Selector */}
                  <Card>
                    <CardContent className="p-4">
                      <FormField
                        control={form.control}
                        name="location"
                        render={() => (
                          <FormItem>
                            <Branch
                              onBranchSelect={handleBranchSelect}
                              initialSelectedBranch={order?.location}
                              disabled={isPending || isSubmitting}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>

                  {/* Customer Information */}
                  <Card>
                    <CardContent className="p-4">
                      <FormField
                        control={form.control}
                        name="customer"
                        render={() => (
                          <FormItem>
                            <Customer
                              onCustomerSelect={handleCustomerSelect}
                              initialSelectedCustomer={currentCustomer}
                              disabled={isPending || isSubmitting}
                              initialShippingAddress={initialShippingAddress}
                              initialBillingAddress={initialBillingAddress}
                              onShippingAddressChange={handleShippingAddressChange}
                              onBillingAddressChange={handleBillingAddressChange}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>

                  {/* Note and Tags */}
                  <Card>
                    <CardContent className="p-4">
                      <FormField
                        control={form.control}
                        name="note"
                        render={() => (
                          <FormItem>
                            <Note
                              initialNote={currentNote}
                              initialTags={tagsArray}
                              onNoteChange={handleNoteChange}
                              onTagsChange={handleTagsChange}
                              disabled={isPending || isSubmitting}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
            <div className="h-20"></div>
          </div>
        </div>
        {/* Footer with action buttons */}
        <div className="fixed inset-x-0 bottom-0 border-t bg-card px-6 pb-6 pt-4 shadow-2xl">
          <div className="relative flex">
            {/* Right column - Buttons */}
            <div className="ml-auto flex items-center justify-end">
              <div className="flex gap-2">
                <Button
                  className="inline-flex w-24 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isPending || isSubmitting}>
                  {t("pages.orders.cancel")}
                </Button>
                <Button
                  className="inline-flex w-32 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                  type="button"
                  disabled={isPending || isSubmitting}
                  onClick={() => {
                    // Manually trigger validation before form submission
                    if (!validateFormData()) {
                      return;
                    }

                    // Get form values and submit manually
                    const values = form.getValues();
                    handleSubmit(values);
                  }}>
                  {isPending || isSubmitting || isUpdating ? (
                    <div className="flex items-center">
                      <Loader2 className="size-4 animate-spin" />
                    </div>
                  ) : isEditMode ? (
                    t("pages.orders.updateOrder")
                  ) : (
                    t("pages.orders.addOrder")
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Add Cancel Confirmation Dialog */}
        <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("pages.orders.confirm")}</DialogTitle>
              <DialogDescription>{t("pages.orders.cancelWarning")}</DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowCancelDialog(false)}>
                {t("pages.orders.cancel")}
              </Button>
              <Button type="button" variant="default" onClick={handleConfirmedCancel}>
                {t("pages.orders.confirmCancel")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </form>
    </Form>
  );
}

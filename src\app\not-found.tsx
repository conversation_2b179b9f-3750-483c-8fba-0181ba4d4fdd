"use client";

import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui/button";

export default function NotFound() {
  const { t } = useTranslation();
  const router = useRouter();
  return (
    <div className="flex min-h-screen items-center bg-background px-4 py-12 sm:px-6 md:px-8 lg:px-12 xl:px-16">
      <div className="w-full space-y-6 text-center">
        <div className="space-y-3">
          <h1 className="animate-bounce text-4xl font-bold tracking-tighter sm:text-5xl">404</h1>
          <p className="text-gray-500">
            <span>{t("error.notFound")}</span>
          </p>
        </div>
        <Button onClick={() => router.push("/login")}>
          <ArrowLeft />
          {t("error.backToHome")}
        </Button>
      </div>
    </div>
  );
}

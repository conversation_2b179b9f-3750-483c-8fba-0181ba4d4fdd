"use client";

import { useEffect, useRef, useState } from "react";
import { Loader2, Mail, MapPin, Pencil, Phone, Plus, Search, User, X } from "lucide-react";
import { useTranslation } from "react-i18next";

import {
  Customer as CustomerType,
  useCustomerDetails,
  useCustomers,
} from "@/features/customer/hooks/customer";
import { CustomerAddress } from "@/features/customer/hooks/type";
import { EditCustomerDialog } from "@/features/orders/components/dialog/edit_customer";
import { SelectAddressDialog } from "@/features/orders/components/dialog/select_address";
import { CustomerDetailSkeleton } from "@/features/orders/components/skeleton/customer_detail_skeleton";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import useDebounce from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

interface CustomerProps {
  onCustomerSelect?: (customer: CustomerType | null) => void;
  initialSelectedCustomer?: CustomerType | null;
  disabled?: boolean;
  readOnly?: boolean;
  initialShippingAddress?: any; // Allow any address object type
  initialBillingAddress?: any; // Allow any address object type
  onShippingAddressChange?: (address: CustomerAddress | null) => void;
  onBillingAddressChange?: (address: CustomerAddress | null) => void;
}

export function Customer({
  onCustomerSelect,
  initialSelectedCustomer,
  disabled,
  readOnly = false,
  initialShippingAddress,
  initialBillingAddress,
  onShippingAddressChange,
  onBillingAddressChange,
}: CustomerProps) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchOpen, setSearchOpen] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>(
    initialSelectedCustomer?.id || ""
  );
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const debouncedSearch = useDebounce(searchQuery, 300);
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectShippingAddressOpen, setSelectShippingAddressOpen] = useState(false);
  const [selectBillingAddressOpen, setSelectBillingAddressOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<CustomerAddress | null>(null);
  const [selectedShippingAddress, setSelectedShippingAddress] = useState<CustomerAddress | null>(
    initialShippingAddress || null
  );
  const [selectedBillingAddress, setSelectedBillingAddress] = useState<CustomerAddress | null>(
    initialBillingAddress || null
  );
  const [isCreateMode, setIsCreateMode] = useState(false);

  // Set initial customer if provided
  useEffect(() => {
    if (initialSelectedCustomer) {
      setSelectedCustomerId(initialSelectedCustomer.id);
      // Don't call onCustomerSelect here to avoid infinite loops
      // Just update local state
    }
  }, [initialSelectedCustomer]);

  // Set initial addresses if provided
  useEffect(() => {
    if (initialShippingAddress) {
      setSelectedShippingAddress(initialShippingAddress);
    }
    if (initialBillingAddress) {
      setSelectedBillingAddress(initialBillingAddress);
    }
  }, [initialShippingAddress, initialBillingAddress]);

  // Fetch selected customer details
  const {
    customer: customerDetails,
    isLoading: isLoadingDetails,
    refetch: refetchCustomerDetails,
  } = useCustomerDetails(selectedCustomerId);

  // Fetch customers list with search and pagination
  const { customers, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage, isFetching } =
    useCustomers({
      query: debouncedSearch,
      // enabled: searchOpen,
    });

  // Reset selected index when search query changes
  useEffect(() => {
    setSelectedIndex(-1);
  }, [searchQuery]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!searchOpen) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) => {
          const next = prev + 1;
          return next >= customers.length ? 0 : next;
        });
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) => {
          const next = prev - 1;
          return next < 0 ? customers.length - 1 : next;
        });
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < customers.length) {
          handleCustomerSelect(customers[selectedIndex]);
        }
        break;
      case "Escape":
        e.preventDefault();
        setSearchOpen(false);
        break;
    }
  };

  // Scroll selected item into view
  useEffect(() => {
    if (selectedIndex >= 0 && containerRef.current) {
      const container = containerRef.current;
      const selectedElement = container.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: "nearest",
          behavior: "smooth",
        });
      }
    }
  }, [selectedIndex]);

  useEffect(() => {
    // When customer details load, update the parent component
    if (customerDetails) {
      onCustomerSelect?.(customerDetails);
    }
  }, [customerDetails, onCustomerSelect]);
  // Also update the handleCustomerSelect function
  const handleCustomerSelect = (customer: CustomerType) => {
    setSelectedCustomerId(customer.id);
    // Don't call onCustomerSelect yet - wait for detailed data to load
    setSearchOpen(false);
    setSearchQuery("");
    // The useEffect hook above will call onCustomerSelect when customerDetails loads
  };

  const clearCustomer = () => {
    setSelectedCustomerId("");
    onCustomerSelect?.(null);
  };

  const getCustomerFullName = (customer: CustomerType) => {
    return `${customer.first_name} ${customer.last_name}`.trim();
  };

  const getFullAddress = (address: CustomerType["billing_address"]) => {
    if (!address) return "";
    const parts = [
      address.address1,
      address.ward,
      address.district,
      address.city,
      address.province,
    ].filter(Boolean);
    return parts.join(", ");
  };

  // Handle scroll for infinite loading
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    if (!hasNextPage || isFetchingNextPage) return;

    const { scrollTop, scrollHeight, clientHeight } = target;
    const threshold = 100;

    if (scrollHeight - (scrollTop + clientHeight) < threshold) {
      fetchNextPage();
    }
  };

  const handleShippingAddressSelect = (address: CustomerAddress) => {
    if (!customerDetails) return;
    // Store the selected address in state for immediate display
    setSelectedShippingAddress(address);
    onShippingAddressChange?.(address);
    // Deep clone the customer to avoid reference issues
    const updatedCustomer = JSON.parse(JSON.stringify(customerDetails));
    // Create a new customer object with the updated shipping address directly set
    const customerWithAddress = {
      ...updatedCustomer,
      selected_shipping_address: address, // This ensures the form will get the direct address reference
      selected_billing_address: selectedBillingAddress || customerDetails.billing_address,
    };

    // Update the customer with the new data
    onCustomerSelect?.(customerWithAddress);
    setSelectShippingAddressOpen(false);
  };

  const handleBillingAddressSelect = (address: CustomerAddress) => {
    if (!customerDetails) return;
    // Store the selected address in state for immediate display
    setSelectedBillingAddress(address);
    onBillingAddressChange?.(address);
    // Deep clone the customer to avoid reference issues
    const updatedCustomer = JSON.parse(JSON.stringify(customerDetails));
    // Create a new customer object with the updated billing address directly set
    const customerWithAddress = {
      ...updatedCustomer,
      selected_billing_address: address, // This ensures the form will get the direct address reference
      selected_shipping_address: selectedShippingAddress || customerDetails.shipping_address,
    };

    // Update the customer with the new data
    onCustomerSelect?.(customerWithAddress);
    setSelectBillingAddressOpen(false);
  };

  const handleEditAddress = (address: CustomerAddress) => {
    setEditingAddress(address);
    setEditDialogOpen(true);
  };

  // Find the shipping and billing addresses for display at render time
  const getShippingAddressForDisplay = () => {
    // First check if we have a selected address in state
    if (selectedShippingAddress) {
      return selectedShippingAddress;
    }
    // In read-only mode, prioritize the initialShippingAddress if provided
    if (readOnly && initialShippingAddress) {
      return initialShippingAddress;
    }
    // If initialSelectedCustomer has a direct shipping_address property, use that
    if (initialSelectedCustomer?.shipping_address) {
      return initialSelectedCustomer.shipping_address;
    }

    // Last resort: Check initialSelectedCustomer's addresses
    const initialDefaultShipping = initialSelectedCustomer?.addresses?.find(
      (addr) => addr.default_shipping
    );
    if (initialDefaultShipping) {
      return initialDefaultShipping;
    }

    if (initialShippingAddress) {
      return initialShippingAddress;
    }

    // If we have a direct shipping_address property on the customer object, use that
    if (customerDetails?.shipping_address) {
      return customerDetails.shipping_address;
    }

    // Check for default_shipping in the customer's addresses array
    const defaultShippingAddress = customerDetails?.addresses?.find(
      (addr) => addr.default_shipping
    );
    if (defaultShippingAddress) {
      return defaultShippingAddress;
    }

    // Nothing found
    return null;
  };

  const getBillingAddressForDisplay = () => {
    // First check if we have a selected address in state
    if (selectedBillingAddress) {
      return selectedBillingAddress;
    }
    // In read-only mode, prioritize the initialBillingAddress if provided
    if (readOnly && initialBillingAddress) {
      return initialBillingAddress;
    }

    // Last resort: Check initialSelectedCustomer's addresses
    const initialDefaultBilling = initialSelectedCustomer?.addresses?.find(
      (addr) => addr.default_billing
    );
    if (initialDefaultBilling) {
      return initialDefaultBilling;
    }

    if (initialBillingAddress) {
      return initialBillingAddress;
    }

    // Check for default_billing in the customer's addresses array
    const defaultBillingAddress = customerDetails?.addresses?.find((addr) => addr.default_billing);
    if (defaultBillingAddress) {
      return defaultBillingAddress;
    }

    // If initialSelectedCustomer has a direct billing_address property, use that
    if (initialSelectedCustomer?.billing_address) {
      return initialSelectedCustomer.billing_address;
    }

    // Nothing found
    return null;
  };

  // Add handler for create mode
  const handleAddCustomerClick = () => {
    setIsCreateMode(true);
    setEditDialogOpen(true);
    setSearchOpen(false);
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <h2 className={cn("text-sm font-medium", readOnly && "text-muted-foreground")}>
          {t("pages.orders.customer")}
        </h2>
        {!readOnly && customerDetails && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="size-6 p-0 text-primary"
            onClick={() => {
              setEditingAddress(null);
              setEditDialogOpen(true);
            }}
            disabled={disabled}>
            <Pencil className="size-4" />
          </Button>
        )}
      </div>

      {selectedCustomerId && isLoadingDetails ? (
        <CustomerDetailSkeleton />
      ) : customerDetails ? (
        <div className="pt-4">
          <div className="space-y-4">
            {/* Customer Name and Close Button */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User
                  className={`size-4 ${!readOnly ? "text-primary" : "text-muted-foreground"}`}
                />
                <span
                  className={`text-sm ${!readOnly ? "cursor-pointer text-primary hover:underline" : ""}`}
                  onClick={!readOnly ? () => setEditDialogOpen(true) : undefined}>
                  {getCustomerFullName(customerDetails)}
                </span>
              </div>
              {!readOnly && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="size-6 p-0"
                  onClick={clearCustomer}
                  disabled={disabled}>
                  <X className="size-4 text-destructive" />
                </Button>
              )}
            </div>

            {/* Points and Group Name */}
            <div className="space-y-2 rounded-md border border-dashed border-border p-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t("pages.orders.accumulatedPoints")}
                </span>
                <span className="text-sm">{customerDetails.loyal_customer?.point || "0"}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t("pages.orders.groupName")}</span>
                <Badge variant="yellow">{customerDetails.customer_group?.name || "—"}</Badge>
              </div>
            </div>
            {/* Phone Number - Always display */}
            <div className="flex items-center gap-2">
              <Phone className="size-4 text-muted-foreground" />
              <span className="truncate text-base">{customerDetails.phone || "—"}</span>
            </div>

            {/* Email - Always display */}
            <div className="flex items-center gap-2">
              <Mail className="size-4 text-muted-foreground" />
              <span className="truncate text-base">{customerDetails.email || "—"}</span>
            </div>

            {/* Shipping Address Section */}
            <div className="space-y-1 border-t pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MapPin className="size-4 text-muted-foreground" />
                  <span className="text-sm font-medium">{t("pages.orders.shippingAddress")}</span>
                </div>
                {!readOnly &&
                  (customerDetails?.addresses && customerDetails.addresses.length > 0 ? (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-6 p-0"
                      onClick={() => setSelectShippingAddressOpen(true)}
                      disabled={disabled}>
                      <Pencil className="size-4 text-primary" />
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-6 p-0"
                      onClick={() => setEditDialogOpen(true)}
                      disabled={disabled}>
                      <Plus className="size-4 text-primary" />
                    </Button>
                  ))}
              </div>
              <p className="pl-6 text-base text-muted-foreground">
                {selectedShippingAddress
                  ? getFullAddress(selectedShippingAddress)
                  : getShippingAddressForDisplay()
                    ? getFullAddress(getShippingAddressForDisplay())
                    : "—"}
              </p>
            </div>

            {/* Billing Address Section */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MapPin className="size-4 text-muted-foreground" />
                  <span className="text-sm font-medium">{t("pages.orders.billingAddress")}</span>
                </div>
                {!readOnly &&
                  (customerDetails?.addresses && customerDetails.addresses.length > 0 ? (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-6 p-0"
                      onClick={() => setSelectBillingAddressOpen(true)}
                      disabled={disabled}>
                      <Pencil className="size-4 text-primary" />
                    </Button>
                  ) : (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="size-6 p-0"
                      onClick={() => setEditDialogOpen(true)}
                      disabled={disabled}>
                      <Plus className="size-4 text-primary" />
                    </Button>
                  ))}
              </div>
              <p className="pl-6 text-base text-muted-foreground">
                {selectedBillingAddress
                  ? getFullAddress(selectedBillingAddress)
                  : getBillingAddressForDisplay()
                    ? getFullAddress(getBillingAddressForDisplay())
                    : "—"}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="relative py-4">
          <Input
            ref={searchInputRef}
            placeholder={t("pages.orders.searchCustomer")}
            leftIcon={<Search className="size-4" />}
            disabled={disabled}
            rightIcon={
              isLoading || isFetching ? (
                <Loader2 className="animate-spin" />
              ) : searchQuery ? (
                <Button
                  variant="ghost"
                  type="button"
                  className="hover:text-foreground"
                  disabled={disabled}
                  onClick={() => setSearchQuery("")}>
                  ✕
                </Button>
              ) : null
            }
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onClick={() => setSearchOpen(true)}
            onKeyDown={handleKeyDown}
          />

          {searchOpen && (
            <div className="relative z-50">
              <div className="fixed inset-0" onClick={() => setSearchOpen(false)} />
              <div
                ref={containerRef}
                onScroll={handleScroll}
                className="absolute inset-x-0 mt-1 max-h-[300px] overflow-y-auto rounded-md border bg-popover p-1 shadow-md">
                {/* Add customer button */}
                <div
                  className="cursor-pointer border-b px-4 py-2 hover:bg-accent"
                  onClick={handleAddCustomerClick}>
                  <div className="flex items-center gap-2">
                    <Plus className="size-4 text-primary" />
                    <span className="text-sm text-primary hover:underline">
                      {t("pages.orders.addCustomer")}
                    </span>
                  </div>
                </div>

                {/* Customer list */}
                {customers.length === 0 ? (
                  <div className="py-6 text-center text-sm text-muted-foreground">
                    {t("pages.orders.noCustomersFound")}
                  </div>
                ) : (
                  customers.map((customer, index) => (
                    <div
                      key={customer.id}
                      className={cn(
                        "flex cursor-pointer flex-col gap-1 rounded-sm p-2",
                        index === selectedIndex ? "bg-accent" : "hover:bg-accent"
                      )}
                      onClick={() => handleCustomerSelect(customer)}>
                      <div className="font-medium">{getCustomerFullName(customer)}</div>
                      {customer.phone && (
                        <div className="text-sm text-muted-foreground">{customer.phone}</div>
                      )}
                    </div>
                  ))
                )}

                {isFetchingNextPage && (
                  <div className="flex items-center justify-center p-2 text-sm text-muted-foreground">
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    {t("pages.orders.loadingMore")}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      <EditCustomerDialog
        customer={customerDetails}
        open={editDialogOpen}
        onOpenChange={(open) => {
          setEditDialogOpen(open);
          if (!open) {
            setIsCreateMode(false);
          }
        }}
        onSuccess={() => {
          if (customerDetails) {
            refetchCustomerDetails();
          }
          setEditingAddress(null);
          setIsCreateMode(false);
        }}
        initialEditAddress={editingAddress}
        focusFirstName={!editingAddress}
        mode={isCreateMode ? "create" : "edit"}
      />

      {customerDetails?.addresses && (
        <>
          <SelectAddressDialog
            open={selectShippingAddressOpen}
            onOpenChange={setSelectShippingAddressOpen}
            addresses={customerDetails.addresses}
            selectedAddressId={
              selectedShippingAddress && selectedShippingAddress.id
                ? String(selectedShippingAddress.id)
                : undefined
            }
            onSelect={handleShippingAddressSelect}
            onEdit={handleEditAddress}
            onAddAddress={() => {
              setEditingAddress(null);
              setEditDialogOpen(true);
            }}
            customer={customerDetails}
            title="Shipping Address"
            defaultShippingKey="default_shipping"
          />

          <SelectAddressDialog
            open={selectBillingAddressOpen}
            onOpenChange={setSelectBillingAddressOpen}
            addresses={customerDetails.addresses}
            selectedAddressId={
              selectedBillingAddress && selectedBillingAddress.id
                ? String(selectedBillingAddress.id)
                : undefined
            }
            onSelect={handleBillingAddressSelect}
            onEdit={handleEditAddress}
            onAddAddress={() => {
              setEditingAddress(null);
              setEditDialogOpen(true);
            }}
            customer={customerDetails}
            title="Billing Address"
            defaultShippingKey="default_billing"
          />
        </>
      )}
    </div>
  );
}

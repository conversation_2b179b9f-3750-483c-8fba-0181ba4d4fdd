import { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";

import { CustomImage } from "@/components/ui/image";

import { Variant } from "../../hooks/types";

interface VariantListProps {
  variants: Variant[];
  selectedVariant?: string;
  onSelectVariant?: (variantId: string) => void;
}

export const VariantList = ({ variants, selectedVariant, onSelectVariant }: VariantListProps) => {
  const { t } = useTranslation();
  const selectedVariantRef = useRef<HTMLDivElement>(null);

  // Scroll to the selected variant when it changes
  useEffect(() => {
    if (selectedVariant && selectedVariantRef.current) {
      selectedVariantRef.current.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [selectedVariant]);

  return (
    <div className="flex h-full flex-col">
      <h4 className="mb-4 font-medium">{t("pages.products.variants")}</h4>
      <div className="min-h-0 flex-1 bg-card">
        <div className="space-y-2">
          {variants.map((variant) => {
            const isSelected = selectedVariant === variant.id;
            return (
              <div
                key={variant.id}
                ref={isSelected ? selectedVariantRef : null}
                className={`flex cursor-pointer items-center rounded-lg p-2 transition-colors ${
                  isSelected ? "bg-primary/10 ring-2 ring-primary/50" : "bg-card hover:bg-muted/50"
                }`}
                onClick={() => onSelectVariant?.(variant.id)}>
                {/* Variant Image */}
                <div className="shrink-0">
                  <div className="relative size-14 rounded-lg">
                    <CustomImage
                      src={variant.images?.[0]?.url}
                      alt={variant.name}
                      fill
                      className="rounded-xl object-cover"
                      priority
                    />
                  </div>
                </div>

                {/* Variant Details */}
                <div className="min-w-0 flex-1 pl-4">
                  <div className="flex flex-col">
                    <span
                      className={`truncate text-base font-medium ${
                        isSelected ? "text-primary" : "text-foreground"
                      }`}
                      title={variant.name}>
                      {variant.name}
                    </span>
                    <span className="truncate text-sm text-muted-foreground">
                      {t("pages.products.sku")}: {variant.sku}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

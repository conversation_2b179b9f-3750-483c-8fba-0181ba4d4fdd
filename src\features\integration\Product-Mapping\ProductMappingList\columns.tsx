import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { Row } from "@tanstack/react-table";
import { <PERSON><PERSON><PERSON><PERSON>gle, LinkIcon, Unlink } from "lucide-react";

import {
  mapApiStatusToEnum,
  MappingProduct,
  MappingProductStatus,
} from "@/features/integration/Product-Mapping/hooks/types";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Badge, BadgeProps } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { LoadingDotPulse } from "@/components/ui/loading-dot-pulse";

import { useProductDetail, useProductMapping } from "../hooks/product-mapping";
import { ProductSyncSelect } from "../ProductSyncSelect/dialog/product-sync-select";

export const getColumns = (t: any, destinationName: string): CustomColumn<MappingProduct>[] => {
  return [
    {
      id: "source_product",
      header: t("pages.productMappingList.headers.product", { product: "Shopify" }),
      cell: ({ row }) => (
        <div className="flex max-w-[200px] items-center  gap-4  truncate">
          <ImageColumn
            src={row.original.standard_source_data?.images?.[0]?.url || ""}
            alt={row.original.standard_source_data?.name || "--"}
            width={0}
            iszoomable={false}
            height={0}
            sizes="100vw"
            className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
          />
          <div className="flex flex-auto flex-col justify-between gap-1 truncate ">
            <TextColumn
              text={
                row.original.standard_source_data?.name ||
                row.original.standard_source_data?.title ||
                "--"
              }
              className="font-medium"
            />
            <TextColumn
              text={`Inventory:
            ${
              row.original.standard_source_data?.variants?.reduce(
                (sum, variant) => sum + (variant.inventories?.available || 0),
                0
              ) || 0
            }`}
              className="truncate text-sm text-muted-foreground"
            />
          </div>
        </div>
      ),
    },
    {
      id: "destination_product",
      header: t("pages.productMappingList.headers.product", { product: destinationName }),
      cell: ({ row }) =>
        row.original.mapping_status === MappingProductStatus.MAP ||
        row.original.mapping_status === MappingProductStatus.SYNC ? (
          <div className="flex max-w-[200px] items-center gap-4 truncate">
            <ImageColumn
              src={row.original.standard_destination_data?.images?.[0]?.url || ""}
              alt={row.original.standard_destination_data?.name || "--"}
              width={0}
              iszoomable={false}
              height={0}
              sizes="100vw"
              className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
            />
            <div className="flex flex-auto flex-col justify-between gap-1 truncate ">
              <TextColumn
                text={
                  row.original.standard_destination_data?.name ||
                  row.original.standard_destination_data?.title ||
                  row.original.standard_destination_data?.id ||
                  "--"
                }
                className="font-medium"
              />
              <TextColumn
                text={`Inventory:
            ${
              row.original.standard_destination_data?.variants?.reduce(
                (sum, variant) => sum + (variant.inventories?.available || 0),
                0
              ) || 0
            }`}
                className="truncate text-sm text-muted-foreground"
              />
            </div>
          </div>
        ) : (
          <TextColumn
            className="text-sm font-medium leading-5 text-muted-foreground"
            text={t("pages.productMappingList.nomap", { destination: destinationName })}
          />
        ),
    },
    {
      id: "price",
      header: t("pages.productMappingList.headers.price"),
      hidden: true,
      cell: ({ row }) => <TextColumn text={`$${row.original.price?.toFixed(2) || "--"}`} />,
    },
    {
      id: "synced_at",
      header: t("pages.productMappingList.headers.last_synced"),
      cell: ({ row }) => <DateColumn date={row.original.last_sync || "--"} />,
    },
    {
      id: "mapping_status",
      header: t("pages.productMappingList.headers.status"),
      cell: ({ row }) => {
        // Convert API status (could be uppercase) to enum value
        const apiStatus = row.original.mapping_status;
        let status: MappingProductStatus;

        // Handle both string and enum types
        if (typeof apiStatus === "string") {
          status = mapApiStatusToEnum(apiStatus);
        } else {
          status = apiStatus;
        }

        const statusColors: Record<MappingProductStatus, string> = {
          [MappingProductStatus.SYNCED]: "sematic_success",
          [MappingProductStatus.MAPPED]: "sematic_info",
          [MappingProductStatus.UNMAPPED]: "sematic_default",
          [MappingProductStatus.ERROR]: "sematic_error",
          [MappingProductStatus.MAP]: "sematic_info",
          [MappingProductStatus.SYNC]: "sematic_info",
        };

        return (
          <Badge className="border-none" variant={statusColors[status] as BadgeProps["variant"]}>
            {t(
              `pages.productMappingList.status.${
                status === MappingProductStatus.SYNCED
                  ? "synced"
                  : status === MappingProductStatus.MAPPED
                    ? "mapped"
                    : status === MappingProductStatus.UNMAPPED
                      ? "unmapped"
                      : "error"
              }`
            )}
          </Badge>
        );
      },
    },
    {
      id: "map-actions",
      header: t("pages.productMappingList.headers.actions"),
      cell: ({ row }) => <MapActionsCell row={row} t={t} />,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }: { row: Row<MappingProduct> }) => <ActionCell row={row} />,
    },
  ];
};

const MapActionsCell = ({ row, t }: { row: Row<MappingProduct>; t: any }) => {
  // Convert API status (could be uppercase) to enum value
  const apiStatus = row.original.mapping_status;
  let status: MappingProductStatus;

  // Handle both string and enum types
  if (typeof apiStatus === "string") {
    status = mapApiStatusToEnum(apiStatus);
  } else {
    status = apiStatus;
  }

  const router = useRouter();
  const { isDialogOpen, isUnmapping, handleMapOpen, handleMapClose, handleMap, handleUnmap } =
    useProductMapping(row.original.id, row.original.connection_id);

  // Create adapter function to match the expected prop type
  const handleMapAdapter = async (destinationProductId: string) => {
    await handleMap({
      destinationProductId,
      mappedVariants: {},
      isExtraDestinationMapping: false,
    });
  };

  // Navigate to product attribute page
  const handleNavigateToEdit = useCallback(() => {
    const encodedId = encodeURIComponent(row.original.id);
    router.push(
      `/product-mapping/${encodedId}/product-attribute?connection_id=${row.original.connection_id}&source_data=true`
    );
  }, [router, row.original.id, row.original.connection_id]);

  return (
    <div className="">
      {status === MappingProductStatus.UNMAPPED && (
        <>
          <Button
            variant="link"
            className="flex items-center gap-1 px-0 text-primary"
            leftIcon={<LinkIcon size={16} />}
            onClick={handleMapOpen}>
            {t("pages.productMappingList.actions.map")}
          </Button>
          <ProductSyncSelect
            product={row.original}
            isOpen={isDialogOpen}
            onClose={handleMapClose}
            onMap={handleMapAdapter}
          />
        </>
      )}
      {(status === MappingProductStatus.MAPPED || status === MappingProductStatus.SYNCED) && (
        <Button
          variant="link"
          className="flex items-center gap-1 px-0 text-muted-foreground"
          leftIcon={isUnmapping ? <div className="size-4" /> : <Unlink size={16} />}
          disabled={isUnmapping}
          onClick={handleUnmap}>
          {isUnmapping ? <LoadingDotPulse /> : t("pages.productMappingList.actions.unmap")}
        </Button>
      )}
      {status === MappingProductStatus.ERROR && (
        <Button
          variant="link"
          className="flex items-center gap-1 px-0 text-destructive "
          leftIcon={<AlertTriangle size={16} />}
          onClick={handleNavigateToEdit}>
          {t("pages.productMappingList.actions.fix")}
        </Button>
      )}
    </div>
  );
};

const ActionCell = ({ row }: { row: Row<MappingProduct> }) => {
  const product = row.original;
  const { handleViewDetail } = useProductDetail(product.id);
  const router = useRouter();

  const handleEdit = useCallback(() => {
    const encodedId = encodeURIComponent(product.id);
    router.push(
      `/product-mapping/${encodedId}/product-attribute?connection_id=${product.connection_id}&source_data=true`
    );
  }, [router, product.id, product.connection_id]);

  return (
    <>
      <ActionGroup
        actions={[
          {
            type: "view",
            onClick: handleViewDetail,
          },
          {
            type: "updateAttribute",
            onClick: handleEdit,
          },
        ]}
      />
    </>
  );
};

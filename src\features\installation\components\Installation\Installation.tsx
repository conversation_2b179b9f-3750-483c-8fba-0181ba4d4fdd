"use client";

import { useSearchParams } from "next/navigation";
import { AlertCircle, Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useChannelInstallation } from "@/features/installation/hooks/use-channel-installation";

export default function Installation() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();

  const sourceChannel = searchParams.get("source_channel");
  const destinationChannel = searchParams.get("destination_channel");

  // Sử dụng hook với các tham số
  const { status, errorMessage } = useChannelInstallation({
    sourceChannel,
    destinationChannel,
  });

  if (status === "error") {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="rounded-full bg-destructive/10 p-3 text-destructive">
            <AlertCircle className="size-12" />
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-semibold">{t("install.installationFailed")}</h1>
            <p className="text-sm text-muted-foreground">{t(errorMessage)}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background">
      <div className="flex flex-col items-center gap-4 text-center">
        <Loader2 className="size-12 animate-spin text-primary" />
        <h1 className="text-2xl font-semibold">{t("install.installing")}</h1>
        <p className="text-sm text-muted-foreground">{t("install.pleaseWait")}</p>
      </div>
    </div>
  );
}

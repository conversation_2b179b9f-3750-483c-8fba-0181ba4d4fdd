import { useTranslation } from "react-i18next";

import { Price } from "../../hooks/types";

interface PriceListProps {
  prices: Price[] | { [key: string]: Price } | undefined | null;
}

export const PriceList = ({ prices }: PriceListProps) => {
  const { t } = useTranslation();
  if (!prices) {
    return (
      <div className="space-y-4">
        <h4 className="font-medium">{t("pages.products.prices")}</h4>
        <p className="text-gray-500">{t("pages.products.noPricesAvailable")}</p>
      </div>
    );
  }

  // Convert array to object format if needed
  const pricesObject = Array.isArray(prices)
    ? prices.reduce(
        (acc, price) => {
          if (price?.price_group?.id) {
            acc[price.price_group.id] = price;
          }
          return acc;
        },
        {} as { [key: string]: Price }
      )
    : prices;

  const priceEntries = Object.entries(pricesObject || {});

  return (
    <div className="space-y-4 bg-card">
      <h4 className="font-medium">Prices</h4>

      <div className="grid grid-cols-2 gap-4 bg-card">
        {priceEntries.length > 0 ? (
          priceEntries.map(([key, price]) => (
            <div key={key} className="w-full">
              <div className="flex items-center justify-between rounded-lg p-3">
                <span className="text-foreground">
                  {price?.price_group?.name || "Unknown Group"}
                </span>
                <div className="flex items-center gap-1">
                  <span className="font-medium">
                    {formatPrice(price?.price)}{" "}
                    <span className="text-sm text-muted-foreground">đ</span>
                  </span>
                </div>
              </div>
              <hr className="border-gray-300" />
            </div>
          ))
        ) : (
          <p className="col-span-2 text-gray-500">{t("pages.products.noPricesAvailable")}</p>
        )}
      </div>
    </div>
  );
};

// Helper function to format price
const formatPrice = (price: number | undefined) => {
  if (!price) return "---";
  return new Intl.NumberFormat("en-US", {
    style: "decimal",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

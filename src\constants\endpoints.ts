export const AUTH_ENDPOINTS = {
  LOGIN: "/auth/login",
  REFRESH_TOKEN: "/auth/refresh",
  FORGOT: "/auth/forgot",
  VERIFY_CODE_RESET: "/auth/confirm_reset_password_code",
  NEW_PASSWORD: "/auth/new_password",
  CONFIRM: "/auth/confirm",
  GET_INFO: "/auth/user_info",
  CHANGE_PASSWORD: "/auth/change_password",
  REGISTER: "/auth/register",
  VERIFY_CODE: "/auth/confirm",
  RESEND_CODE: "/auth/resend_code",
} as const;

export const REPORT_ENDPOINTS = {
  GET_LIST: "/reports",
  GET_DETAIL: "/reports/:id",
  CREATE: "/reports",
  UPDATE: "/reports/:id",
  DELETE: "/reports/:id",
} as const;

export const SAVE_FILTER_ENDPOINTS = {
  GET_LIST: "/filter/filters",
  CREATE: "/filter/filters",
  UPDATE: "/filter/filters",
  DELETE: "/filter/filters",
} as const;

export const PRODUCT_ENDPOINTS = {
  LIST: "/product/products",
  CREATE: "/product/products",
  UPDATE: "/product/products/:id",
  DELETE: "/product/products/:id",
  GET_BY_ID: "/product/products/:id",
  LIST_DELETE: "product/products/bulk-delete",

  // Additional product-related endpoints
  BRANDS: "/product/brands",
  CATEGORIES: "/product/categories",
  VARIANTS: "/product/variants",
  UNITS: "/product/units",
} as const;

export const VARIANT_ENDPOINTS = {
  LIST: "/product/variants",
  DELETE: "/product/variants/:id",
} as const;

export const BRAND_ENDPOINTS = {
  LIST: "/product/brands",
  CREATE: "/product/brands",
  UPDATE: "/product/brands/:id",
  DELETE: "/product/brands/:id",
} as const;

export const MEDIA_ENDPOINTS = {
  UPLOAD_IMAGE: "/media/images/upload",
} as const;

export const AI_ENDPOINTS = {
  OPTIMIZE: "/ai/product_optimizer",
} as const;

export const ORDER_ENDPOINTS = {
  LIST: "/order/orders",
  CREATE: "order/orders",
  UPDATE: "/order/orders/:id",
  GET_BY_ID: "order/orders/:id",
} as const;

export const INTEGRATION_ENDPOINTS = {
  SYNC_RECORD_LIST: "/flows/connections/list_sync_records",
  SYNC_RECORD_DETAIL: "/flows/connections/get_sync_records",
  FETCH_EVENT_LIST: "/flows/connections/list_fetch_events",
  FETCH_EVENT_DETAIL: (id: string) => `/flows/connections/get_fetch_events/${id}`,
  CONNECTION_LIST: "/connections/list",
  CONNECTION_UPDATE_STATUS: (id: string) =>
    `/connections/connections/${id}/switch_connection_status`,
  CONNECTION_SETUP_FIELDS: (channelKey: string) => `/connections/setup_fields/${channelKey}`,
} as const;

export const MAPPING_PRODUCT_ENDPOINTS = {
  LIST: "/flows/mappings/product",
  UNMAP: "/flows/mappings/unmap",
  MAP: "/flows/mappings/map",
  DETAIL: "/flows/mappings/product/details",
} as const;

export const PRODUCT_DESTINATION_DATA = {
  LIST: (connectionId: string) => `flows/connections/${connectionId}/destination_data/product`,
  DETAIL: (connectionId: string, productId: string) =>
    `flows/connections/${connectionId}/destination_data/product/${productId}`,
} as const;

export const PRODUCT_ATTRIBUTE_ENDPOINTS = {
  LIST: (connectionId: string) => `flows/${connectionId}/product/attributes`,
  DETAIL: (connectionId: string) => `flows/${connectionId}/product/mapping_attributes`,
  UPDATE: (connectionId: string) => `flows/${connectionId}/product/mapping_attributes`,
  DELETE: (connectionId: string) => `flows/${connectionId}/product/mapping_attributes`,
} as const;

export const TRANSFORMATION_ENDPOINTS = {
  LIST: "/flows/mappings/transformations",
  HANDLE_TRANSFORMATION: "/flows/mappings/transform",
} as const;

export const SYNC_PRODUCT_ENDPOINTS = {
  SYNC: (connectionId: string) => `/flows/sync_mapping/${connectionId}/product`,
} as const;

export const CHANNEL_ENDPOINTS = {
  LIST: "/connections/channels",
  CONNECTION_SETUP_FIELDS: (channelKey: string) => `/connections/setup_fields/${channelKey}`,
  CONNECTION_SETUP: (connectionType: string) => `/connections/setup/${connectionType}`,
  GET_CONNECTION: (connectionId: string) => `/connections/connection/${connectionId}`,
  INSTALL_CONNECTION: "/connections/install",
  CONNECTION_SYNC_SETTINGS: `/connections/connection/synchronization`,
  LIST_PLANS: "/connections/list_plans",
  SUBSCRIBE_PLAN: "/connections/subscribe_plan",
  SUBSCRIPTION_CALLBACK: (connectionId: string, chargeId: string) =>
    `/subscription_callback/${connectionId}/${chargeId}`,
};

export const VERSION_ENDPOINTS = {
  GET_VERSION: "/version/get_version",
} as const;

export const KNOWLEDGE_ENDPOINTS = {
  LIST: "/onexbots/knowledge",
  CREATE: "/onexbots/knowledge",
  GET_BY_ID: (id: string) => `/onexbots/knowledge/${id}`,
  UPDATE: (id: string) => `/onexbots/knowledge/${id}`,
  DELETE: (id: string) => `/onexbots/knowledge/${id}`,
  CREATE_KNOWLEDGE_BY_URL: "/onexbots/knowledge/url",
  CREATE_KNOWLEDGE_BY_FILE: "/onexbots/knowledge/file",
  CREATE_KNOWLEDGE_BY_TEXT: "/onexbots/knowledge/text",
  GET_UPLOAD_URLS: "/onexbots/knowledge/get_upload_urls",
} as const;

export const SETTING_ENDPOINTS = "/settings/settings" as const;

export const DEPARTMENT_ENDPOINTS = {
  LIST: "/onexbots/departments",
  GET_BY_ID: "/onexbots/departments/:id",
  CREATE: "/onexbots/departments",
  UPDATE: "/onexbots/departments/:id",
} as const;

export const CONVERSATION_ENDPOINTS = {
  LIST: "/onexbots/conversations",
  GET_LIST_MESSENGER: (id: string) => `/onexbots/conversations/${id}/messages`,
  CHAT_MESSENGER: (id: string) => `/api/v1/staff/staff/${id}/chat/stream`,
  CREATE_CONVERSATION: "/onexbots/public/conversations",
  CREATE_MESSAGE: (id: string) => `/onexbots/conversations/${id}/messages`,
};
export const TASK_ENDPOINTS = {
  LIST: "/onexbots/tasks",
  GET_BY_ID: "/onexbots/tasks/:id",
  CREATE: "/onexbots/tasks",
  UPDATE: "/onexbots/tasks/:id",
  DELETE: "/onexbots/tasks/:id",
} as const;

export const STAFF_ENDPOINTS = {
  LIST: "/onexbots/virtual-staff",
  DETAIL: "/onexbots/virtual-staff/:id",
  PUBLIC_DETAIL: "/onexbots/public/virtual-staff/:id",
  CREATE: "/onexbots/virtual-staff",
  UPDATE: "/onexbots/virtual-staff/:id",
  DELETE: "/onexbots/virtual-staff/:id",
} as const;

export const ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  REPORT: REPORT_ENDPOINTS,
  PRODUCT: PRODUCT_ENDPOINTS,
  VARIANT: VARIANT_ENDPOINTS,
  BRAND: BRAND_ENDPOINTS,
  ORDER: ORDER_ENDPOINTS,
  MEDIA: MEDIA_ENDPOINTS,
  AI: AI_ENDPOINTS,
  CHANNEL: CHANNEL_ENDPOINTS,
  INTEGRATION_ENDPOINTS: INTEGRATION_ENDPOINTS,
  MAPPING_PRODUCT_ENDPOINTS: MAPPING_PRODUCT_ENDPOINTS,
  PRODUCT_DESTINATION_DATA: PRODUCT_DESTINATION_DATA,
  PRODUCT_ATTRIBUTE_ENDPOINTS: PRODUCT_ATTRIBUTE_ENDPOINTS,
  TRANSFORMATION_ENDPOINTS: TRANSFORMATION_ENDPOINTS,
  VERSION: VERSION_ENDPOINTS,
  SYNC_PRODUCT_ENDPOINTS: SYNC_PRODUCT_ENDPOINTS,
  SETTING: SETTING_ENDPOINTS,
  CONVERSATION: CONVERSATION_ENDPOINTS,
  STAFF: STAFF_ENDPOINTS,
  KNOWLEDGE: KNOWLEDGE_ENDPOINTS,
  TASK: TASK_ENDPOINTS,
} as const;

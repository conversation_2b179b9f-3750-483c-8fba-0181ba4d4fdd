import { authProtectedPaths } from "@/constants/paths";

export const data = {
  // Product Management
  dashboard: {
    title: "nav.dashboard",
    endpoint: authProtectedPaths.DASHBOARD,
    parent: null,
  },
  product: {
    title: "nav.product",
    endpoint: null,
    parent: null,
  },
  productList: {
    title: "nav.productList",
    endpoint: authProtectedPaths.PRODUCTS,
    parent: "product",
  },
  productDetail: {
    title: "nav.productDetail",
    endpoint: authProtectedPaths.PRODUCTS_ID,
    parent: "productList",
  },
  newProduct: {
    title: "nav.newProduct",
    endpoint: authProtectedPaths.PRODUCTS_NEW,
    parent: "productList",
  },
  editProduct: {
    title: "nav.editProduct",
    endpoint: authProtectedPaths.PRODUCTS_ID_EDIT,
    parent: "productList",
  },
  variantList: {
    title: "nav.variantsList",
    endpoint: authProtectedPaths.VARIANTS,
    parent: "product",
  },
  brandList: {
    title: "nav.brandList",
    endpoint: authProtectedPaths.BRANDS,
    parent: "product",
  },
  categoryList: {
    title: "nav.categoryList",
    endpoint: authProtectedPaths.CATEGORIES,
    parent: "product",
  },

  // Order Management
  order: {
    title: "nav.order",
    endpoint: null,
    parent: null,
  },
  orderList: {
    title: "nav.orderList",
    endpoint: authProtectedPaths.ORDERS,
    parent: "order",
  },
  orderDetail: {
    title: "nav.orderDetail",
    endpoint: authProtectedPaths.ORDERS_ID,
    parent: "orderList",
  },
  orderEdit: {
    title: "nav.orderEdit",
    endpoint: authProtectedPaths.ORDERS_ID_EDIT,
    parent: "orderList",
  },
  orderManual: {
    title: "nav.orderManual",
    endpoint: authProtectedPaths.ORDERS_NEW,
    parent: "orderList",
  },
  orderProcess: {
    title: "nav.orderProcess",
    endpoint: authProtectedPaths.ORDER_PROCESS,
    parent: "order",
  },
  returnOrderList: {
    title: "nav.returnOrderList",
    endpoint: authProtectedPaths.RETURN_ORDERS,
    parent: "order",
  },
  packageList: {
    title: "nav.packageList",
    endpoint: authProtectedPaths.PACKAGES,
    parent: "order",
  },

  // Purchase Order Management
  purchaseOrder: {
    title: "nav.purchaseOrder",
    endpoint: null,
    parent: null,
  },
  purchaseOrderList: {
    title: "nav.purchaseOrderList",
    endpoint: authProtectedPaths.PURCHASE_ORDERS,
    parent: "purchaseOrder",
  },
  supplierList: {
    title: "nav.supplierList",
    endpoint: authProtectedPaths.SUPPLIERS,
    parent: "purchaseOrder",
  },

  // Logistics Management
  logistics: {
    title: "nav.logistics",
    endpoint: null,
    parent: null,
  },
  shippingProviderList: {
    title: "nav.shippingProviderList",
    endpoint: authProtectedPaths.SHIPPING_PROVIDERS,
    parent: "logistics",
  },

  // Customer Management
  customers: {
    title: "nav.customers",
    endpoint: null,
    parent: null,
  },
  customerDashboard: {
    title: "nav.customerDashboard",
    endpoint: authProtectedPaths.CUSTOMER_DASHBOARD,
    parent: "customers",
  },
  customerList: {
    title: "nav.customerList",
    endpoint: authProtectedPaths.CUSTOMERS,
    parent: "customers",
  },
  customerDetail: {
    title: "nav.customerDetail",
    endpoint: authProtectedPaths.CUSTOMERS_ID,
    parent: "customerList",
  },
  customerGroupList: {
    title: "nav.customerGroupList",
    endpoint: authProtectedPaths.CUSTOMER_GROUPS,
    parent: "customers",
  },
  loyaltyProgram: {
    title: "nav.loyaltyProgram",
    endpoint: authProtectedPaths.LOYALTY_PROGRAMS,
    parent: "customers",
  },
  rewardProgram: {
    title: "nav.rewardProgram",
    endpoint: authProtectedPaths.REWARD_PROGRAMS,
    parent: "customers",
  },

  // Inventory Management
  inventory: {
    title: "nav.inventory",
    endpoint: null,
    parent: null,
  },
  locationList: {
    title: "nav.locationList",
    endpoint: authProtectedPaths.LOCATIONS,
    parent: "inventory",
  },
  inventoryList: {
    title: "nav.inventoryList",
    endpoint: authProtectedPaths.INVENTORY_ITEMS,
    parent: "inventory",
  },
  stockAdjustmentList: {
    title: "nav.stockAdjustmentList",
    endpoint: authProtectedPaths.STOCK_ADJUSTMENTS,
    parent: "inventory",
  },
  stockRelocateList: {
    title: "nav.stockRelocateList",
    endpoint: authProtectedPaths.STOCK_RELOCATES,
    parent: "inventory",
  },

  // Promotion Management
  promotion: {
    title: "nav.promotion",
    endpoint: null,
    parent: null,
  },
  discountList: {
    title: "nav.discountList",
    endpoint: authProtectedPaths.DISCOUNTS,
    parent: "promotion",
  },
  voucherList: {
    title: "nav.voucherList",
    endpoint: authProtectedPaths.VOUCHERS,
    parent: "promotion",
  },

  // Import Management
  import: {
    title: "nav.import",
    endpoint: null,
    parent: null,
  },
  importList: {
    title: "nav.importList",
    endpoint: authProtectedPaths.IMPORTS,
    parent: "import",
  },
  recordList: {
    title: "nav.recordList",
    endpoint: authProtectedPaths.RECORDS,
    parent: "import",
  },

  // Report Management
  report: {
    title: "nav.report",
    endpoint: null,
    parent: null,
  },
  productReport: {
    title: "nav.productReport",
    endpoint: authProtectedPaths.REPORT_PRODUCTS,
    parent: "report",
  },

  // Finance Management
  finance: {
    title: "nav.finance",
    endpoint: null,
    parent: null,
  },
  account: {
    title: "nav.account",
    endpoint: authProtectedPaths.ACCOUNTS,
    parent: "finance",
  },
  paymentMethod: {
    title: "nav.paymentMethod",
    endpoint: authProtectedPaths.PAYMENT_METHODS,
    parent: "finance",
  },
  transaction: {
    title: "nav.transaction",
    endpoint: authProtectedPaths.TRANSACTIONS,
    parent: "finance",
  },

  // Integration Management
  integration: {
    title: "nav.integration",
    endpoint: null,
    parent: null,
  },
  fetchEvent: {
    title: "nav.fetchEvent",
    endpoint: authProtectedPaths.FETCH_EVENTS,
    parent: "integration",
  },
  detailFetchEvent: {
    title: "nav.detailFetchEvent",
    endpoint: authProtectedPaths.FETCH_EVENTS_ID,
    parent: "fetchEvent",
  },
  syncRecords: {
    title: "nav.syncRecords",
    endpoint: authProtectedPaths.SYNC_RECORDS,
    parent: "integration",
  },
  channel: {
    title: "nav.channel",
    endpoint: authProtectedPaths.CHANNELS,
    parent: "integration",
  },
  supportedChannels: {
    title: "nav.supportedChannels",
    endpoint: authProtectedPaths.SUPPORTED_CHANNELS,
    parent: "channel",
  },
  installChannel: {
    title: "nav.installChannel",
    endpoint: authProtectedPaths.INSTALL_CHANNEL,
    parent: "supportedChannels",
  },
  productMapping: {
    title: "nav.productMapping",
    endpoint: authProtectedPaths.PRODUCT_MAPPING,
    parent: "integration",
  },
  productMappingDetail: {
    title: "nav.productMappingDetail",
    endpoint: authProtectedPaths.PRODUCT_MAPPING_ID,
    parent: "productMapping",
  },
  productMappingAttribute: {
    title: "nav.productMappingAttribute",
    endpoint: authProtectedPaths.PRODUCT_MAPPING_ATTRIBUTE,
    parent: "productMapping",
  },

  // Website Management
  website: {
    title: "nav.website",
    endpoint: null,
    parent: null,
  },
  blogCategory: {
    title: "nav.blogCategory",
    endpoint: authProtectedPaths.BLOG_CATEGORIES,
    parent: "website",
  },
  blogList: {
    title: "nav.blogList",
    endpoint: authProtectedPaths.BLOGS,
    parent: "website",
  },

  // Notification Management
  notification: {
    title: "nav.notification",
    endpoint: null,
    parent: null,
  },
  notificationList: {
    title: "nav.notificationList",
    endpoint: authProtectedPaths.NOTIFICATIONS,
    parent: "notification",
  },

  // Loyalty App
  loyaltyApp: {
    title: "nav.loyaltyApp",
    endpoint: authProtectedPaths.LOYALTY_APP,
    parent: null,
  },

  // POS Management
  pos: {
    title: "nav.pos",
    endpoint: null,
    parent: null,
  },
  terminalList: {
    title: "nav.terminalList",
    endpoint: authProtectedPaths.TERMINALS,
    parent: "pos",
  },
  shiftList: {
    title: "nav.shiftList",
    endpoint: authProtectedPaths.SHIFTS,
    parent: "pos",
  },
  posPage: {
    title: "nav.pos",
    endpoint: authProtectedPaths.POS,
    parent: "pos",
  },
  posFnB: {
    title: "nav.posFnB",
    endpoint: authProtectedPaths.POS_FNB,
    parent: "pos",
  },

  // Administration
  settings: {
    title: "nav.settings",
    endpoint: authProtectedPaths.SETTINGS,
    parent: null,
  },
  themeSetting: {
    title: "pages.settings.themeSetting",
    endpoint: authProtectedPaths.SETTINGS_THEME,
    parent: "settings",
  },

  // Bots
  staff: {
    title: "nav.staff",
    endpoint: null,
    parent: null,
  },
  department: {
    title: "nav.department",
    endpoint: authProtectedPaths.DEPARTMENT,
    parent: "staff",
  },
  staffList: {
    title: "nav.staffList",
    endpoint: authProtectedPaths.STAFF,
    parent: "staff",
  },
  knowledge: {
    title: "nav.knowledge",
    endpoint: authProtectedPaths.KNOWLEDGE,
    parent: null,
  },
  conversation: {
    title: "nav.conversation",
    endpoint: authProtectedPaths.CONVERSATION,
    parent: null,
  },
  interact: {
    title: "nav.interact",
    endpoint: authProtectedPaths.INTERACT,
    parent: "staff",
  },
  editStaff: {
    title: "nav.editStaff",
    endpoint: authProtectedPaths.STAFF_EDIT,
    parent: "interact",
  },
};

import React from "react";
import { Globe } from "lucide-react";

import { Combobox } from "@/components/ui/combobox";
import { useLanguage } from "@/hooks/use-language";

interface LanguageComboboxProps {
  className?: string;
}

export const LanguageCombobox: React.FC<LanguageComboboxProps> = ({ className }) => {
  const languages = [
    { id: "en", name: "english", disabled: false, displayValue: "English" },
    { id: "vi", name: "vietnam", disabled: false, displayValue: "Vietnam" },
  ];
  const { currentLanguage, toggleLanguage } = useLanguage();
  return (
    <div className={`w-fit  ${className || ""}`}>
      <Combobox
        value={currentLanguage}
        items={languages}
        isLoading={false}
        isLoadingMore={false}
        onValueChange={toggleLanguage}
        placeholder="Search"
        searchPlaceholder="Search"
        isShowChevronsUpDown={false}
        leftIcon={Globe}
        variantButton="ghost"
      />
    </div>
  );
};

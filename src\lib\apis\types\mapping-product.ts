import { Product } from "@/features/products/hooks/types";

export enum MappingProductStatus {
  SYNCED = "synced",
  MAPPED = "mapped",
  UNMAPPED = "unmapped",
  ERROR = "error",
}

export interface MappingProduct {
  id: string;
  source_product: Product;
  destination_product?: Product;
  price: number;
  synced_at: string;
  status: MappingProductStatus;
}

export interface MappingProductMapPayload {
  connection_id: string;
  sync_record_id: string;
  destination_data_id: string;
  is_extra_destination_mapping: boolean;
  extra_mapping_data: Record<string, string>; // source_variant_sku: destination_variant_sku
  record_type: string;
}

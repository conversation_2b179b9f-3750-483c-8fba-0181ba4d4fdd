"use client";

import { useEffect, useState } from "react";
import type { Route } from "next";
import { useRouter } from "next/navigation";
import { ChevronsUpDown, Languages, LogOut, Moon, SettingsIcon, Sun, UserIcon } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import userImg from "@/assets/images/user.png";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { useLanguage } from "@/hooks/use-language";
import { clearAuth } from "@/lib/auth";

interface UserInfo {
  Username?: string;
  Email?: string;
  UserAttributes?: {
    "custom:role"?: string;
    name?: string;
    email?: string;
  };
}

export function NavUser() {
  const router = useRouter();
  const { isTablet } = useSidebar();
  const { t, i18n } = useTranslation();
  const { theme, setTheme } = useTheme();
  const { toggleLanguage } = useLanguage();
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  useEffect(() => {
    // Chỉ get user info ở client side
    const authUser = localStorage.getItem("auth_user");
    if (authUser) {
      try {
        setUserInfo(JSON.parse(authUser));
      } catch (error) {
        console.error("Failed to parse auth user:", error);
      }
    }
  }, []);

  const userName = userInfo?.UserAttributes?.name || userInfo?.Username || "User";
  const userEmail = userInfo?.UserAttributes?.email || userInfo?.Email || "";
  const userRole = userInfo?.UserAttributes?.["custom:role"] || "";

  const handleLogout = () => {
    try {
      clearAuth();
      toast.success("Logged out successfully");
      router.push("/login" as Route);
    } catch {
      toast.error("Error logging out");
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
              <Avatar className="size-8 rounded-lg">
                <AvatarImage src={userImg.src} alt={userName} />
                <AvatarFallback className="rounded-lg">CN</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{userName}</span>
                <span className="truncate text-xs">{userEmail}</span>
                {userRole && <span className="truncate text-xs">{userRole}</span>}
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isTablet ? "bottom" : "right"}
            align="end"
            sideOffset={4}>
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="size-8 rounded-lg">
                  <AvatarImage src={userImg.src} alt={userName} />
                  <AvatarFallback className="rounded-lg">{userName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{userName}</span>
                  <span className="truncate text-xs">{userEmail}</span>
                  {userRole && <span className="truncate text-xs">{userRole}</span>}
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem className="flex cursor-pointer items-center gap-2">
                <UserIcon size={16} />
                {t("profile.profile")}
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                className="flex cursor-pointer items-center gap-2"
                onClick={(e: React.MouseEvent<HTMLDivElement>) => {
                  e.preventDefault();
                  setTheme(theme === "dark" ? "light" : "dark");
                }}>
                {theme === "dark" ? <Moon size={16} /> : <Sun size={16} />}

                <div className="flex w-full items-center justify-between">
                  {t("profile.darkMode")}
                  <span className="text-muted-foreground">
                    {theme === "dark" ? t("profile.on") : t("profile.off")}
                  </span>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex cursor-pointer items-center gap-2"
                onClick={(e: React.MouseEvent<HTMLDivElement>) => {
                  e.preventDefault();
                  toggleLanguage();
                }}>
                <Languages size={16} />
                <div className="flex w-full items-center justify-between">
                  {t("profile.language")}
                  <span className="text-muted-foreground">
                    {i18n.language === "en" ? t("profile.english") : t("profile.vietnamese")}
                  </span>
                </div>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex cursor-pointer items-center gap-2"
              onClick={(e: React.MouseEvent<HTMLDivElement>) => {
                e.preventDefault();
                router.push("/settings" as Route);
              }}>
              <SettingsIcon size={16} />
              {t("profile.settings")}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex cursor-pointer items-center gap-2 text-destructive"
              onClick={handleLogout}>
              <LogOut size={16} />
              {t("profile.logout")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

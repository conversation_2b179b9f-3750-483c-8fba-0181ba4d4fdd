import { useCallback, useEffect, useState } from "react";
import { CheckIcon } from "@radix-ui/react-icons";
import { useQuery } from "@tanstack/react-query";
import { CirclePlus, Search } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useChannels } from "@/features/integration/hooks/use-channel";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import useDebounce from "@/hooks/use-debounce";
import { privateApi } from "@/lib/api_helper";
import { ResponseList } from "@/lib/apis/types/common";
import { cn } from "@/lib/utils";

import { Input } from "../ui/input";

interface Option {
  id: string;
  name: string;
  count?: number;
}

interface DataTableFacetedFilterProps {
  title: string;
  value: string[];
  onChange: (value: string[]) => void;
  options?: Option[];
  remote?: boolean;
  pathUrlLoad?: string;
  defaultValue?: string[];
  pageSize?: number;
  isChannelFetch?: boolean;
}

export const fetcher = async (
  url: string,
  search?: string,
  page = 0,
  limit = 10
): Promise<ResponseList<Option>> => {
  return await privateApi.get(url, {
    params: {
      limit,
      page,
      ...(search && { query: search }),
    },
  });
};

export function DataTableFacetedFilter({
  title,
  value = [],
  onChange,
  options: defaultOptions = [],
  remote = false,
  pathUrlLoad,
  defaultValue = [],
  pageSize = 999,
  isChannelFetch = false,
}: DataTableFacetedFilterProps) {
  const { t } = useTranslation();
  const [searchValue, setSearchValue] = useState("");
  const debouncedSearchValue = useDebounce(searchValue, 500);
  const selectedValues = new Set(value.length ? value : defaultValue);
  const [page, setPage] = useState(0);
  const [localOptions, setLocalOptions] = useState<Option[]>([]);

  // Fetch channels if isChannelFetch is true
  const { data: channelsData } = useChannels({ enabled: isChannelFetch });
  // Reset page and local options when search changes
  useEffect(() => {
    setPage(0);
    setLocalOptions([]);
  }, [debouncedSearchValue]);
  const { data, isLoading } = useQuery({
    queryKey:
      remote && pathUrlLoad && !isChannelFetch
        ? (["faceted-filter", pathUrlLoad, debouncedSearchValue, page, pageSize] as const)
        : ["faceted-filter"],
    queryFn: () =>
      pathUrlLoad && !isChannelFetch
        ? fetcher(pathUrlLoad, debouncedSearchValue, page, pageSize)
        : null,
    enabled: remote && !isChannelFetch && !!pathUrlLoad,
  });

  // Update local options when data changes
  useEffect(() => {
    if (data) {
      if (page === 0) {
        setLocalOptions(data.items);
      } else {
        setLocalOptions((prev) => [...prev, ...data.items]);
      }
    }
  }, [data, page]);

  // Process channel data if isChannelFetch is true
  useEffect(() => {
    if (isChannelFetch && channelsData) {
      const channelOptions = [
        { id: "optiwarehouse", name: "OptiWarehouse" },
        ...channelsData.map((channel) => ({
          id: channel.key,
          name: channel.name,
        })),
      ];

      // Filter by search term if needed
      const filteredChannels = debouncedSearchValue
        ? channelOptions.filter((option) =>
            option.name.toLowerCase().includes(debouncedSearchValue.toLowerCase())
          )
        : channelOptions;

      setLocalOptions(filteredChannels);
    }
  }, [isChannelFetch, channelsData, debouncedSearchValue]);

  const options = remote || isChannelFetch ? localOptions : defaultOptions;
  const total = remote
    ? isChannelFetch
      ? localOptions.length
      : data?.total
    : defaultOptions.length;
  const hasMore = remote && !isChannelFetch ? localOptions.length < (data?.total || 0) : false;

  const handleLoadMore = useCallback(() => {
    if (!isChannelFetch) {
      setPage((prev) => prev + 1);
    }
  }, [isChannelFetch]);

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const filteredOptions =
    remote || isChannelFetch
      ? options
      : options.filter((option) =>
          option.name.toLowerCase().includes(debouncedSearchValue.toLowerCase())
        );
  const handleSelect = (optionId: string) => {
    const newSelectedValues = new Set(selectedValues);
    if (newSelectedValues.has(optionId)) {
      newSelectedValues.delete(optionId);
    } else {
      newSelectedValues.add(optionId);
    }
    onChange(Array.from(newSelectedValues));
  };

  const handleSelectAll = () => {
    if (selectedValues.size === options.length) {
      onChange([]);
    } else {
      onChange(options.map((opt) => opt.id));
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    const threshold = 20;
    const isNearBottom = target.scrollHeight - (target.scrollTop + target.clientHeight) < threshold;

    if (isNearBottom && hasMore && !isLoading) {
      handleLoadMore();
    }
  };
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="default"
          className="border-dashed px-3"
          leftIcon={<CirclePlus size={16} />}>
          {title}
          {selectedValues.size > 0 && (
            <>
              <Separator orientation="vertical" className=" h-4" />
              <div className="flex space-x-1">
                {selectedValues.size > 2 ? (
                  <Badge variant="secondary" className="rounded-sm px-1 font-normal">
                    {selectedValues.size} selected
                  </Badge>
                ) : (
                  Array.from(selectedValues).map((value) => {
                    const matchingOption = options.find((option) => option.id === value);
                    return (
                      <Badge
                        key={value}
                        variant="secondary"
                        className="rounded-sm px-1 font-normal">
                        {matchingOption ? matchingOption.name : "Not loaded"}
                      </Badge>
                    );
                  })
                )}
              </div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px]  p-0 text-sm" align="start">
        <div className="flex items-center border-b px-3">
          <Search className="mr-2 size-4 shrink-0 opacity-50" />
          <Input
            placeholder="Search..."
            value={searchValue}
            onChange={(e) => handleSearch(e.target.value)}
            className="h-10 w-full border-0 bg-transparent pl-0 outline-none placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div>
        <div className="space-y-1 p-1">
          <div
            className="mx-1 flex cursor-pointer items-center justify-between space-x-2 border-b px-2 py-1 hover:rounded-sm hover:bg-accent hover:text-accent-foreground"
            onClick={handleSelectAll}>
            <div className="flex  items-center space-x-2">
              <div
                className={cn(
                  "flex h-4 w-4 items-center justify-center rounded-sm border",
                  selectedValues.size === options.length
                    ? "bg-primary text-primary-foreground border-primary"
                    : "opacity-100 border-muted-foreground"
                )}>
                <CheckIcon
                  className={cn(
                    "h-4 w-4",
                    selectedValues.size === options.length ? "opacity-100" : "opacity-0"
                  )}
                />
              </div>
              <span className="font-medium">All</span>
            </div>
            {total && total > 0 && (
              <span className="ml-auto text-xs text-muted-foreground">
                {filteredOptions.length}
              </span>
            )}
          </div>
          <div
            className={cn(
              "overflow-y-auto px-1 max-w-[200px] overflow-x-hidden",
              Math.max(options.length, data?.total || 0) < 10 ? "h-fit" : "h-[200px]"
            )}
            onScroll={handleScroll}>
            <div className="space-y-1">
              {filteredOptions.map((option) => {
                const isSelected = selectedValues.has(option.id);
                return (
                  <div
                    key={option.id}
                    className="flex cursor-pointer items-center justify-between space-x-2 overflow-x-hidden rounded-sm px-2 py-1 hover:rounded-sm hover:bg-accent hover:text-accent-foreground"
                    onClick={() => handleSelect(option.id)}>
                    <div className="flex max-w-full items-center space-x-2">
                      <div
                        className={cn(
                          "flex h-4 w-4 items-center justify-center rounded-sm border  flex-none ",
                          isSelected
                            ? "bg-primary text-primary-foreground border-primary"
                            : "opacity-100 border-muted-foreground"
                        )}>
                        <CheckIcon
                          className={cn("h-4 w-4", isSelected ? "opacity-100" : "opacity-0")}
                        />
                      </div>
                      <div className=" flex-auto truncate">{option.name || option.id}</div>
                    </div>
                    {option.count !== undefined && (
                      <span className="ml-auto text-xs text-muted-foreground">{option.count}</span>
                    )}
                  </div>
                );
              })}
              {isLoading && (
                <div className="flex justify-center py-2">
                  <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                </div>
              )}
            </div>
          </div>
          {selectedValues.size > 0 && (
            <>
              <Separator />
              <Button
                variant="ghost"
                className="w-full justify-center text-popover-foreground"
                onClick={() => onChange([])}>
                {t("table.filter.clearFilter")}
              </Button>
            </>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}

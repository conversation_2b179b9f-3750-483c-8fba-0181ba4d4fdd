"use client";

import { useRouter } from "next/navigation";
import { CircleAlert } from "lucide-react";
import { useTranslation } from "react-i18next";
import { FcGoogle } from "react-icons/fc";

import { BrandSection } from "@/features/auth/components/brand-section";
import { Footer } from "@/features/auth/components/public-footer";
import { useLogin } from "@/features/auth/hooks/useLogin";

import { Button } from "@/components/ui/button";
import { Form, FormField } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

export const Login = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const { loginSubmit, loading, form, error, handleGoogleLogin, handleFocus } = useLogin();

  return (
    <div className="min-h-screen w-screen bg-bg-secondary">
      <div className="grid min-h-screen w-screen lg:grid-cols-[1fr_2fr]">
        <BrandSection />
        <div className="relative flex flex-col justify-between p-8">
          <div className="flex flex-1 items-center justify-center">
            <div className="relative z-10 w-full max-w-[400px] space-y-6">
              <div className="space-y-1.5">
                <h1 className="text-2xl font-semibold tracking-tight">{t("auth.loginTitle")}</h1>
                <p className="text-sm text-muted-foreground">{t("auth.loginSubtitle")}</p>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(loginSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="username"
                      render={({ field, fieldState }) => (
                        <Input
                          {...field}
                          tabIndex={1}
                          label={t("auth.usernameOrEmail")}
                          placeholder={t("auth.usernameOrEmailPlaceholder")}
                          disabled={loading}
                          error={fieldState.error?.message}
                          onFocus={() => handleFocus()}
                        />
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field, fieldState }) => (
                        <Input
                          {...field}
                          tabIndex={2}
                          label={t("auth.password")}
                          rightElement={
                            <Button
                              type="button"
                              tabIndex={3}
                              variant="link"
                              className="h-fit p-0 text-sm leading-none"
                              onClick={() => router.push("/forgot-password")}>
                              {t("auth.forgot")}
                            </Button>
                          }
                          type="password"
                          placeholder={t("auth.passwordPlaceholder")}
                          disabled={loading}
                          error={fieldState.error?.message}
                          onFocus={() => handleFocus()}
                        />
                      )}
                    />
                  </div>
                  <div className="space-y-2">
                    {error && (
                      <div className="flex items-center text-sm text-red-500">
                        <CircleAlert className="mr-2 size-4" />
                        {t(error)}
                      </div>
                    )}
                    <Button type="submit" disabled={loading} loading={loading} className="w-full">
                      {t("auth.loginButton")}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full py-0"
                      disabled={loading}
                      onClick={handleGoogleLogin}>
                      <FcGoogle className="mr-2 size-4" />
                      {t("auth.loginWithGoogle")}
                    </Button>
                  </div>
                </form>
              </Form>

              <div className="space-x-2 text-center text-sm">
                <span>{t("auth.noAccount")}</span>
                <Button
                  type="button"
                  variant="link"
                  className="h-fit p-0 text-sm font-normal leading-none"
                  onClick={() => router.push("/signup")}
                  disabled={loading}>
                  {t("auth.signUp")}
                </Button>
              </div>
            </div>
          </div>

          <Footer />
        </div>
      </div>
    </div>
  );
};

import { test, expect } from '@playwright/test';
import { AUTH_ENDPOINTS } from "@/constants/endpoints";
import { publicPaths } from '@/middleware';
import config from '@/config';

const BASE_URL = 'http://localhost:3000'
const LOGIN_ROUTE = publicPaths[0]
const DASHBOARD_ROUTE = '/dashboard'
const API_URL = config.API_URL
const loginInfo = {
  username: 'onexapis_admin',
  password: 'Admin@123'
}
const loginApiUrl = `${API_URL}${AUTH_ENDPOINTS.LOGIN}`;

test.describe('End-to-End Login Tests', () => {
  test('should successfully call login API', async ({ request }) => {
    const response = await request.post(loginApiUrl, {
      data: {
        ...loginInfo
      }
    });

    expect(response.status()).toBe(200);

    // 4. Parse and verify the response body
    const responseBody = await response.json();
    expect(responseBody).toHaveProperty('AccessToken'); // Ensure a token is returned
    expect(responseBody).toHaveProperty('RefreshToken');
  });

  const testCases = [
    { username: 'wronguser', password: 'wrongpassword', expectedStatus: 401, expectedError: "InvalidCredential" },
    { username: '', password: 'password123', expectedStatus: 401, expectedError: "InvalidCredential" }, // Empty username
    { username: 'testuser', password: '', expectedStatus: 401, expectedError: "InvalidCredential" }, // Empty password
  ];

  for (const { username, password, expectedStatus, expectedError } of testCases) {
    test(`should fail to login with username: "${username}" and password: "${password}"`, async ({ request }) => {
      const response = await request.post(loginApiUrl, { data: { username, password } });
      const responseJSON = await response.json()
      console.log('res err', responseJSON.error)
      expect(response.status()).toBe(expectedStatus);
      expect(responseJSON.error).toBe(expectedError);
    });
  }

  test('should redirect to main page on successful login', async ({ page }) => {
    await page.goto(`${BASE_URL}${LOGIN_ROUTE}`);

    await page.fill('input[name="email"]', loginInfo.username);
    await page.fill('input[name="password"]', loginInfo.password);

    await page.click('button[type="submit"]');

    await expect(page).toHaveURL(new RegExp(`${BASE_URL}${DASHBOARD_ROUTE}`), { timeout: 300000 });
  });

  test('should stay on /login with an error message for incorrect credentials', async ({ page }) => {

    await page.goto(`${BASE_URL}${LOGIN_ROUTE}`);


    await page.fill('input[name="email"]', 'wronguser');
    await page.fill('input[name="password"]', 'wrongpassword');


    await page.click('button[type="submit"]');

    await expect(page).toHaveURL(new RegExp(`${BASE_URL}${LOGIN_ROUTE}`));

    const errorMessage = page.locator('text=Request failed with status code 401');
    await errorMessage.waitFor();

    await expect(errorMessage).toBeVisible();

    await expect(errorMessage).toHaveText('Request failed with status code 401');
  });
});

// npx playwright test E2Etests/Login.test.ts

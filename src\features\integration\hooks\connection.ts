import { InfiniteData, useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { Connection, connectionApis, ConnectionStatus } from "@/lib/apis/connection";
import { ResponseList } from "@/lib/apis/types/common";

import { connectionKeys, ICommonParams } from "./keys";

interface UseFetchEventsOptions extends Partial<ICommonParams> {
  enabled?: boolean;
}

export function useConnection(options: UseFetchEventsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const query = useInfiniteQuery({
    queryKey: connectionKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      connectionApis.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const connections = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    connections,
    total,
  };
}

interface UseUpdateConnectionStatusOptions {
  onSuccess?: (message: string) => void;
  onError?: (error: Error) => void;
}

interface UpdateStatusResponse {
  message: string;
}

export function useUpdateConnectionStatus(options: UseUpdateConnectionStatusOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (connectionId: string) => connectionApis.updateStatus(connectionId),
    onSuccess: (response: UpdateStatusResponse, connectionId) => {
      queryClient.setQueriesData<InfiniteData<ResponseList<Connection>>>(
        { queryKey: connectionKeys.lists() },
        (oldData: InfiniteData<ResponseList<Connection>> | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page: ResponseList<Connection>) => ({
              ...page,
              items: page.items.map((item: Connection) =>
                item.id === connectionId
                  ? {
                      ...item,
                      status:
                        item.status === ConnectionStatus.ACTIVE
                          ? ConnectionStatus.INACTIVE
                          : ConnectionStatus.ACTIVE,
                    }
                  : item
              ),
            })),
          };
        }
      );

      onSuccess?.(response.message);
    },
    onError: (error: Error) => {
      onError?.(error);
    },
  });
}

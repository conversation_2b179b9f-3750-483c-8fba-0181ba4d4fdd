import React from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Card } from "@/components/ui";
import { But<PERSON> } from "@/components/ui/button";

import { Transformation } from "../hooks/types";
import { SourceFieldSelect } from "./source-field-select";
import { TransformationItem } from "./transformation-item";

interface TransformationFormProps {
  sourceField: string;
  onSourceFieldChange: (value: string) => void;
  transformations: Transformation[];
  onTransformationTypeChange: (id: string, type: string) => void;
  onConfigChange: (id: string, config: any) => void;
  onRemoveTransformation: (id: string) => void;
  onAddTransformation: () => void;
  onTransformationsChange?: (transformations: Transformation[]) => void;
  sourceData?: any;
  sourceFields?: string[];
}

export function TransformationForm({
  sourceField,
  onSourceFieldChange,
  transformations,
  onTransformationTypeChange,
  onConfigChange,
  onRemoveTransformation,
  onAddTransformation,
  onTransformationsChange,
  sourceData,
  sourceFields,
}: TransformationFormProps) {
  const { t } = useTranslation();

  // Update parent component whenever transformations change
  React.useEffect(() => {
    if (onTransformationsChange) {
      const formattedTransformations = transformations
        .filter((t) => t.type) // Only include transformations with a type
        .map(({ type, config }) => ({
          type,
          config,
        }));
      onTransformationsChange(formattedTransformations);
    }
  }, [transformations, onTransformationsChange]);

  return (
    <Card
      className="h-fit space-y-4 border-none p-4
    ">
      <h3 className="text-lg font-semibold">
        {t("pages.productMapping.advancedMapping.transformationForm")}
      </h3>
      <SourceFieldSelect
        value={sourceField}
        onChange={onSourceFieldChange}
        sourceData={sourceData}
        sourceFields={sourceFields}
      />

      <div className="space-y-4">
        {transformations.map((transformation, index) => (
          <TransformationItem
            key={transformation.id}
            transformation={transformation}
            index={index}
            onTransformationTypeChange={onTransformationTypeChange}
            onConfigChange={onConfigChange}
            onRemoveTransformation={onRemoveTransformation}
          />
        ))}
      </div>

      <Button
        variant="outline"
        className="w-full"
        leftIcon={<Plus size={16} />}
        onClick={onAddTransformation}>
        {t("pages.productMapping.advancedMapping.addTransformation")}
      </Button>
    </Card>
  );
}

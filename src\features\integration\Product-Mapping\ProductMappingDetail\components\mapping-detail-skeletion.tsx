import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

export function MappingDetailSkeleton() {
  return (
    <div className="px-4 pb-4">
      <Card className="border-none">
        <CardContent className="p-0">
          {/* Mapping Status Skeleton */}
          <div className="p-4">
            <Label>Mapping status</Label>
            <div className="mt-2 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-5 w-16" />
              </div>
              <Skeleton className="h-8 w-24" />
            </div>
          </div>

          <Separator />

          {/* Product Comparison Skeleton */}
          <div className="p-4">
            <div className="flex flex-col gap-6">
              <div className="grid grid-cols-[1fr,auto,1fr] gap-2">
                <div className="flex flex-col space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <div className="flex gap-4">
                    <Skeleton className="size-16 rounded" />
                    <div className="flex flex-col gap-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Skeleton className="size-6 rounded-full" />
                </div>
                <div className="flex flex-col space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <div className="flex gap-4">
                    <Skeleton className="size-16 rounded" />
                    <div className="flex flex-col gap-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Variants Skeleton */}
          <div className="px-4 pt-4">
            <Label>Variants</Label>
            <div className="mt-2 grid grid-cols-[1fr,auto,1fr]">
              {/* Left Variants */}
              <div>
                <div className="py-4 pl-4">
                  <div className="space-y-4">
                    {[1, 2, 3].map((_, i) => (
                      <div key={i} className="flex items-center justify-between border-b px-4 pb-4">
                        <div className="flex items-center gap-3">
                          <Skeleton className="size-10 rounded-md" />
                          <div>
                            <Skeleton className="h-5 w-24" />
                            <Skeleton className="mt-1 h-4 w-16" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Middle Arrows */}
              <div className="flex flex-col justify-center">
                {[1, 2, 3].map((_, i) => (
                  <div key={i} className="flex h-[72px] items-center justify-center">
                    <Skeleton className="size-4" />
                  </div>
                ))}
              </div>

              {/* Right Variants */}
              <div>
                <div className="py-4 pr-4">
                  <div className="space-y-4">
                    {[1, 2, 3].map((_, i) => (
                      <div key={i} className="flex items-center justify-between border-b pb-4 pl-4">
                        <div className="flex items-center gap-3">
                          <Skeleton className="size-10 rounded-md" />
                          <div>
                            <Skeleton className="h-5 w-24" />
                            <Skeleton className="mt-1 h-4 w-16" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="h-20"></div>

      {/* Footer with action buttons skeleton */}
      <div className="fixed inset-x-0 bottom-0 border-t bg-card px-6 pb-6 pt-4 shadow-2xl">
        <div className="relative flex">
          <div className="ml-auto flex items-center justify-end">
            <div className="flex gap-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

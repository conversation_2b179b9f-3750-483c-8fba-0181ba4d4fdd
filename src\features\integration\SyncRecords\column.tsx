import { Row } from "@tanstack/react-table";

import Channel<PERSON>ogo, { DateColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { SyncRecord } from "@/lib/apis/sync-record";

export const columns = (t: any): CustomColumn<SyncRecord>[] => [
  {
    id: "channel",
    accessorKey: "channel",
    header: t("pages.syncRecords.columns.channel"),
    sorter: true,
    sortKey: "channel",
    cell: ({ row }: { row: Row<SyncRecord> }) => (
      <ChannelLogo
        channelKey={row?.original?.channel}
        href={`/sync-records/${encodeURIComponent(row?.original?.id)}`}
      />
    ),
  },
  {
    id: "header",
    accessorKey: "header",
    header: t("pages.syncRecords.columns.header"),

    sorter: true,
    sortKey: "record_type",
    cell: ({ row }: { row: Row<SyncRecord> }) => (
      <span className="text-sm">{row?.original?.record_type}</span>
    ),
  },
  {
    id: "fetchEventId",
    accessorKey: "fetchEventId",
    header: t("pages.syncRecords.columns.fetchEventId"),
    sorter: true,
    sortKey: "fetch_event_id",
    cell: ({ row }: { row: Row<SyncRecord> }) => (
      <span className="text-sm">{row?.original?.fetch_event_id}</span>
    ),
  },
  {
    id: "connectionId",
    accessorKey: "connectionId",
    header: t("pages.syncRecords.columns.connectionId"),
    cell: ({ row }: { row: Row<SyncRecord> }) => (
      <span className="text-sm">{row?.original?.connection_id}</span>
    ),
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    header: t("pages.syncRecords.columns.lastUpdated"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<SyncRecord> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "fetchedAt",
    accessorKey: "fetchedAt",
    header: t("pages.syncRecords.columns.fetchedAt"),
    sorter: true,
    sortKey: "fetched_at",
    cell: ({ row }: { row: Row<SyncRecord> }) => <DateColumn date={row?.original?.fetched_at} />,
  },
  {
    id: "finishedAt",
    accessorKey: "finishedAt",
    header: t("pages.syncRecords.columns.finishedAt"),
    sorter: true,
    sortKey: "finishedAt",
    cell: ({ row }: { row: Row<SyncRecord> }) => <DateColumn date={row?.original?.finished_at} />,
  },
  {
    id: "publishedAt",
    accessorKey: "publishedAt",
    header: t("pages.syncRecords.columns.publishedAt"),
    sorter: true,
    sortKey: "published_at",
    cell: ({ row }: { row: Row<SyncRecord> }) => <DateColumn date={row?.original?.published_at} />,
  },
  {
    id: "transformedAt",
    accessorKey: "transformedAt",
    header: t("pages.syncRecords.columns.transformedAt"),
    sorter: true,
    sortKey: "transformed_at",
    cell: ({ row }: { row: Row<SyncRecord> }) => (
      <DateColumn date={row?.original?.transformed_at} />
    ),
  },
  {
    id: "actions",
    header: t("pages.syncRecords.columns.actions"),
    cell: ({ row }: { row: Row<SyncRecord> }) => <ActionCell row={row} />,
  },
];

const ActionCell = ({ row }: { row: Row<SyncRecord> }) => {
  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          href: `/sync-records/${encodeURIComponent(row?.original?.id)}`,
        },
      ]}
    />
  );
};

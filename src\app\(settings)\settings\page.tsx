"use client";

import Image from "next/image";
import Link from "next/link";
import { useTranslation } from "react-i18next";

import { Logs, POSSetups, StoreSetups } from "@/features/settings/constant";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import toNavUrl from "@/utils/helpers/nav-url-formater";

export interface SettingBox {
  title: string;
  description: string;
  icon?: any;
  to: string;
  img?: string;
}

const BoxSetting = ({ title, description, icon: Icon, to, img = "" }: SettingBox) => {
  const { t } = useTranslation();

  return (
    <Link href={toNavUrl(to)}>
      <Button
        variant="outline"
        className="flex size-full items-center justify-start p-4 hover:bg-primary/5">
        <div className="flex items-center gap-3">
          {Icon && <Icon className="size-6 text-primary" />}
          {img && <Image src={img} alt={title} width={32} height={32} />}
          <div className="text-left">
            <p className=" text-lg font-medium">{t(title)}</p>
            <p className=" text-wrap text-sm text-muted-foreground">{t(description)}</p>
          </div>
        </div>
      </Button>
    </Link>
  );
};

export default function SettingsPage() {
  const { t } = useTranslation();

  return (
    <div className="space-y-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle>{t("Store setups")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {StoreSetups.map((item, index) => (
              <BoxSetting key={index} {...item} />
            ))}
          </div>
        </CardContent>
      </Card>

      {POSSetups.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t("Sales setups")}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {POSSetups.map((item, index) => (
                <BoxSetting key={index} {...item} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>{t("Logs")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Logs.map((item, index) => (
              <BoxSetting key={index} {...item} />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

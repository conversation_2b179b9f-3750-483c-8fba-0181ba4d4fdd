"use client";

import { useEffect, useMemo, useState } from "react";
import { t } from "i18next";
import debounce from "lodash/debounce";
import { Search } from "lucide-react";

import StaffSelection from "@/features/bots/staff/components/interact/staff-selection";
import { VirtualStaffModel } from "@/features/bots/staff/hooks/type";

import NoImage from "@/assets/images/NoImage.png";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";

import { useGetChatList } from "../hooks/use-conversation";
import ConversationItem from "./conversation-item";

export interface CONVERSATION {
  role: string;
  company_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  id: string;
  assignee_id: string;
}

interface ChatListProps {
  onChangeConversation: (conversation: CONVERSATION) => void;
  onChangeSelectedStaff?: (staff: VirtualStaffModel) => void;
}

export const ChatListConversation: React.FC<ChatListProps> = ({
  onChangeConversation,
  onChangeSelectedStaff,
}: ChatListProps) => {
  const [conversationActive, setConversationActive] = useState<CONVERSATION | undefined>(undefined);
  const [searchInput, setSearchInput] = useState("");
  const [search, setSearch] = useState("");
  const allStaffOption = {
    id: undefined,
    image: NoImage.src,
    name: "All Staff",
    role: undefined,
  };
  const [searchChatBot, setSearchChatBot] = useState<VirtualStaffModel>(
    allStaffOption as unknown as VirtualStaffModel
  );
  const { chatList, isLoading } = useGetChatList({ assignee_id: searchChatBot?.id });

  useEffect(() => {
    if (chatList.length > 0 && !conversationActive) {
      setConversationActive(chatList[0]);
    }
  }, [chatList]);

  useEffect(() => {
    if (conversationActive) {
      onChangeConversation(conversationActive);
    }
  }, [conversationActive, onChangeConversation]);

  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearch(value);
      }, 300),
    []
  );

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  useEffect(() => {
    debouncedSearch(searchInput);
  }, [searchInput, debouncedSearch]);

  function formatDate(dateString: string): string {
    const inputDate = new Date(dateString);
    const now = new Date();

    const inputDay = new Date(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate());
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const diffTime = today.getTime() - inputDay.getTime();
    const diffDays = diffTime / (1000 * 60 * 60 * 24);

    if (diffDays === 0) {
      return "Today";
    } else if (diffDays === 1) {
      return "Yesterday";
    } else if (diffDays > 1) {
      return `${diffDays} days ago`;
    } else {
      return "In the future";
    }
  }

  const handleStaffSelection = (staff: VirtualStaffModel) => {
    setSearchChatBot(staff);
    onChangeSelectedStaff?.(staff);
  };

  return (
    <div className="flex flex-col  gap-2 max-md:max-h-[500px] md:flex-1 md:overflow-hidden md:pr-2 ">
      <div className="flex items-center justify-between px-1">
        <StaffSelection currentStaff={searchChatBot} onSelect={handleStaffSelection} />
        <div className="flex items-center gap-1">
          <span className="text-sm font-medium">Unread</span>
          <Switch />
        </div>
      </div>
      <div className=" px-1 pt-4">
        <Input
          className="w-full"
          placeholder={t("Search")}
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
          leftIcon={<Search size={16} />}
        />
      </div>
      <div className="flex-1 space-y-2 overflow-y-auto ">
        {isLoading
          ? Array.from({ length: 5 }).map((_, idx) => <ConversationSkeleton key={idx} />)
          : chatList.map((conv) => (
              <ConversationItem
                key={conv.id}
                conversation={conv}
                isActive={conversationActive?.id === conv.id}
                onClick={() => setConversationActive(conv)}
                formatDate={formatDate}
              />
            ))}
      </div>
    </div>
  );
};

const ConversationSkeleton = () => {
  return (
    <div className="flex animate-pulse items-center justify-between rounded-lg bg-muted p-3">
      <div className="min-w-0 flex-1">
        <div className="mb-2 flex items-center justify-between">
          <div className="h-4 w-1/3 rounded bg-muted" />
          <div className="ml-2 h-3 w-1/6 rounded bg-muted" />
        </div>
        <div className="h-3 w-1/4 rounded bg-muted" />
      </div>
      <div className="relative ml-3 size-7 rounded-full border border-dashed border-border bg-muted" />
    </div>
  );
};

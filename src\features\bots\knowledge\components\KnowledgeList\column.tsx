import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";
import { FileText, FileType, Globe } from "lucide-react";

import { Knowledge } from "@/features/bots/knowledge/types";

import { ByteColumn, DateColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export const columns = (
  useDeleteKnowledgeMutation: any,
  isDeleting: boolean,
  t: any
): CustomColumn<Knowledge>[] => [
  {
    id: "name",
    accessorKey: "name",
    header: t("pages.knowledge.headers.file"),
    cell: ({ row }: { row: Row<Knowledge> }) => {
      const knowledge = row.original;
      const type = row.original.source;
      return (
        <div className="flex items-center gap-2 text-primary">
          {type === "FILE" ? (
            <FileText size={18} className="flex-none" />
          ) : type === "URL" ? (
            <Globe size={18} className="flex-none" />
          ) : (
            <FileType size={18} className="flex-none" />
          )}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="cursor-pointer truncate font-medium hover:underline">
                  {knowledge.name}
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p>{knowledge.name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      );
    },
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.knowledge.headers.status"),
    cell: ({ row }: { row: Row<Knowledge> }) => {
      const status = row.original.status;
      let variant = "";
      const label = t(`pages.knowledge.status.${status.toLowerCase()}`);
      switch (status.toLowerCase()) {
        case "pending":
          variant = "yellow";
          break;
        case "processing":
          variant = "orange";
          break;
        case "ready":
          variant = "green";
          break;
        case "error":
          variant = "destructive";
          break;
        default:
          variant = "sematic_default";
          break;
      }
      return (
        <Badge variant={variant as any} className="text-xs font-semibold hover:no-underline">
          {label}
        </Badge>
      );
    },
  },
  {
    id: "size",
    accessorKey: "size",
    sorter: true,
    sortKey: "size",
    header: t("pages.knowledge.headers.size"),
    cell: ({ row }: { row: Row<Knowledge> }) => <ByteColumn bytes={row.original.size} />,
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    sorter: true,
    sortKey: "updated_at",
    header: t("pages.knowledge.headers.updatedAt"),
    cell: ({ row }: { row: Row<Knowledge> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: "",
    cell: ({ row }: { row: Row<Knowledge> }) => (
      <ActionCell
        useDeleteKnowledgeMutation={useDeleteKnowledgeMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];

const ActionCell = ({
  useDeleteKnowledgeMutation,
  row,
  isDeleting,
}: {
  useDeleteKnowledgeMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Knowledge>;
}) => {
  const router = useRouter();
  const knowledge = row.original;
  // const { refetch } = useProducts();
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    window.location.href = `/knowledge/${knowledge.id}`;
  }, [knowledge.id]);

  const handleEdit = useCallback(() => {
    window.location.href = `/knowledge/${knowledge.id}/edit`;
  }, [knowledge.id]);

  const handleDelete = useCallback(async () => {
    return useDeleteKnowledgeMutation.mutateAsync(knowledge.id);
  }, [useDeleteKnowledgeMutation, knowledge.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

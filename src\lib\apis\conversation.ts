import { MESSAGE } from "@/features/xbots/hooks/types";

import config from "@/config";
import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi, publicApi } from "../api_helper";
import { ResponseList } from "./types/common";

interface CONVERSATION {
  id: string;
  customer_name: string;
  customer_phone_number: string;
  created_at: string;
  updated_at: string;
  company_id: string;
  role: string;
  assignee_id: string;
  name: string;
}

interface MESSENGER_CONVERSATION {
  message?: string;
  role?: string;
  external_user_id?: string;
  connection_id?: string;
  user_id?: string;
  name?: string;
  phone_number?: string;
  content?: string;
}

interface CREATE_CONVERSATION {
  customer_name: string;
  customer_phone_number: string;
  assignee_id: string;
}

interface ChatMessengerResponse {
  response: string;
  conversation_id: string;
}

const baseURL = config.API_URL_ONEXBOTS;

export const conversationApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<CONVERSATION>>(ENDPOINTS.CONVERSATION.LIST, {
      params,
    });
  },
  getMessenger: async (id: string, params: any) => {
    return await privateApi.get<ResponseList<MESSAGE>>(
      ENDPOINTS.CONVERSATION.GET_LIST_MESSENGER(id),
      { params }
    );
  },
  chatMessenger: async (id: string, payload: MESSENGER_CONVERSATION) => {
    return await publicApi.post<MESSAGE>(
      `${baseURL}${ENDPOINTS.CONVERSATION.CHAT_MESSENGER(id)}`,
      payload
    );
  },
  createMessage: async (id: string, payload: MESSENGER_CONVERSATION) => {
    return await privateApi.post<MESSAGE>(ENDPOINTS.CONVERSATION.CREATE_MESSAGE(id), payload);
  },
  createConversation: async (payload: CREATE_CONVERSATION) => {
    return await publicApi.post<ResponseList<CONVERSATION>>(
      ENDPOINTS.CONVERSATION.CREATE_CONVERSATION,
      payload
    );
  },
  streamChatMessenger: async (
    id: string,
    payload: MESSENGER_CONVERSATION,
    onChunk: (chunk: { content: string; conversation_id: string; done: boolean }) => void
  ) => {
    let lastContent = "";
    const url = `${baseURL}${ENDPOINTS.CONVERSATION.CHAT_MESSENGER(id)}`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });

    if (!response.body) throw new Error("No response body for streaming");
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";
    let done = false;
    while (!done) {
      const { value, done: streamDone } = await reader.read();
      if (value) {
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";
        for (const line of lines) {
          const trimmed = line.trim();
          if (trimmed.startsWith("data: ")) {
            try {
              const jsonStr = trimmed.replace("data: ", "").trim();
              if (jsonStr) {
                const data = JSON.parse(jsonStr);
                if (data.content) {
                  lastContent = data.content;
                }
                if (data.done && !data.content) {
                  data.content = lastContent;
                }
                onChunk(data);
              }
            } catch (e) {
              console.error("Error parsing SSE chunk:", e);
            }
          }
        }
      }
      done = streamDone;
    }
    return response;
  },
};

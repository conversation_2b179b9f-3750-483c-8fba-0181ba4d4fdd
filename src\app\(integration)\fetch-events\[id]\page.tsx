"use client";

import { useParams } from "next/navigation";
import { useTranslation } from "react-i18next";

import EventItem from "@/features/integration/FetchEvents/FetchEventDetail/event-item";
import { useFetchEventDetail } from "@/features/integration/hooks/fetch-events";

import { Card, CardTitle, Skeleton } from "@/components/ui";
import { Badge } from "@/components/ui/badge";

export default function FetchEventDetailPage() {
  const { id } = useParams();
  const { t } = useTranslation();
  const { data: eventDetail, isLoading, error } = useFetchEventDetail(id as string);
  if (error) return <div>Error loading event details</div>;

  return (
    <div className="flex h-fit w-full flex-col gap-y-4 p-4 pt-0">
      <div className="flex items-center gap-x-4">
        <CardTitle>
          {t("pages.fetchEventDetail.title")} {id}
        </CardTitle>
        <Badge className="rounded-full bg-primary">{eventDetail?.data?.length || 0}</Badge>
      </div>
      {isLoading ? (
        <div className=" flex flex-col gap-4">
          {[...Array(2)].map((_, index) => (
            <Card key={`skeleton-${index}`} className="gap-4 overflow-hidden p-4">
              <h1 className="mb-6 text-xl font-semibold">
                <Skeleton className="h-8 w-[400px]" />
              </h1>
              <div className="grid grid-cols-1 md:grid-cols-2 md:gap-4">
                {[...Array(8)].map((_, idx) => (
                  <div key={`skeleton-row-${index}-${idx}`} className="border-b py-3">
                    <Skeleton className="h-6" />
                  </div>
                ))}
              </div>
            </Card>
          ))}
        </div>
      ) : eventDetail?.data?.length ? (
        <div className="flex flex-col gap-4">
          {eventDetail.data.map((item, index) => (
            <EventItem key={item.id || `event-${index}`} data={item} index={index} />
          ))}
        </div>
      ) : (
        <Card className="p-4 text-center">No data available</Card>
      )}
    </div>
  );
}

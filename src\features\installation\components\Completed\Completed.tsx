"use client";

import dynamic from "next/dynamic";

import LogoBackground from "@/assets/images/logo-opacity-0dot05.png";
import LogoTopTextBottom from "@/assets/images/logo-top-text-bottom.svg";
import AnimationDone from "@/assets/lottie/AnimationDone.json";
import { Button } from "@/components/ui";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

export const Completed: React.FC = () => {
  return (
    <div
      className="min-h-screen"
      style={{
        backgroundImage: `url(${LogoBackground.src})`,
        backgroundSize: "auto 100%",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}>
      <div className="flex h-auto justify-center pt-32">
        <LogoTopTextBottom />
      </div>
      <div className="flex w-full justify-center pt-10 text-4xl font-bold">Connect Completed!</div>
      <div className="flex w-full justify-center overflow-hidden pt-10">
        <Lottie animationData={AnimationDone} loop={false} className="-mb-16 -mt-28 size-96" />
      </div>
      <div className="flex w-full justify-center pt-10">
        <Button className="w-10/12 md:w-1/3 lg:w-1/3">Go to Dashboard</Button>
      </div>
    </div>
  );
};

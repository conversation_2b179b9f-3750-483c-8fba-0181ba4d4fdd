"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ArrowLeftIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { BrandSection } from "@/features/auth/components/brand-section";
import { Footer } from "@/features/auth/components/public-footer";
import { useForgotPassword } from "@/features/auth/hooks/useForgotPassword";
import { useVerifyCodeReset } from "@/features/auth/hooks/useVerifyCodeReset";

import { Button } from "@/components/ui/button";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";

export const VerifyCodeReset = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const username = searchParams.get("username");
  const { t } = useTranslation();
  const [code, setCode] = useState("");

  const {
    onSubmit: verifyCode,
    loading: verifyLoading,
    error: verifyError,
    reset: resetVerifyMutation,
  } = useVerifyCodeReset();
  const {
    onForgotPasswordSubmit: resendCode,
    loading: resendLoading,
    countdown,
    isCheckingDelay,
    checkCountdown,
  } = useForgotPassword();

  useEffect(() => {
    if (!username) {
      router.push("/forgot-password");
      return;
    }

    checkCountdown({ username });
  }, [username]);

  const handleVerifyCode = (code: string) => {
    if (!username) return;
    verifyCode({ username, code });
  };

  const handleResendCode = () => {
    if (!username || countdown > 0 || isCheckingDelay) return;
    resetVerifyMutation();
    resendCode({ email: username });
    setCode("");
  };

  if (!username) {
    router.replace("/forgot-password");
    return null;
  }

  const isLoading = verifyLoading || resendLoading;

  return (
    <div className="min-h-screen w-screen bg-bg-secondary">
      <div className="grid min-h-screen w-screen lg:grid-cols-[1fr_2fr]">
        <BrandSection />
        <div className="relative flex flex-col justify-between p-8">
          <div className="flex flex-1 items-center justify-center">
            <div className="relative z-10 w-full max-w-[400px] space-y-6">
              <div className="space-y-1.5 text-center">
                <h1 className="text-2xl font-semibold tracking-tight">
                  {t("auth.verificationCode")}
                </h1>
                <p className="text-sm text-muted-foreground">
                  {t("auth.verificationCodeDescription", { username })}
                </p>
              </div>

              <form
                className="space-y-6"
                onSubmit={(e) => {
                  e.preventDefault();
                  handleVerifyCode(code);
                }}>
                <div className="flex justify-center">
                  <InputOTP
                    maxLength={6}
                    value={code}
                    onChange={(value) => {
                      setCode(value);
                      if (value.length === 6) {
                        handleVerifyCode(value);
                      }
                    }}
                    onFocus={() => {
                      resetVerifyMutation();
                    }}
                    disabled={isLoading}
                    containerClassName="gap-4">
                    <InputOTPGroup>
                      <InputOTPSlot index={0} className="size-14 text-lg" />
                      <InputOTPSlot index={1} className="size-14 text-lg" />
                      <InputOTPSlot index={2} className="size-14 text-lg" />
                      <InputOTPSlot index={3} className="size-14 text-lg" />
                      <InputOTPSlot index={4} className="size-14 text-lg" />
                      <InputOTPSlot index={5} className="size-14 text-lg" />
                    </InputOTPGroup>
                  </InputOTP>
                </div>

                <div className="space-y-4 text-center">
                  {verifyError && (
                    <div className="text-sm text-red-500">{t(String(verifyError))}</div>
                  )}
                  <Button
                    type="button"
                    variant="link"
                    className="h-fit p-0 text-sm leading-none text-primary hover:text-primary/90 disabled:cursor-not-allowed disabled:text-muted-foreground"
                    onClick={handleResendCode}
                    disabled={isLoading || countdown > 0 || isCheckingDelay}>
                    {countdown > 0
                      ? t("auth.resendCodeCountdown", { seconds: countdown })
                      : isCheckingDelay
                        ? t("common.loading")
                        : t("auth.sendNewCode")}
                  </Button>

                  <div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="md"
                      className="text-sm leading-none"
                      onClick={() => router.push("/forgot-password")}
                      disabled={isLoading}>
                      <ArrowLeftIcon className="size-4 text-primary" />
                      {t("auth.backToForgotPassword")}
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <Footer />
        </div>
      </div>
    </div>
  );
};

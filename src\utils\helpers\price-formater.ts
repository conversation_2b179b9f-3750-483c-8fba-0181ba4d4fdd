export const formatPrice = (value: string | number | readonly string[] | undefined): string => {
  if (!value) return "";
  // Remove any non-digit characters
  const number = value.toString().replace(/\D/g, "");
  // Format with thousand separators
  return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

export const parsePrice = (value: string): number => {
  // Remove commas and convert to number
  return Number(value.replace(/,/g, ""));
};

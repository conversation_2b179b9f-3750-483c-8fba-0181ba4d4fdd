"use client";

import { useState } from "react";
import { getCoreRowModel, Row, Table, useReactTable } from "@tanstack/react-table";
import { ChevronDown, ChevronRight } from "lucide-react";

import useDatatable from "@/components/custom-table/hooks/use-data-table";
import { CustomColumn, DataTable, ExpandableConfig } from "@/components/data-table/data-table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

interface TableContainerProps<TData> {
  columns: CustomColumn<TData>[];
  data: TData[];
  loading?: boolean;
  total: number;
  pageSize: number;
  currentPage: number;
  expandable?: ExpandableConfig<TData>;
  selectable?: boolean;
  onSelectionChange?: (selectedRows: TData[]) => void;
  onHandleDelete?: (listIndexId: number[], setRows: () => void) => void;
  allowDelete?: boolean;
}

export function TableContainer<TData>({
  columns: userColumns,
  data,
  loading,
  total,
  pageSize,
  currentPage,
  expandable,
  selectable = true,
  onSelectionChange,
  onHandleDelete,
  allowDelete = true,
}: TableContainerProps<TData>) {
  const { handlePageChange, handlePageSizeChange, handleSort, getInitialParams, handleExport } =
    useDatatable();
  const [selectedRows, setSelectedRows] = useState<Record<string, boolean>>({});
  const [expandedRows, setExpandedRows] = useState<string[]>([]);
  const [expandedRow, setExpandedRow] = useState<string | null>(null);
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>(() => {
    const initialVisibility: Record<string, boolean> = {};
    userColumns.forEach((column) => {
      if (column.id && column.hidden !== undefined) {
        initialVisibility[column.id] = !column.hidden;
      }
    });
    return initialVisibility;
  });

  const toggleRowExpansion = (rowId: string) => {
    setExpandedRow((prev) => (prev === rowId ? null : rowId));
  };
  const onDelete = (listIndexId: number[]) => {
    if (onHandleDelete) {
      onHandleDelete(listIndexId, () => {
        setSelectedRows({});
      });
    }
  };
  const isRowExpanded = (rowId: string) => expandedRow === rowId;

  const handleSelectAll = (checked: boolean) => {
    const newSelected: Record<string, boolean> = {};
    table.getRowModel().rows.forEach((row) => {
      newSelected[row.id] = checked;
    });
    setSelectedRows(newSelected);
    if (onSelectionChange) {
      const selectedData = checked ? data : [];
      onSelectionChange(selectedData);
    }
  };

  const handleSelectRow = (rowId: string, checked: boolean) => {
    setSelectedRows((prev) => ({ ...prev, [rowId]: checked }));
    if (onSelectionChange) {
      const newSelectedRows = {
        ...selectedRows,
        [rowId]: checked,
      };
      const selectedData = data.filter((_, index) => newSelectedRows[String(index)]);
      onSelectionChange(selectedData);
    }
  };
  const onExport = () => {
    handleExport({ data, filename: "export" });
  };
  const columns = [
    ...(selectable
      ? [
          {
            id: "select",
            header: ({ table }: { table: Table<TData> }) => (
              <div className="size-4">
                <Checkbox
                  checked={
                    table.getRowModel().rows.length > 0 &&
                    table.getRowModel().rows.every((row) => selectedRows[row.id])
                  }
                  onCheckedChange={handleSelectAll}
                />
              </div>
            ),
            cell: ({ row }: { row: Row<TData> }) => (
              <div className="size-4">
                <Checkbox
                  checked={selectedRows[row.id]}
                  onCheckedChange={(checked: boolean) => handleSelectRow(row.id, checked)}
                />
              </div>
            ),
          },
        ]
      : []),
    ...(expandable?.enabled
      ? [
          {
            id: "expand",
            header: () => null,
            cell: ({ row }: { row: Row<TData> }) =>
              expandable?.enabled ? (
                <Button
                  role="expand"
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleRowExpansion(row.id);
                  }}>
                  {isRowExpanded(row.id) ? (
                    <ChevronDown className="size-4" />
                  ) : (
                    <ChevronRight className="size-4" />
                  )}
                </Button>
              ) : null,
          },
        ]
      : []),
    ...userColumns,
  ];
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    defaultColumn: {
      minSize: 0,
      size: Number.MAX_SAFE_INTEGER,
      maxSize: Number.MAX_SAFE_INTEGER,
    },
    state: {
      columnVisibility,
    },
    onColumnVisibilityChange: setColumnVisibility,
  });
  return (
    <div id="data-table-wrapper" className="flex flex-auto flex-col space-y-0 overflow-hidden">
      <DataTable
        table={table}
        columns={columns}
        loading={loading}
        expandable={expandable}
        total={total}
        pageSize={pageSize}
        currentPage={currentPage}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        selectedRows={selectedRows}
        expandedRows={[expandedRow].filter(Boolean) as string[]}
        handleSort={handleSort}
        getInitialParams={getInitialParams}
        onDelete={onDelete}
        // onRowClick={expandable?.enabled ? toggleRowExpansion : undefined}
        allowDelete={allowDelete}
      />
    </div>
  );
}

import { publicPaths } from '@/middleware';
import { test, expect } from '@playwright/test';
import { AUTH_ENDPOINTS } from "@/constants/endpoints";
import config from '@/config';

const BASE_URL = "http://localhost:3000"
const loginPath = publicPaths[0]
const signupPath = publicPaths[1]
const forgotpasswordPath = publicPaths[2]
const verifycodePath = publicPaths[4]
const API_URL = config.API_URL

const testInput = {
  email: '<EMAIL>',
  username: 'trannhon',
  password: 'Admin@123',
}

const forgotPasswordApiUrl = `${API_URL}${AUTH_ENDPOINTS.FORGOT}`;
const changePasswordApiUrl = `${API_URL}${AUTH_ENDPOINTS.CHANGE_PASSWORD}`;

test.describe('Test apis', () => {
  test('change password', async ({ page, request }) => {
    // Reset password code has been sent to your email
    await page.route(forgotPasswordApiUrl, async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify({ success: true, message: "Mocked API response" }),
      });
    });
    await page.route(changePasswordApiUrl, async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify({ success: true, message: "Mocked API response" }),
      });
    });
    await page.route(`${API_URL}${AUTH_ENDPOINTS.VERIFY_CODE}`, async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify({ success: true, message: "Mocked API response" }),
      });
    });

    await page.goto(`${BASE_URL}${loginPath}`); // Replace with your actual login URL

    await page.locator('button:has-text("Forgot?")').click();

    await expect(page).toHaveURL(`${BASE_URL}${forgotpasswordPath}`);

    await page.fill('input[name="email"]', testInput.email);

    await page.click('button[type="submit"]');

    await page.getByText('Reset password code has been sent to your email').waitFor();

    await expect(page).toHaveURL(/http:\/\/localhost:3000\/reset-password.*/);

    await page.fill('input[name="code"]', '111111');
    await page.fill('input[name="newPassword"]', testInput.password);
    await page.fill('input[name="confirmPassword"]', testInput.password);

    await page.click('button[type="submit"]');

    await page.getByText('Password has been reset successfully').waitFor();
  })
})

// npx playwright test E2Etests/ForgotPassword.test.ts

import { Transformation } from "../hooks/types";

export interface MappingField {
  sourceField: string;
  destinationField: string;
  required?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  optional?: boolean;
  enabled?: boolean;
  hasAdvancedMapping?: boolean;
  transformations?: Transformation[];
  transformationCount?: number;
}

export interface MappingCategory {
  id: string;
  name: string;
  label: string;
  errors: number;
  mapped: number;
  total: number;
  fields: MappingField[];
}

export const mockCategories: MappingCategory[] = [
  {
    id: "product",
    name: "Product attributes",
    label: "Product attributes",
    errors: 2,
    mapped: 3,
    total: 5,
    fields: [
      {
        sourceField: "Product Title",
        destinationField: "Title",
        required: true,
        enabled: true,
        // Simple mapping - no transformations needed
      },
      {
        sourceField: "Description",
        destinationField: "Description",
        optional: true,
        enabled: true,
        hasAdvancedMapping: true,
        transformations: [
          {
            type: "replace",
            config: {
              pattern: "<[^>]*>",
              replacement: "",
            },
          },
          {
            type: "condition",
            config: {
              maxLength: 500,
              addEllipsis: true,
            },
          },
        ],
      },
      {
        sourceField: "Price",
        destinationField: "Price",
        required: true,
        enabled: true,
        hasAdvancedMapping: true,
        transformations: [
          {
            type: "math",
            config: {
              operation: "multiply",
              value: 1.1, // USD to EUR conversion
            },
          },
        ],
      },
      {
        sourceField: "SKU",
        destinationField: "Price",
        required: true,
        hasError: true,
        errorMessage: "Invalid field type",
        enabled: false,
      },
      {
        sourceField: "Brand",
        destinationField: "Product title, Description",
        optional: true,
        enabled: true,
        hasAdvancedMapping: true,
        transformations: [
          {
            type: "concat",
            config: {
              template: "{Brand} - {Product Title}",
              fields: ["Brand", "Product Title"],
            },
          },
        ],
      },
    ],
  },
  {
    id: "category",
    name: "Category attributes",
    label: "Category attributes",
    errors: 0,
    mapped: 2,
    total: 2,
    fields: [
      {
        sourceField: "Category Path",
        destinationField: "Category",
        required: true,
        enabled: true,
        hasAdvancedMapping: true,
        transformations: [
          {
            type: "split",
            config: {
              delimiter: " > ",
              maxDepth: 3,
            },
          },
        ],
      },
      {
        sourceField: "Sub-Category",
        destinationField: "Sub-Category",
        optional: true,
        enabled: false,
      },
    ],
  },
  {
    id: "pricing",
    name: "Pricing attributes",
    label: "Pricing attributes",
    errors: 1,
    mapped: 1,
    total: 2,
    fields: [
      {
        sourceField: "Price",
        destinationField: "Price",
        required: true,
        enabled: true,
        hasAdvancedMapping: true,
        transformations: [
          {
            type: "math",
            config: {
              operation: "multiply",
              value: 1.1,
            },
          },
        ],
      },
      {
        sourceField: "",
        destinationField: "Sale Price",
        optional: true,
        enabled: false,
      },
    ],
  },
];

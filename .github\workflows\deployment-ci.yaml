name: 🚀 Vercel Deployment

on:
  push:
    branches:
      - main
      - dev
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        project:
          - name: optiwarehouse-app
            org_id: ${{ secrets.VERCEL_ORG_ID }}
            project_id: ${{ secrets.VERCEL_PROJECT_ID }}
            token: ${{ secrets.VERCEL_TOKEN }}
          - name: onexbots
            org_id: ${{ secrets.VERCEL_ORG_ID }}
            project_id: ${{ secrets.VERCEL_PROJECT_ID_ONXBOTS }}
            token: ${{ secrets.VERCEL_TOKEN }}
          - name: onexsync
            org_id: ${{ secrets.VERCEL_ORG_ID }}
            project_id: ${{ secrets.VERCEL_PROJECT_ID_ONEXSYNC }}
            token: ${{ secrets.VERCEL_TOKEN }}

    environment: ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}

    steps:
      - name: 📥 Checkout
        uses: actions/checkout@v4

      - name: 🛠️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "yarn"

      - name: 📦 Install Yarn
        run: npm install -g yarn

      - name: 🔧 Install Vercel CLI
        run: yarn global add vercel@latest

      - name: 📚 Install Dependencies
        run: yarn install

      - name: ⚡ Pull Vercel Environment Information
        env:
          VERCEL_ORG_ID: ${{ matrix.project.org_id }}
          VERCEL_PROJECT_ID: ${{ matrix.project.project_id }}
        run: vercel pull --yes --environment=${{ github.ref == 'refs/heads/main' && 'production' || 'preview' }} --token=${{ matrix.project.token }}

      - name: 🏗️ Build Project Artifacts
        env:
          VERCEL_ORG_ID: ${{ matrix.project.org_id }}
          VERCEL_PROJECT_ID: ${{ matrix.project.project_id }}
        run: vercel build ${{ github.ref == 'refs/heads/main' && '--prod' || '' }} --token=${{ matrix.project.token }}

      - name: 🚀 Deploy to Vercel
        id: deploy
        env:
          VERCEL_ORG_ID: ${{ matrix.project.org_id }}
          VERCEL_PROJECT_ID: ${{ matrix.project.project_id }}
        run: |
          set +e
          DEPLOYMENT_URL=$(vercel deploy --prebuilt ${{ github.ref == 'refs/heads/main' && '--prod' || '' }} --token=${{ matrix.project.token }})
          DEPLOY_EXIT_CODE=$?
          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV
          echo "DEPLOY_EXIT_CODE=$DEPLOY_EXIT_CODE" >> $GITHUB_ENV
          exit $DEPLOY_EXIT_CODE

      - name: 📢 Send Success Notification
        if: success()
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"
          
          # Format the title (first line of commit message)
          TITLE=$(echo "$COMMIT_MSG" | head -n1)
          FORMATTED_TITLE=$(echo "$TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')
          
          # Extract and format the body (remaining lines after the first line)
          BODY=$(echo "$COMMIT_MSG" | tail -n +2 | sed '/^$/d')
          if [ ! -z "$BODY" ]; then
            FORMATTED_BODY=$(echo "$BODY" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g' | sed 's/^/* /')
          else
            FORMATTED_BODY="No additional details"
          fi
          
          # Convert comma-separated user IDs to Slack mentions
          MENTIONS=$(echo "${{ vars.SLACK_TEST_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')
          
          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "✅ Deployment Successful!",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n`${{ github.ref_name }}`"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Deployment URL:*\n<${{ github.ref == 'refs/heads/dev' && 'https://dev.optiwarehouse.com' || env.DEPLOYMENT_URL }}|View Deployment> 🔗"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Summary:*\n'"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Details:*\n'"$FORMATTED_BODY"'"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "cc: '"$MENTIONS"'"
                  }
                ]
              },
              {
                "type": "divider"
              }
            ]
          }' ${{ vars.DEPLOY_WEBHOOK_URL }}

      - name: 📢 Send Failure Notification
        if: failure()
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"
          
          # Format the title (first line of commit message)
          TITLE=$(echo "$COMMIT_MSG" | head -n1)
          FORMATTED_TITLE=$(echo "$TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')
          
          # Extract and format the body (remaining lines after the first line)
          BODY=$(echo "$COMMIT_MSG" | tail -n +2 | sed '/^$/d')
          if [ ! -z "$BODY" ]; then
            FORMATTED_BODY=$(echo "$BODY" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g' | sed 's/^/* /')
          else
            FORMATTED_BODY="No additional details"
          fi
          
          # Convert comma-separated user IDs to Slack mentions
          MENTIONS=$(echo "${{ vars.SLACK_DEV_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')
          
          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "❌ Deployment Failed!",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n`${{ github.ref_name }}`"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Summary:*\n'"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Details:*\n'"$FORMATTED_BODY"'"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Workflow Details:*\n<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run> 🔍"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "🚨 '"$MENTIONS"' Please check!"
                  }
                ]
              },
              {
                "type": "divider"
              }
            ]
          }' ${{ vars.REVIEW_WEBHOOK_URL }}

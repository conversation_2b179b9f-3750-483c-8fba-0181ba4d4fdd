import { Product } from "@/features/products/hooks/types";

export enum MappingProductStatus {
  SYNCED = "synced",
  MAPPED = "mapped",
  UNMAPPED = "unmapped",
  ERROR = "error",
  MAP = "MAPPED",
  SYNC = "SYNCED",
}

// Define a mapping from API uppercase values to enum values
export const mapApiStatusToEnum = (apiStatus: string): MappingProductStatus => {
  switch (apiStatus.toUpperCase()) {
    case "SYNCED":
      return MappingProductStatus.SYNCED;
    case "MAPPED":
      return MappingProductStatus.MAPPED;
    case "UNMAPPED":
      return MappingProductStatus.UNMAPPED;
    case "ERROR":
    case "ERRORS":
      return MappingProductStatus.ERROR;
    default:
      return MappingProductStatus.UNMAPPED; // Default fallback
  }
};

export interface MappingProduct {
  id: string;
  standard_source_data: Product;
  standard_destination_data?: Product;
  price: number;
  last_sync: string;
  mapping_status: MappingProductStatus;
  channel: string;
  record_type: string;
  connection_id: string;
}

// ProductMappingDetail types based on the provided response
export interface ProductVariant {
  name: string;
  id: string;
  price?: number;
  images?: string;
  sku?: string;
  title?: string;
}

export interface SourceProductData {
  name: string;
  id: string;
  variants: ProductVariant[];
  title: string | null;
  price: string;
}

export interface StandardData {
  name: string;
  id: string;
  variants: ProductVariant[];
  price: string;
  image: string;
}

export interface DestinationProductData {
  external_updated_at: string;
  company_id: string;
  updated_at: string;
  external_created_at: string;
  connection_id: string;
  extra: string;
  name: string;
  created_at: string;
  id: string;
  type: string;
  standard_data: StandardData;
  variants?: Array<{
    id: string;
    name?: string;
    title?: string;
    price?: string | number;
    image?: string;
    images?: string;
  }>;
}

export interface VariantMappingData {
  [sourceVariantId: string]: string; // Maps source variant ID to destination variant ID
}

export interface OtherMappings {
  mapping_data: VariantMappingData;
  is_extra_destination_mapping: boolean;
}

export interface ProductMappingDetail {
  id: string;
  connection_id: string;
  record_type: string;
  channel: string;
  last_sync: string;
  mapping_status: string;
  standard_source_data: SourceProductData;
  standard_destination_data: DestinationProductData;
  other_mappings: OtherMappings;
}

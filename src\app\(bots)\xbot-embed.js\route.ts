import fs from "fs";
import path from "path";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log("xbot-embed.js route handler called");

    // Path to the embed script
    const filePath = path.join(process.cwd(), "src", "features", "xbots", "embed-script.ts");
    console.log(`Reading file from: ${filePath}`);

    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      return new NextResponse(`File not found: ${filePath}`, { status: 404 });
    }

    // Read the file
    const fileContent = fs.readFileSync(filePath, "utf8");
    console.log(`File size: ${fileContent.length} bytes`);

    // Remove TypeScript interfaces and declarations
    console.log("Transforming TypeScript to JavaScript");
    const jsContent = fileContent
      .replace(/\/\/ Define the XBot interface for TypeScript[\s\S]*?}\n\n/g, "")
      .replace(/interface XBotInterface[\s\S]*?}\n/g, "")
      .replace(/\/\/ Extend Window interface[\s\S]*?}\n}/g, "")
      .replace(/export \{\};/g, "")
      .replace(/as HTMLScriptElement/g, "")
      .replace(/: string/g, "")
      .replace(/\(window as any\)\.XBot/g, "window.XBot");

    console.log(`Transformed JavaScript size: ${jsContent.length} bytes`);

    // Return the JavaScript content with the appropriate content type
    return new NextResponse(jsContent, {
      headers: {
        "Content-Type": "application/javascript",
        "Cache-Control": "public, max-age=3600",
      },
    });
  } catch (error) {
    console.error("Error serving embed script:", error);
    return new NextResponse(
      `Error serving embed script: ${error instanceof Error ? error.message : String(error)}`,
      {
        status: 500,
        headers: {
          "Content-Type": "text/plain",
        },
      }
    );
  }
}

import { Customer } from "@/features/customer/hooks/type";

interface Staff {
  id: string;
  name: string;
}

interface Source {
  id: string;
  channel_name: string;
}

interface Location {
  id: string;
  name: string;
}

interface Address {
  address1: string;
  address2: string;
  city: string;
  country: string;
  country_code: string;
  default_billing: boolean;
  default_shipping: boolean;
  district: string;
  first_name: string;
  last_name: string;
  name: string;
  phone: string;
  province: string;
  province_code: string;
  ward: string;
  zip: string;
}

interface OrderLineItem {
  id: string;
  name: string;
  product_id: string;
  quantity: number;
  sku: string;
  unit_price: string;
  sale_price: string;
  variant_id: string;
  variant_name: string;
  discount: string;
  image_url: string;
  note: string;
  custom: boolean;
  location: Location;
}

export interface Order {
  company_id: string;
  created_at: string;
  updated_at: string;
  customer: Customer;
  number: string;
  id: string;
  order_type: string;
  status: string;
  note: string;
  source: Source;
  staff: Staff;
  payment_status: string;
  order_line_items: OrderLineItem[];
  total: string;
  discount: string;
  discount_by_customer_group: string;
  shift_id: string;
  sub_total: number;
  voucher_code: string;
  billing_address: Address;
  shipping_address: Address;
  location: Location;
  tags: string;
  other_fees: string;
  shipping_fee: string;
  tax: string;
  sub_voucher_total: string;
}

export interface OrderVariant {
  id: string;
  name: string;
  sku: string;
  price?: number;
  unit_price?: number;
  sale_price?: number;
  custom?: boolean;
  unit?: {
    id: string;
    name: string;
  };
  prices?: {
    price: number;
    price_group: {
      name: string;
      id: string;
    };
  }[];
  images?: {
    url: string;
  }[];
  inventories?: {
    available: number;
    on_hand: number;
    cost: number;
    company_id: string;
    location_id: string;
  }[];
}

export interface OrderItem {
  variant_id: string;
  name: string;
  sku: string;
  image: string;
  price: number;
  sale_price?: number;
  unit_price?: number;
  discount?: string | number;
  quantity: number;
  total: number;
  variant: OrderVariant;
  note?: string;
  custom?: boolean;
}

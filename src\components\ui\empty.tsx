import { PackageOpen } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function Empty() {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col items-center justify-center py-4">
      <PackageOpen className="size-12 text-gray-400" />
      <p className="mt-4 text-base font-medium ">{t("common.empty.title")}</p>
      <p className="mt-1 text-sm text-gray-500">{t("common.empty.description")}</p>
    </div>
  );
}

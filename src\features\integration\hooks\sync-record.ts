import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { syncRecordApi } from "@/lib/apis/sync-record";

import { ICommonParams, syncRecordKeys } from "./keys";

interface UseSyncRecordsOptions extends Partial<ICommonParams> {
  enabled?: boolean;
}

export function useSyncRecords(options: UseSyncRecordsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;

  const query = useInfiniteQuery({
    queryKey: syncRecordKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 1 }) =>
      syncRecordApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    enabled,
  });

  const syncRecords = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    syncRecords,
    total,
  };
}

interface UseSyncRecordDetailOptions {
  sync_record_id?: string;
  enabled?: boolean;
}

export function useSyncRecordDetail(options: UseSyncRecordDetailOptions = {}) {
  const { sync_record_id, enabled = true } = options;

  const query = useQuery({
    queryKey: sync_record_id ? syncRecordKeys.detail(sync_record_id) : ["sync-record", "detail"],
    queryFn: async () => {
      if (!sync_record_id) {
        throw new Error("Sync Record ID is required");
      }
      return syncRecordApi.detail(sync_record_id);
    },
    enabled: enabled && !!sync_record_id,
  });

  return {
    ...query,
    syncRecordData: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
  };
}

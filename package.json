{"name": "optiwarehouse-app", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "create-component": "node scripts/create-component.mjs", "create-feature": "node scripts/create-feature.mjs"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.2", "@lexical/react": "^0.27.2", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.5.1", "@tanstack/react-query": "^5.66.0", "@tanstack/react-table": "^8.20.6", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "emblor": "^1.4.7", "framer-motion": "^11.0.8", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.2", "input-otp": "^1.4.2", "lexical": "^0.27.2", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.471.2", "nanoid": "^4.0.0", "next": "14.2.16", "next-i18next": "^15.4.1", "next-themes": "^0.4.4", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.1.3", "react18-json-view": "^0.2.9", "readline-sync": "^1.4.10", "recharts": "^2.15.0", "remark-gfm": "^4.0.1", "reselect": "^5.1.1", "sass": "^1.83.4", "sharp": "^0.33.5", "sonner": "^1.7.2", "styled-components": "^6.1.14", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuidv4": "^6.2.13", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@playwright/test": "^1.50.1", "@svgr/webpack": "^8.1.0", "@tanstack/react-query-devtools": "^5.66.9", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/jest-axe": "^3.5.9", "@types/lodash": "^4.17.16", "@types/node": "^20.17.14", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "babel-jest": "^29.7.0", "eslint": "^8", "eslint-config-next": "14.2.16", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-tailwindcss": "^3.18.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-axe": "^9.0.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.5.1", "tailwindcss": "^3.4.1", "typescript": "^5", "url-loader": "^4.1.1"}}
import { useTranslation } from "react-i18next";

import { CustomImage } from "@/components/ui/image";

import { Variant } from "../../hooks/types";

interface VariantDetailsProps {
  variant: Variant;
}

export const VariantDetails = ({ variant }: VariantDetailsProps) => {
  const { t } = useTranslation();
  return (
    <div className="space-y-4">
      <h4 className="font-medium">{t("pages.products.variantDetails")}</h4>

      <div className="flex gap-4 pt-4">
        {/* Variant Image */}
        <div className="relative size-32 shrink-0 overflow-hidden rounded-lg">
          <CustomImage
            src={variant.images?.[0]?.url}
            alt={variant.name}
            fill
            className="rounded-lg object-cover"
          />
        </div>

        {/* Details Grid */}
        <div className="w-full">
          <div className="h-full space-y-4 rounded-lg bg-card">
            {/* Metadata Grid */}
            <div className="grid h-full grid-cols-1 gap-y-2">
              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.name")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">{variant.name}</span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.sku")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">{variant.sku}</span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.barcode")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.barcode || "---"}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.option1")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.option1 || "---"}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.option2")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.option2 || "---"}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.option3")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.option3 || "---"}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.unit")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.measurements?.weight_unit || "---"}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.weight")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.measurements?.weight_value} {variant.measurements?.weight_unit}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.height")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.measurements?.height_value} {variant.measurements?.height_unit}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.width")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.measurements?.width_value} {variant.measurements?.width_unit}
                </span>
              </div>

              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.length")}:</span>
                <span className="flex-1 truncate pl-2 text-sm font-medium">
                  {variant.measurements?.length_value} {variant.measurements?.length_unit}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

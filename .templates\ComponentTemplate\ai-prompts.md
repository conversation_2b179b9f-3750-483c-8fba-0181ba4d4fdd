# AI Agent Development Prompts

## Development Workflow Overview
This is a strict 7-step process that MUST be followed in order. Each step builds upon the previous ones and is required for a complete implementation.

1. [Design Analysis & Documentation](./step1-design-analysis.md)
   - Design System Analysis
   - Layout Structure Analysis
   - Visual Specifications
   - Typography & Colors
   - States & Variations
   - Responsive Design
   ✓ Output: Complete ComponentName.md with exact specifications
   → Update progress in ComponentName.md

2. [Test Case Generation](./step2-test-generation.md)
   - Create comprehensive test suite
   - Cover all specifications
   - Include accessibility tests
   ✓ Output: ComponentName.test.tsx
   → Update progress in ComponentName.md

3. [Visual Implementation](./step3-visual-implementation.md)
   - Build component structure
   - Implement styling
   - Match design exactly
   - Quality checks during development
   ✓ Output: Initial ComponentName.tsx that matches design 1:1
   → Update progress in ComponentName.md

4. [Visual Verification](./step4-visual-verification.md)
   - Compare with design
   - Verify all states
   - Check responsive behavior
   ✓ Output: Verified visual implementation
   → Update progress in ComponentName.md

5. [Functional Implementation](./step5-functional-implementation.md)
   - Add interactions
   - Implement state management
   - Ensure accessibility
   ✓ Output: Complete ComponentName.tsx
   → Update progress in ComponentName.md

6. [Testing](./step6-testing.md)
   - Run all test suites
   - Document test results
   - Fix any failures
   ✓ Output: Fully tested component
   → Update progress in ComponentName.md

7. [Finalization](./step7-finalization.md)
   - Update documentation
   - Register component
   - Create demo page
   - Final quality checks
   ✓ Output: Production-ready component
   → Update progress in ComponentName.md

Important Rules:
- ALL steps must be completed in order
- Each step must pass before moving to the next
- No skipping steps or partial implementations
- Complete all steps in a single continuous flow
- Handle minor issues and uncertainties independently
- Progress must be documented in ComponentName.md after each step
- Only ask for user input if:
  1. Design files are completely missing
  2. Critical business logic decisions are needed
  3. Authentication/API requirements are unclear
- If tests fail in step 6:
  - Visual test failures → Return to Step 3
  - Functional test failures → Return to Step 5
  - Accessibility test failures → Return to Step 5
- Maximum 3 iterations per fix attempt before escalating to user
- Document completion of each step in component file header

Progress Tracking in ComponentName.md:
```markdown
# ComponentName Development Progress

## Step 1: Design Analysis & Documentation
Status: [Complete/In Progress/Not Started]
Notes: [Any relevant notes or issues]

## Step 2: Test Case Generation
Status: [Complete/In Progress/Not Started]
Notes: [Any relevant notes or issues]

## Step 3: Visual Implementation
Status: [Complete/In Progress/Not Started]
Notes: [Any relevant notes or issues]

## Step 4: Visual Verification
Status: [Complete/In Progress/Not Started]
Notes: [Any relevant notes or issues]

## Step 5: Functional Implementation
Status: [Complete/In Progress/Not Started]
Notes: [Any relevant notes or issues]

## Step 6: Testing
Status: [Complete/In Progress/Not Started]
Notes: [Any relevant notes or issues]

## Step 7: Finalization
Status: [Complete/In Progress/Not Started]
Notes: [Any relevant notes or issues]
```

Completion Checklist:
□ Step 1: Design Analysis & Documentation
□ Step 2: Test Case Generation
□ Step 3: Visual Implementation
□ Step 4: Visual Verification
□ Step 5: Functional Implementation
□ Step 6: Testing
□ Step 7: Finalization
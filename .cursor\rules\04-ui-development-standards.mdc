---
description: 
globs: 
alwaysApply: false
---
# UI Development Standards

## Component Reusability
- Check `src/components` for existing components before creating new ones
- Common components to reuse:
  - Buttons, inputs, forms
  - Layout components
  - Modal/dialog components
  - Data display components
  - Navigation elements

## Component Structure
1. File Organization:
   ```typescript
   // ComponentName/index.tsx - Main component file
   // ComponentName/ComponentName.types.ts - Type definitions
   // ComponentName/useComponentName.ts - Component logic hook
   // ComponentName/ComponentName.test.tsx - Tests
   ```

2. Component Template:
   ```typescript
   import { type ComponentNameProps } from './ComponentName.types';
   import { useComponentName } from './useComponentName';

   export const ComponentName = ({ ...props }: ComponentNameProps) => {
     const { ...logic } = useComponentName(props);
     
     return (
       // JSX here
     );
   };
   ```

## Logic Separation
- Extract all business logic into custom hooks
- Hook naming convention: `use{ComponentName}`
- Keep components purely presentational
- Place shared hooks in `src/hooks`
- Component-specific hooks stay with component

## Clean Code Guidelines
1. Component Rules:
   - One component = one responsibility
   - Maximum 100 lines per component file
   - Extract repeated patterns into components
   - Use meaningful component and prop names

2. Props Management:
   - Define clear prop interfaces
   - Use default props when appropriate
   - Destructure props at component top level
   - Document complex props with comments

## UI Generation from Images
1. Matching Visual Design:
   - Use exact pixel measurements
   - Use semantic theme colors:
     ```typescript
     // Prefer semantic theme colors
     className="bg-primary"         // ✅ Good
     className="text-muted"        // ✅ Good
     className="border-accent"     // ✅ Good
     className="bg-blue-500"      // ❌ Avoid direct color values

     // Use theme opacity utilities
     className="bg-primary/75"     // ✅ Good
     className="text-muted/50"    // ✅ Good
     style={{ opacity: 0.75 }}   // ❌ Avoid custom opacity

     // For gradients, use theme colors
     className="bg-gradient-to-r from-primary to-accent" // ✅ Good
     ```

   - Available theme colors:
     ```typescript
     // Semantic color tokens
     - primary: Base brand color
     - secondary: Secondary brand color
     - accent: Accent color for highlights
     - muted: Subdued text and backgrounds
     - background: Page/component backgrounds
     - foreground: Primary text color
     - card: Card/container backgrounds
     - card-foreground: Text color in cards
     - popover: Popover/modal backgrounds
     - popover-foreground: Text in popovers
     - border: Border colors
     - input: Form input backgrounds
     - destructive: Error/delete actions
     - success: Success states
     - warning: Warning states
     - info: Information states
     ```

2. Implementation Steps:
   - Start with layout structure
   - Use semantic theme colors for styling
   - Implement responsive breakpoints
   - Match interactions and animations
   - Verify against original design

3. Theme Color Usage:
   ```typescript
   // Component examples using theme colors
   const Button = ({ variant = "default", ...props }) => (
     <button 
       className={cn(
         // Base styles
         "bg-primary text-primary-foreground hover:bg-primary/90",
         // Variant styles
         {
           "bg-secondary text-secondary-foreground": variant === "secondary",
           "bg-accent text-accent-foreground": variant === "accent",
           "bg-destructive text-destructive-foreground": variant === "destructive",
           "bg-muted text-muted-foreground": variant === "ghost"
         }
       )}
       {...props}
     />
   );

   const Card = ({ children }) => (
     <div className="bg-card text-card-foreground border border-border rounded-lg p-4">
       {children}
     </div>
   );

   const Alert = ({ variant = "info", children }) => (
     <div className={cn(
       "border rounded-md p-4",
       {
         "bg-info/10 border-info text-info": variant === "info",
         "bg-success/10 border-success text-success": variant === "success",
         "bg-warning/10 border-warning text-warning": variant === "warning",
         "bg-destructive/10 border-destructive text-destructive": variant === "error"
       }
     )}>
       {children}
     </div>
   );
   ```

4. Responsive Variants:
   ```typescript
   // Use responsive classes with theme colors
   className="
     bg-muted          // Mobile default
     sm:bg-background  // Small screens
     md:bg-card       // Medium screens
     lg:bg-popover   // Large screens
     dark:bg-muted   // Dark mode
   "
   ```

## Code Splitting Best Practices
1. Component Level:
   - Split large components into smaller ones
   - Create separate files for types
   - Extract repeated styles into style utilities
   - Move constants to separate files

2. Logic Level:
   - Split complex calculations into utilities
   - Extract form validation logic
   - Separate API calls into service files
   - Move state management to hooks

## Example Component Structure
```typescript
// Button/index.tsx
export const Button = ({ children, variant, ...props }: ButtonProps) => {
  const { handleClick, buttonClasses } = useButton({ variant, ...props });
  
  return (
    <button 
      className={buttonClasses}
      onClick={handleClick}
      {...props}
    >
      {children}
    </button>
  );
};

// Button/useButton.ts
export const useButton = ({ variant, ...props }: ButtonProps) => {
  const handleClick = () => {
    // Click logic here
  };

  const buttonClasses = getButtonClasses(variant);
  
  return { handleClick, buttonClasses };
};

// Button/Button.types.ts
export interface ButtonProps {
  variant?: 'primary' | 'secondary';
  // other props...
}
```

## Testing Requirements
- Test component rendering
- Test user interactions
- Test different prop combinations
- Test error states
- Test accessibility
- Test responsive behavior

## Performance Considerations
- Lazy load components when possible
- Use proper key props in lists
- Memoize expensive calculations
- Optimize re-renders
- Use image optimization
- Implement proper loading states

"use client";

import <PERSON><PERSON> from "lottie-react";

import LogoBackground from "@/assets/images/logo-opacity-0dot05.png";
import AnimationDone from "@/assets/lottie/AnimationDone.json";
import AnimationFail from "@/assets/lottie/AnimationFail.json";
import { Button } from "@/components/ui";

export type TestConnectionStatus = "success" | "fail";

interface TestConnectionProps {
  status: TestConnectionStatus;
}

export const TestConnection: React.FC<TestConnectionProps> = ({ status }) => {
  return (
    <div
      className="min-h-screen"
      style={{
        backgroundImage: `url(${LogoBackground.src})`,
        backgroundSize: "auto 100%",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}>
      {/* You can use the status prop here to render different content based on the status */}
      <div className="flex justify-center overflow-hidden pt-44">
        {status == "success" && (
          <Lottie animationData={AnimationDone} loop={false} className="-mb-16 size-80" />
        )}
        {status == "fail" && (
          <Lottie animationData={AnimationFail} loop={false} className="size-56" />
        )}
      </div>
      {status == "success" && (
        <>
          <div className="flex w-full justify-center text-3xl font-bold text-foreground">
            Test connection was successful
          </div>
          <div className="flex w-full justify-center text-xs text-muted-foreground">
            Please wait... the system will automatically redirect in a few seconds.
          </div>
        </>
      )}
      {status == "fail" && (
        <>
          <div className="flex w-full justify-center text-3xl font-bold text-foreground">
            Connection test failed
          </div>
          <div className="flex w-full justify-center pt-4">
            <div className="grid w-fit grid-cols-2 gap-2">
              <div className="col-span-1">
                <Button variant="ghost" className="mr-auto">
                  Back to setup
                </Button>
              </div>
              <div className="col-span-1">
                <Button variant="default" className="ml-auto">
                  Try again
                </Button>
              </div>
            </div>
          </div>
          <div className="flex w-full justify-center pt-6">
            <a
              href="#"
              className="relative font-bold text-primary transition duration-300 hover:underline">
              Contact Support
            </a>
          </div>
        </>
      )}
    </div>
  );
};

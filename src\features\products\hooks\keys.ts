export interface IGetProductsParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}
export const productKeys = {
  all: () => ["product"] as const,
  lists: () => [...productKeys.all(), "list"] as const,
  list: (params: IGetProductsParams) => [...productKeys.lists(), params] as const,
  details: () => [...productKeys.all(), "detail"] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
  brands: () => [...productKeys.all(), "brands"] as const,
  categories: () => [...productKeys.all(), "categories"] as const,
};

export const variantKeys = {
  all: () => ["variant"] as const,
  lists: () => [...variantKeys.all(), "list"] as const,
  list: (params: IGetProductsParams) => [...variantKeys.lists(), params] as const,
  details: () => [...variantKeys.all(), "detail"] as const,
  detail: (id: string) => [...variantKeys.details(), id] as const,
};

export const categoryKeys = {
  all: () => ["category"] as const,
  lists: () => [...categoryKeys.all(), "list"] as const,
  list: (params: IGetProductsParams) => [...categoryKeys.lists(), params] as const,
  details: () => [...categoryKeys.all(), "detail"] as const,
  detail: (id: string) => [...categoryKeys.details(), id] as const,
};

export const QUERY_KEYS = {
  PRICE_GROUPS: ["priceGroups"] as const,
  UNITS: ["units"] as const,
  BRANDS: ["brands"],
  CATEGORIES: ["categories"],
  PRODUCTS: ["products"],
  VARIANTS: ["variants"],
} as const;

"use client";

import { useEffect, useState } from "react";
import { Pencil } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { useDepartments } from "@/features/bots/department/hooks/department";
import { STAFF_AVATARS } from "@/features/bots/staff/components/create-staff/staff-avatar";
import { StaffAvatarForm } from "@/features/bots/staff/components/create-staff/upload-staff-avatar";
import { CreateImage } from "@/features/products/hooks/types";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui";
import { Combobox } from "@/components/ui/combobox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ToggleButton } from "@/components/ui/toggle-button";
import { uppercaseToTitleCase } from "@/utils/helpers/text-formater";

import { ROLE_EXPERTISE } from "../constants/role-expertise";

interface DomainExpertise {
  id: string;
  name: string;
}

interface EditStaffProps {
  form: UseFormReturn<any>;
}

export function EditStaff({ form }: EditStaffProps) {
  const { t } = useTranslation();
  const [expertiseInput, setExpertiseInput] = useState("");
  const { departments, isLoading: isLoadingDepartments } = useDepartments();
  const [customExpertise, setCustomExpertise] = useState<Record<string, string[]>>({});
  const [avatarDialogOpen, setAvatarDialogOpen] = useState(false);
  const [avatar, setAvatar] = useState<CreateImage | null>(null);

  // Initialize customExpertise from form data when component mounts or staff data changes
  useEffect(() => {
    const currentRole = form.watch("staffInfo.role");
    const selectedExpertise = form.watch("knowledge_base.domain_expertise_ids") || [];

    if (currentRole) {
      // Filter out expertise that are not in default ROLE_EXPERTISE
      const customOnes = selectedExpertise.filter(
        (exp: string) => !ROLE_EXPERTISE[currentRole]?.includes(exp)
      );

      if (customOnes.length > 0) {
        setCustomExpertise((prev) => ({
          ...prev,
          [currentRole]: Array.from(new Set(customOnes)), // Remove duplicates
        }));
      }
    }
  }, [form.watch("staffInfo.role")]);

  // Initialize avatar from form data
  useEffect(() => {
    const currentImage = form.watch("staffInfo.image");
    if (currentImage) {
      // Check if currentImage is an object with image property
      const imageUrl = typeof currentImage === "object" ? currentImage.image : currentImage;
      const imageName = typeof currentImage === "object" ? currentImage.name : "current_avatar.png";
      setAvatar({
        name: imageName,
        image: imageUrl,
      });
    }
  }, [form.watch("staffInfo.image")]);

  const handleExpertiseInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && expertiseInput.trim() !== "") {
      e.preventDefault();

      const currentRole = form.watch("staffInfo.role") as keyof typeof ROLE_EXPERTISE;

      // Create uppercase version of the input for consistency
      const newExpertise = expertiseInput.trim().toUpperCase().replace(/\s+/g, "_");

      // Add to customExpertise if it's not already in default ROLE_EXPERTISE
      if (currentRole && !ROLE_EXPERTISE[currentRole]?.includes(newExpertise)) {
        setCustomExpertise((prev) => ({
          ...prev,
          [currentRole]: Array.from(new Set([...(prev[currentRole] || []), newExpertise])),
        }));
      }

      // Add to form values if not already present
      const currentExpertise = form.getValues("knowledge_base.domain_expertise_ids") || [];
      if (!currentExpertise.includes(newExpertise)) {
        form.setValue(
          "knowledge_base.domain_expertise_ids",
          Array.from(new Set([...currentExpertise, newExpertise])),
          {
            shouldDirty: true,
            shouldTouch: true,
            shouldValidate: true,
          }
        );
      }

      setExpertiseInput("");
    }
  };

  // Add input change handler to enforce limit while typing
  const handleExpertiseInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length <= 50) {
      setExpertiseInput(value);
    }
  };

  const handleExpertiseChange = (values: string | string[]) => {
    // Ensure values is always an array and remove duplicates
    const valueArray = Array.isArray(values) ? values : [values];
    const uniqueValues = Array.from(new Set(valueArray));
    form.setValue("knowledge_base.domain_expertise_ids", uniqueValues, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const getCurrentRoleExpertise = () => {
    const currentRole = form.watch("staffInfo.role") as keyof typeof ROLE_EXPERTISE;
    if (!currentRole) return [];

    // Combine default and custom expertise for the current role and remove duplicates
    return Array.from(
      new Set([...(ROLE_EXPERTISE[currentRole] || []), ...(customExpertise[currentRole] || [])])
    );
  };

  return (
    <Form {...form}>
      <div className="space-y-2">
        <h2 className="text-base font-semibold">{t("pages.staff.editStaff.title")}</h2>

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="shrink-0">
              <button
                type="button"
                className="group relative rounded-full focus:outline-none"
                onClick={() => setAvatarDialogOpen(true)}
                aria-label="Change avatar">
                <Avatar className="size-16 border-2 border-primary">
                  <AvatarImage
                    src={
                      avatar?.image ||
                      (typeof form.watch("staffInfo.image") === "object"
                        ? form.watch("staffInfo.image").image
                        : form.watch("staffInfo.image")) ||
                      STAFF_AVATARS[0].image.src
                    }
                    className="object-contain"
                    alt={form.watch("staffInfo.name") || "Staff avatar"}
                  />
                  <AvatarFallback>
                    {form.watch("staffInfo.name")?.charAt(0)?.toUpperCase() || "S"}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute left-0 top-0 flex size-16 items-center justify-center rounded-full bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
                  <Pencil className="size-6 text-white" />
                </div>
              </button>
            </div>
            <div className="grow space-y-2">
              <FormField
                control={form.control}
                name="staffInfo.name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">
                      {t("pages.staff.editStaff.staffName")} <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Sarah Johnson" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <StaffAvatarForm
            open={avatarDialogOpen}
            onOpenChange={setAvatarDialogOpen}
            value={avatar}
            onChange={(value) => {
              setAvatar(value);
              if (value) {
                // Set the complete image object in the form
                form.setValue(
                  "staffInfo.image",
                  {
                    name: value.name,
                    image: value.image,
                  },
                  {
                    shouldDirty: true,
                    shouldTouch: true,
                    shouldValidate: true,
                  }
                );
              }
            }}
          />

          <FormField
            control={form.control}
            name="staffInfo.role"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-sm font-medium">
                  {t("pages.staff.editStaff.rolePurpose")} <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value}
                    onValueChange={field.onChange}
                    items={Object.keys(ROLE_EXPERTISE).map((role) => ({
                      id: role,
                      name: uppercaseToTitleCase(role),
                    }))}
                    placeholder={t("pages.staff.editStaff.selectRole")}
                    searchPlaceholder={t("pages.staff.editStaff.searchRoles")}
                    emptyText={t("pages.staff.editStaff.noRoleFound")}
                    variantButton="outline"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="staffInfo.department"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-sm font-medium">
                  {t("pages.staff.editStaff.department")} <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value}
                    onValueChange={field.onChange}
                    items={departments.map((dept) => ({
                      id: dept.id,
                      name: dept.name,
                    }))}
                    placeholder={t("pages.staff.editStaff.selectDepartment")}
                    searchPlaceholder={t("pages.staff.editStaff.searchDepartments")}
                    emptyText={t("pages.staff.editStaff.noDepartmentFound")}
                    variantButton="outline"
                    isLoading={isLoadingDepartments}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="space-y-2">
            <Label htmlFor="domain-expertise" className="text-sm font-medium">
              {t("pages.staff.editStaff.domainExpertise")}
            </Label>

            <div className="space-y-1">
              <Input
                id="domain-expertise"
                value={expertiseInput}
                onChange={handleExpertiseInputChange}
                onKeyDown={handleExpertiseInputKeyDown}
                placeholder={t("pages.staff.editStaff.customExpertisePlaceholder")}
                maxLength={50}
                className={expertiseInput.length === 50 ? "border-destructive" : ""}
              />
              {expertiseInput.length === 50 && (
                <p className="text-xs text-destructive">{t("pages.staff.maxCharactersReached")}</p>
              )}
            </div>

            <FormField
              control={form.control}
              name="knowledge_base.domain_expertise_ids"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <ToggleButton
                      options={getCurrentRoleExpertise().map((exp) => ({
                        id: exp,
                        label: uppercaseToTitleCase(exp),
                        value: exp,
                      }))}
                      value={field.value || []}
                      onValueChange={handleExpertiseChange}
                      isMultiSelect={true}
                      className="mb-4"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="staffInfo.requireNamePhone"
            render={({ field }) => (
              <FormItem className="flex items-center justify-between rounded-md border border-border p-4">
                <FormLabel className="text-sm font-medium">
                  {t("pages.staff.editStaff.namePhoneRequirement")}
                </FormLabel>
                <FormControl>
                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </Form>
  );
}

export type PricingTier = "free" | "starter" | "pro" | "agency";

export type PlanType = "monthly" | "annually";

export interface PlatformIntegrationProps {
  leftPlatform: {
    name: string;
    logo: any; // or more specific type for your logo
    logoAlt: string;
  };
  rightPlatform: {
    name: string;
    logo: any; // or more specific type for your logo
    logoAlt: string;
  };
  syncSettings: {
    products: boolean;
    inventory: boolean;
    orders: boolean;
  };
}

export interface SyncSettingDetail {
  key: string;
  title: string;
  description: string;
}

export interface Integration {
  _list: boolean;
  add: boolean;
  update: boolean;
  delete: boolean;
}

export interface PlanFeature {
  // integration: Integration;
  description: string;
  enable: boolean;
}

export interface Plan {
  id: string;
  updated_at: string;
  created_at: string;
  service_id: string;
  code: string;
  sale_price: string;
  price: string;
  description: string;
  name: string;
  features: PlanFeature[];
  is_popular?: boolean;
}

export interface SubscribePlanResponse {
  shopify_charge_id: string;
  confirmation_url: string;
  subscription_id: string;
}

export interface InstallationData {
  source_id: string;
  destination_id: string;
  status: string;
  [key: string]: string;
}

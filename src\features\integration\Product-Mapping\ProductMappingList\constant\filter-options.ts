import { FilterOption } from "@/components/data-table/types";

export const ProductMappingStatus: FilterOption[] = [
  { value: "ALL", label: "pages.productMappingList.filters.status.all", noFilter: true },
  { value: "SYNCED", label: "pages.productMappingList.filters.status.synced" },
  { value: "MAPPED", label: "pages.productMappingList.filters.status.mapped" },
  { value: "UNMAPPED", label: "pages.productMappingList.filters.status.unMapped" },
  { value: "ERROR", label: "pages.productMappingList.filters.status.errors" },
];

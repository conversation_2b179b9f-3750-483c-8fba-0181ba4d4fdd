"use client";

import { Fragment, useEffect, useRef, useState } from "react";
import { ColumnDef, flexRender, Table as ReactTable, Row } from "@tanstack/react-table";
import { motion } from "framer-motion";
import { Settings2, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";

import { CustomPagination } from "@/components/custom-pagination";
import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";

import { TableSkeleton } from "../custom-table/skeleton/table-skeleton";
import { ConfirmDialog } from "../ui/confirm-dialog";
import Empty from "../ui/empty";
import { DataTableViewDialog } from "./data-table-view-dialog";
import SortIcon from "./sort-icon";
import styles from "./style.module.scss";

export type CustomColumn<TData> = ColumnDef<TData> & {
  sorter?: boolean;
  isMainColumn?: boolean;
  sortKey?: string;
  hidden?: boolean;
};

export interface ExpandableConfig<TData> {
  enabled?: boolean;
  content: (row: Row<TData>) => React.ReactNode;
}

interface DataTableProps<TData> {
  columns: CustomColumn<TData>[];
  loading?: boolean;
  expandable?: ExpandableConfig<TData>;
  total: number;
  table: ReactTable<TData>;
  pageSize: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  selectable?: boolean;
  onSelectionChange?: (selectedRows: TData[]) => void;
  selectedRows: Record<string, boolean>;
  expandedRows: string[];
  handleSort: (column: CustomColumn<unknown>) => void;
  getInitialParams: Record<string, unknown>;
  onDelete: (selectedRows: number[]) => void;
  onRowClick?: (rowId: string) => void;
  allowDelete?: boolean;
}

export function DataTable<TData>({
  columns,
  loading = false,
  expandable,
  total,
  pageSize,
  table,
  currentPage,
  onPageChange,
  onPageSizeChange,
  selectedRows,
  expandedRows,
  handleSort,
  getInitialParams,
  onDelete,
  onRowClick,
  allowDelete = true,
}: DataTableProps<TData>) {
  const { t } = useTranslation();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const expandedRowRef = useRef<HTMLTableRowElement>(null);

  useEffect(() => {
    if (expandedRowRef.current) {
      if (expandedRowRef.current.parentElement) {
        expandedRowRef.current.style.scrollMarginTop = "100px";
      }
      expandedRowRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [expandedRows]);

  const formatedSelectedRow: number[] = Object.keys(selectedRows)
    .filter((key) => selectedRows[key]) // Filter valid keys
    .map((key) => Number(key));
  const mainColumn = columns.find((column) => column.isMainColumn)?.id;
  return (
    <div className="flex h-full flex-auto flex-col space-y-4 overflow-hidden">
      <div
        className={`${styles.scrollbarCustom} w-full min-w-0 overflow-auto rounded-md border duration-100`}>
        <Table className="flex-none">
          <TableHeader className="sticky top-0 z-30 bg-card">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const column = header.column.columnDef as CustomColumn<TData>;
                  const isSortable = column.sorter;
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(
                        "relative",
                        header.id === "select"
                          ? "sticky left-0 z-30 w-[48px] bg-card transition-colors group-hover:bg-table-hover group-data-[state=selected]:bg-muted"
                          : header.id === "actions"
                            ? "sticky right-0 z-30 w-[48px] bg-card text-center transition-colors group-hover:bg-table-hover group-data-[state=selected]:bg-muted"
                            : (column as CustomColumn<TData>).isMainColumn
                              ? "max-w-[200px] sm:max-w-[250px] md:max-w-[300px] lg:max-w-[400px] 2xl:max-w-[500px]  truncate "
                              : "",
                        isSortable ? "cursor-pointer" : ""
                      )}
                      onClick={() => isSortable && handleSort(column as CustomColumn<unknown>)}>
                      <div className="flex items-center  gap-2 text-nowrap text-center">
                        {header.isPlaceholder || header.id === "actions" ? (
                          <div className="flex w-full justify-end">
                            <Button
                              disabled={loading}
                              className="flex-none"
                              variant="outline"
                              size="sm"
                              leftIcon={<Settings2 size={16} />}
                              onClick={() => setIsViewDialogOpen(true)}
                            />
                          </div>
                        ) : (
                          flexRender(header.column.columnDef.header, header.getContext())
                        )}

                        {isSortable && (
                          <SortIcon
                            directionSort={
                              getInitialParams[`sort_${column.sortKey}`] as SortDirection
                            }
                          />
                        )}
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableSkeleton
                columns={columns.filter(
                  (column) => table.getColumn(column.id as string)?.getIsVisible() !== false
                )}
                mainColumn={mainColumn}
              />
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <Fragment key={row.id}>
                  <TableRow
                    data-state={selectedRows[row.id] && "selected"}
                    className={expandable?.enabled ? "hover:bg-muted/50" : ""}
                    onClick={() => onRowClick?.(row.id)}>
                    {row.getVisibleCells().map((cell) => {
                      return (
                        <TableCell
                          key={cell.id}
                          className={cn(
                            cell.column.id === "select"
                              ? "w-[48px] sticky left-0 z-20 bg-card group-hover:bg-table-hover transition-colors group-data-[state=selected]:bg-muted"
                              : cell.column.id === "actions"
                                ? "w-[48px] min-w-[48px] sticky right-0 z-20 bg-card group-hover:bg-table-hover transition-colors group-data-[state=selected]:bg-muted text-end"
                                : (cell.column.columnDef as CustomColumn<TData>).isMainColumn
                                  ? "max-w-[200px] sm:max-w-[250px] md:max-w-[300px] lg:max-w-[400px] 2xl:max-w-[500px] truncate block "
                                  : ""
                          )}>
                          {cell.column.id === "actions" ? (
                            <div className="ml-auto flex w-[48px] justify-center">
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </div>
                          ) : (
                            flexRender(cell.column.columnDef.cell, cell.getContext())
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                  {expandable?.enabled && expandedRows.includes(row.id) && (
                    <motion.tr
                      ref={expandedRowRef}
                      className={`bg-muted/50 ${styles.expandedRow}`}
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}>
                      <TableCell colSpan={columns.length}>{expandable.content(row)}</TableCell>
                    </motion.tr>
                  )}
                </Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <Empty />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {formatedSelectedRow.length > 0 && allowDelete && (
        <div className="flex items-center justify-between gap-2 text-nowrap text-center">
          <Button
            leftIcon={<Trash size={16} />}
            variant="outlineDestructive"
            color="crimson"
            size="sm"
            onClick={() => setIsDeleteDialogOpen(true)}>
            {t("table.selected.delete", { count: formatedSelectedRow.length })}
          </Button>
        </div>
      )}
      {formatedSelectedRow.length === 0 && (
        <CustomPagination
          total={total}
          pageSize={pageSize}
          currentPage={currentPage + 1}
          onPageSizeChange={onPageSizeChange}
          onPageChange={(page) => onPageChange(page - 1)}
        />
      )}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        variant="destructive"
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={() => onDelete(formatedSelectedRow)}
        title={t("common.areYouSure")}
        description={
          formatedSelectedRow.length === 1
            ? t("common.deleteProductConfirmation")
            : t("common.deleteListProductConfirmation", { count: formatedSelectedRow.length })
        }
        confirmText={t("common.delete")}
        cancelText={t("common.cancel")}
      />
      <DataTableViewDialog
        table={table}
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
      />
    </div>
  );
}

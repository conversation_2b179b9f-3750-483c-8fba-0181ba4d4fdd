"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowLeft, ArrowR<PERSON>, Link, Unlink } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { CustomImage } from "@/components/ui/image";
import { Table, TableBody, TableCell } from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

import { DestinationProductData, ProductVariant } from "../hooks/types";

interface VariantMappingViewProps {
  sourceProduct: {
    id: string;
    name: string;
    image: string;
    price: number;
  };
  standardSourceData?: {
    id: string;
    title: string;
    images: string[];
    variants: Array<{
      id: string;
      title: string;
      price: string;
      image: string | null;
    }>;
  };
  destProduct: DestinationProductData;
  sourceVariants: {
    id: string;
    name: string;
    image?: string;
    images?: string;
    price: number;
    color?: string;
  }[];
  destVariants: ProductVariant[];
  onBack: () => void;
  selectedSourceVariant: string | null;
  activeSourceForMapping: string | null;
  mappedVariants: Record<string, string>;
  onSourceVariantSelect: (variantId: string) => void;
  onMapVariant: (sourceVariantId: string, destVariantId: string) => void;
  onUnmapVariant: (sourceVariantId: string) => void;
  onUnmapDestVariant: (destVariantId: string) => void;
  isDestVariantMapped: (destVariantId: string) => boolean;
  isDestinationMappedToActiveSource: (destVariantId: string) => boolean;
  clearSelection: () => void;
}

export default function VariantMappingView({
  sourceProduct,
  standardSourceData,
  destProduct,
  sourceVariants,
  destVariants,
  onBack,
  selectedSourceVariant,
  activeSourceForMapping,
  mappedVariants,
  onSourceVariantSelect,
  onMapVariant,
  onUnmapVariant,
  onUnmapDestVariant,
  isDestVariantMapped,
  isDestinationMappedToActiveSource,
  clearSelection,
}: VariantMappingViewProps) {
  // State for confirm dialog
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  // Refs for tables to handle scrolling synchronization
  const sourceTableRef = useRef<HTMLDivElement>(null);
  const destTableRef = useRef<HTMLDivElement>(null);

  // Ref to prevent multiple selections happening simultaneously
  const selectInProgress = useRef(false);

  // Reset selection flag when unmounting or when variants change
  useEffect(() => {
    return () => {
      selectInProgress.current = false;
    };
  }, [sourceVariants]);

  // Direct auto-selection of first variant without useEffect
  if (
    sourceVariants.length > 0 &&
    !selectedSourceVariant &&
    !activeSourceForMapping &&
    !selectInProgress.current
  ) {
    // Prevent multiple simultaneous selections
    selectInProgress.current = true;

    // Select first variant directly
    const firstVariantId = sourceVariants[0].id;
    console.log("Auto-selecting first variant in render:", firstVariantId);

    // Use a zero-delay timeout to avoid React warnings about state updates during render
    setTimeout(() => {
      onSourceVariantSelect(firstVariantId);
      // Reset flag after selection completes
      selectInProgress.current = false;
    }, 0);
  }

  // Handle back button with confirmation if mappings exist
  const handleBackWithConfirmation = useCallback(() => {
    // If there are mapped variants, ask for confirmation
    if (Object.keys(mappedVariants).length > 0) {
      setConfirmDialogOpen(true);
    } else {
      // If no mappings, just go back
      onBack();
    }
  }, [mappedVariants, onBack]);

  // Return to selection screen
  const handleConfirmBack = useCallback(() => {
    clearSelection(); // Clear selection

    // Clear all mappings directly
    onMapVariant("__clear_all__", "__clear_all__");

    onBack();
    setConfirmDialogOpen(false);
  }, [onBack, clearSelection, onMapVariant]);

  // Add scroll synchronization between tables
  useEffect(() => {
    const sourceTable = sourceTableRef.current;
    const destTable = destTableRef.current;

    if (!sourceTable || !destTable) return;

    // Function to sync scrolling
    const handleScroll = (e: Event) => {
      const source = e.target as HTMLDivElement;
      const target = source === sourceTable ? destTable : sourceTable;

      // Directly set scroll position
      target.scrollTop = source.scrollTop;
    };

    // Add listeners to both tables
    sourceTable.addEventListener("scroll", handleScroll);
    destTable.addEventListener("scroll", handleScroll);

    return () => {
      sourceTable.removeEventListener("scroll", handleScroll);
      destTable.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Create a common structure for both source and destination sides to ensure alignment
  const createAlignedRows = () => {
    // Total rows needed = all source variants + all destination variants - mapped pairs
    const maxVariants = Math.max(sourceVariants.length, destVariants.length);

    // Create arrays to hold both source and destination variants with proper alignment
    const alignedSourceRows: ((typeof sourceVariants)[0] | null)[] = new Array(maxVariants).fill(
      null
    );
    const alignedDestRows: ((typeof destVariants)[0] | null)[] = new Array(maxVariants).fill(null);

    // First place all mapped pairs at the same index in both arrays
    let nextIndex = 0;

    // Process mapped pairs first - these must align exactly
    Object.entries(mappedVariants).forEach(([sourceId, destId]) => {
      const sourceVariant = sourceVariants.find((v) => v.id === sourceId);
      const destVariant = destVariants.find((v) => v.id === destId);

      if (sourceVariant && destVariant) {
        alignedSourceRows[nextIndex] = sourceVariant;
        alignedDestRows[nextIndex] = destVariant;
        nextIndex++;
      }
    });

    // Add remaining unmapped sources
    sourceVariants.forEach((variant) => {
      if (!(variant.id in mappedVariants)) {
        // Find first empty slot
        const emptyIndex = alignedSourceRows.findIndex((item) => item === null);
        if (emptyIndex >= 0) {
          alignedSourceRows[emptyIndex] = variant;
        } else {
          // This shouldn't happen if maxVariants is calculated correctly
          alignedSourceRows.push(variant);
        }
      }
    });

    // Add remaining unmapped destinations
    destVariants.forEach((variant) => {
      if (!Object.values(mappedVariants).includes(variant.id)) {
        // Find first empty slot
        const emptyIndex = alignedDestRows.findIndex((item) => item === null);
        if (emptyIndex >= 0) {
          alignedDestRows[emptyIndex] = variant;
        } else {
          // This shouldn't happen if maxVariants is calculated correctly
          alignedDestRows.push(variant);
        }
      }
    });

    return { alignedSourceRows, alignedDestRows };
  };

  // Generate aligned rows for both sides
  const { alignedSourceRows, alignedDestRows } = createAlignedRows();

  // Check if we should disable destination variant interactions
  const disableDestinationInteraction = !selectedSourceVariant && !activeSourceForMapping;

  // Animation variants for framer-motion
  const animationVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  };

  // Transitions for list items
  const listTransition = {
    type: "spring",
    stiffness: 300,
    damping: 30,
  };

  return (
    <>
      {/* Confirm Dialog */}
      <ConfirmDialog
        open={confirmDialogOpen}
        onOpenChange={setConfirmDialogOpen}
        title="Discard Variant Mappings"
        description="Going back will clear all your variant mappings. Are you sure you want to continue?"
        cancelText="Cancel"
        confirmText="Discard and Go Back"
        variant="destructive"
        onConfirm={handleConfirmBack}
        onCancel={() => setConfirmDialogOpen(false)}
      />

      {/* Source product */}
      <div className="col-span-1 pl-6">
        <div className="flex justify-start py-2">
          <div className="text-sm text-muted-foreground">Source product</div>
        </div>

        <div className="flex flex-col overflow-hidden bg-muted px-4 opacity-50">
          <Card className="border-none bg-transparent py-4">
            <div className="flex items-center gap-4">
              <CustomImage
                src={standardSourceData?.images?.[0] || sourceProduct.image || "/placeholder.svg"}
                alt={standardSourceData?.title || sourceProduct.name}
                width={40}
                height={40}
                className="max-h-[40px] max-w-[40px] rounded-md"
              />
              <div className="min-w-0 flex-1">
                <p className="truncate text-sm font-medium">
                  {standardSourceData?.title || sourceProduct.name}
                </p>
                <p className="text-sm text-muted-foreground">
                  Price: ${sourceProduct.price ? sourceProduct.price.toFixed(2) : "--"}
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Variants label - only on source side */}
        <div className="h-5">
          <h3 className="py-2 text-sm font-medium">Variants</h3>
        </div>

        {/* Source Variants table */}
        <div ref={sourceTableRef} className="max-h-[300px] overflow-y-auto py-4">
          <Table>
            <TableBody>
              <AnimatePresence initial={false}>
                {alignedSourceRows.map((variant, index) => {
                  // If null, render an empty placeholder row
                  if (variant === null) {
                    return (
                      <tr key={`empty-source-${index}`} className="h-[76px]">
                        <TableCell className="py-2 opacity-0">
                          <div className="invisible flex items-center gap-4">
                            <div className="size-[40px]" />
                            <div className="min-w-0 flex-1">
                              <p className="text-sm font-medium">&nbsp;</p>
                              <p className="text-sm">&nbsp;</p>
                            </div>
                          </div>
                        </TableCell>
                      </tr>
                    );
                  }

                  const isMapped = variant.id in mappedVariants;
                  const isSelected = selectedSourceVariant === variant.id;
                  const isActiveForMapping = activeSourceForMapping === variant.id;

                  // Find corresponding standard source variant
                  const standardVariant = standardSourceData?.variants?.find(
                    (v) => v.id === variant.id
                  );

                  return (
                    <motion.tr
                      key={variant.id}
                      layout
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      variants={animationVariants}
                      transition={{
                        ...listTransition,
                        delay: index * 0.03,
                      }}
                      className={cn(
                        "cursor-pointer border-none hover:bg-bg-primary flex-none",
                        isSelected && "bg-accent",
                        isActiveForMapping && !isSelected && "bg-blue-100",
                        !isActiveForMapping && isMapped && "bg-muted"
                      )}
                      onClick={() => onSourceVariantSelect(variant.id)}>
                      <TableCell className="relative py-2">
                        <div className="flex items-center gap-4">
                          <CustomImage
                            src={
                              standardVariant?.image ||
                              standardSourceData?.images?.[0] ||
                              variant.image ||
                              "/placeholder.svg"
                            }
                            alt={standardVariant?.title || variant.name}
                            width={40}
                            height={40}
                            className="max-h-[40px] max-w-[40px] rounded-md"
                          />
                          <div className="min-w-0 flex-1">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <p className="truncate text-sm font-medium">
                                    {standardVariant?.title || variant.name}
                                  </p>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{standardVariant?.title || variant.name}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <p className={"text-sm"}>
                              Price: $
                              {standardVariant?.price
                                ? parseFloat(standardVariant.price).toFixed(2)
                                : variant.price
                                  ? variant.price.toFixed(2)
                                  : "--"}
                            </p>
                          </div>
                          <ArrowRight className="size-4 text-muted-foreground" />
                        </div>
                      </TableCell>
                    </motion.tr>
                  );
                })}
              </AnimatePresence>
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Destination variant table */}
      <div className="col-span-2 pr-6">
        <div className="flex justify-start">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBackWithConfirmation}
            className="flex items-center gap-1 px-2 text-muted-foreground hover:bg-transparent">
            <ArrowLeft className="size-4" /> Back to selection
          </Button>
        </div>

        {/* Destination product card */}
        <div className="flex flex-col overflow-hidden bg-muted opacity-50">
          <Card className="border-none bg-transparent p-4">
            <div className="flex items-center gap-4">
              <CustomImage
                src={destProduct.standard_data?.image || "/placeholder.svg"}
                alt={destProduct.name}
                width={40}
                height={40}
                className="max-h-[40px] max-w-[40px] rounded-md"
              />
              <div className="min-w-0 flex-1">
                <p className="truncate text-sm font-medium">{destProduct.name}</p>
                <p className="text-sm text-muted-foreground">
                  Price: $
                  {destProduct.standard_data?.price
                    ? parseFloat(destProduct.standard_data.price).toFixed(2)
                    : "--"}
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Empty div to align with Variants label on the left */}
        <div className="h-5"></div>

        {/* Helper message when no source variant is selected */}
        {disableDestinationInteraction && (
          <div className="mb-2 text-center text-sm text-muted-foreground">
            Select a source variant to enable mapping
          </div>
        )}

        {/* Destination variants table */}
        <div ref={destTableRef} className="max-h-[300px] overflow-y-auto py-4 pr-2">
          <Table>
            <TableBody>
              <AnimatePresence initial={false}>
                {alignedDestRows.map((variant, index) => {
                  // If null, render an empty placeholder row
                  if (variant === null) {
                    return (
                      <tr key={`empty-dest-${index}`} className="h-[56px]">
                        <TableCell className="py-2 opacity-0">
                          <div className="invisible flex items-center gap-4">
                            <div className="size-[40px]" />
                            <div className="min-w-0 flex-1">
                              <p className="text-sm font-medium">&nbsp;</p>
                              <p className="text-sm">&nbsp;</p>
                            </div>
                          </div>
                        </TableCell>
                      </tr>
                    );
                  }

                  const isMapped = isDestVariantMapped(variant.id);
                  const isMappedToActive = isDestinationMappedToActiveSource(variant.id);

                  return (
                    <motion.tr
                      key={variant.id}
                      layout
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      variants={animationVariants}
                      transition={{
                        ...listTransition,
                        delay: index * 0.03,
                      }}
                      className={cn(
                        "hover:bg-bg-primary cursor-pointer",
                        isMappedToActive && "bg-accent",
                        isMapped && activeSourceForMapping && !isMappedToActive && "bg-muted",
                        isMapped && !activeSourceForMapping && "bg-muted",
                        (!isMapped && !activeSourceForMapping) || disableDestinationInteraction
                          ? "cursor-not-allowed hover:bg-transparent opacity-50"
                          : ""
                      )}
                      onClick={() => {
                        if (disableDestinationInteraction) return; // Prevent interaction if disabled

                        if (isMapped) {
                          if (
                            activeSourceForMapping &&
                            mappedVariants[activeSourceForMapping] === variant.id
                          ) {
                            onUnmapDestVariant(variant.id);
                          } else if (activeSourceForMapping) {
                            // Allow remapping even if already mapped to something else
                            onMapVariant(activeSourceForMapping, variant.id);
                          }
                        } else if (activeSourceForMapping) {
                          onMapVariant(activeSourceForMapping, variant.id);
                        }
                      }}>
                      <TableCell className="py-2">
                        <div className="flex items-center gap-4">
                          <CustomImage
                            src={variant.images || variant.images?.[0] || "/placeholder.svg"}
                            alt={variant.name}
                            width={40}
                            height={40}
                            className="max-h-[40px] max-w-[40px] rounded-md"
                          />
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium">
                              {variant.name || variant.title || variant.sku || "--"}
                            </p>
                            <p className={"text-sm"}>
                              Price: $
                              {variant.price
                                ? typeof variant.price === "number"
                                  ? variant.price.toFixed(2)
                                  : variant.price
                                : "--"}
                            </p>
                          </div>
                          {isMapped && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="group"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Find which source variant is mapped to this destination
                                const sourceId = Object.entries(mappedVariants).find(
                                  ([srcId, dstId]) => dstId === variant.id
                                )?.[0];
                                if (sourceId) {
                                  onUnmapVariant(sourceId);
                                }
                              }}>
                              <Link
                                size="12"
                                className="cursor-pointer text-sematic-success group-hover:hidden"
                              />
                              <Unlink
                                size="12"
                                className="hidden cursor-pointer text-red-500 group-hover:block"
                              />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </motion.tr>
                  );
                })}
              </AnimatePresence>
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  );
}

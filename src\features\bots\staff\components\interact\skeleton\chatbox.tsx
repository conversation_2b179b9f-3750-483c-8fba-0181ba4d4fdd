import { Skeleton } from "@/components/ui";

export default function ChatboxSkeleton() {
  return (
    <div className="m-4 flex-1 space-y-4">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex w-full flex-col gap-2 ">
          <Skeleton key={i} className="h-8 w-3/5 self-end" />
          <Skeleton key={i} className="h-10 w-3/5 self-start" />
          <Skeleton key={i} className="h-10 w-3/5 self-start" />
        </div>
      ))}
    </div>
  );
}

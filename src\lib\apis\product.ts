import { OrderVariant } from "@/features/orders/hooks/types";
import type {
  BrandI<PERSON>,
  BrandResponse,
  CategoryItem,
  CategoryResponse,
  CreateProduct,
  PriceGroup,
  PriceGroupResponse,
  Product,
  UnitItem,
  UnitResponse,
} from "@/features/products/hooks/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

export interface ApiResponse<T> {
  data: T;
  message?: string;
  status?: number;
}

// Add these types
interface ImageUploadPayload {
  name: string;
  image: string;
  prefix: string;
}

interface UploadedImage {
  name: string;
  image: string;
  prefix: string;
}

// Add interface for pagination params
interface PaginationParams {
  limit?: number;
  page?: number;
}

// Export the payload interface since it's needed for the API call
export interface OptimizeProductPayload {
  title: string;
  description: string;
  target_marketplace: "amazon" | "ebay" | "general";
  max_title_length: number;
}

// Keep response interfaces internal
interface OptimizeResponse {
  message: string;
  result: {
    optimized: {
      title: string;
      description: string;
    };
    analysis: {
      key_changes: {
        title: {
          original: string;
          optimized: string;
        };
        description: {
          original: string;
          optimized: string;
        };
      };
      improvements: {
        title: string;
        description: string;
      };
      seo_impact: string;
      readability_score: string;
    };
  };
}

export const productApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Product>>(ENDPOINTS.PRODUCT.LIST, {
      params,
    });
  },
  create: async (data: Partial<CreateProduct>) => {
    return await privateApi.post<Product>(ENDPOINTS.PRODUCT.CREATE, data);
  },

  update: async (id: string, data: Partial<Product>) => {
    const url = ENDPOINTS.PRODUCT.UPDATE.replace(":id", id);
    return await privateApi.put<Product>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.PRODUCT.DELETE.replace(":id", id);
    return await privateApi.delete(url);
  },

  deleteListProducts: async (listId: string[]) => {
    return await privateApi.post<Product>(ENDPOINTS.PRODUCT.LIST_DELETE, { product_ids: listId });
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.PRODUCT.GET_BY_ID.replace(":id", id);
    return await privateApi.get<Product>(url);
  },

  // Brand APIs
  getBrands: async (params?: PaginationParams) => {
    return await privateApi.get<BrandResponse>(ENDPOINTS.PRODUCT.BRANDS, { params });
  },

  createBrand: async (data: { name: string; image: UploadedImage | null }) => {
    return await privateApi.post<BrandItem>(ENDPOINTS.PRODUCT.BRANDS, data);
  },

  // Category APIs
  getCategories: async (params?: PaginationParams) => {
    return await privateApi.get<CategoryResponse>(ENDPOINTS.PRODUCT.CATEGORIES, { params });
  },

  createCategory: async (data: {
    name: string;
    image: UploadedImage | null;
    parent_category_id: string | null;
  }): Promise<CategoryItem> => {
    return await privateApi.post<CategoryItem>(ENDPOINTS.PRODUCT.CATEGORIES, data);
  },

  // Unit APIs
  getUnits: async (params?: PaginationParams) => {
    return await privateApi.get<UnitResponse>(ENDPOINTS.PRODUCT.UNITS, {
      params: {
        limit: 100,
        ...params,
      },
    });
  },

  createUnit: async (data: { name: string; ratio: number }) => {
    return await privateApi.post<UnitItem>(ENDPOINTS.PRODUCT.UNITS, data);
  },
};
export const UploadImage = async (data: ImageUploadPayload) => {
  return await privateApi.post<UploadedImage>(`${ENDPOINTS.MEDIA.UPLOAD_IMAGE}`, data);
};

// Price Group APIs
export const priceGroupApi = {
  list: async (params?: PaginationParams) => {
    return await privateApi.get<PriceGroupResponse>("/price/price_groups", { params });
  },

  create: async (name: string) => {
    return await privateApi.post<PriceGroup>("/price/price_groups", { name });
  },
};

// Update the optimize function
export const optimizeProduct = (data: OptimizeProductPayload) => {
  return privateApi.post<OptimizeResponse>(ENDPOINTS.AI.OPTIMIZE, data);
};

// Add variant API
export const variantApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<OrderVariant>>("/product/variants", {
      params,
    });
  },
};

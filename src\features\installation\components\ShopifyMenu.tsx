import React from "react";

import { SwitchThemeButton } from "@/components/buttons/switch-theme-button/switch-theme-button";
import { Separator } from "@/components/ui";

import { LanguageCombobox } from "./LanguageCombobox";

interface MenuProps {
  className?: string;
}

export const ShopifyMenu: React.FC<MenuProps> = ({ className }) => {
  return (
    <div className={`right-10 top-6 z-50 md:fixed ${className || ""}`}>
      <div className="flex h-5 items-center text-sm">
        <LanguageCombobox />
        <Separator className="ml-1 mr-4 h-full font-bold" orientation="vertical" />
        <SwitchThemeButton />
      </div>
    </div>
  );
};

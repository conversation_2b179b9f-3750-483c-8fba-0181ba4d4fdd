import { useRouter } from "next/navigation";
import { PencilLine } from "lucide-react";
import { useTranslation } from "react-i18next";

import defaultAvatar from "@/assets/images/staff/default_avatar.png";
import { Button } from "@/components/ui";
import { CustomImage } from "@/components/ui/image";
import { uppercaseToTitleCase } from "@/utils/helpers/text-formater";

import { VirtualStaffModel } from "../../hooks/type";
import StaffSelection from "./staff-selection";

interface StaffHeaderProps {
  virtualStaff: VirtualStaffModel;
}

export default function StaffHeader({ virtualStaff }: StaffHeaderProps) {
  const { t } = useTranslation();
  const router = useRouter();

  const handleSelect = (staff: VirtualStaffModel) => {
    if (staff && staff.id !== virtualStaff?.id) {
      router.push(`/staff/${staff.id}/interact`);
    }
  };
  return (
    <div className="flex h-[72px] items-center justify-between gap-4 p-4">
      <StaffSelection currentStaff={virtualStaff!} onSelect={handleSelect} />
      <Button
        variant="ghost"
        onClick={() => router.push(`/staff/${virtualStaff.id}/interact/edit`)}
        leftIcon={<PencilLine size={16} />}>
        {t("common.edit")}
      </Button>
    </div>
  );
}

export function ChatHeaderInfo({
  image,
  name,
  role,
}: {
  image: string;
  name: string;
  role: string;
}) {
  return (
    <div className="flex min-w-[220px] items-center gap-3">
      <div className="relative">
        <CustomImage
          src={image || defaultAvatar.src}
          defaultImage={defaultAvatar.src}
          alt={name || ""}
          width={40}
          height={40}
          className="size-10 flex-none rounded-lg object-cover"
        />
        <span className="absolute -bottom-1 -right-1 size-4 rounded-full border-4 border-card bg-sematic-success" />
      </div>
      <div className="flex max-w-[164px] flex-col ">
        <span className="flex items-center gap-1 truncate text-base font-semibold text-accent-foreground">
          {name}
        </span>
        <span className="truncate text-sm text-muted-foreground">
          {uppercaseToTitleCase(role || "")}
        </span>
      </div>
    </div>
  );
}

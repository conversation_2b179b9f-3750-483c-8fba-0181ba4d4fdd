"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { Circle } from "lucide-react";
import { useTranslation } from "react-i18next";

import EditStaffFooter from "@/features/bots/staff/components/staff_edit/footer";
import RightPanel from "@/features/bots/staff/components/staff_edit/right_panel";
import InteractionStyleTab from "@/features/bots/staff/components/staff_edit/tabs/interaction_style_tab";
import KnowledgeTab from "@/features/bots/staff/components/staff_edit/tabs/knowledge_tab";
import SkillsTab from "@/features/bots/staff/components/staff_edit/tabs/skills_tab";
import StaffInfoTab from "@/features/bots/staff/components/staff_edit/tabs/staff_info_tab";
import TaskTab from "@/features/bots/staff/components/staff_edit/tabs/task_tab";
import { mapStaffToFormValues } from "@/features/bots/staff/hooks/form";
import { useEditStaff } from "@/features/bots/staff/hooks/use-edit-staff";

import { ScrollArea } from "@/components/ui";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function DetailStaffPage() {
  const { t } = useTranslation();
  const params = useParams();
  const staffId = params.id as string;
  const { form, staff, isLoading, isUpdating, onSubmit, handleCancel, isTabDirty } =
    useEditStaff(staffId);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  useEffect(() => {
    if (staff) {
      form.reset(mapStaffToFormValues(staff));
    }
  }, [staff, form]);

  const handleCancelWithConfirm = () => {
    if (form.formState.isDirty) {
      setShowConfirmDialog(true);
    } else {
      handleCancel();
    }
  };

  const STAFF_TABS = [
    {
      id: "staff-info",
      label: "Staff information",
      component: <StaffInfoTab form={form} staffId={staffId} staffImage={staff?.image?.url} />,
    },
    {
      id: "interaction-style",
      label: "Interaction Style",
      component: <InteractionStyleTab form={form} />,
    },
    {
      id: "knowledge",
      label: "Knowledge",
      component: <KnowledgeTab form={form} />,
    },
    {
      id: "skills",
      label: "Skills",
      component: <SkillsTab form={form} />,
    },
    {
      id: "task",
      label: "Task",
      component: <TaskTab form={form} />,
    },
  ] as const;

  return (
    <>
      <div className="flex flex-col overflow-hidden pt-0 md:h-[calc(100vh-64px)]">
        <div className="flex flex-auto flex-col overflow-y-hidden">
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex h-full flex-col"
            onKeyDown={(e) => {
              if (e.key === "Enter" && e.target instanceof HTMLInputElement) {
                e.preventDefault();
              }
            }}>
            <Tabs
              className="flex flex-auto flex-col overflow-y-hidden p-4"
              defaultValue={STAFF_TABS[0].id}>
              <TabsList className="mb-4 flex size-fit flex-none flex-wrap justify-start gap-2 rounded-lg p-1">
                {STAFF_TABS.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="relative h-9 px-6 py-1.5 text-sm font-medium">
                    <span className="flex items-center gap-2">
                      {isTabDirty(tab.id) && (
                        <Circle className="size-2 fill-primary text-primary" />
                      )}
                      {tab.label}
                    </span>
                  </TabsTrigger>
                ))}
              </TabsList>
              <div className="grid flex-auto grid-cols-12 gap-4 overflow-hidden">
                <div className="col-span-12 flex flex-col overflow-hidden md:col-span-8">
                  {STAFF_TABS.map((tab) => (
                    <TabsContent
                      className="flex flex-auto overflow-hidden data-[state=inactive]:size-0 data-[state=inactive]:flex-none"
                      key={tab.id}
                      value={tab.id}>
                      <ScrollArea className="min-h-full flex-auto">{tab.component}</ScrollArea>
                    </TabsContent>
                  ))}
                </div>
                <div className="col-span-12 flex flex-auto flex-col overflow-hidden md:col-span-4">
                  <RightPanel staff={staff} radarData={[]} />
                </div>
              </div>
            </Tabs>
            <EditStaffFooter
              isUpdating={isUpdating}
              form={form}
              onSubmit={onSubmit}
              onCancel={handleCancelWithConfirm}
            />
          </form>
        </div>
      </div>

      <ConfirmDialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        title={t("common.unsavedChanges")}
        description={t("common.leaveWithoutSavingDescription")}
        confirmText={t("common.areYouSure")}
        cancelText={t("common.cancel")}
        variant="destructive"
        onConfirm={() => {
          setShowConfirmDialog(false);
          handleCancel();
        }}
        onCancel={() => setShowConfirmDialog(false)}
      />
    </>
  );
}

import { test, expect } from '@playwright/test';
import { publicPaths } from '@/middleware';
import { AUTH_ENDPOINTS } from "@/constants/endpoints";
import config from '@/config';

const BASE_URL = "http://localhost:3000"
const loginPath = publicPaths[0]
const signupPath = publicPaths[1]
const verifycodePath = publicPaths[4]
const API_URL = config.API_URL

const testInput = {
    email: '<EMAIL>',
    username: 'trannhon',
    password: 'Admin@123',
    confirmPassword: 'Admin@123',
}

const signupURL = `${BASE_URL}${signupPath}`

test.describe('Redirect to Login', () => {
    test('should redirect to Login when back to Login', async ({ page }) => {
        await page.goto(`${BASE_URL}${signupPath}`);
        await page.getByRole('button', { name: /log in/i }).click();
        await expect(page).toHaveURL(`${BASE_URL}${loginPath}`, { timeout: 10000 });
    })
})

test.describe('Sign up', () => {
    test('should show user already exists', async ({ page }) => {
        await page.goto(`${BASE_URL}${signupPath}`);
        await page.fill('input[name="email"]', testInput.email);
        await page.fill('input[name="username"]', testInput.username);
        await page.fill('input[name="password"]', testInput.password);
        await page.fill('input[name="confirmPassword"]', testInput.confirmPassword);
        await page.click('button[type="submit"]');

        await page.getByText('Request failed with status code 500').waitFor();
    })

    test('should signup successfully', async ({ page }) => {
        await page.route(`${API_URL}${AUTH_ENDPOINTS.REGISTER}`, async (route) => {
            await route.fulfill({
                status: 200,
                contentType: "application/json",
                body: JSON.stringify({ success: true, message: "Mocked API response" }),
            });
        });
        await page.route(`${API_URL}${AUTH_ENDPOINTS.VERIFY_CODE}`, async (route) => {
            await route.fulfill({
                status: 200,
                contentType: "application/json",
                body: JSON.stringify({ success: true, message: "Mocked API response" }),
            });
        });

        await page.goto(`${BASE_URL}${signupPath}`);
        await page.fill('input[name="email"]', testInput.email);
        await page.fill('input[name="username"]', testInput.username);
        await page.fill('input[name="password"]', testInput.password);
        await page.fill('input[name="confirmPassword"]', testInput.confirmPassword);
        await page.click('button[type="submit"]');

        await page.getByText('Sign up successful').waitFor();

        await expect(page).toHaveURL(/.*localhost:3000\/verify-confirmation-code\?.*/);

        await page.fill('input[placeholder="Enter the code"]', '111111');

        await page.locator('button:has-text("Verify Code")').click();

        await page.getByText('Code verified successfully').waitFor();
    });
})

// npx playwright test E2Etests/Signup.test.ts
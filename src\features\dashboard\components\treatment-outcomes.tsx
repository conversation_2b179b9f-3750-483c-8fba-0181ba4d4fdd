"use client";

import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

import { Card } from "@/components/ui/card";

interface OutcomeData {
  name: string;
  value: number;
  color: string;
}

interface TreatmentOutcomesProps {
  data: OutcomeData[];
}

export function TreatmentOutcomes({ data }: TreatmentOutcomesProps) {
  const { t } = useTranslation();

  return (
    <Card className="p-6">
      <h2 className="mb-6 text-lg font-semibold">{t("pages.overview.treatmentOutcomes.title")}</h2>
      <div className="flex flex-col items-center">
        <div className="h-[300px] w-full">
          <ResponsiveContainer>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value">
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="grid grid-cols-1 gap-x-16 gap-y-4 md:grid-cols-2">
          {data.map((entry, index) => (
            <div key={index} className="flex items-center justify-between gap-8">
              <div className="flex items-center gap-2">
                <div className="size-3 rounded-full" style={{ backgroundColor: entry.color }} />
                <span className="text-sm text-muted-foreground">
                  {t(`pages.overview.treatmentOutcomes.${entry.name.toLowerCase()}`)}
                </span>
              </div>
              <span className="text-sm font-medium">{entry.value}%</span>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
}

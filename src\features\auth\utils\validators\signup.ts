import * as z from "zod";

export const signupSchema = z
  .object({
    email: z.string().email("validation.invalidEmail"),
    username: z
      .string()
      .regex(/^[a-zA-Z0-9_]+$/, "validation.usernameSpecialCharacters")
      .min(3, "validation.invalidUsername"),
    password: z.string().min(8, "validation.invalidPassword"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "validation.passwordsDoNotMatch",
    path: ["confirmPassword"],
  });

export type SignupFormValues = z.infer<typeof signupSchema>;

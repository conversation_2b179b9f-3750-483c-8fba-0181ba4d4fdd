"use client";

import { useState } from "react";

import { VirtualStaffModel } from "@/features/bots/staff/hooks/type";
import { ChatListConversation, CONVERSATION } from "@/features/xbots/components/conversation-list";
import { useGetListMessenger } from "@/features/xbots/hooks/use-conversation";
import { VirtualStaff } from "@/features/xbots/virtual-staff";

import { Card } from "@/components/ui";

export default function ConversationPage() {
  const [conversation, setConversation] = useState<CONVERSATION>();
  const [selectedStaff, setSelectedStaff] = useState<VirtualStaffModel>();

  const {
    listMessenger,
    isLoading,
    isFetching,
    isError,
    error,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useGetListMessenger(conversation?.id);

  return (
    <div className="flex flex-col overflow-y-auto p-4 md:h-full md:flex-row md:overflow-hidden ">
      <ChatListConversation
        onChangeConversation={(conversation) => {
          if (conversation) {
            setConversation(conversation);
          }
        }}
        onChangeSelectedStaff={(staff) => setSelectedStaff(staff)}
      />
      <div className="flex overflow-hidden max-md:max-h-[500px] md:flex-[2]">
        <Card className="flex flex-auto overflow-hidden border-none bg-card ">
          <VirtualStaff
            listMessenger={listMessenger}
            botId={conversation?.assignee_id || ""}
            isConversation={true}
            roleBot={conversation?.role || ""}
            selectedStaff={selectedStaff}
            isStream={false}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
            isFetchingNextPage={isFetchingNextPage}
            conversationId={conversation?.id}
            senderRoles={["USER", "VIRTUAL_STAFF"]}
            receiverRoles={["EXTERNAL_USER"]}
          />
        </Card>
      </div>
    </div>
  );
}

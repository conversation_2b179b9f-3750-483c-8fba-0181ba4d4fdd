import { useTranslation } from "react-i18next";

import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

import { Option } from "./other-filter-group";

type SingleFilterGroupProps = {
  selectedOption: string;
  options: Option[];
  onSelect: (value: string) => void;
};

export default function SingleFilterGroup({
  selectedOption,
  options,
  onSelect,
}: SingleFilterGroupProps) {
  const { t } = useTranslation();
  return (
    <ToggleGroup
      type="single"
      value={selectedOption || undefined}
      onValueChange={(value) => value && onSelect?.(value)}
      className="flex flex-wrap gap-2 sm:flex-nowrap">
      {options.map((option) => {
        const isNoFilterOption = option.noFilter === true;
        const isSelected = option.value === selectedOption;

        return (
          <ToggleGroupItem
            size={"default"}
            key={option.value}
            value={option.value}
            className={`w-fit max-w-[200px] ${
              isNoFilterOption
                ? isSelected
                  ? "bg-neutral-200 hover:bg-neutral-300"
                  : "bg-neutral-100 hover:bg-neutral-300"
                : isSelected
                  ? "bg-neutral-200 hover:bg-neutral-300"
                  : "bg-neutral-100 hover:bg-neutral-300"
            }`}>
            <span className="truncate">{t(option.label)}</span>
          </ToggleGroupItem>
        );
      })}
    </ToggleGroup>
  );
}

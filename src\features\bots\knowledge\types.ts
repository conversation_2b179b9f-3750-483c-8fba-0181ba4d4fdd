export type KnowledgeStatus = "PENDING" | "PROCESSING" | "READY" | "ERROR";
export type KnowledgeFileType = "URL" | "FILE" | "DIRECT_TEXT";

export interface Knowledge {
  id: string;
  name: string;
  status: KnowledgeStatus;
  source: KnowledgeFileType;
  size: number; // bytes
  updated_at: string;
  url: string;
}

export interface KnowledgeURLResponse {
  message: string;
  success: boolean;
  data: Knowledge[];
}

export interface KnowledgeFileResponse {
  message: string;
  success: boolean;
  data: Knowledge[];
}

export interface KnowledgeTextResponse {
  message: string;
  success: boolean;
  data: Knowledge[];
}

export interface KnowledgeUploadUrl {
  url: string;
  s3_key: string;
}

export interface GetUploadUrlsPayload {
  files: {
    file_name: string;
    type: string;
    size: number;
  }[];
}

export interface CreateKnowledgeByFilePayload {
  files: {
    file_name: string;
    size: number;
    type: string;
    s3_key: string;
  }[];
}

export interface CreateKnowledgeByTextPayload {
  knowledge_name: string;
  content: string;
}

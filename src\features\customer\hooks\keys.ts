export interface IGetCustomersParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}

export const CUSTOMER_KEYS = {
  all: ["customers"] as const,
  lists: () => [...CUSTOMER_KEYS.all, "list"] as const,
  list: (params: IGetCustomersParams) => [...CUSTOMER_KEYS.lists(), params] as const,
  details: () => [...CUSTOMER_KEYS.all, "detail"] as const,
  detail: (id: string) => [...CUSTOMER_KEYS.details(), id] as const,
} as const;

export const ADDRESS_KEYS = {
  all: ["addresses"] as const,
  lists: () => [...ADDRESS_KEYS.all, "list"] as const,
  list: (query: string) => [...ADDRESS_KEYS.lists(), { query }] as const,
} as const;

export const QUERY_KEYS = {
  CUSTOMERS: ["customers"],
  CUSTOMER_DETAILS: (id: string) => ["customer", id],
  CUSTOMER_GROUPS: ["customer-groups"],
  ADDRESSES: ["addresses"],
  ADDRESS_PROVINCES: (query: string = "") => ["address-provinces", "infinite", { query }],
  ADDRESS_DISTRICTS: (provinceCode?: string, query: string = "") => [
    "address-districts",
    "infinite",
    { provinceCode, query },
  ],
  ADDRESS_WARDS: (provinceCode?: string, districtCode?: string, query: string = "") => [
    "address-wards",
    "infinite",
    { provinceCode, districtCode, query },
  ],
} as const;

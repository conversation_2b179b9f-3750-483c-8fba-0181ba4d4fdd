import { useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

import CustomerDetail from "@/features/customer/components/CustomerDetail/customer-detail";
import { CustomersDetailSkeleton } from "@/features/customer/components/CustomerDetail/customer-detail-skeleton";
import { customerOrderColumns } from "@/features/customer/components/CustomerDetail/customer-order-columns";
import PurchaseInfo from "@/features/customer/components/CustomerDetail/purchase-info";
import SaleSuggestion from "@/features/customer/components/CustomerDetail/sale-suggestion";
import { useCustomerDetails } from "@/features/customer/hooks/customer";
import { useCustomerOrders } from "@/features/customer/hooks/customer-orders";
import { EditCustomerDialog } from "@/features/orders/components/dialog/edit_customer";

import { TableContainer } from "@/components/custom-table/container/table-container";
import { Button } from "@/components/ui/button";
import { Card, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

interface CustomersDetailProps {
  customerId: string;
}

export const CustomersDetail = ({ customerId }: CustomersDetailProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  // Fetch customer details
  const { customer, isLoading: isLoadingCustomer } = useCustomerDetails(customerId);

  // Fetch customer orders
  const {
    orders,
    total: totalOrders,
    isLoading: isLoadingOrders,
  } = useCustomerOrders({
    customerId,
    page: currentPage,
    limit: 20,
  });

  const orderColumns = useMemo(() => customerOrderColumns(t), [t]);

  // Handle cancel button click
  const handleCancel = () => {
    setOpenConfirmDialog(true);
  };

  // Handle confirm cancel
  const handleConfirmCancel = () => {
    router.push("/customers");
  };

  // Handle edit button click
  const handleEdit = () => {
    setOpenEditDialog(true);
  };

  if (isLoadingCustomer) {
    return <CustomersDetailSkeleton />;
  }

  if (!customer) {
    return (
      <div className="container mx-auto py-6">
        <Card className="mx-auto max-w-md">
          <CardHeader>
            <CardTitle>Customer not found</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              The requested customer could not be found or you don't have permission to view it.
            </p>
            <div className="mt-6 flex justify-end">
              <Button onClick={() => router.push("/customers")}>{t("common.back")}</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Transform customer data for the components
  const customerData = {
    name: `${customer.first_name || ""} ${customer.last_name || ""}`.trim(),
    birthday: customer.birthday || "",
    gender: customer.gender || "",
    phone: customer.phone || "",
    email: customer.email || "",
    shippingAddress: customer.shipping_address?.address1 || "",
    billingAddress: customer.billing_address?.address1 || "",
    groupName: customer.customer_group?.name || "",
    loyalPoints: customer.loyal_customer?.point || 0,
    redeemPoints: customer.loyal_customer?.used_point || 0,
    tags: [], // Fixed the tags property
  };

  // Purchase information (using properties from actual API shape)
  const purchaseData = {
    totalSpent: 0, // Replace with actual API field if available
    totalProductsPurchased: 0, // Replace with actual API field if available
    purchasedOrders: 0, // Replace with actual API field if available
    totalProductsReturned: 0, // Replace with actual API field if available
    lastOrderAt: "—", // Replace with actual API field if available
  };

  // Sales suggestion information (using properties from actual API shape)
  const salesData = {
    defaultPriceGroup: "—", // Fixed the customer_group properties
    defaultPaymentMethod: "—",
    discountPercent: "—",
  };

  return (
    <div className="space-y-4 px-4 pb-20">
      {/* Main customer details */}
      <CustomerDetail customer={customerData} />

      {/* Purchase and Sales information */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <PurchaseInfo {...purchaseData} />
        <SaleSuggestion {...salesData} />
      </div>

      {/* Order history */}
      <Card className="w-full">
        <CardHeader className="flex flex-row items-center justify-between px-6 py-4">
          <CardTitle className="font-medium">{t("pages.orders.orderHistory")}</CardTitle>
          <Button variant="outline" size="sm">
            {t("table.export.title")}
          </Button>
        </CardHeader>
        <CardContent>
          <TableContainer
            columns={orderColumns}
            data={orders || []}
            loading={isLoadingOrders}
            total={totalOrders || 0}
            pageSize={20}
            currentPage={currentPage}
            selectable
          />
        </CardContent>
      </Card>

      {/* Footer with action buttons - sticky at bottom */}
      <div className="fixed inset-x-0 bottom-0 z-30 border-t bg-card p-4 shadow-2xl">
        <div className="relative flex">
          <div className="ml-auto flex items-center justify-end">
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                className="inline-flex w-24 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
                onClick={handleCancel}>
                {t("common.cancel")}
              </Button>
              <Button
                type="button"
                variant="outline"
                className="inline-flex w-24 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                onClick={handleEdit}>
                {t("common.edit")}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Confirm Dialog for Cancel */}
      <ConfirmDialog
        open={openConfirmDialog}
        onOpenChange={setOpenConfirmDialog}
        title="Cancel Changes"
        description="Are you sure you want to leave this page? Any unsaved changes will be lost."
        cancelText="No, stay on page"
        confirmText="Yes, leave page"
        onConfirm={handleConfirmCancel}
      />

      {/* Edit Customer Dialog */}
      <EditCustomerDialog
        customer={customer}
        open={openEditDialog}
        onOpenChange={setOpenEditDialog}
        onSuccess={() => {
          // Refetch customer data after successful edit
          window.location.reload();
        }}
        mode="edit"
      />
    </div>
  );
};

export default CustomersDetail;

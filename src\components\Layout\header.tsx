"use client";

import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";

import { CustomBreadcrumb } from "./CustomBreadCrumb/custom-breadcrumb";

interface HeaderProps {
  breadcrumb?: { label: string; href: string }[];
}

export function Header({ breadcrumb }: HeaderProps) {
  return (
    <header className="sticky top-0 z-[2] w-full bg-background">
      <div className="flex h-16 items-center px-4">
        <div className="flex w-full items-center justify-between gap-4">
          <div className="flex items-center gap-4 ">
            <SidebarTrigger />
            {breadcrumb && (
              <>
                <Separator className="h-4" orientation="vertical" />
                <CustomBreadcrumb />
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

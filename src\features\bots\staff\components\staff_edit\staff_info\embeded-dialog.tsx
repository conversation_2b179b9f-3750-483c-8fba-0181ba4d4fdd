import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { EmbedInstructions } from "./embeded/instructions";
import { EmbedScript } from "./embeded/script";

interface EmbedDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staffId: string;
  themeColor?: string;
  staffImage?: string;
}

export function EmbedDialog({
  open,
  onOpenChange,
  staffId,
  themeColor,
  staffImage,
}: EmbedDialogProps) {
  console.log("staffImage", staffImage);
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex flex-col sm:max-w-[1000px]">
        <div className="flex-none">
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle>Embed Virtual Staff Widget</DialogTitle>
          </DialogHeader>
          <div className="pt-2 text-sm text-muted-foreground">
            Follow the instructions below to embed the virtual staff widget on your website
          </div>
        </div>
        <div className="flex-auto overflow-y-auto">
          <Card>
            <EmbedScript staffId={staffId} themeColor={themeColor} staffImage={staffImage} />
          </Card>
          <Card className="mt-4">
            <EmbedInstructions />
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}

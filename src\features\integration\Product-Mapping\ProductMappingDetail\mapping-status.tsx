import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react";
import { useTranslation } from "react-i18next";

import LogoShopify from "@/assets/images/logo-shopify.png";
import LogoTiktok from "@/assets/images/logo-tiktok.png";
import { Button } from "@/components/ui/button";
import { CustomImage } from "@/components/ui/image";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import { formatSyncDate } from "@/utils/helpers/date-formater";

import { ProductMappingDetail } from "../hooks/types";

interface Platform {
  name: string;
  icon: any;
  connection_id: string;
  lastSync: string | null;
}

interface MappingStatusProps {
  productMappingDetail: ProductMappingDetail | any;
  onRefresh?: () => void;
  onPlatformChange?: (connectionId: string) => void;
}

// Platform icon mapping
const platformIcons = {
  tiktok_shop: LogoTiktok,
  shopify: LogoShopify,
  // Add more platforms as needed
};

export const MappingStatus = ({
  productMappingDetail,
  onRefresh,
  onPlatformChange,
}: MappingStatusProps) => {
  const { t } = useTranslation();
  const [isSyncing, setIsSyncing] = useState(false);
  const [selectedPlatformId, setSelectedPlatformId] = useState<string>("");
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [lastSyncDate, setLastSyncDate] = useState<string>("");

  // Process the data when productMappingDetail changes
  useEffect(() => {
    // Extract actual data from the response
    const responseData =
      productMappingDetail?.success && productMappingDetail?.data
        ? productMappingDetail.data
        : Array.isArray(productMappingDetail)
          ? productMappingDetail
          : [];

    if (responseData.length > 0) {
      // Extract available platforms from the data
      const availablePlatforms = responseData.map((item: any) => ({
        name: item.channel
          ? item.channel.replace("_", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())
          : "Unknown",
        icon: platformIcons[item.channel as keyof typeof platformIcons] || LogoTiktok,
        connection_id: item.connection_id || "",
        lastSync: item.last_sync || null,
      }));

      setPlatforms(availablePlatforms);

      // Set default selected platform to the first one with valid data
      if (availablePlatforms.length > 0 && !selectedPlatformId) {
        // Prefer mapped items first
        const mappedPlatform = availablePlatforms.find((p: Platform) =>
          responseData.find(
            (item: any) =>
              item.connection_id === p.connection_id &&
              item.mapping_status === "MAPPED" &&
              item.standard_destination_data
          )
        );

        const defaultPlatform =
          mappedPlatform ||
          availablePlatforms.find((p: Platform) => p.lastSync && p.connection_id) ||
          availablePlatforms[0];

        setSelectedPlatformId(defaultPlatform.connection_id);

        // Set the last sync date for the selected platform
        if (defaultPlatform.lastSync) {
          setLastSyncDate(formatSyncDate(defaultPlatform.lastSync));
        }

        // Notify parent component of platform selection
        if (onPlatformChange) {
          onPlatformChange(defaultPlatform.connection_id);
        }
      }
    }
  }, [productMappingDetail, onPlatformChange]);

  const handleSync = () => {
    setIsSyncing(true);
    if (onRefresh) {
      onRefresh();
    }
    setTimeout(() => {
      setIsSyncing(false);
    }, 3000);
  };

  // Update last sync date when platform selection changes
  const handlePlatformChange = (value: string) => {
    setSelectedPlatformId(value);

    // Find the selected platform
    const selectedPlatform = platforms.find((p) => p.connection_id === value);

    // Update last sync date if available
    if (selectedPlatform?.lastSync) {
      setLastSyncDate(formatSyncDate(selectedPlatform.lastSync));
    }

    // Notify parent component of platform change
    if (onPlatformChange) {
      onPlatformChange(value);
    }
  };

  // Find the selected platform object based on the connection_id
  const selectedPlatform =
    platforms.find((p) => p.connection_id === selectedPlatformId) ||
    platforms.find((p) => p.lastSync && p.connection_id) ||
    platforms[0];

  return (
    <div className="flex flex-col gap-4 py-2 md:flex-row md:items-center md:justify-between md:gap-0">
      {/* Left Platform - Source API */}
      <div className="w-full md:w-auto md:flex-1">
        <div className="flex items-center gap-3">
          <div className="relative size-10">
            <CustomImage src={LogoShopify.src} alt="Shopify" width={40} height={40} />
          </div>
          <div>
            <Label className="text-base font-medium">OneXAPI</Label>
            <div className="text-xs text-muted-foreground">
              {t("productMapping.lastSynced")}: {lastSyncDate}
            </div>
          </div>
        </div>
      </div>

      {/* Middle Sync Button */}
      <div className="flex items-center justify-center">
        <Button
          variant="outline"
          size="icon"
          className="size-12"
          onClick={handleSync}
          disabled={isSyncing}>
          <RefreshCw className={`size-5 ${isSyncing ? "animate-spin" : ""}`} />
        </Button>
      </div>

      {/* Right Platform - Dynamic Select */}
      <div className="flex w-full justify-start md:w-auto md:flex-1 md:justify-end">
        {platforms.length > 0 && (
          <Select value={selectedPlatformId} onValueChange={handlePlatformChange}>
            <SelectTrigger className="size-full max-w-full rounded-md border border-border p-2 shadow-sm hover:bg-border/50 md:max-w-64">
              <div className="flex items-center gap-3">
                <div className="flex-none">
                  {selectedPlatform && (
                    <CustomImage
                      src={selectedPlatform.icon.src}
                      alt={selectedPlatform.name}
                      width={40}
                      height={40}
                    />
                  )}
                </div>
                <div className="truncate">
                  <div className="flex items-center gap-2">
                    <Label className="text-base font-medium">{selectedPlatform?.name}</Label>
                    <div className="flex size-4 items-center justify-center rounded-full">
                      <CircleCheck className="size-4 stroke-1 text-green-500" />
                    </div>
                  </div>
                  <div className="truncate text-xs text-muted-foreground">
                    {t("productMapping.lastSynced")}: {lastSyncDate}
                  </div>
                </div>
              </div>
            </SelectTrigger>
            <SelectContent>
              {platforms.map((p) => (
                <SelectItem key={p.connection_id} value={p.connection_id}>
                  <div className="flex items-center gap-3">
                    <div className="relative size-8">
                      <CustomImage src={p.icon.src} alt={p.name} width={32} height={32} />
                    </div>
                    <div className="font-medium">{p.name}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  );
};

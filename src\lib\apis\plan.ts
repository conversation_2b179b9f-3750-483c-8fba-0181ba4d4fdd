import { Plan, PlanFeature, SubscribePlanResponse } from "@/features/installation/hooks/type";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseAxiosDetail } from "./types/common";

export const planApi = {
  getPlanList: async (params: Record<string, unknown>) => {
    const response = await privateApi.get<ResponseAxiosDetail<Plan>>(ENDPOINTS.CHANNEL.LIST_PLANS, {
      params,
    });

    return response;
  },
  subscribePlan: async (data: {
    plan_id: string;
    connection_id: string;
    duration: "MONTHLY" | "YEARLY";
    id: string;
    updated_at: string;
    created_at: string;
    service_id: string;
    code: string;
    sale_price: string;
    price: string;
    description: string;
    name: string;
    features: PlanFeature[];
  }) => {
    const response = await privateApi.post<ResponseAxiosDetail<SubscribePlanResponse>>(
      ENDPOINTS.CHANNEL.SUBSCRIBE_PLAN,
      data
    );
    return response;
  },
};

const temporaryPlan = {
  id: "234",
  updated_at: "2024-03-28T10:31:35.765061+00:00",
  service_id: "5c4bf15b-29fc-492c-a338-b15f3ce4b5be",
  code: "PREMIUM",
  sale_price: "149000",
  created_at: "2024-03-28T10:31:35.765061+00:00",
  price: "180000",
  description: "Premium features for growing businesses",
  name: "Premium Plan",
  currency: "USD",
  trial_days: "14",
  is_popular: true,
  features: {
    product: {
      _list: true,
      add: true,
      list_tags: true,
      get: true,
      get_by_slug: true,
      update: true,
      delete: true,
    },
    order: {
      _list: true,
      add: true,
      update: true,
      delete: true,
      get: true,
      order_process: true,
    },
    location: {
      _list: true,
      add: true,
      update: true,
      delete: true,
      get: true,
    },
    integration: {
      _list: true,
      add: true,
      update: true,
      delete: true,
    },
  },
};

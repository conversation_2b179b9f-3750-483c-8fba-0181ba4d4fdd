import React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { Button, Input } from "@/components/ui";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";

interface CreateDepartmentDialogProps {
  open: boolean;
  onCancel?: () => void;
  onCreate: (data: FormValues) => void;
  isPending: boolean;
}

type FormValues = {
  departmentName: string;
  description?: string;
};

const CreateDepartmentDialog: React.FC<CreateDepartmentDialogProps> = ({
  open,
  onCancel,
  onCreate,
  isPending,
}) => {
  const { t } = useTranslation();
  const schema = z.object({
    departmentName: z.string().min(1, t("pages.department.departmentNameRequired")),
    description: z.string().optional(),
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: { departmentName: "", description: "" },
    mode: "onChange",
  });

  const handleSubmit = async (data: FormValues) => {
    try {
      await onCreate(data);
      setTimeout(() => {
        form.reset();
        onCancel?.();
      }, 500);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen && !isPending) {
          form.reset();
          onCancel?.();
        }
      }}>
      <DialogContent className="max-w-md p-0">
        <DialogHeader className="p-4 pb-0">
          <DialogTitle className="text-lg font-semibold">
            {t("pages.department.createDepartment")}
          </DialogTitle>
        </DialogHeader>
        <div className="px-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <FormField
                name="departmentName"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("pages.department.departmentName")}{" "}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("pages.department.enterDepartmentName")}
                        {...field}
                        autoFocus
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="description"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("pages.department.description")}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t("pages.department.enterDescription")}
                        {...field}
                        rows={4}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="-mx-4 mt-4 border-t">
                <div className="flex h-16 items-center justify-between px-4 bg-card">
                  <div className="flex items-center gap-2">
                    {/* Add any left side content here if needed */}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      className="inline-flex min-w-20 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
                      type="button"
                      variant="outline"
                      onClick={() => {
                        form.reset();
                        onCancel?.();
                      }}>
                      {t("common.cancel")}
                    </Button>
                    <Button
                      className="inline-flex min-w-20 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                      type="submit"
                      variant="default"
                      disabled={!form.formState.isValid || isPending}
                      loading={isPending}>
                      {t("common.create")}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateDepartmentDialog;

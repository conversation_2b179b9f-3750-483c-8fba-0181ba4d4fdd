import { useCallback, useTransition } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";
import { MessagesSquare } from "lucide-react";

import { VirtualStaffModel } from "@/features/bots/staff/hooks/type";

import defaultAvatar from "@/assets/images/staff/default_avatar.png";
import { ImageColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Button } from "@/components/ui";
import { authProtectedPaths } from "@/constants/paths";
import toNavUrl from "@/utils/helpers/nav-url-formater";

export const columns = (
  useDeleteStaffMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<VirtualStaffModel>[] => [
  {
    id: "name",
    accessorKey: "name",
    header: t("pages.staff.columns.staff"),
    isMainColumn: true,
    cell: ({ row }: { row: Row<VirtualStaffModel> }) => {
      const staff = row.original;
      return (
        <div className="flex items-center gap-4 truncate">
          <ImageColumn
            src={staff?.image?.url || defaultAvatar.src}
            alt={staff?.name}
            width={0}
            iszoomable={false}
            height={0}
            sizes="100vw"
            className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
          />
          <div className="flex flex-auto flex-col justify-between gap-1 truncate ">
            <TextColumn text={staff?.name} className=" font-medium" />
          </div>
        </div>
      );
    },
  },
  {
    id: "role",
    accessorKey: "role",
    header: t("pages.staff.columns.role"),
    cell: ({ row }: { row: Row<VirtualStaffModel> }) => (
      <div className="font-medium ">{row.original?.role}</div>
    ),
  },
  {
    id: "skills",
    accessorKey: "skills",
    header: t("pages.staff.columns.skills"),
    cell: ({ row }: { row: Row<VirtualStaffModel> }) => (
      <div className="max-w-[300px] truncate ">{(row.original?.skills || []).join(", ")}</div>
    ),
  },
  {
    id: "task",
    accessorKey: "task",
    header: t("pages.staff.columns.task"),
    cell: () => <div className="font-medium ">21/30</div>, // Placeholder
  },
  {
    id: "action",
    header: t("pages.staff.columns.actions"),
    cell: ({ row }: { row: Row<VirtualStaffModel> }) => (
      <Link href={toNavUrl(authProtectedPaths.INTERACT.replace(":id", row.original.id))}>
        <Button className="font-semibold" variant="secondary">
          <MessagesSquare size={16} />
          {t("pages.staff.interact")}
        </Button>
      </Link>
    ), // Placeholder
  },
  {
    id: "actions",
    header: t("pages.staff.columns.actions"),
    cell: ({ row }: { row: Row<VirtualStaffModel> }) => <></>,
  },
];

const ActionCell = ({
  useDeleteStaffMutation,
  row,
  isDeleting,
}: {
  useDeleteStaffMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<VirtualStaffModel>;
}) => {
  const router = useRouter();
  const staff = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(("/staff/" + staff.id) as any);
    });
  }, [router, staff.id]);

  const handleEdit = useCallback(() => {
    router.push(("/staff/" + staff.id + "/edit") as any);
  }, [router, staff.id]);

  const handleDelete = useCallback(async () => {
    return useDeleteStaffMutation.mutateAsync(staff.id);
  }, [useDeleteStaffMutation, staff.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

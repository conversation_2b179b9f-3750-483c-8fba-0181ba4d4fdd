import { useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { clearAuth } from "@/lib/auth";

export const useLogout = () => {
  const { t } = useTranslation();
  const router = useRouter();

  const handleLogout = useCallback(() => {
    clearAuth();
    toast.success(t("auth.logoutSuccess"));
    router.push("/login");
  }, [t, router]);

  useEffect(() => {
    handleLogout();
  }, [handleLogout]);
};

"use client";

import { useEffect, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UploadImage } from "@/lib/apis/product";

interface AddCategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: {
    name: string;
    parent_category_id: string | null;
    image: { id: string; name: string; url: string } | null;
  }) => Promise<any>;
  categories: { id: string; name: string }[];
  initialName?: string;
}

export function AddCategoryDialog({
  open,
  onOpenChange,
  onSubmit,
  categories,
  initialName = "",
}: AddCategoryDialogProps) {
  const [name, setName] = useState("");
  const [parentId, setParentId] = useState<string>("");
  const [image, setImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (open && initialName) {
      setName(initialName);
    }
  }, [open, initialName]);

  const handleImageChange = async (base64: string | null) => {
    if (!base64) {
      setImage(null);
      return;
    }

    try {
      setIsUploading(true);
      const fileName = `category_${Date.now()}.png`;
      const uploadedImage = await UploadImage({
        name: fileName,
        image: base64,
        prefix: "media",
      });
      setImage(uploadedImage.image);
    } catch (error) {
      console.error("Failed to upload image:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const result = await onSubmit({
        name,
        parent_category_id: parentId || null,
        image: image ? { id: "1", name: "image", url: image } : null,
      });

      // Clear form and close dialog after successful creation
      setName("");
      setParentId("");
      setImage(null);
      onOpenChange(false);

      return result;
    } catch (error) {
      console.error("Failed to create category:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add new category</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label>Category name</Label>
            <Input
              placeholder="Enter category name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label>Category Parent</Label>
            <Combobox
              value={parentId}
              onValueChange={setParentId}
              items={categories}
              placeholder="Select Parent"
            />
          </div>
          <div className="space-y-2">
            <Label>Image</Label>
            <ImageUpload value={image} onChange={handleImageChange} className="h-[200px]" />
          </div>
          <Button className="w-full" onClick={handleSubmit} disabled={!name.trim() || isUploading}>
            Add
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

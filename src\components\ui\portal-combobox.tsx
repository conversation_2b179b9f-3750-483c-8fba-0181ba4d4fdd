"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Loader2, Plus, X } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";

import { Popover, PopoverContent, PopoverTrigger } from "./portal-popover";

interface ComboboxProps {
  value: string;
  onValueChange: (value: string) => void;
  items: {
    id: string;
    name: string;
    disabled?: boolean;
    displayValue?: string;
  }[];
  placeholder: string;
  searchPlaceholder?: string;
  emptyText?: string;
  onCreateNew?: (value: string) => void;
  onLoadMore?: () => void;
  onClear?: () => void;
  isLoading?: boolean;
  hasNextPage?: boolean;
  isLoadingMore?: boolean;
  isShowChevronsUpDown?: boolean;
  leftIcon?: React.ComponentType<{ className?: string }>;
  variantButton?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
}

export function PortalCombobox({
  value,
  onValueChange,
  items,
  placeholder,
  searchPlaceholder,
  emptyText,
  onCreateNew,
  onLoadMore,
  onClear,
  isLoading,
  hasNextPage,
  isLoadingMore,
  isShowChevronsUpDown = true,
  leftIcon,
  variantButton = "outline",
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const { t } = useTranslation();

  const selectedItem = items.find((item) => item.id === value);

  // Move filtering logic into useMemo to optimize performance
  const filteredItems = React.useMemo(() => {
    const query = searchQuery.toLowerCase().trim();
    return items.filter(
      (item) =>
        item.name.toLowerCase().includes(query) ||
        (item.displayValue && item.displayValue.toLowerCase().includes(query))
    );
  }, [items, searchQuery]);

  const handleSelect = (itemId: string) => {
    console.log("Item clicked:", itemId);
    onValueChange(itemId);
    setOpen(false);
    setSearchQuery("");
  };

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  // Reset search when popover closes
  React.useEffect(() => {
    if (!open) {
      setSearchQuery("");
    }
  }, [open]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const bottom =
      e.currentTarget.scrollHeight - e.currentTarget.scrollTop === e.currentTarget.clientHeight;
    if (bottom && hasNextPage && !isLoadingMore && onLoadMore) {
      onLoadMore();
    }
  };

  return (
    <Popover modal={true} open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={variantButton}
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between">
          {leftIcon && React.createElement(leftIcon)}
          {selectedItem ? (
            <div className="flex w-full items-center justify-between">
              <span className="truncate text-sm font-normal">
                {selectedItem.displayValue || selectedItem.name}
              </span>
              {onClear && (
                <X
                  className="ml-2 size-4 shrink-0 cursor-pointer opacity-50 hover:opacity-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    onClear();
                    setSearchQuery("");
                  }}
                />
              )}
            </div>
          ) : (
            <span className="truncate text-sm font-normal">{placeholder}</span>
          )}
          {isShowChevronsUpDown && <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchPlaceholder || `Search ${placeholder.toLowerCase()}...`}
            value={searchQuery}
            onValueChange={handleSearch}
          />
          <CommandList onScroll={handleScroll}>
            <CommandGroup>
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <div
                    key={item.id}
                    onClick={() => !item.disabled && handleSelect(item.id)}
                    className={cn(
                      "cursor-pointer hover:bg-accent",
                      item.disabled && "cursor-not-allowed opacity-50"
                    )}>
                    <CommandItem
                      className={`text-foreground ${value === item.id ? "bg-accent text-accent-foreground" : ""}`}
                      disabled={item.disabled}>
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4 text-foreground",
                          value === item.id ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <span className="flex-1 truncate text-foreground">
                        {item.displayValue || item.name}
                      </span>
                    </CommandItem>
                  </div>
                ))
              ) : (
                <CommandEmpty>
                  {emptyText || "No results found."}
                  {onCreateNew && searchQuery && (
                    <Button
                      variant="ghost"
                      className="mt-2 w-full justify-start"
                      onClick={() => {
                        onCreateNew(searchQuery);
                        setOpen(false);
                        setSearchQuery("");
                      }}>
                      <Plus className="size-4" />
                      {searchQuery}
                    </Button>
                  )}
                </CommandEmpty>
              )}
            </CommandGroup>
            {isLoading && (
              <Button variant="ghost" className="w-full" disabled>
                <Loader2 className="mr-2 size-4 animate-spin" />
                {t("common.loading")}
              </Button>
            )}
            {isLoadingMore && (
              <Button variant="ghost" className="w-full" disabled>
                <Loader2 className="mr-2 size-4 animate-spin" />
                {t("common.loadingMore")}
              </Button>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

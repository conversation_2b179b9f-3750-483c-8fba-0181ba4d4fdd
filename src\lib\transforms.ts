import { AuthUser, TokenData, UserInfoResponse } from "@/lib/apis/types/auth";

export const transformUserInfo = (userInfo: UserInfoResponse, tokenData: TokenData): AuthUser => {
  // Transform UserAttributes array to object
  const userAttributes = userInfo.UserAttributes.reduce(
    (acc, item) => ({
      ...acc,
      [item.Name]: item.Value,
    }),
    {}
  );

  return {
    Username: userInfo.Username,
    UserAttributes: userAttributes,
    Token: tokenData,
  };
};

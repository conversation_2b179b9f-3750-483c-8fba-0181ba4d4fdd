// import { ImageProps } from "next/image";
// import { fireEvent, render, screen, waitFor } from "@testing-library/react";
// import userEvent from "@testing-library/user-event";
// import { axe } from "jest-axe";
// import { act } from "react-dom/test-utils";

// import { AppSidebar } from "./Sidebar";

// // Mock next/image
// jest.mock("next/image", () => ({
//   __esModule: true,
//   default: (props: ImageProps) => {
//     const { src, ...imgProps } = props;
//     // eslint-disable-next-line @next/next/no-img-element
//     return <img {...imgProps} src={src.toString()} alt={props.alt || ""} />;
//   },
// }));

// // Mock usePathname hook
// jest.mock("next/navigation", () => ({
//   usePathname: () => "/dashboard",
// }));

// // Mock useMediaQuery hook
// jest.mock("@/hooks/use-media-query", () => ({
//   useMediaQuery: () => false,
// }));

// // Mock next-i18next
// jest.mock("next-i18next", () => ({
//   useTranslation: () => ({
//     t: (key: string) => key,
//     i18n: {
//       changeLanguage: jest.fn(),
//       language: "en",
//     },
//   }),
// }));

// // Mock next-themes
// jest.mock("next-themes", () => ({
//   useTheme: () => ({
//     theme: "light",
//     setTheme: jest.fn(),
//   }),
// }));

// const mockUser = {
//   name: "John Doe",
//   email: "<EMAIL>",
//   image: "https://github.com/shadcn.png",
// };

// const onBranchChange = jest.fn();
// const onLogout = jest.fn();

// const defaultProps = {
//   user: mockUser,
//   currentBranch: "All",
//   branches: [
//     { name: "All", count: 10 },
//     { name: "Ho Chi Minh", count: 5 },
//     { name: "Ha Noi", count: 3 },
//     { name: "Da Nang", count: 2 },
//   ],
//   onBranchChange,
//   onLogout,
// };

// describe("Sidebar", () => {
//   it("should render without errors", () => {
//     render(<AppSidebar {...defaultProps} />);
//     expect(screen.getByAltText("OptiWarehouse Logo")).toBeInTheDocument();
//   });

//   it("should render all navigation items", () => {
//     render(<AppSidebar {...defaultProps} />);
//     const menuItems = screen.getAllByRole("menuitem");
//     expect(menuItems).toHaveLength(10);
//   });

//   it("should highlight active item", () => {
//     render(<AppSidebar {...defaultProps} />);
//     const dashboardLink = screen.getByRole("menuitem", { current: "page" });
//     expect(dashboardLink).toHaveClass("bg-accent");
//   });

//   it("should toggle between expanded and collapsed states", async () => {
//     render(<AppSidebar {...defaultProps} />);
//     const toggleButton = screen.getByLabelText("Collapse sidebar");
//     const logo = screen.getByAltText("OptiWarehouse Logo");

//     await act(async () => {
//       fireEvent.click(toggleButton);
//     });

//     expect(logo).toHaveAttribute("width", "32");

//     await act(async () => {
//       fireEvent.click(toggleButton);
//     });

//     expect(logo).toHaveAttribute("width", "140");
//   });

//   it("should be accessible", async () => {
//     const { container } = render(<AppSidebar {...defaultProps} />);
//     const results = await axe(container);
//     expect(results).toHaveNoViolations();
//   });

//   it("should support keyboard navigation", async () => {
//     render(<AppSidebar {...defaultProps} />);

//     const menuItems = screen.getAllByRole("menuitem");
//     const firstItem = menuItems[0];
//     const secondItem = menuItems[1];

//     // Focus first item
//     firstItem.focus();
//     expect(document.activeElement).toBe(firstItem);

//     // Tab to second item
//     userEvent.tab();
//     await waitFor(() => {
//       expect(document.activeElement).toBe(secondItem);
//     });
//   });

//   it("should render user profile when user prop is provided", () => {
//     render(<AppSidebar {...defaultProps} />);
//     expect(screen.getByText(mockUser.name)).toBeInTheDocument();
//     expect(screen.getByText(mockUser.email)).toBeInTheDocument();
//     expect(screen.getByAltText(mockUser.name)).toBeInTheDocument();
//   });

//   it("should render branch selection when onBranchChange prop is provided", () => {
//     render(<AppSidebar {...defaultProps} />);
//     expect(screen.getByText("All")).toBeInTheDocument();
//   });

//   it("should call onBranchChange when selecting a branch", async () => {
//     render(<AppSidebar {...defaultProps} />);

//     // Open branch dropdown
//     const branchButton = screen.getByRole("button", { name: /all/i });
//     await act(async () => {
//       fireEvent.click(branchButton);
//     });

//     // Wait for dropdown content to be visible and find the item
//     await waitFor(async () => {
//       const branchItem = screen.getByText(/Ho Chi Minh/i);
//       await act(async () => {
//         fireEvent.click(branchItem);
//       });
//       expect(onBranchChange).toHaveBeenCalledWith("Ho Chi Minh");
//     });
//   });

//   it("should call onLogout when clicking logout button", async () => {
//     render(<AppSidebar {...defaultProps} />);

//     // Open profile dropdown
//     const profileButton = screen.getByRole("button", {
//       name: new RegExp(defaultProps.user.name, "i"),
//     });
//     await act(async () => {
//       fireEvent.click(profileButton);
//     });

//     // Wait for dropdown content to be visible and find the item
//     await waitFor(async () => {
//       const logoutButton = screen.getByText(/sidebar.profile.logout/i);
//       await act(async () => {
//         fireEvent.click(logoutButton);
//       });
//       expect(onLogout).toHaveBeenCalled();
//     });
//   });

//   it("should toggle sub-items when clicking on parent item", async () => {
//     render(<AppSidebar {...defaultProps} />);

//     const productsButton = screen.getByRole("button", {
//       name: /sidebar.nav.products/i,
//     });
//     await act(async () => {
//       fireEvent.click(productsButton);
//     });

//     await waitFor(async () => {
//       const subItem = screen.getByText(/sidebar.nav.productsList/i);
//       expect(subItem).toBeVisible();
//     });
//   });

//   it("should handle keyboard shortcuts for branch switching", async () => {
//     render(<AppSidebar {...defaultProps} />);

//     await act(async () => {
//       fireEvent.keyDown(document, { key: "b", ctrlKey: true });
//     });

//     await waitFor(async () => {
//       const branchItem = screen.getByText(/ho chi minh/i);
//       expect(branchItem).toBeVisible();
//     });
//   });

//   it("should handle theme switching correctly", async () => {
//     const { rerender } = render(<AppSidebar {...defaultProps} />);

//     // Test light theme logo
//     expect(screen.getByAltText("Logo")).toHaveAttribute(
//       "src",
//       expect.stringContaining("vcare-logo-text")
//     );

//     // Mock dark theme
//     jest.mock("next-themes", () => ({
//       useTheme: () => ({
//         theme: "dark",
//         setTheme: jest.fn(),
//       }),
//     }));

//     rerender(<AppSidebar {...defaultProps} />);
//     expect(screen.getByAltText("Logo")).toHaveAttribute(
//       "src",
//       expect.stringContaining("vcare-logo-text-dark")
//     );
//   });

//   it("should handle mobile view correctly", async () => {
//     // Mock mobile view
//     jest.mock("@/hooks/use-media-query", () => ({
//       useMediaQuery: () => true,
//     }));

//     render(<AppSidebar {...defaultProps} />);

//     const sidebar = screen.getByRole("complementary");
//     expect(sidebar).toHaveClass("group-data-[collapsible=icon]:overflow-auto");
//   });

//   it("should calculate active states correctly based on pathname", () => {
//     // Mock different pathnames
//     jest.mock("next/navigation", () => ({
//       usePathname: () => "/products",
//     }));

//     render(<AppSidebar {...defaultProps} />);

//     const productsLink = screen.getByRole("menuitem", {
//       name: /sidebar.nav.products/i,
//     });
//     expect(productsLink).toHaveClass("bg-accent");

//     // Test sub-item active state
//     jest.mock("next/navigation", () => ({
//       usePathname: () => "/products/new",
//     }));

//     const { rerender } = render(<AppSidebar {...defaultProps} />);
//     rerender(<AppSidebar {...defaultProps} />);

//     const newProductLink = screen.getByRole("menuitem", {
//       name: /sidebar.nav.productsNew/i,
//     });
//     expect(newProductLink).toHaveClass("bg-accent");
//     expect(productsLink).toHaveClass("bg-accent"); // Parent should also be active
//   });

//   it("should handle scroll area behavior", () => {
//     render(<AppSidebar {...defaultProps} />);

//     const scrollArea = screen
//       .getByRole("complementary")
//       .querySelector("[data-radix-scroll-area-viewport]");
//     expect(scrollArea).toBeInTheDocument();
//     expect(scrollArea).toHaveClass("flex-1");
//   });
// });

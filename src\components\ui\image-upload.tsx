"use client";

import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Eye, ImagePlus, Plus, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

interface ImageUploadProps {
  value: string | null;
  onChange: (value: string | null) => void;
  className?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxTotalSize?: number;
  isSquare?: boolean;
}

// Add size constant
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

// Add Zod schemas
const filenameSchema = z.string();

const imageFileSchema = z.object({
  name: filenameSchema,
  size: z.number().max(MAX_FILE_SIZE, "File size must not exceed 5MB"),
  type: z.string().startsWith("image/", "Only image files are allowed"),
});

export function ImageUpload({
  value,
  onChange,
  className,
  multiple = false,
  maxTotalSize = MAX_FILE_SIZE,
  isSquare = false,
}: ImageUploadProps) {
  const { t } = useTranslation();
  const [isUploading, setIsUploading] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [fileList, setFileList] = useState<{ url: string; size: number }[]>([]);
  const [totalSize, setTotalSize] = useState(0);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (value) {
      try {
        const files = multiple ? JSON.parse(value) : [value];
        const processedFiles = files.map((url: string) => {
          // Try to extract size from base64 if available
          const base64Length = url.split(",")[1]?.length || 0;
          const sizeInBytes = Math.ceil((base64Length * 3) / 4);
          return { url, size: sizeInBytes };
        });
        setFileList(processedFiles);
        const total = processedFiles.reduce(
          (acc: number, file: { size?: number }) => acc + (file.size ?? 0),
          0
        );
        setTotalSize(total);
      } catch (error) {
        console.error("Error processing files:", error);
        setFileList([]);
        setTotalSize(0);
      }
    } else {
      setFileList([]);
      setTotalSize(0);
    }
  }, [value, multiple]);

  const convertToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Include the filename in the data URL metadata
        const base64Data = result.split(",")[1];
        const mimeType = file.type;
        resolve(`data:${mimeType};name=${encodeURIComponent(file.name)};base64,${base64Data}`);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handlePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl);
    setPreviewOpen(true);
  };

  const handleRemove = (indexToRemove: number) => {
    const removedFile = fileList[indexToRemove];
    const newFileList = fileList.filter((_, index) => index !== indexToRemove);
    setFileList(newFileList);
    setTotalSize((prev) => Math.max(0, prev - (removedFile.size ?? 0)));
    onChange(
      multiple ? JSON.stringify(newFileList.map((file) => file.url)) : newFileList[0]?.url || null
    );
  };

  const handleFiles = async (files: File[]) => {
    try {
      // Validate files
      const validationResults = await Promise.all(
        files.map(async (file) => {
          const fileValidation = imageFileSchema.safeParse({
            name: file.name,
            size: file.size,
            type: file.type,
          });

          if (!fileValidation.success) {
            return {
              file,
              error: fileValidation.error.errors[0].message,
            };
          }

          return { file, error: null };
        })
      );

      // Check for validation errors
      const errors = validationResults.filter((result) => result.error);
      if (errors.length > 0) {
        toast.error(t("common.error"), {
          description: errors[0].error,
        });
        return;
      }

      // Calculate new total size including existing files
      const newFilesTotalSize = files.reduce((acc: number, file: File) => acc + file.size, 0);
      const projectedTotalSize = totalSize + newFilesTotalSize;

      if (projectedTotalSize > maxTotalSize) {
        toast.error(t("common.error"), {
          description: `Total file size must not exceed 5MB. Current total: ${(
            totalSize /
            (1024 * 1024)
          ).toFixed(2)}MB, Trying to add: ${(newFilesTotalSize / (1024 * 1024)).toFixed(2)}MB`,
        });
        return;
      }

      setIsUploading(true);
      try {
        const newFiles = await Promise.all(
          files.map(async (file) => {
            const base64 = await convertToBase64(file);
            return {
              url: base64,
              size: file.size,
            };
          })
        );

        let newFileList: { url: string; size: number }[];

        if (multiple) {
          // Keep existing files and add new ones
          const existingFiles = fileList.map((f) => ({ url: f.url, size: f.size ?? 0 }));
          newFileList = [...existingFiles, ...newFiles];
        } else {
          newFileList = [newFiles[0]];
        }

        setFileList(newFileList);
        const newTotal = newFileList.reduce(
          (acc: number, file: { size: number }) => acc + file.size,
          0
        );
        setTotalSize(newTotal);
        onChange(
          multiple ? JSON.stringify(newFileList.map((file) => file.url)) : newFileList[0].url
        );

        // Reset the file input value
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error(t("common.error"), {
          description: error instanceof Error ? error.message : t("common.uploadError"),
        });
      } finally {
        setIsUploading(false);
      }
    } catch (error) {
      console.error("Error validating files:", error);
      toast.error(t("common.error"), {
        description: t("common.uploadError"),
      });
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    const files = Array.from(e.dataTransfer.files);
    await handleFiles(files);
  };

  return (
    <div className={cn("border-2 border-dashed bg-muted rounded-lg hover:bg-muted/50", className)}>
      <div onDrop={handleDrop} onDragOver={handleDragOver}>
        <label className="block cursor-pointer">
          <input
            type="file"
            ref={fileInputRef}
            accept="image/*"
            className="hidden"
            onChange={(e) => {
              if (e.target.files) {
                handleFiles(Array.from(e.target.files));
              }
            }}
            disabled={isUploading}
            multiple={multiple}
          />
          <div className="p-6">
            <div className="flex flex-col items-center justify-center self-stretch">
              {fileList.length === 0 ? (
                <>
                  <div
                    className={cn(
                      "flex w-full flex-col items-center justify-center text-muted-foreground",
                      isSquare ? "aspect-square" : "min-h-[160px]"
                    )}>
                    {isUploading ? (
                      <Button variant="ghost" disabled uploading className="pointer-events-none" />
                    ) : (
                      <>
                        <ImagePlus className="size-12 stroke-[1]" />
                        <div className="text-center">
                          <span className="text-sm italic">{t("common.uploadImage")}</span>
                          <span className="ml-1 text-sm font-bold text-primary">
                            {t("common.upload")}
                          </span>
                        </div>
                        <div className="mt-2 text-center">
                          <span className="text-sm text-muted-foreground">
                            {t("common.totalSize")}: {(totalSize / (1024 * 1024)).toFixed(2)}MB /
                            5MB
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </>
              ) : (
                <>
                  <div
                    className={cn(
                      "max-h-[300px] w-full overflow-hidden",
                      isSquare ? "aspect-square" : ""
                    )}>
                    <div
                      className={cn(
                        "grid h-full gap-2 overflow-y-auto",
                        multiple
                          ? isSquare
                            ? "grid-cols-1"
                            : "grid-cols-5 max-h-[250px]"
                          : "flex items-center justify-center"
                      )}>
                      {fileList.map((file, index) => (
                        <div
                          key={index}
                          className={cn(
                            "group relative shrink-0",
                            isSquare
                              ? "aspect-square w-full"
                              : multiple
                                ? "aspect-square"
                                : "w-full aspect-square"
                          )}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handlePreview(file.url);
                          }}>
                          <div className="relative aspect-square w-full cursor-pointer">
                            <Image
                              src={file.url}
                              alt={`Upload ${index + 1}`}
                              fill
                              className={cn(
                                "rounded-lg object-cover",
                                isSquare ? "object-contain" : "object-cover"
                              )}
                              unoptimized
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handlePreview(file.url);
                              }}
                            />
                            <div className="absolute inset-0 rounded-lg bg-black/40 opacity-0 transition-opacity group-hover:opacity-100">
                              <div className="absolute right-2 top-2 flex gap-2">
                                <button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    handlePreview(file.url);
                                  }}
                                  className="rounded-full bg-white/20 p-1.5 transition-colors hover:bg-white/40">
                                  <Eye className="size-4 text-white" />
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    handleRemove(index);
                                  }}
                                  className="rounded-full bg-white/20 p-1.5 transition-colors hover:bg-white/40">
                                  <Trash2 className="size-4 text-white" />
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                      {multiple && (
                        <div
                          className="group relative flex aspect-square shrink-0 cursor-pointer items-center justify-center rounded-lg border border-dashed border-muted-foreground/50 hover:bg-muted/80"
                          onClick={(e) => {
                            e.preventDefault();
                            if (fileInputRef.current) {
                              fileInputRef.current.click();
                            }
                          }}>
                          <Plus className="size-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="mt-4 text-center">
                    {/* <span className="text-sm text-muted-foreground">
                      {t("common.totalSize")}: {(totalSize / (1024 * 1024)).toFixed(2)}MB / 5MB
                    </span> */}
                  </div>
                </>
              )}
            </div>
          </div>
        </label>
      </div>

      {/* Preview Dialog */}
      <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
        <DialogContent className="max-h-[90vh] max-w-[90vw] p-0">
          {previewImage && (
            <div className="relative flex size-full items-center justify-center bg-black/50">
              <Image
                src={previewImage}
                alt="Preview"
                width={1200}
                height={800}
                className="max-h-[90vh] max-w-[90vw] object-contain"
                unoptimized
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

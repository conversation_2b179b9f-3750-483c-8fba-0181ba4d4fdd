import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { Row } from "@tanstack/react-table";
import { CircleCheck } from "lucide-react";
import { toast } from "sonner";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Badge } from "@/components/ui/badge";
import { Connection, ConnectionStatus } from "@/lib/apis/connection";
import { cn } from "@/lib/utils";

import { useUpdateConnectionStatus } from "../hooks/connection";

const capitalizeFirstLetter = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

const getStatusColor = (status: ConnectionStatus) => {
  switch (status) {
    case ConnectionStatus.ACTIVE:
      return "bg-success text-white ";
    case ConnectionStatus.PENDING:
      return "bg-warning text-black";
    case ConnectionStatus.INACTIVE:
      return "bg-destructive text-white";
    default:
      return "bg-gray-100 text-gray-700 border-gray-300";
  }
};

export const columns = (t: any): CustomColumn<Connection>[] => [
  {
    id: "channel",
    accessorKey: "channel",
    header: t("pages.channel.headers.channel"),
    isMainColumn: true,
    cell: ({ row }: { row: Row<Connection> }) => (
      <div className="flex items-center gap-4">
        <ImageColumn
          iszoomable={false}
          src={row.original.image}
          width={40}
          height={40}
          alt="Image"
        />
        <TextColumn text={row.original.name} className="text-sm font-normal text-foreground" />
      </div>
    ),
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.channel.headers.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<Connection> }) => {
      const status = row.original.status as ConnectionStatus;
      return (
        <Badge
          variant="outline"
          className={cn(
            "text-xs font-medium border hover:no-underline hover:opacity-100",
            getStatusColor(status)
          )}>
          {capitalizeFirstLetter(status)}
        </Badge>
      );
    },
  },
  {
    id: "url",
    accessorKey: "url",
    header: t("pages.channel.headers.url"),
    sorter: true,
    sortKey: "url",
    cell: ({ row }: { row: Row<Connection> }) => (
      <span className="text-sm">{row?.original?.url || "---"}</span>
    ),
  },
  {
    id: "createdAt",
    accessorKey: "created_at",
    header: t("pages.channel.headers.createdAt"),
    sorter: true,
    hidden: true,
    sortKey: "created_at",
    cell: ({ row }: { row: Row<Connection> }) => <DateColumn date={row?.original?.created_at} />,
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    header: t("pages.channel.headers.lastUpdated"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Connection> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.channel.headers.actions"),
    cell: ({ row }: { row: Row<Connection> }) => <ActionCell t={t} row={row} />,
  },
];

const ActionCell = ({ row, t }: { row: Row<Connection>; t: any }) => {
  const { mutate: updateConnectionStatus } = useUpdateConnectionStatus({
    onSuccess: (message) => {
      toast.success(message);
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const router = useRouter();
  const connection = row.original;
  const status = connection.status as ConnectionStatus;

  const handleView = useCallback(() => {
    router.push(`/channels/connections/${connection.id}`);
  }, [router, connection.id]);

  const handleStatusUpdate = useCallback(() => {
    updateConnectionStatus(connection.id);
  }, [updateConnectionStatus, connection.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "edit",
          title: t("pages.channel.actions.configure"),
          onClick: handleView,
        },
        {
          type: "edit",
          title:
            status === ConnectionStatus.INACTIVE
              ? t("pages.channel.actions.activate")
              : t("pages.channel.actions.deactivate"),
          customIcon: <CircleCheck size={16} />,
          onClick: handleStatusUpdate,
        },
      ]}
    />
  );
};

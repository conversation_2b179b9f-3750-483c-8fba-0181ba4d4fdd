export interface IVariant {
  color: string;
  price: number;
  image: string;
}

export interface IProductMapping {
  productImage: string[];
  productTitle: string;
  description: string;
  price: number;
  sku: string;
  brand: string;
  category: string;
  inventory: number;
  variants: IVariant[];
}

export const mockProductMapping: {
  shopify: IProductMapping;
  tiktok: IProductMapping;
} = {
  shopify: {
    productImage: ["/camera1.jpg", "/camera2.jpg", "/camera3.jpg"],
    productTitle: "Digital Camera MX10 Ultra",
    description: "---",
    price: 49.99,
    sku: "123456",
    brand: "Sony",
    category: "Cameras",
    inventory: 99,
    variants: [
      {
        color: "Black",
        price: 50,
        image: "/camera-black.jpg",
      },
      {
        color: "White",
        price: 50,
        image: "/camera-white.jpg",
      },
      {
        color: "Blue",
        price: 50,
        image: "/camera-blue.jpg",
      },
    ],
  },
  tiktok: {
    productImage: ["/drone1.jpg", "/drone2.jpg"],
    productTitle: "Sky Explorer Drone X5",
    description: "High-performance drone with 4K camera.",
    price: 199.99,
    sku: "DRN-98765",
    brand: "DJI",
    category: "Drones",
    inventory: 50,
    variants: [
      {
        color: "Gray",
        price: 199.99,
        image: "/drone-gray.jpg",
      },
      {
        color: "Red",
        price: 209.99,
        image: "/drone-red.jpg",
      },
      {
        color: "Blue",
        price: 209.99,
        image: "/drone-blue.jpg",
      },
    ],
  },
};

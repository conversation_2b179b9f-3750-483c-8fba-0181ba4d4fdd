---
description: 
globs: 
alwaysApply: false
---
# UI Verification and Design Matching Guidelines

## Design Inspection Process
1. Initial Setup:
   ```typescript
   // Use browser dev tools for measurements
   // Enable overlay grid for alignment
   // Set up responsive breakpoints matching design
   // Use color picker for exact color values
   ```

2. Key Metrics to Check:
   ```typescript
   interface DesignMetrics {
     spacing: {
       padding: string;    // "16px 24px"
       margin: string;     // "8px"
       gap: string;       // "12px"
     };
     typography: {
       fontSize: string;   // "16px"
       lineHeight: string; // "1.5"
       fontWeight: number; // 500
       fontFamily: string; // "Inter"
     };
     colors: {
       hex: string;       // "#1A2B3C"
       rgb: string;       // "rgb(26, 43, 60)"
       opacity: number;   // 0.8
     };
     layout: {
       width: string;     // "328px"
       height: string;    // "48px"
       position: string;  // "relative"
     };
   }
   ```

## Verification Checklist
1. Layout and Positioning:
   - [ ] Element dimensions match design
   - [ ] Spacing between elements is exact
   - [ ] Alignment (left, right, center) matches
   - [ ] Grid system adherence
   - [ ] Responsive breakpoints behavior

2. Typography:
   - [ ] Font family is correct
   - [ ] Font size matches design
   - [ ] Font weight is accurate
   - [ ] Line height matches
   - [ ] Letter spacing is correct
   - [ ] Text alignment matches

3. Colors and Styling:
   - [ ] Background colors match exactly
   - [ ] Text colors are correct
   - [ ] Border colors and styles match
   - [ ] Shadows match design
   - [ ] Opacity levels are accurate
   - [ ] Gradients match precisely

4. Components:
   - [ ] Button sizes and padding
   - [ ] Input field dimensions
   - [ ] Icon sizes and colors
   - [ ] Card/container styling
   - [ ] Interactive state styling

## Implementation Example
```typescript
// Component with precise styling
import styled from 'styled-components';

const StyledButton = styled.button<{ $design: DesignMetrics }>`
  // Exact spacing
  padding: ${props => props.$design.spacing.padding};
  margin: ${props => props.$design.spacing.margin};
  
  // Precise typography
  font-size: ${props => props.$design.typography.fontSize};
  line-height: ${props => props.$design.typography.lineHeight};
  font-weight: ${props => props.$design.typography.fontWeight};
  font-family: ${props => props.$design.typography.fontFamily};
  
  // Exact colors
  background-color: ${props => props.$design.colors.hex};
  opacity: ${props => props.$design.colors.opacity};
  
  // Precise layout
  width: ${props => props.$design.layout.width};
  height: ${props => props.$design.layout.height};
  position: ${props => props.$design.layout.position};
`;
```

## Measurement Tools and Techniques
1. Browser Dev Tools:
   ```typescript
   // Use Element Inspector for:
   - Precise pixel measurements
   - Color picking
   - Font inspection
   - Box model verification
   - Computed styles checking
   ```

2. Design Overlay:
   ```typescript
   // Implement design overlay component
   const DesignOverlay = styled.div`
     position: fixed;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     pointer-events: none;
     opacity: 0.5;
     z-index: 9999;
     background-image: url('design.png');
     background-size: contain;
   `;
   ```

## Responsive Design Verification
1. Breakpoint Testing:
   ```typescript
   const breakpoints = {
     mobile: '320px',
     tablet: '768px',
     desktop: '1024px',
     wide: '1440px'
   };

   // Test each breakpoint
   Object.entries(breakpoints).forEach(([device, width]) => {
     describe(`${device} layout`, () => {
       it('matches design specifications', () => {
         // Verify layout at each breakpoint
       });
     });
   });
   ```

2. Element Scaling:
   ```typescript
   const ScaledElement = styled.div<{ $baseSize: number }>`
     // Fluid scaling based on viewport
     font-size: clamp(
       ${props => props.$baseSize * 0.75}px,
       ${props => props.$baseSize}px,
       ${props => props.$baseSize * 1.25}px
     );
   `;
   ```

## Common Issues and Solutions
1. Pixel-Perfect Alignment:
   ```typescript
   // Use CSS Grid for precise alignment
   const GridLayout = styled.div`
     display: grid;
     grid-template-columns: repeat(12, 1fr);
     gap: 16px;
     padding: 24px;
     
     // Ensure pixel-perfect alignment
     transform: translateZ(0);
     backface-visibility: hidden;
   `;
   ```

2. Font Rendering:
   ```typescript
   // Ensure consistent font rendering
   const Text = styled.p`
     -webkit-font-smoothing: antialiased;
     -moz-osx-font-smoothing: grayscale;
     text-rendering: optimizeLegibility;
   `;
   ```

## Verification Process
1. Initial Setup:
   - Open design image and implementation side by side
   - Enable grid overlay
   - Set viewport to design dimensions
   - Prepare measurement tools

2. Systematic Check:
   - Start with layout structure
   - Move to component sizing
   - Check typography details
   - Verify colors and effects
   - Test responsive behavior

3. Documentation:
   ```typescript
   interface DesignVerification {
     component: string;
     status: 'matched' | 'needs-adjustment' | 'verified';
     discrepancies?: {
       type: 'spacing' | 'color' | 'typography' | 'layout';
       description: string;
       currentValue: string;
       expectedValue: string;
     }[];
     notes?: string;
   }
   ```

## Quality Assurance Steps
1. Visual Regression Testing:
   ```typescript
   // Example using Jest and react-testing-library
   it('matches design snapshot', () => {
     const { container } = render(<Component />);
     expect(container).toMatchSnapshot();
   });
   ```

2. Accessibility Verification:
   ```typescript
   // Ensure design compliance doesn't break accessibility
   it('maintains accessibility standards', () => {
     const { container } = render(<Component />);
     expect(axe(container)).toHaveNoViolations();
   });
   ```

3. Cross-browser Testing:
   - Verify in Chrome, Firefox, Safari
   - Check mobile browsers
   - Test different OS renderings

## Final Checklist
- [ ] All measurements match design specs
- [ ] Colors are exactly matched (including opacity)
- [ ] Typography is precise
- [ ] Spacing is consistent
- [ ] Responsive behavior matches design
- [ ] Interactive states are correct
- [ ] Animations match design
- [ ] Cross-browser compatibility verified
- [ ] Accessibility maintained
- [ ] Performance optimized

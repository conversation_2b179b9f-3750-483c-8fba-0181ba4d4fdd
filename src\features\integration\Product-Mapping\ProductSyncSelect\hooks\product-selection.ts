import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

import { DestinationProductData } from "@/features/integration/Product-Mapping/hooks/types";

import { productDestinationDataAPI } from "@/lib/apis/connection";
import { ResponseList } from "@/lib/apis/types/common";
import { MappingProduct } from "@/lib/apis/types/mapping-product";

import { productSyncSelectKeys } from "./keys";

interface UseDestinationProductsOptions {
  connectionId?: string;
  enabled?: boolean;
}

export const useDestinationProducts = (options: UseDestinationProductsOptions = {}) => {
  const searchParams = useSearchParams();
  const { connectionId: propConnectionId, enabled = true } = options;

  // Try to get connectionId from props first, then fallback to URL params
  const connectionId = propConnectionId || searchParams.get("connection_id") || undefined;

  const query = useQuery<ResponseList<DestinationProductData>>({
    queryKey: productSyncSelectKeys.list({ connection_id: connectionId }),
    queryFn: async () => {
      const response = await productDestinationDataAPI.list({ connection_id: connectionId });
      return response;
    },
    enabled: enabled && !!connectionId,
  });

  return {
    ...query,
    destinationProducts: query.data?.items || [],
    total: query.data?.total || 0,
    isLoading: query.isLoading,
    isError: query.isError,
  };
};

interface UseDestinationProductDetailOptions {
  connectionId?: string;
  productId?: string;
  enabled?: boolean;
}

export const useDestinationProductDetail = (options: UseDestinationProductDetailOptions = {}) => {
  const searchParams = useSearchParams();
  const { connectionId: propConnectionId, productId: propProductId, enabled = true } = options;

  // Try to get connectionId and productId from props first, then fallback to URL params
  const connectionId = propConnectionId || searchParams.get("connection_id") || undefined;
  const productId = propProductId || searchParams.get("product_id") || undefined;

  const query = useQuery<DestinationProductData>({
    queryKey: productSyncSelectKeys.detail({ connection_id: connectionId, product_id: productId }),
    queryFn: async () => {
      if (!connectionId || !productId) {
        throw new Error("Connection ID and Product ID are required");
      }
      const response = await productDestinationDataAPI.detail(connectionId, productId);
      return response;
    },
    enabled: enabled && !!connectionId && !!productId,
  });
  console.log("query", query);

  return {
    ...query,
    productDetail: query.data,
    variants: query.data?.standard_data?.variants || [],
    isLoading: query.isLoading,
    isError: query.isError,
  };
};

export const useProductSelection = (
  product: MappingProduct,
  onClose: () => void,
  onMap?: (data: {
    destination_data_id: string;
    is_extra_destination_mapping: boolean;
    extra_mapping_data: Record<string, string>;
  }) => Promise<void>
) => {
  // ... rest of the code ...
};

"use client";

import { useMemo, useState } from "react";
import { LayoutGrid, List, PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { CreateStaffDialog } from "@/features/bots/staff/components/create-staff/create-staff-dialog";
import { columns } from "@/features/bots/staff/components/staff_list/column";
import StaffGrid from "@/features/bots/staff/components/staff_list/staff_grid";
import { useStaff } from "@/features/bots/staff/hooks/staff";
import { Role } from "@/features/bots/staff/hooks/type";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui";
import { uppercaseToTitleCase } from "@/utils/helpers/text-formater";

export default function StaffListPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );
  const { staff, total, isLoading, isFetching, refetch, useDeleteStaffMutation } =
    useStaff(options);
  const staffRoleOptions = Object.values(Role).map((role) => ({
    label: uppercaseToTitleCase(role),
    value: role,
  }));
  const isTableLoading = isLoading || isFetching;

  // Add state for staff create dialog
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Create a memoized initialData object that reacts to filter changes
  const staffInitialData = useMemo(
    () => ({
      name: "",
      department: getInitialParams["department_id"]?.toString() || "",
      role: (getInitialParams["role"] as Role) || Role.VIRTUAL_STAFF,
    }),
    [getInitialParams]
  );

  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "staff",
      searchPlaceHolder: t("pages.staff.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "department_id",
          type: EFilterType.SELECT_BOX,
          title: t("pages.staff.filters.department"),
          defaultValue: getInitialParams["department_id"],
          remote: true,
          pathUrlLoad: "/onexbots/departments",
        },
        {
          id: "role",
          type: EFilterType.SELECT_BOX,
          title: t("pages.staff.filters.role"),
          defaultValue: getInitialParams["role"],
          dataOption: staffRoleOptions,
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams, staffRoleOptions]);

  const handleOpenCreateDialog = () => {
    setIsCreateDialogOpen(true);
  };

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("pages.staff.actionButton.create"),
        icon: PlusIcon,
        onClick: handleOpenCreateDialog,
      },
    ],
    showRefresh: false,
  };

  // Add view state
  const [view, setView] = useState<"list" | "grid">("grid");

  return (
    <>
      <TableCard>
        <TableHeader
          title={t("pages.staff.title")}
          filterType="staff"
          data={staff || []}
          filterProps={filterConfig as FilterTableProps}
          rightComponent={<GroupButton {...groupButtonConfig} />}
          customRightFilter={<ViewSwitcher view={view} setView={setView} />}
          isSaveFilters={false}
          isExportable={false}
        />
        {view === "list" ? (
          <TableContainer
            columns={columns(useDeleteStaffMutation, isFetching, t)}
            data={staff || []}
            loading={isTableLoading}
            total={total}
            pageSize={Number(getInitialParams.limit)}
            currentPage={Number(getInitialParams.page)}
          />
        ) : (
          <StaffGrid
            total={total}
            pageSize={Number(getInitialParams.limit)}
            currentPage={Number(getInitialParams.page)}
            staff={staff || []}
            loading={isTableLoading}
          />
        )}
      </TableCard>

      {/* Add the CreateStaffDialog component */}
      <CreateStaffDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        initialData={staffInitialData}
      />
    </>
  );
}

function ViewSwitcher({
  view,
  setView,
}: {
  view: "list" | "grid";
  setView: (view: "list" | "grid") => void;
}) {
  return (
    <Tabs value={view} onValueChange={(view) => setView(view as "list" | "grid")}>
      <TabsList>
        <TabsTrigger value="grid">
          <LayoutGrid size={16} />
        </TabsTrigger>
        <TabsTrigger value="list">
          <List size={16} />
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}

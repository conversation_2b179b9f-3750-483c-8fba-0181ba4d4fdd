"use client";

import { useRouter } from "next/navigation";
import { CircleAlert, Mail } from "lucide-react";
import { useTranslation } from "react-i18next";

import { BrandSection } from "@/features/auth/components/brand-section";
import { Footer } from "@/features/auth/components/public-footer";
import { useForgotPassword } from "@/features/auth/hooks/useForgotPassword";

import { Button } from "@/components/ui/button";
import { Form, FormField } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

export const ForgotPassword = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const { onForgotPasswordSubmit, loading, form, error, handleFocus } = useForgotPassword();

  return (
    <div className="min-h-screen w-screen bg-bg-secondary">
      <div className="grid min-h-screen w-screen lg:grid-cols-[1fr_2fr]">
        <BrandSection />
        <div className="relative flex flex-col justify-between p-8">
          <div className="flex flex-1 items-center justify-center">
            <div className="relative z-10 w-full max-w-[400px] space-y-6">
              <div className="space-y-1.5 text-center">
                <h1 className="text-2xl font-semibold tracking-tight">
                  {t("auth.forgotPasswordTitle")}
                </h1>
                <p className="text-sm text-muted-foreground">
                  {t("auth.forgotPasswordDescription")}
                </p>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onForgotPasswordSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field, fieldState }) => (
                      <Input
                        {...field}
                        autoComplete="email"
                        placeholder={t("auth.emailPlaceholder")}
                        disabled={loading}
                        error={fieldState.error?.message}
                        onFocus={handleFocus}
                      />
                    )}
                  />

                  <div className="space-y-2">
                    {error && (
                      <div className="flex items-center text-sm text-red-500">
                        <CircleAlert className="mr-2 size-4" />
                        {t(String(error))}
                      </div>
                    )}
                    <Button type="submit" disabled={loading} className="w-full">
                      <Mail className="mr-2 size-5" />
                      {loading ? t("auth.sending") : t("auth.sendInstructions")}
                    </Button>
                  </div>

                  <div className="space-x-2 text-center text-sm">
                    <span>{t("auth.noAccount")}</span>
                    <Button
                      type="button"
                      variant="link"
                      className="h-fit p-0 text-sm font-medium leading-none underline hover:text-foreground"
                      onClick={() => router.push("/signup")}>
                      {t("auth.signUp")}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </div>

          <Footer />
        </div>
      </div>
    </div>
  );
};

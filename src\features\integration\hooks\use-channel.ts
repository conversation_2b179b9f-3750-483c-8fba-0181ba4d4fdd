import { useQuery } from "@tanstack/react-query";

import { channelApi } from "@/lib/apis/channel";

/**
 * Hook để lấy danh sách channels
 */
export function useChannels({ enabled = false }: { enabled: boolean }) {
  return useQuery({
    queryKey: ["channels"],
    queryFn: () => channelApi.list(),
    staleTime: 1000 * 60 * 5, // 5 phút
    enabled: enabled,
  });
}

/**
 * Hook để lấy thông tin chi tiết của một connection dựa vào ID
 */
export function useConnection(connectionId: string) {
  return useQuery({
    queryKey: ["connection", connectionId],
    queryFn: () => channelApi.getConnection(connectionId),
    enabled: !!connectionId,
  });
}

"use client";

import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui";

interface EditStaffFooterProps {
  form: UseFormReturn<any>;
  isUpdating?: boolean;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export default function EditStaffFooter({
  isUpdating = false,
  form,
  onSubmit,
  onCancel,
}: EditStaffFooterProps) {
  const { t } = useTranslation();
  const isDirty = form.formState.isDirty;

  return (
    <div className="flex h-16 flex-none items-center justify-end gap-2 bg-card px-6 py-0">
      <Button disabled={isUpdating} variant="outline" onClick={onCancel} type="button">
        {t("common.cancel")}
      </Button>
      <Button loading={isUpdating} onClick={form.handleSubmit(onSubmit)} disabled={!isDirty}>
        {t("common.saveChanges")}
      </Button>
    </div>
  );
}

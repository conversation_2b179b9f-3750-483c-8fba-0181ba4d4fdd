import { Skeleton } from "@/components/ui/skeleton";

export function SavedFilterSkeleton() {
  return (
    <div className="flex items-center justify-between gap-2">
      <div className="">
        <div className="flex h-9 items-center gap-2 border-b border-transparent">
          {/* Default tabs */}
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className={`h-8 ${i === 0 ? "w-[60px]" : "w-[120px]"} rounded`} />
          ))}
        </div>
      </div>
      {/* <Skeleton className="h-9 w-[100px] rounded" /> */}
    </div>
  );
}

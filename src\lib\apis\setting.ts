import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";

export interface ColorSettings {
  light: Record<string, string>;
  dark: Record<string, string>;
}

export interface LogoImageData {
  id: string;
  name: string;
  url: string;
}

export interface LogoSettings {
  lightLogo: LogoImageData | null;
  darkLogo: LogoImageData | null;
  lightIcon: LogoImageData | null;
  darkIcon: LogoImageData | null;
  favicon: LogoImageData | null;
}

export interface SettingPayload {
  setting_name: string;
  setting_value: ColorSettings | LogoSettings;
}

export const settingApi = {
  updateColorSettings: async (colors: ColorSettings) => {
    const payload: SettingPayload = {
      setting_name: "color",
      setting_value: colors,
    };
    return privateApi.post(ENDPOINTS.SETTING, payload);
  },
  updateLogoSetting: async (setting_name: string, setting_value: LogoSettings) => {
    const payload: SettingPayload = {
      setting_name,
      setting_value,
    };
    return privateApi.post(ENDPOINTS.SETTING, payload);
  },
};

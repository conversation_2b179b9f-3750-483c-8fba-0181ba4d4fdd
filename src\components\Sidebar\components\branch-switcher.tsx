"use client";

import * as React from "react";
import { ChevronsUpDown, Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

export function BranchSwitcher({
  branches,
}: {
  branches: {
    name: string;
    logo: React.ElementType;
    plan: string;
  }[];
}) {
  const { isTablet } = useSidebar();
  const [activeBranch, setActiveBranch] = React.useState(branches[0]);
  const { t } = useTranslation();
  return (
    <SidebarGroup>
      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                  <activeBranch.logo className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{"BaaBaby"}</span>
                  <span className="truncate text-xs">{t(activeBranch.name)}</span>
                </div>
                <ChevronsUpDown className="ml-auto" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              align="start"
              side={isTablet ? "bottom" : "right"}
              sideOffset={16}>
              <DropdownMenuLabel className="text-xs text-muted-foreground">
                {t("branch.branch")}
              </DropdownMenuLabel>
              {branches.map((branch, index) => (
                <DropdownMenuItem
                  key={branch.name}
                  onClick={() => setActiveBranch(branch)}
                  className="gap-2 p-2">
                  <div className="flex size-6 items-center justify-center rounded-sm border">
                    <branch.logo className="size-4 shrink-0" />
                  </div>
                  {t(branch.name)}
                  <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem className="gap-2 p-2">
                <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                  <Plus className="size-4" />
                </div>
                <div className="font-medium text-muted-foreground">{t("branch.addBranch")}</div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
}

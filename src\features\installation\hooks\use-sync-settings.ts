import { useState } from "react";
import { t } from "i18next";
import { toast } from "sonner";

import { channel<PERSON>pi } from "@/lib/apis/channel";
import { SyncSettingsPayload } from "@/lib/apis/types/channel";
import { COOKIES } from "@/utils/constants/cookies";
import { getCookie, setCookie } from "@/utils/cookie";

interface SyncSettings {
  products: boolean;
  inventory: boolean;
  orders: boolean;
}

interface SyncSettingsConfig {
  key: keyof SyncSettings;
  title: string;
  description: string;
}

interface UseSyncSettingsProps {
  syncSettingsConfig: SyncSettingsConfig[];
  initialSettings?: Partial<SyncSettings>;
}

export const useSyncSettings = ({ syncSettingsConfig, initialSettings }: UseSyncSettingsProps) => {
  const [syncSettings, setSyncSettings] = useState<SyncSettings>({
    products: initialSettings?.products ?? false,
    inventory: initialSettings?.inventory ?? false,
    orders: initialSettings?.orders ?? false,
  });

  const [isSuccess, setIsSuccess] = useState(false);
  const [message, setMessage] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);
  const handleSwitchToggle = (key: keyof SyncSettings) => {
    setSyncSettings((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const onSyncSetting = async (sourceId: string, destinationId: string) => {
    try {
      setIsConnecting(true);
      const payload: SyncSettingsPayload = {
        actions: {
          product: syncSettings.products,
          order: syncSettings.orders,
          inventory: syncSettings.inventory,
        },
        source: sourceId,
        destination: destinationId,
      };
      const response = await channelApi.syncSettings(payload);
      if (response.authorization_url) {
        window.open(response.authorization_url, "_blank");
        toast.error(t("pages.install.authorizeDestination"));
      } else {
        setIsSuccess(true);
        setMessage(response.message);
        setCookie(COOKIES.INSTALLATION_DATA, {
          ...getCookie(COOKIES.INSTALLATION_DATA),
          status: COOKIES.INSTALLATION_STATUS.COMPLETED,
        });
      }
    } catch (error) {
      setMessage(String(error));
      setIsSuccess(false);
    } finally {
      setIsConnecting(false);
    }
  };

  return {
    syncSettingsConfig,
    syncSettings,
    handleSwitchToggle,
    onSyncSetting,
    isSuccess,
    message,
    isConnecting,
  };
};

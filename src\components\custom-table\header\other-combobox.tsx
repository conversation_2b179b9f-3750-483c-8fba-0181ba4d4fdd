"use client";

import React, { useCallback, useState } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { Check, Loader2, Search } from "lucide-react";

import { fetcher } from "@/components/data-table/data-table-faceted-filter";
import { Button, Input, Popover, PopoverContent, PopoverTrigger } from "@/components/ui";
import Empty from "@/components/ui/empty";
import useDebounce from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

import { Option } from "./other-filter-group";

interface OtherComboboxProps {
  selectedValues: Set<string>;
  onComboboxChange: (value: Option) => void;
  remote?: boolean;
  pathUrlLoad?: string;
  isChannelFetch?: boolean;
  handleAddOptions: (values: Option[]) => void;
}

interface ResponseItem {
  id: string;
  name: string;
  count?: number;
}

export default function OtherCombobox({
  selectedValues,
  pathUrlLoad,
  onComboboxChange,
  remote = false,
  isChannelFetch = false,
  handleAddOptions,
}: OtherComboboxProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [open, setOpen] = useState(false);
  const [pageSize] = useState(10);
  const debouncedSearchValue = useDebounce(searchQuery, 300);

  const { data, hasNextPage, isFetchingNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey:
      remote && pathUrlLoad && !isChannelFetch
        ? ["faceted-filter", pathUrlLoad, debouncedSearchValue]
        : ["faceted-filter"],
    queryFn: ({ pageParam = 0 }) =>
      pathUrlLoad && !isChannelFetch
        ? fetcher(pathUrlLoad, debouncedSearchValue, pageParam, pageSize)
        : null,
    enabled: remote && !isChannelFetch && !!pathUrlLoad,
    initialPageParam: 0,
    getNextPageParam: (lastPage) =>
      lastPage?.total && lastPage.total > (+lastPage.page + 1) * pageSize
        ? +lastPage.page + 1
        : undefined,
  });

  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value);
  }, []);

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const target = e.currentTarget;
      const threshold = 20;
      const isNearBottom =
        target.scrollHeight - (target.scrollTop + target.clientHeight) < threshold;

      if (isNearBottom && hasNextPage && !isFetchingNextPage) {
        fetchNextPage().then((response) => {
          if (response.data) {
            // Check if any of the newly loaded items are in selectedValues
            const newSelectedItems = response.data.pages[response.data.pages.length - 1]?.items
              .filter((item: ResponseItem) => selectedValues.has(item.id))
              .map((item: ResponseItem) => ({ value: item.id, label: item.name }));

            if (newSelectedItems && newSelectedItems.length > 0) {
              handleAddOptions(newSelectedItems);
            }
          }
        });
      }
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage, selectedValues, handleAddOptions]
  );

  const options = data?.pages.flatMap((page) => page?.items || []) || [];
  const total = data?.pages[0]?.total || 0;
  const missingCount = Array.from(selectedValues)
    .filter((value) => value !== "all") // Filter out "all"
    .filter((value) => !options.some((option) => option.id === value)).length;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            "size-full bg-neutral-100",
            open || missingCount
              ? "ring-1 ring-primary ring-offset-1 bg-accent"
              : "hover:bg-muted hover:text-muted-foreground"
          )}
          variant={"ghost"}>
          Other
          {missingCount > 0 && (
            <span className="font-medium text-muted-foreground">({missingCount})</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="z-50 w-[345px] rounded-md border bg-popover p-0 shadow-md"
        align="start">
        <div className="flex flex-col">
          <div className="flex items-center border-b px-3 py-1">
            <Search className="mr-2 size-4 shrink-0 opacity-50" />
            <Input
              placeholder="Search..."
              value={searchQuery}
              containerClassName="flex-1"
              onChange={(e) => handleSearch(e.target.value)}
              className="h-8 flex-1 border-0 bg-transparent pl-0 outline-none placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>
          <div className="space-y-1">
            <div
              className={cn(
                "overflow-y-auto px-1 max-w-full overflow-x-hidden",
                Math.max(options.length, total) < 10 ? "h-fit" : "h-[200px]"
              )}
              onScroll={handleScroll}>
              <div className="space-y-1 py-1">
                {options.map((option) => {
                  const isSelected = selectedValues.has(option.id);
                  return (
                    <div
                      key={option.id}
                      className={cn(
                        "flex cursor-pointer items-center justify-between space-x-2 overflow-x-hidden rounded-sm px-2 py-1",
                        "hover:bg-accent hover:text-accent-foreground transition-colors"
                      )}
                      onClick={() => onComboboxChange({ value: option.id, label: option.name })}>
                      <div className="flex items-center gap-2">
                        <Check
                          className={cn(
                            "size-4 shrink-0",
                            isSelected ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <span className="flex-1 truncate">{option.name || option.id}</span>
                      </div>
                      {option.count !== undefined && (
                        <span
                          className={cn(
                            "ml-auto text-xs",
                            isSelected ? "text-primary-foreground" : "text-muted-foreground"
                          )}>
                          {option.count}
                        </span>
                      )}
                    </div>
                  );
                })}
                {isFetchingNextPage && (
                  <div className="flex justify-center py-2">
                    <Loader2 className="size-4 animate-spin" />
                  </div>
                )}
                {options.length === 0 && !isFetchingNextPage && <Empty />}
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

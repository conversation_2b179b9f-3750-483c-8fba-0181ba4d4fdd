import { useCallback } from "react";
import { useTranslation } from "react-i18next";

export const useLanguage = () => {
  const { i18n } = useTranslation();

  const toggleLanguage = useCallback(() => {
    try {
      const newLang = i18n.language === "en" ? "vi" : "en";
      i18n.changeLanguage(newLang);
      document.documentElement.lang = newLang;
      localStorage.setItem("i18nextLng", newLang);
    } catch (error) {
      console.error("Error changing language:", error);
    }
  }, [i18n]);

  return {
    currentLanguage: i18n.language,
    toggleLanguage,
  };
};

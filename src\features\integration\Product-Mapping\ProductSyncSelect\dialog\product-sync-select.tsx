"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { Link, Loader2 } from "lucide-react";

import { useSyncRecordDetail } from "@/features/integration/hooks/sync-record";
import { useProductMapping } from "@/features/integration/Product-Mapping/hooks/product-mapping";
import { MappingProduct } from "@/features/integration/Product-Mapping/hooks/types";
import { useDestinationProducts } from "@/features/integration/Product-Mapping/ProductSyncSelect/hooks/product-selection";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { useProductSelection } from "../../hooks/product-selection";
import ProductSelectionView from "../product-selection-view";
import VariantMappingView from "../variant-mapping-view";

interface ProductSyncSelectProps {
  product: MappingProduct;
  isOpen: boolean;
  onClose: () => void;
  onMap?: (destinationProductId: string) => Promise<void>;
}

export function ProductSyncSelect({ product, isOpen, onClose, onMap }: ProductSyncSelectProps) {
  // Get the product mapping hook with its loading state
  const { isMapping, handleMap: handleMappingAction } = useProductMapping(
    product.id,
    product.connection_id
  );

  // Fetch destination products when dialog opens
  const { destinationProducts: apiDestinationProducts, isLoading: isLoadingDestinations } =
    useDestinationProducts({
      enabled: isOpen,
      connectionId: product.connection_id,
    });

  const { data: syncRecordData, isLoading: isLoadingSyncRecord } = useSyncRecordDetail({
    sync_record_id: product.id,
    enabled: isOpen,
  });
  console.log("destinationProducts", apiDestinationProducts);
  console.log("syncRecordData", (syncRecordData as any)?.data?.channels?.[0]?.standard_source_data);

  // For debugging API responses
  const [lastDebugResponse, setLastDebugResponse] = useState<any>(null);

  const {
    selectedDestProduct,
    selectedProductVariants,
    mapVariants,
    isLoading,
    isLoadingVariants,
    isVariantMappingView,
    sourceVariants,
    destinationProducts,
    mappedVariants,
    selectedSourceVariant,
    activeSourceForMapping,
    handleProductSelect,
    handleBack,
    handleMap,
    handleVariantMappingChange,
    handleSourceVariantSelect,
    handleMapVariant,
    handleUnmapVariant,
    handleUnmapDestVariant,
    isDestVariantMapped,
    isDestinationMappedToActiveSource,
    getDestinationVariants,
    clearSelection,
  } = useProductSelection(product, onClose, onMap);

  // State to track if we should close the dialog after mapping
  const [shouldCloseAfterMapping, setShouldCloseAfterMapping] = useState(false);

  // Effect to handle delayed closing of the dialog
  useEffect(() => {
    if (shouldCloseAfterMapping && !isMapping) {
      onClose();
      setShouldCloseAfterMapping(false);
    }
  }, [shouldCloseAfterMapping, isMapping, onClose]);

  // When API data is loaded, we want to update our source of products
  useEffect(() => {
    if (apiDestinationProducts && apiDestinationProducts.length > 0) {
      console.log("Destination products loaded:", apiDestinationProducts.length);

      // Store first product for debugging
      if (apiDestinationProducts[0]) {
        setLastDebugResponse(apiDestinationProducts[0]);
      }
    }
  }, [apiDestinationProducts]);

  // Directly select the first source variant when entering variant mapping view
  useEffect(() => {
    // If we've just transitioned to the variant mapping view and have source variants
    if (
      isVariantMappingView &&
      !selectedSourceVariant &&
      !activeSourceForMapping &&
      !isLoadingVariants &&
      sourceVariants.length > 0
    ) {
      console.log("Direct auto-selection in product-sync-select:", sourceVariants[0].id);

      // Force immediate selection of the first source variant
      handleSourceVariantSelect(sourceVariants[0].id);
    }
  }, [
    isVariantMappingView,
    selectedSourceVariant,
    activeSourceForMapping,
    isLoadingVariants,
    sourceVariants,
    handleSourceVariantSelect,
  ]);

  // Log when variant data is loaded
  useEffect(() => {
    if (selectedProductVariants && selectedProductVariants.length > 0) {
      console.log("Variant data loaded:", selectedProductVariants.length);
    }
  }, [selectedProductVariants]);

  // Adapt source variants to match the expected interface
  const adaptedSourceVariants = sourceVariants.map((variant) => ({
    id: variant.id,
    name: variant.name,
    image: variant.images || "/placeholder.svg",
    price: variant.price || 0,
    color: "Default",
  }));

  // For debugging
  console.log("Selected dest product:", selectedDestProduct);
  console.log("Product variants:", selectedProductVariants);

  const handleMapSubmit = useCallback(async () => {
    if (!selectedDestProduct) return;

    try {
      // Set flag to close dialog after mapping is complete
      setShouldCloseAfterMapping(true);

      // Create the complete payload required by the API
      const mappingPayload = {
        destinationProductId: selectedDestProduct.id,
        mappedVariants: mappedVariants,
        isExtraDestinationMapping: mapVariants,
      };

      console.log("Mapping payload:", mappingPayload);

      // Use the mapping action from the hook
      await handleMappingAction(mappingPayload);
    } catch (error) {
      console.error("Failed to map product:", error);
      // If there's an error, don't close the dialog
      setShouldCloseAfterMapping(false);
    }
  }, [selectedDestProduct, mappedVariants, mapVariants, handleMappingAction]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="flex h-[90vh] max-w-[900px] flex-col p-0">
        <DialogHeader className="px-6 pb-3 pt-6">
          <DialogTitle>
            Map your {product.standard_source_data.brand?.name || "Source"} product
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            This product has not been synced yet. Please select a target product for mapping.
          </p>
        </DialogHeader>

        <div className="grid flex-1 grid-cols-3 divide-x overflow-auto">
          {isVariantMappingView ? (
            isLoadingVariants ? (
              <div className="col-span-3 flex h-full items-center justify-center">
                <div className="flex flex-col items-center gap-4">
                  <Loader2 className="size-12 animate-spin text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">Loading product variants...</p>
                </div>
              </div>
            ) : (
              <VariantMappingView
                sourceProduct={{
                  id: (syncRecordData as any)?.data?.channels?.[0]?.standard_source_data?.id || "",
                  name:
                    (syncRecordData as any)?.data?.channels?.[0]?.standard_source_data?.title || "",
                  image:
                    (syncRecordData as any)?.data?.channels?.[0]?.standard_source_data
                      ?.images?.[0] || "/placeholder.svg",
                  price: product.price,
                }}
                standardSourceData={
                  (syncRecordData as any)?.data?.channels?.[0]?.standard_source_data
                }
                destProduct={selectedDestProduct!}
                sourceVariants={
                  (syncRecordData as any)?.data?.channels?.[0]?.standard_source_data?.variants?.map(
                    (variant: { id: string; sku: string; image: string; price: string }) => ({
                      id: variant.id,
                      name: variant.sku || "",
                      image: variant.image || "",
                      price: variant.price ? parseFloat(variant.price) : 0,
                      color: "Default",
                    })
                  ) || []
                }
                destVariants={getDestinationVariants(selectedDestProduct?.id || "")}
                onBack={handleBack}
                selectedSourceVariant={selectedSourceVariant}
                activeSourceForMapping={activeSourceForMapping}
                mappedVariants={mappedVariants}
                onSourceVariantSelect={handleSourceVariantSelect}
                onMapVariant={handleMapVariant}
                onUnmapVariant={handleUnmapVariant}
                onUnmapDestVariant={handleUnmapDestVariant}
                isDestVariantMapped={isDestVariantMapped}
                isDestinationMappedToActiveSource={isDestinationMappedToActiveSource}
                clearSelection={clearSelection}
              />
            )
          ) : (
            <ProductSelectionView
              sourceProduct={{
                id: product.standard_source_data.id,
                name: product.standard_source_data.name,
                image: "/placeholder.svg",
                price: product.price,
              }}
              standardSourceData={
                (syncRecordData as any)?.data?.channels?.[0]?.standard_source_data
              }
              destinationProducts={apiDestinationProducts || []}
              isLoading={isLoadingDestinations}
              onProductSelect={handleProductSelect}
            />
          )}
        </div>

        <div className="flex items-center justify-between border-t bg-card p-6">
          <div className="flex items-center gap-2">
            {isVariantMappingView ? (
              <>
                <Checkbox
                  id="map-variants"
                  checked={mapVariants}
                  onCheckedChange={(checked) => handleVariantMappingChange(checked as boolean)}
                />
                <label htmlFor="map-variants" className="cursor-pointer text-sm">
                  Map variants that the target platform does not have
                </label>
              </>
            ) : (
              <div className="invisible">Placeholder</div>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              className="inline-flex min-w-20 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
              variant="outline"
              onClick={onClose}>
              Cancel
            </Button>
            <Button
              className="inline-flex min-w-20 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
              disabled={
                !selectedDestProduct ||
                isLoading ||
                isLoadingDestinations ||
                isLoadingVariants ||
                isMapping
              }
              loading={isMapping}
              onClick={handleMapSubmit}>
              <Link className="size-4" />
              Map
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

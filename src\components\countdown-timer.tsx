import { useEffect, useState } from "react";

interface CountdownTimerProps {
  isActive: boolean;
  onComplete: () => void;
  initialTime?: number;
}

export function CountdownTimer({ isActive, onComplete, initialTime = 60 }: CountdownTimerProps) {
  const [timeLeft, setTimeLeft] = useState(initialTime);

  useEffect(() => {
    if (isActive && timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime <= 1) {
            clearInterval(timer);
            onComplete();
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isActive, timeLeft, onComplete]);

  useEffect(() => {
    if (isActive) {
      setTimeLeft(initialTime);
    }
  }, [isActive, initialTime]);

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  return (
    <span>
      {minutes.toString().padStart(2, "0")}:{seconds.toString().padStart(2, "0")}
    </span>
  );
}

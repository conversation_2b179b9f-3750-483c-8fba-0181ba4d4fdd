import Amazon from "@/assets/mock/integration/product-mapping/Amazon.png";
import <PERSON><PERSON><PERSON> from "@/assets/mock/integration/product-mapping/Lazada.png";
import Shopee from "@/assets/mock/integration/product-mapping/Shopee.png";
import TikTok from "@/assets/mock/integration/product-mapping/Tik Tok.png";
import { Channel } from "@/lib/apis/types/channel";

export const mockChannels: Channel[] = [
  {
    key: "tiktok_shop",
    name: "Tiktok Shop",
    channel_type: "ECOMMERCE",
    description: "Tiktok Shop eCommerce platform",
    logo: TikTok.src,
    installed: true,
  },
  {
    key: "shopee",
    name: "Shopee",
    channel_type: "ECOMMERCE",
    description: "Shopee eCommerce platform",
    logo: Shopee.src,
    installed: true,
  },

  {
    key: "lazada",
    name: "<PERSON><PERSON><PERSON>",
    channel_type: "MARKETPLACE",
    description: "Lazada Marketplace",
    logo: Lazada.src,
    installed: true,
  },
  {
    key: "amazon",
    name: "Amazon",
    channel_type: "MARKETPLACE",
    description: "Amazon Marketplace",
    logo: Amazon.src,
    installed: true,
  },
];

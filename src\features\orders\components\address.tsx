"use client";

import { useEffect, useState } from "react";
import { Minus, Plus, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useDistricts, useProvinces, useWards } from "@/features/customer/hooks/address";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Combobox } from "@/components/ui/combobox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import useDebounce from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

export interface AddressData {
  id: string;
  address_line1: string;
  province: string;
  province_code?: string;
  district: string;
  district_code?: string;
  ward: string;
  ward_code?: string;
  recipient_name?: string;
  recipient_phone?: string;
  is_shipping_default?: boolean;
  is_billing_default?: boolean;
  first_name?: string;
  last_name?: string;
}

interface AddressProps {
  address: AddressData;
  index: number;
  onDelete: (id: string) => void;
  onChange: (id: string, data: Partial<AddressData>) => void;
  showRecipientInfo?: boolean;
  toggleRecipientInfo?: () => void;
  customer?: {
    first_name?: string;
    last_name?: string;
    phone?: string;
  };
}

export function Address({
  address,
  index,
  onDelete,
  onChange,
  showRecipientInfo,
  toggleRecipientInfo,
  customer,
}: AddressProps) {
  const [provinceQuery, setProvinceQuery] = useState("");
  const [districtQuery] = useState("");
  const [wardQuery] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const { t } = useTranslation();
  const debouncedProvinceQuery = useDebounce(provinceQuery, 300);
  const debouncedDistrictQuery = useDebounce(districtQuery, 300);
  const debouncedWardQuery = useDebounce(wardQuery, 300);

  // If we have a province name but no province code, we need to fetch the province first
  const initialProvinceQuery = address.province && !address.province_code ? address.province : "";

  // Use the province name to search for the province if we don't have the code yet
  const { provinces: allProvinces } = useProvinces(initialProvinceQuery);

  // Find the province code from the queried provinces if needed
  useEffect(() => {
    if (address.province && !address.province_code && allProvinces.length > 0) {
      // Find the province by name
      const foundProvince = allProvinces.find(
        (p) =>
          p.name.toLowerCase() === address.province.toLowerCase() ||
          p.name_with_type.toLowerCase() === address.province.toLowerCase()
      );

      if (foundProvince) {
        // Update the address with the found province code
        onChange(address.id, {
          province_code: foundProvince.code,
        });
      }
    }
  }, [address.province, address.province_code, allProvinces, address.id, onChange]);

  // Make sure province_code is defined before using it
  const provinceCode = address.province_code || "";
  const districtCode = address.district_code || "";

  const {
    provinces,
    isLoading: isLoadingProvinces,
    fetchNextPage: fetchNextProvinces,
    hasNextPage: hasNextProvinces,
    isFetchingNextPage: isFetchingNextProvinces,
  } = useProvinces(debouncedProvinceQuery);

  const {
    districts,
    isLoading: isLoadingDistricts,
    fetchNextPage: fetchNextDistricts,
    hasNextPage: hasNextDistricts,
    isFetchingNextPage: isFetchingNextDistricts,
  } = useDistricts(provinceCode, debouncedDistrictQuery);

  // Similar to province, find district code if we have district name but no code
  useEffect(() => {
    if (provinceCode && address.district && !address.district_code && districts.length > 0) {
      // Find the district by name
      const foundDistrict = districts.find(
        (d) =>
          d.name.toLowerCase() === address.district.toLowerCase() ||
          d.name_with_type.toLowerCase() === address.district.toLowerCase()
      );

      if (foundDistrict) {
        // Update the address with the found district code
        onChange(address.id, {
          district_code: foundDistrict.code,
        });
      }
    }
  }, [address.district, address.district_code, districts, provinceCode, address.id, onChange]);

  const {
    wards,
    isLoading: isLoadingWards,
    fetchNextPage: fetchNextWards,
    hasNextPage: hasNextWards,
    isFetchingNextPage: isFetchingNextWards,
  } = useWards(provinceCode, districtCode, debouncedWardQuery);

  // Find ward code if needed
  useEffect(() => {
    if (provinceCode && districtCode && address.ward && !address.ward_code && wards.length > 0) {
      // Find the ward by name
      const foundWard = wards.find(
        (w) =>
          w.name.toLowerCase() === address.ward.toLowerCase() ||
          w.name_with_type.toLowerCase() === address.ward.toLowerCase()
      );

      if (foundWard) {
        // Update the address with the found ward code
        onChange(address.id, {
          ward_code: foundWard.code,
        });
      }
    }
  }, [address.ward, address.ward_code, wards, provinceCode, districtCode, address.id, onChange]);

  // Ensure data is loaded initially when the component mounts
  useEffect(() => {
    // If we have province, district, and ward data but no province_code or district_code
    // we need to fetch them in sequence
    if (address.province && !provinceCode) {
      // Trigger initial province search to find the province code
      setProvinceQuery(address.province);
    }
  }, [address.province, provinceCode]);

  const handleChange = (key: keyof AddressData, value: any) => {
    // For address fields, only update if the value has changed

    if (
      (key === "address_line1" || key === "province" || key === "district" || key === "ward") &&
      value === address[key]
    ) {
      return;
    }

    onChange(address.id, { [key]: value });
  };

  const handleProvinceSelect = (provinceId: string) => {
    const province = provinces.find((p) => p.code === provinceId);
    if (province) {
      onChange(address.id, {
        province: province.name,
        province_code: province.code,
        district: "",
        district_code: undefined,
        ward: "",
        ward_code: undefined,
      });
    }
  };

  const handleDistrictSelect = (districtId: string) => {
    const district = districts.find((d) => d.code === districtId);
    if (district) {
      onChange(address.id, {
        district: district.name,
        district_code: district.code,
        ward: "",
        ward_code: undefined,
      });
    }
  };

  const handleWardSelect = (wardId: string) => {
    const ward = wards.find((w) => w.code === wardId);
    if (ward) {
      onChange(address.id, {
        ward: ward.name,
        ward_code: ward.code,
      });
    }
  };

  // Update displayName to use customer info as fallback
  const displayName =
    address.recipient_name || (customer ? `${customer.first_name || ""}`.trim() : "");

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmedDelete = () => {
    setShowDeleteDialog(false);
    onDelete(address.id);
  };

  return (
    <div className="space-y-4 rounded-lg border bg-bg-primary p-4">
      <div className="flex items-center justify-between text-sm font-medium">
        <div>
          {t("pages.orders.address")} {index + 1}
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id={`shipping-default-${address.id}`}
              checked={address.is_shipping_default}
              onCheckedChange={(checked) => handleChange("is_shipping_default", Boolean(checked))}
            />
            <label
              htmlFor={`shipping-default-${address.id}`}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {t("pages.orders.shippingDefault")}
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id={`billing-default-${address.id}`}
              checked={address.is_billing_default}
              onCheckedChange={(checked) => handleChange("is_billing_default", Boolean(checked))}
            />
            <label
              htmlFor={`billing-default-${address.id}`}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {t("pages.orders.billingDefault")}
            </label>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="size-8 text-destructive hover:bg-destructive/10 hover:text-destructive"
            onClick={handleDelete}>
            <Trash2 className="size-4" />
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <Input
            value={address.address_line1}
            data-input-type="address"
            onChange={(e) => handleChange("address_line1", e.target.value)}
            placeholder={t("pages.orders.enterAddress")}
          />
        </div>

        <div className="grid grid-cols-3 gap-4">
          {/* Province Combobox */}
          <div className="space-y-2">
            <div className="text-sm font-medium">{t("pages.orders.province")}</div>
            <Combobox
              value={address.province_code || ""}
              onValueChange={handleProvinceSelect}
              items={provinces.map((province) => ({
                id: province.code,
                name: province.name_with_type,
              }))}
              placeholder={t("pages.orders.selectProvince")}
              searchPlaceholder={t("pages.orders.searchProvince")}
              emptyText={t("pages.orders.noProvincesFound")}
              isLoading={isLoadingProvinces}
              onLoadMore={hasNextProvinces ? fetchNextProvinces : undefined}
              isLoadingMore={isFetchingNextProvinces}
            />
          </div>

          {/* District Combobox */}
          <div className="space-y-2">
            <div className="text-sm font-medium">{t("pages.orders.district")}</div>
            <Combobox
              value={address.district_code || ""}
              onValueChange={handleDistrictSelect}
              items={districts.map((district) => ({
                id: district.code,
                name: district.name_with_type,
              }))}
              placeholder={t("pages.orders.selectDistrict")}
              searchPlaceholder={t("pages.orders.searchDistrict")}
              emptyText={t("pages.orders.noDistrictsFound")}
              isLoading={isLoadingDistricts}
              onLoadMore={hasNextDistricts ? fetchNextDistricts : undefined}
              isLoadingMore={isFetchingNextDistricts}
            />
          </div>

          {/* Ward Combobox */}
          <div className="space-y-2">
            <div className="text-sm font-medium">{t("pages.orders.ward")}</div>
            <Combobox
              value={address.ward_code || ""}
              onValueChange={handleWardSelect}
              items={wards.map((ward) => ({
                id: ward.code,
                name: ward.name_with_type,
              }))}
              placeholder={t("pages.orders.selectWard")}
              searchPlaceholder={t("pages.orders.searchWard")}
              emptyText={t("pages.orders.noWardsFound")}
              isLoading={isLoadingWards}
              onLoadMore={hasNextWards ? fetchNextWards : undefined}
              isLoadingMore={isFetchingNextWards}
            />
          </div>
        </div>

        {toggleRecipientInfo && (
          <Button
            type="button"
            variant="ghost"
            className={cn(
              "h-8 px-2 text-sm hover:underline hover:text-primary",
              showRecipientInfo ? "text-primary" : "text-primary"
            )}
            onClick={toggleRecipientInfo}>
            {showRecipientInfo ? <Minus className="size-4" /> : <Plus className="size-4" />}
            {showRecipientInfo
              ? t("pages.orders.removeRecipientInfo")
              : t("pages.orders.addRecipientInfo")}
          </Button>
        )}

        {showRecipientInfo && (
          <div className="grid grid-cols-2 gap-4 pt-2">
            <div className="space-y-2">
              <div className="text-sm">{t("pages.orders.Name")}</div>
              <Input
                value={displayName}
                onChange={(e) => handleChange("recipient_name", e.target.value)}
                placeholder={t("pages.orders.enterName")}
              />
            </div>
            <div className="space-y-2">
              <div className="text-sm">{t("pages.orders.phoneNumber")}</div>
              <Input
                type="tel"
                data-input-type="phone"
                value={address.recipient_phone || customer?.phone || ""}
                onChange={(e) => {
                  const value = e.target.value.replace(/[^0-9]/g, "");
                  handleChange("recipient_phone", value);
                }}
                onKeyPress={(e) => {
                  if (
                    !/[0-9]/.test(e.key) &&
                    e.key !== "Backspace" &&
                    e.key !== "Delete" &&
                    e.key !== "ArrowLeft" &&
                    e.key !== "ArrowRight"
                  ) {
                    e.preventDefault();
                  }
                }}
                placeholder={t("pages.orders.enterPhoneNumber")}
              />
            </div>
          </div>
        )}
      </div>

      {/* Add Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("pages.orders.confirm")}</DialogTitle>
            <DialogDescription>{t("pages.orders.cancelDelete")}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowDeleteDialog(false)}>
              {t("pages.orders.cancel")}
            </Button>
            <Button type="button" variant="destructive" onClick={handleConfirmedDelete}>
              {t("pages.orders.confirmDelete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

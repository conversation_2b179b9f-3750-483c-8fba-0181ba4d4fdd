"use client";

import { useRef } from "react";
import { FileText, ImagePlus, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";

interface FileUploaderProps {
  fileList: File[];
  setFileList: (files: File[]) => void;
  isUploading: boolean;
  setIsUploading?: (v: boolean) => void;
  multiple?: boolean;
  accept?: string;
  className?: string;
}

const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
const DEFAULT_ACCEPT = ".pdf,.docx,.txt,.csv";

export function FileUploader({
  fileList,
  setFileList,
  isUploading,
  multiple = true,
  accept = DEFAULT_ACCEPT,
}: FileUploaderProps) {
  const { t } = useTranslation();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const totalSize = fileList.reduce((acc, file) => acc + file.size, 0);

  const handleRemove = (index: number) => {
    const newFileList = fileList.filter((_, i) => i !== index);
    setFileList(newFileList);
  };

  const handleFiles = (files: File[]) => {
    let newList = multiple ? [...fileList, ...files] : [files[0]];
    // Lọc file vượt quá 25MB
    const oversizedFiles = newList.filter((file) => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      toast.error(t("pages.knowledge.upload.fileTooLarge"), {
        description: t("pages.knowledge.upload.fileTooLarge", { max: 25 }),
      });
      newList = newList.filter((file) => file.size <= MAX_FILE_SIZE);
    }
    // Loại bỏ file trùng tên
    newList = newList.filter(
      (file, idx, arr) => arr.findIndex((f) => f.name === file.name && f.size === file.size) === idx
    );
    setFileList(newList);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.files) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };

  return (
    <div className="flex flex-auto flex-col gap-4 overflow-y-hidden p-1">
      {/* Drag & drop area */}
      <div
        className="flex-none rounded-xl border-2 border-dashed border-gray-300 p-0 transition hover:bg-muted"
        onDrop={handleDrop}
        onDragOver={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}>
        <label className="block w-full cursor-pointer">
          <input
            type="file"
            ref={fileInputRef}
            accept={accept}
            className="hidden"
            disabled={isUploading}
            multiple={multiple}
            onChange={(e) => {
              if (e.target.files) handleFiles(Array.from(e.target.files));
            }}
          />
          <div className="flex flex-col items-center justify-center py-8">
            <ImagePlus className="mb-2 text-gray-400" size={38} strokeWidth={0.5} />
            <span className="text-sm italic text-gray-500">
              {t("pages.knowledge.upload.dragAndDrop")}{" "}
              <span className="cursor-pointer font-semibold italic text-primary">
                {t("pages.knowledge.upload.uploadButton")}
              </span>
            </span>
            <span className="mt-1 text-xs italic text-gray-400">
              {t("pages.knowledge.upload.supportedFiles")}
            </span>
            <span className="mt-1 text-xs text-gray-400">
              {t("pages.knowledge.upload.totalSize")}:{" "}
              {totalSize < 1024 * 1024
                ? `${(totalSize / 1024).toFixed(0)}KB`
                : `${(totalSize / (1024 * 1024)).toFixed(2)}MB`}
            </span>
          </div>
        </label>
      </div>
      {/* File list */}
      {fileList.length > 0 && (
        <div className="flex flex-auto flex-col gap-2 overflow-y-auto border-t pt-4">
          {fileList.map((file, idx) => (
            <div
              key={file.name + file.size + idx}
              className="flex items-center justify-between rounded-lg border bg-card px-4 py-3 shadow-sm">
              <div className="flex items-center gap-2">
                <FileText size={14} className="shrink-0" />
                <div className="block truncate text-sm font-medium">{file.name}</div>
              </div>
              <div className="flex shrink-0 items-center gap-4">
                <span className="shrink-0 text-sm text-gray-500">
                  {file.size < 1024 * 1024
                    ? `${(file.size / 1024).toFixed(0)}KB`
                    : `${(file.size / (1024 * 1024)).toFixed(1)}MB`}
                </span>
                <Button
                  variant="ghost"
                  onClick={() => handleRemove(idx)}
                  disabled={isUploading}
                  size="xs"
                  type="button">
                  <Trash className="size-4 text-destructive" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

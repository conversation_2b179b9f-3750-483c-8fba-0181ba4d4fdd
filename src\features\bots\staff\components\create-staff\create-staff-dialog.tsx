"use client";

import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Pencil, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { useDepartments } from "@/features/bots/department/hooks/department";
import { useCreateStaff } from "@/features/bots/staff/hooks/staff";
import { CreateImage } from "@/features/products/hooks/types";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { Role } from "../../hooks/type";
import { STAFF_AVATARS } from "./staff-avatar";
import { StaffAvatarForm } from "./upload-staff-avatar";

const roles = Object.entries(Role).map(([key, value]) => ({
  id: value,
  name: key
    .split("_")
    .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
    .join(" "),
}));

type FormValues = {
  name: string;
  department: string;
  role: Role;
  image?: {
    name: string;
    image: string;
  };
};

interface CreateStaffDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: FormValues) => Promise<any>;
  initialData?: FormValues;
  isEdit?: boolean;
  onClose: () => void;
}

export function CreateStaffDialog({
  open = true,
  onOpenChange,
  onSubmit,
  initialData,
  isEdit = false,
  onClose,
}: CreateStaffDialogProps) {
  const { t } = useTranslation();
  const { departments } = useDepartments();
  const createStaffMutation = useCreateStaff();
  const [avatarDialogOpen, setAvatarDialogOpen] = useState(false);
  const [avatar, setAvatar] = useState<CreateImage | null>(null);

  const schema = z.object({
    name: z
      .string()
      .min(1, t("pages.staff.staffNameRequired"))
      .max(250, t("pages.staff.maxCharacters")),
    department: z.string().min(1, t("pages.department.departmentNameRequired")),
    role: z.nativeEnum(Role, {
      required_error: t("validation.required"),
    }),
    image: z
      .object({
        name: z.string(),
        image: z.string(),
      })
      .optional(),
  });

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: initialData?.name || "",
      department: initialData?.department || "",
      role: initialData?.role || Role.VIRTUAL_STAFF,
      image: initialData?.image || {
        name: "default_avatar.png",
        image: STAFF_AVATARS[0].image.src,
      },
    },
    mode: "onChange",
  });

  useEffect(() => {
    const convertDefaultAvatar = async () => {
      try {
        const response = await fetch(STAFF_AVATARS[0].image.src);
        const blob = await response.blob();
        const reader = new FileReader();

        reader.onloadend = () => {
          const base64data = reader.result as string;
          const defaultAvatar = {
            name: "default_avatar.png",
            image: base64data,
          };
          setAvatar(defaultAvatar);
          form.setValue("image", defaultAvatar);
        };

        reader.readAsDataURL(blob);
      } catch (error) {
        console.error("Error converting default avatar:", error);
      }
    };

    if (!initialData?.image) {
      convertDefaultAvatar();
    }
  }, [initialData, form]);

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.setValue("department", initialData.department);
      form.setValue("role", initialData.role);
    }
  }, [initialData, form]);

  // Reset form when dialog is closed
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const resetForm = () => {
    // Reset the form to default values or initialData if provided
    form.reset({
      name: "",
      department: initialData?.department || "",
      role: initialData?.role || Role.VIRTUAL_STAFF,
      image: {
        name: "default_avatar.png",
        image: STAFF_AVATARS[0].image.src,
      },
    });

    // Reset avatar state
    setAvatar(null);

    // Also reset the avatar dialog
    setAvatarDialogOpen(false);
  };

  const departmentOptions = departments.map((dept) => ({
    id: dept.id,
    name: dept.name,
  }));

  const handleSubmit = async (data: FormValues) => {
    if (onSubmit) {
      await onSubmit({
        ...data,
        image: avatar ? { name: avatar.name, image: avatar.image } : data.image,
      });
    } else {
      await createStaffMutation.mutateAsync({
        name: data.name,
        department_id: data.department,
        role: data.role,
        image: avatar || data.image,
      });
      // Wait for 5 seconds before closing the dialog
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    // Reset form after successful submission
    resetForm();

    // Close the dialog
    onClose();
    onOpenChange?.(false);
  };

  const handleCancel = () => {
    // Reset form when canceling
    resetForm();

    // Close the dialog
    onClose();
    onOpenChange?.(false);
  };

  const nameValue = form.watch("name");

  // If not open, don't render anything
  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="flex max-h-[90vh] w-full max-w-md flex-col rounded-lg border border-border bg-card">
        <div className="flex items-center justify-between p-4">
          <h2 className="text-lg font-semibold">
            {isEdit ? t("pages.staff.editStaff") : t("pages.staff.createStaff")}
          </h2>
          <Button variant="ghost" size="icon" onClick={handleCancel}>
            <X className="size-4" />
          </Button>
        </div>

        <div className="flex flex-col items-center rounded-full p-0">
          <button
            type="button"
            className="group relative rounded-full focus:outline-none"
            onClick={() => setAvatarDialogOpen(true)}
            aria-label="Change avatar">
            <Avatar className="mb-2 size-20 border-2 border-neutral">
              <AvatarImage
                src={avatar?.image || STAFF_AVATARS[0].image.src}
                className="object-contain"
                alt="Avatar"
              />
              <AvatarFallback>{nameValue?.charAt(0)?.toUpperCase() || "S"}</AvatarFallback>
            </Avatar>
            <div className="absolute left-0 top-0 flex size-[80px] items-center justify-center rounded-full bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
              <Pencil className="size-6 text-white" />
            </div>
          </button>
        </div>

        <StaffAvatarForm
          open={avatarDialogOpen}
          onOpenChange={setAvatarDialogOpen}
          value={avatar}
          onChange={(value) => {
            setAvatar(value);
            form.setValue("image", value!);
          }}
        />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="flex flex-col">
            <div className="flex-1 overflow-y-auto px-4 pb-4">
              <div className="flex flex-col gap-4">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center justify-between">
                    <FormLabel className="font-semibold">
                      {t("pages.staff.staffName")} <span className="text-destructive">*</span>
                    </FormLabel>
                    <span className="text-xs text-muted-foreground">{nameValue.length}/250</span>
                  </div>
                  <FormField
                    name="name"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            placeholder={t("pages.staff.enterStaffName")}
                            maxLength={250}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <FormLabel className="font-semibold">
                    {t("pages.department.title")} <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormField
                    name="department"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Combobox
                            value={field.value}
                            onValueChange={field.onChange}
                            items={departmentOptions}
                            placeholder={t("pages.staff.selectDepartment")}
                            searchPlaceholder={t("pages.staff.searchDepartments")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex flex-col gap-2">
                  <FormLabel className="font-semibold">
                    {t("pages.staff.role")} <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormField
                    name="role"
                    control={form.control}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Combobox
                            value={field.value}
                            onValueChange={field.onChange}
                            items={roles}
                            placeholder={t("pages.staff.selectRole")}
                            searchPlaceholder={t("pages.staff.searchRoles")}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="flex h-16 items-center justify-between rounded-b-lg border-t bg-card px-6">
              <div className="flex items-center gap-2">
                {/* Add any left side content here if needed */}
              </div>
              <div className="flex gap-2">
                <Button
                  className="inline-flex min-w-20 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
                  type="button"
                  variant="outline"
                  onClick={handleCancel}>
                  {t("common.cancel")}
                </Button>
                <Button
                  className="inline-flex min-w-20 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                  type="submit"
                  variant="default"
                  loading={createStaffMutation.isPending}
                  disabled={!form.formState.isValid || createStaffMutation.isPending}>
                  {isEdit
                    ? t("pages.staff.update")
                    : createStaffMutation.isPending
                      ? t("pages.staff.creating")
                      : t("common.create")}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}

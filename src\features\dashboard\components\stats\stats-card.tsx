import { ReactNode } from "react";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

interface StatsCardProps {
  title: string;
  value: string;
  change: string;
  icon: ReactNode;
}

export function StatsCard({ title, value, change, icon }: StatsCardProps) {
  return (
    <Card className="p-6">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
        <div className="flex items-center justify-center">{icon}</div>
      </div>
      <div className="space-y-2 truncate">
        <p className="text-3xl font-bold tracking-tight">{value}</p>
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">{change} from last month</p>
        </div>
        <Button variant="link" className="float-end h-auto p-0 text-sm">
          View more
        </Button>
      </div>
    </Card>
  );
}

import { useRef } from "react";
import { Send } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslation } from "react-i18next";

import LogoOXBDark from "@/assets/logos/logo-OXB-dark.png";
import LogoOXBLight from "@/assets/logos/logo-OXB-light.png";
import { Button } from "@/components/ui/button";
import { CustomImage } from "@/components/ui/image";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface FooterProps {
  className?: string;
  isLoading?: boolean;
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  handleSubmit: () => void;
  showInformation?: boolean;
  userInfo?: {
    name: string;
    phoneNumber: string;
  };
  onUserInfoChange?: (field: "name" | "phoneNumber", value: string) => void;
  onUserInfoSubmit?: () => void;
}

export default function Footer({
  input,
  handleInputChange,
  handleKeyDown,
  handleSubmit,
  isLoading,
  showInformation = false,
  userInfo,
  onUserInfoChange,
  onUserInfoSubmit,
  className,
}: FooterProps) {
  const { t } = useTranslation();
  const inputRef = useRef<HTMLInputElement>(null);
  const { theme, systemTheme } = useTheme();
  const currentMode = theme === "system" ? systemTheme : theme;
  const isDark = currentMode === "dark";

  if (showInformation) {
    const isButtonDisabled = !userInfo?.name || !userInfo?.phoneNumber || isLoading;

    return (
      <div className="flex-none p-4 shadow-md">
        {/* <Button
          className="w-full bg-orange-500 hover:bg-orange-600"
          onClick={onUserInfoSubmit}
          loading={isSubmitting}
          disabled={isButtonDisabled}>
          {t("common.start", "Start")}
        </Button> */}
        <div className="flex items-center justify-center">
          <span className="text-xs font-medium text-muted-foreground">
            {t("common.poweredBy", "Powered by")}
          </span>
          <CustomImage
            src={isDark ? LogoOXBDark : LogoOXBLight}
            alt="OXB Logo"
            width={60}
            height={60}
            className="size-auto"
          />
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex-none p-4 pt-0 shadow-md", className)}>
      <Input
        ref={inputRef}
        className="h-[52px] w-full rounded-lg bg-bg-primary"
        placeholder={t("chat.placeholder", "Say something...")}
        value={input}
        onChange={handleInputChange}
        onKeyDown={(e) => {
          if (!isLoading) handleKeyDown(e);
        }}
        rightIcon={
          <Button
            type="button"
            size="icon"
            variant="default"
            className="size-10 rounded-lg"
            onClick={handleSubmit}
            disabled={isLoading || !input.trim()}>
            <Send size={16} />
          </Button>
        }
      />
      <div className="mt-2 flex items-center justify-center">
        <span className="text-xs font-medium text-muted-foreground">
          {t("common.poweredBy", "Powered by")}
        </span>
        <CustomImage
          src={isDark ? LogoOXBDark : LogoOXBLight}
          alt="OXB Logo"
          width={60}
          height={60}
          className="size-auto"
        />
      </div>
    </div>
  );
}

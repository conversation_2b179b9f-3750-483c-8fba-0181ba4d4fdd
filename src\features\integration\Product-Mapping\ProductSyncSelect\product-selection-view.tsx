"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Loader2, Search } from "lucide-react";

import { StandardSourceData } from "@/features/integration/SyncRecords/hooks/types";

import { CustomImage } from "@/components/ui/image";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";

import { DestinationProductData } from "../hooks/types";

interface ProductSelectionViewProps {
  sourceProduct: {
    id: string;
    name: string;
    image: string;
    price: number;
    title?: string;
  };
  standardSourceData?: StandardSourceData;
  destinationProducts: DestinationProductData[];
  isLoading?: boolean;
  onProductSelect: (product: DestinationProductData) => void;
}

export default function ProductSelectionView({
  sourceProduct,
  standardSourceData,
  destinationProducts,
  isLoading = false,
  onProductSelect,
}: ProductSelectionViewProps) {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter products based on search query
  const filteredProducts = destinationProducts.filter((product) =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  console.log("standardSourceData", standardSourceData?.images?.[0]);

  return (
    <>
      {/* Left side - Source Product */}
      <div className="col-span-1 pl-6">
        <h3 className="py-2 text-sm text-muted-foreground">Source product</h3>

        <div className="flex items-center gap-4 bg-bg-primary p-4">
          <CustomImage
            src={standardSourceData?.images?.[0]}
            alt={standardSourceData?.title || sourceProduct.name}
            width={40}
            height={40}
            className="max-h-[40px] max-w-[40px] rounded-md"
          />
          <div className="min-w-0 flex-1">
            <p className="truncate text-sm font-medium">
              {standardSourceData?.title || sourceProduct.name || "--"}
            </p>
            <p className="text-sm text-muted-foreground">
              Price: ${sourceProduct.price ? sourceProduct.price.toFixed(2) : "--"}
            </p>
          </div>
          <ArrowRight className="size-4 text-muted-foreground" />
        </div>
      </div>

      {/* Right side - Destination Products */}
      <div className="col-span-2">
        <div className="mb-4 px-4">
          <h3 className="py-2 text-sm text-muted-foreground">Destination products</h3>
          <Input
            leftIcon={<Search className="size-4 text-muted-foreground" />}
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {isLoading ? (
          <div className="flex h-[400px] items-center justify-center">
            <Loader2 className="size-12 animate-spin text-muted-foreground" />
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className="max-h-full overflow-y-auto">
            <Table>
              <TableBody>
                {filteredProducts.map((product) => (
                  <TableRow
                    key={product.id}
                    className="cursor-pointer hover:bg-background"
                    onClick={() => onProductSelect(product)}>
                    <TableCell className="w-[70px]">
                      <CustomImage
                        src={product.standard_data?.image || "/placeholder.svg"}
                        alt={product.name}
                        width={40}
                        height={40}
                        className="rounded-md object-contain"
                      />
                    </TableCell>
                    <TableCell>
                      <div className="min-w-0 flex-1">
                        <p className="truncate text-sm font-medium">{product.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {product.standard_data?.price &&
                            `Price: $${parseFloat(product.standard_data.price).toFixed(2)}`}
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="py-8 text-center text-muted-foreground">No matching products found</div>
        )}
      </div>
    </>
  );
}

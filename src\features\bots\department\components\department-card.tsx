import React from "react";
import Link from "next/link";
import { LayoutGrid } from "lucide-react";
import { useTranslation } from "react-i18next";

import CustomAvatarGroup from "@/components/custom-avatar-group";
import { Button } from "@/components/ui";
import { authProtectedPaths } from "@/constants/paths";

import { Department } from "../hooks/types";

interface DepartmentCardProps {
  department: Department;
  classNames?: string;
}

/**
 * DepartmentCard
 * Component for department card feature that displays the department information.
 */
const DepartmentCard: React.FC<DepartmentCardProps> = ({ department, classNames = "" }) => {
  const { t } = useTranslation();
  const hasStaffs =
    department.staffs && Array.isArray(department.staffs) && department.staffs.length > 0;

  return (
    <div className={`h-full rounded-lg bg-bg-primary p-4 ${classNames}`}>
      <div className="flex h-full flex-col justify-between">
        <div className="flex items-center justify-between pb-3">
          <div className="flex items-center gap-2 text-card-foreground">
            <LayoutGrid strokeWidth={1} className="size-4 opacity-80" />{" "}
            <span className="text-sm font-medium">{department.name}</span>
          </div>
          {department.staffs && Array.isArray(department.staffs) && (
            <CustomAvatarGroup
              images={department.staffs
                .map((staff) => staff.image?.url)
                .filter((img): img is string | undefined => img !== null)}
              size="md"
            />
          )}
        </div>

        {department.description && (
          <div className="truncate text-xs text-muted-foreground">{department.description}</div>
        )}

        <div className="flex justify-end">
          {hasStaffs ? (
            <Button variant="default" asChild>
              <Link
                className="text-sm font-medium text-primary hover:underline"
                href={{
                  pathname: authProtectedPaths.STAFF,
                  query: { department_id: department.id },
                }}>
                {t("pages.department.viewStaff")}
              </Link>
            </Button>
          ) : (
            <Button variant="default" asChild>
              <span className="cursor-not-allowed text-sm font-medium text-primary/50">
                {t("pages.department.viewStaff")}
              </span>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DepartmentCard;

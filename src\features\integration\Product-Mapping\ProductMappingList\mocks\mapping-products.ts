import { MappingProduct, MappingProductStatus } from "@/lib/apis/types/mapping-product";

// Mock variant data
const createVariants = (productId: string, colors: string[]) => {
  return colors.map((color, index) => ({
    id: `${productId}-var-${index + 1}`,
    name: `${color} Variant`,
    sku: `${productId}-${color.toLowerCase()}`,
    barcode: `${productId}${index + 1}`,
    option1: color,
    option2: "",
    option3: "",
    inventories: { available: Math.floor(Math.random() * 100) + 1 },
    prices: [{ price: 49.99 + index * 5, price_group: { id: `pg${index + 1}`, name: "Retail" } }],
    images: [{ id: `img-${productId}-${index + 1}`, name: `${color.toLowerCase()}.jpg`, url: "" }],
    measurements: {
      weight_value: 0,
      weight_unit: "g",
      height_value: 0,
      height_unit: "cm",
      width_value: 0,
      width_unit: "cm",
      length_value: 0,
      length_unit: "cm",
    },
  }));
};

export const mockMappingProducts: MappingProduct[] = [
  {
    id: "1",
    source_product: {
      company_id: "c1",
      created_at: "2025-04-01T10:00:00Z",
      updated_at: "2025-04-01T12:00:00Z",
      publish: true,
      name: "Digital Camera Digital Camera Digital Camera",
      id: "sp1",
      sku: "DC-001",
      barcode: "*********",
      brand: { id: "b1", name: "Canon" },
      category: { id: "cat1", name: "Electronics" },
      images: [{ id: "img1", name: "camera.jpg", url: "" }],
      prices: [{ price: 49.99, price_group: { id: "pg1", name: "Retail" } }],
      variants: createVariants("sp1", ["Black", "Silver", "Red"]),
      options: [{ id: "opt1", name: "Color", values: ["Black", "Silver", "Red"] }],
      description: "A high-quality digital camera.",
      shortDescription: "Digital Camera",
      tags: "camera, electronics",
      source: null,
    },
    destination_product: {
      company_id: "c1",
      created_at: "2025-04-01T10:00:00Z",
      updated_at: "2025-04-01T12:00:00Z",
      publish: true,
      name: "Digital Camera",
      id: "dp1",
      sku: "DC-001",
      barcode: "*********",
      brand: { id: "b1", name: "Canon" },
      category: { id: "cat1", name: "Electronics" },
      images: [{ id: "img1", name: "camera.jpg", url: "" }],
      prices: [{ price: 49.99, price_group: { id: "pg1", name: "Retail" } }],
      variants: createVariants("dp1", ["Black", "Silver"]),
      options: [{ id: "opt1", name: "Color", values: ["Black", "Silver"] }],
      description: "A high-quality digital camera.",
      shortDescription: "Digital Camera",
      tags: "camera, electronics",
      source: null,
    },
    price: 49.99,
    synced_at: "2025-04-01T10:19:06.477499+00:00",
    status: MappingProductStatus.SYNCED,
  },
  {
    id: "2",
    source_product: {
      company_id: "c1",
      created_at: "2025-04-01T10:00:00Z",
      updated_at: "2025-04-01T12:00:00Z",
      publish: true,
      name: "         Smartphone Smartphone Smartphone SmartphoneSmartphoneSmartphone         ",
      id: "sp2",
      sku: "SP-001",
      barcode: "*********",
      brand: { id: "b2", name: "Samsung" },
      category: { id: "cat2", name: "Mobile Phones" },
      images: [{ id: "img2", name: "smartphone.jpg", url: "" }],
      prices: [{ price: 699.99, price_group: { id: "pg2", name: "Retail" } }],
      variants: createVariants("sp2", [
        "Black",
        "White",
        "Blue",
        "Gold",
        "Purple",
        "Red",
        "Green",
        "Yellow",
      ]),
      options: [{ id: "opt2", name: "Color", values: ["Black", "White", "Blue", "Gold"] }],
      description: "A high-end smartphone.",
      shortDescription: "Smartphone",
      tags: "smartphone, mobile",
      source: null,
    },
    destination_product: undefined,
    price: 699.99,
    synced_at: "2025-04-08T10:00:00Z",
    status: MappingProductStatus.UNMAPPED,
  },
  {
    id: "3",
    source_product: {
      company_id: "c1",
      created_at: "2025-04-01T10:00:00Z",
      updated_at: "2025-04-01T12:00:00Z",
      publish: true,
      name: "Laptop",
      id: "sp3",
      sku: "LP-001",
      barcode: "1122334455",
      brand: { id: "b3", name: "Dell" },
      category: { id: "cat3", name: "Computers" },
      images: [{ id: "img3", name: "laptop.jpg", url: "" }],
      prices: [{ price: 999.99, price_group: { id: "pg3", name: "Retail" } }],
      variants: createVariants("sp3", ["Black", "Silver"]),
      options: [{ id: "opt3", name: "Color", values: ["Black", "Silver"] }],
      description: "A powerful laptop.",
      shortDescription: "Laptop",
      tags: "laptop, computers",
      source: null,
    },
    destination_product: {
      company_id: "c1",
      created_at: "2025-04-01T10:00:00Z",
      updated_at: "2025-04-01T12:00:00Z",
      publish: true,
      name: "Laptop",
      id: "dp3",
      sku: "LP-001",
      barcode: "1122334455",
      brand: { id: "b3", name: "Dell" },
      category: { id: "cat3", name: "Computers" },
      images: [{ id: "img3", name: "laptop.jpg", url: "" }],
      prices: [{ price: 999.99, price_group: { id: "pg3", name: "Retail" } }],
      variants: createVariants("dp3", ["Black", "Silver", "White", "Yellow", "Green", "Diamond"]),
      options: [
        {
          id: "opt3",
          name: "Color",
          values: ["Black", "Silver", "White", "Yellow", "Green", "Diamond"],
        },
      ],
      description: "A powerful laptop.",
      shortDescription: "Laptop",
      tags: "laptop, computers",
      source: null,
    },
    price: 999.99,
    synced_at: "2025-04-08T10:00:00Z",
    status: MappingProductStatus.ERROR,
  },
  {
    id: "4",
    source_product: {
      company_id: "c1",
      created_at: "2025-04-01T10:00:00Z",
      updated_at: "2025-04-01T12:00:00Z",
      publish: true,
      name: "Smart Watch",
      id: "sp4",
      sku: "SW-001",
      barcode: "55667788",
      brand: { id: "b4", name: "Apple" },
      category: { id: "cat4", name: "Wearables" },
      images: [{ id: "img4", name: "watch.jpg", url: "" }],
      prices: [{ price: 299.99, price_group: { id: "pg4", name: "Retail" } }],
      variants: createVariants("sp4", ["Black", "White", "Pink"]),
      options: [{ id: "opt4", name: "Color", values: ["Black", "White", "Pink"] }],
      description: "A smart watch with health tracking features.",
      shortDescription: "Smart Watch",
      tags: "watch, wearable, health",
      source: null,
    },
    destination_product: {
      company_id: "c1",
      created_at: "2025-04-01T10:00:00Z",
      updated_at: "2025-04-01T12:00:00Z",
      publish: true,
      name: "Smart Watch Series 3",
      id: "dp4",
      sku: "SW-001",
      barcode: "55667788",
      brand: { id: "b4", name: "Apple" },
      category: { id: "cat4", name: "Wearables" },
      images: [{ id: "img4", name: "watch.jpg", url: "" }],
      prices: [{ price: 299.99, price_group: { id: "pg4", name: "Retail" } }],
      variants: createVariants("dp4", ["Black", "White"]),
      options: [{ id: "opt4", name: "Color", values: ["Black", "White"] }],
      description: "A smart watch with health tracking features.",
      shortDescription: "Smart Watch",
      tags: "watch, wearable, health",
      source: null,
    },
    price: 299.99,
    synced_at: "2025-04-08T10:00:00Z",
    status: MappingProductStatus.MAPPED,
  },
];

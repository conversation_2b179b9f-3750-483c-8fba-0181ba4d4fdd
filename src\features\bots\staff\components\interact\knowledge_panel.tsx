import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CopyCheck,
  FileText,
  LibraryBig,
  Loader2,
  Trash2,
  Upload as UploadIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import AddKnowledgeDialog from "@/features/bots/knowledge/components/knowledge-dialog/add-knowledge";
import { Knowledge } from "@/features/bots/knowledge/types";
import { staffKeys } from "@/features/bots/staff/hooks/keys";
import { useUpdateStaff } from "@/features/bots/staff/hooks/staff";
import { VirtualStaffModel } from "@/features/bots/staff/hooks/type";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";

import KnowledgePanelSkeleton from "./skeleton/knowledge_panel";

interface StaffKnowledgePanelProps {
  staff: VirtualStaffModel;
  isLoading: boolean;
  onRefetch?: () => void;
}

const StatusBadge = ({ status }: { status: string }) => {
  const { t } = useTranslation();
  const getVariant = (status: string) => {
    switch (status) {
      case "ERROR":
        return "sematic_error";
      case "PENDING":
        return "sematic_warning";
      case "READY":
        return "sematic_success";
      case "PROCESSING":
        return "sematic_info";
    }
  };

  return (
    <Badge variant={getVariant(status)} className="border-none text-xs font-semibold">
      {t(`pages.knowledge.status.${status.toLowerCase()}`)}
    </Badge>
  );
};

const WarningBanner = () => {
  const { t } = useTranslation();
  return (
    <div className=" flex items-start gap-2 rounded  px-3 py-2 text-xs text-sematic-warning">
      <AlertTriangle size={16} className="flex-none text-sematic-warning" />
      <span className="text-sm">{t("pages.staff.knowledge.warning")}</span>
    </div>
  );
};

const formatFileSize = (bytes: number | string): string => {
  const numBytes = typeof bytes === "string" ? parseInt(bytes) : bytes;

  if (isNaN(numBytes)) return "0 KB";

  if (numBytes >= 1024 * 1024) {
    // Convert to MB and round to 1 decimal place
    return `${(numBytes / (1024 * 1024)).toFixed(1)} MB`;
  } else {
    // Convert to KB and round to whole number
    return `${Math.round(numBytes / 1024)} KB`;
  }
};

const FileList = ({
  files,
  onKnowledgeChange,
  onDelete,
  isDeleting,
  deletingId,
}: {
  files: Knowledge[];
  onKnowledgeChange?: (knowledge: Knowledge[]) => void;
  onDelete?: (knowledge: Knowledge) => void;
  isDeleting?: boolean;
  deletingId?: string;
}) => (
  <div className="w-full">
    <Table>
      <TableBody>
        {files.map((file, i) => (
          <TableRow key={i}>
            <TableCell className="h-[48px] py-0">
              <div className="flex max-w-[280px] items-center gap-2 lg:max-w-[300px]">
                <FileText size={16} className="shrink-0 text-muted-foreground" />
                <span className="truncate">{file.name}</span>
              </div>
            </TableCell>
            <TableCell className="h-[48px] py-0 text-right">
              <span className="text-xs text-muted-foreground">{formatFileSize(file.size)}</span>
            </TableCell>
            <TableCell className="h-[48px] py-0 text-right">
              <StatusBadge status={file.status} />
            </TableCell>
            <TableCell className="h-[48px] py-0 text-right">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onDelete?.(file)}
                disabled={isDeleting}
                className="h-8 w-8">
                {isDeleting && deletingId === file.id ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Trash2
                    size={16}
                    className={isDeleting ? "text-muted-foreground" : "text-red-500"}
                  />
                )}
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
);

const KnowledgeTabs = ({
  files,
  staff,
  isLoading,
  onRefetch,
}: {
  files: Knowledge[];
  staff: VirtualStaffModel;
  isLoading: boolean;
  onRefetch?: () => void;
}) => {
  const { t } = useTranslation();
  const [isAddKnowledgeOpen, setIsAddKnowledgeOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedKnowledge, setSelectedKnowledge] = useState<Knowledge[]>(files);
  const [deletingId, setDeletingId] = useState<string>();
  const [lastDeletedId, setLastDeletedId] = useState<string>();
  const queryClient = useQueryClient();

  // Update selected knowledge when files prop changes
  useEffect(() => {
    setSelectedKnowledge(files);
  }, [files]);

  const updateStaffMutation = useUpdateStaff({
    onSuccess: (updatedStaff) => {
      toast.success(t("common.knowledgeUpdated") || "Knowledge updated successfully");
      queryClient.invalidateQueries({ queryKey: staffKeys.detail(updatedStaff.id) });
      setIsSaving(false);
    },
    onError: () => {
      toast.error(t("common.failedToUpdateKnowledge") || "Failed to update knowledge");
      setIsSaving(false);
    },
  });

  const handleUploadClick = () => {
    setIsAddKnowledgeOpen(true);
  };

  const handleKnowledgeSave = async (newSelectedKnowledge: Knowledge[]) => {
    try {
      setIsSaving(true);

      const payload = {
        KNOWLEDGE: {
          configuration: {
            knowledge_base: {
              knowledge_ids: newSelectedKnowledge.map((k) => k.id),
            },
          },
        },
      };

      await updateStaffMutation.mutateAsync({
        id: staff.id,
        data: payload,
      });

      setSelectedKnowledge(newSelectedKnowledge);
      setTimeout(() => {
        onRefetch?.();
      }, 2000);
    } catch (error) {
      console.error("Error saving knowledge:", error);
      setIsSaving(false);
      setSelectedKnowledge(files);
    }
  };

  const handleKnowledgeDelete = async (knowledgeToDelete: Knowledge) => {
    try {
      setDeletingId(knowledgeToDelete.id);
      const newSelectedKnowledge = selectedKnowledge.filter((k) => k.id !== knowledgeToDelete.id);

      const payload = {
        KNOWLEDGE: {
          configuration: {
            knowledge_base: {
              knowledge_ids: newSelectedKnowledge.map((k) => k.id),
            },
          },
        },
      };

      await updateStaffMutation.mutateAsync({
        id: staff.id,
        data: payload,
      });

      setLastDeletedId(knowledgeToDelete.id);
      setSelectedKnowledge(newSelectedKnowledge);
      setTimeout(() => {
        onRefetch?.();
      }, 2000);
    } catch (error) {
      console.error("Error deleting knowledge:", error);
      setSelectedKnowledge(files);
      toast.error(t("common.failedToDeleteKnowledge") || "Failed to delete knowledge");
    } finally {
      setDeletingId(undefined);
    }
  };

  return (
    <>
      <Tabs defaultValue="knowledge" className="flex h-auto flex-col overflow-hidden">
        <div className="flex flex-none items-center justify-between ">
          <TabsList className="items-end rounded-none border-b border-transparent bg-transparent p-0">
            <TabsTrigger
              value="knowledge"
              className="group relative h-9 items-end rounded-none border-b-2 border-transparent px-5 text-sm  font-semibold data-[state=active]:border-primary  data-[state=active]:bg-transparent data-[state=active]:shadow-none">
              <span className="flex items-center gap-1">
                <LibraryBig size={16} /> {t("pages.staff.knowledge.tab")}
                <Badge variant="sematic_error" className="border-none  text-xs">
                  {t("pages.staff.knowledge.baby")}
                </Badge>
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="task"
              className="group relative h-9 items-end rounded-none border-b-2 border-transparent px-5 text-sm  font-semibold text-muted-foreground data-[state=active]:border-primary  data-[state=active]:bg-transparent data-[state=active]:shadow-none">
              <span className="flex items-center gap-1">
                <CopyCheck size={16} />
                <span className="px-2 py-0.5">{t("pages.staff.task.tab")}</span>
              </span>
            </TabsTrigger>
          </TabsList>
          <Button
            variant="link"
            size="sm"
            leftIcon={<UploadIcon size={16} />}
            onClick={handleUploadClick}
            disabled={isSaving}>
            {t("common.upload")}
          </Button>
        </div>
        <TabsContent value="knowledge" className="flex flex-auto flex-col overflow-hidden">
          {isLoading ? (
            <KnowledgePanelSkeleton />
          ) : (
            <div className="flex-auto flex-col overflow-y-auto">
              <div className="sticky top-0 z-10 bg-card">
                <WarningBanner />
              </div>
              <FileList
                files={selectedKnowledge}
                onDelete={handleKnowledgeDelete}
                isDeleting={updateStaffMutation.isPending}
                deletingId={deletingId}
              />
            </div>
          )}
        </TabsContent>
        <TabsContent value="task">
          <div className="flex h-64 items-center justify-center text-muted-foreground">
            {t("pages.staff.task.noTasks")}
          </div>
        </TabsContent>
      </Tabs>

      <AddKnowledgeDialog
        open={isAddKnowledgeOpen}
        onOpenChange={(open) => {
          setIsAddKnowledgeOpen(open);
          if (!open) {
            setIsSaving(false);
            setLastDeletedId(undefined);
          }
        }}
        onClose={() => {
          setIsAddKnowledgeOpen(false);
          setIsSaving(false);
          setLastDeletedId(undefined);
        }}
        showKnowledgeSection={true}
        selectedKnowledge={selectedKnowledge}
        onSave={handleKnowledgeSave}
        lastDeletedId={lastDeletedId}
        isStaffContext={true}
      />
    </>
  );
};

export default function StaffKnowledgePanel({
  staff,
  isLoading,
  onRefetch,
}: StaffKnowledgePanelProps) {
  // Get the knowledge list from staff configuration
  const knowledgeList = staff?.configuration?.knowledge_base?.knowledge_list || [];

  return (
    <div className="flex max-h-[50%] flex-1  flex-col p-4">
      <KnowledgeTabs
        files={knowledgeList}
        staff={staff}
        isLoading={isLoading}
        onRefetch={onRefetch}
      />
    </div>
  );
}

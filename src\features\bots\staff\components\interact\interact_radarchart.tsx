import { useTranslation } from "react-i18next";

import CustomRadarChart, { RadarChartData } from "../radar_chart";

interface StaffRadarChartProps {
  radarData: RadarChartData[];
}

export default function StaffRadarChart({ radarData }: StaffRadarChartProps) {
  const { t } = useTranslation();
  return (
    <div className="flex-1 min-h-[230px] h-[50%] p-4 overflow-auto">
      <span className="text-base font-semibold">{t("pages.staff.score")}</span>
      <div className="flex items-center justify-center">
        <CustomRadarChart radarData={radarData} />
      </div>
    </div>
  );
}

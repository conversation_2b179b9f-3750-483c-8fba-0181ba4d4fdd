name: 🚀 Vercel Multi-Project Deployment

on:
  push:
    branches: [main, dev]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy ${{ matrix.project.name }}
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    strategy:
      fail-fast: false  # Continue other deployments if one fails
      matrix:
        project:
          - name: optiwarehouse-app
            project_id: ${{ secrets.VERCEL_PROJECT_ID }}
            directory: "."
            dev_url: "https://dev.optiwarehouse.com"
            build_command: "yarn build"
          - name: onexbots
            project_id: ${{ secrets.VERCEL_PROJECT_ID_ONXBOTS }}
            directory: "."
            dev_url: ""
            build_command: "yarn build"
          - name: onexsync
            project_id: ${{ secrets.VERCEL_PROJECT_ID_ONEXSYNC }}
            directory: "."
            dev_url: ""
            build_command: "yarn build"

    environment: ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🏷️ Display Project Info
        run: |
          echo "🚀 Deploying: ${{ matrix.project.name }}"
          echo "🌍 Environment: ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}"
          echo "🌿 Branch: ${{ github.ref_name }}"
          echo "📁 Directory: ${{ matrix.project.directory }}"
          echo "🔨 Build Command: ${{ matrix.project.build_command }}"

      - name: 🛠️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: yarn
          cache-dependency-path: ${{ matrix.project.directory }}/yarn.lock

      - name: 📦 Cache Dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            ~/.yarn
            node_modules
          key: ${{ runner.os }}-deps-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-deps-

      - name: ✅ Validate Environment
        run: |
          echo "Validating environment variables for ${{ matrix.project.name }}..."
          
          if [[ -z "${{ secrets.VERCEL_ORG_ID }}" ]]; then
            echo "❌ VERCEL_ORG_ID is missing"
            exit 1
          fi
          
          if [[ -z "${{ matrix.project.project_id }}" ]]; then
            echo "❌ Project ID is missing for ${{ matrix.project.name }}"
            exit 1
          fi
          
          if [[ -z "${{ secrets.VERCEL_TOKEN }}" ]]; then
            echo "❌ VERCEL_TOKEN is missing"
            exit 1
          fi
          
          echo "✅ All environment variables validated"

      - name: 🔧 Install Vercel CLI
        run: npm install -g vercel@latest

      - name: 📚 Install Dependencies
        working-directory: ${{ matrix.project.directory }}
        run: yarn install --frozen-lockfile

      - name: ⚡ Pull Vercel Environment
        working-directory: ${{ matrix.project.directory }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ matrix.project.project_id }}
        run: |
          echo "Pulling Vercel environment for ${{ matrix.project.name }}..."
          vercel pull --yes --environment=${{ github.ref == 'refs/heads/main' && 'production' || 'preview' }} --token=${{ secrets.VERCEL_TOKEN }}

      - name: 🏗️ Build Project
        working-directory: ${{ matrix.project.directory }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ matrix.project.project_id }}
        run: |
          echo "Building ${{ matrix.project.name }}..."
          vercel build ${{ github.ref == 'refs/heads/main' && '--prod' || '' }} --token=${{ secrets.VERCEL_TOKEN }}

      - name: 🚀 Deploy to Vercel
        id: deploy
        working-directory: ${{ matrix.project.directory }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ matrix.project.project_id }}
        run: |
          echo "🚀 Starting deployment for ${{ matrix.project.name }}..."
          echo "Environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'preview' }}"
          
          set +e
          DEPLOYMENT_OUTPUT=$(vercel deploy --prebuilt ${{ github.ref == 'refs/heads/main' && '--prod' || '' }} --token=${{ secrets.VERCEL_TOKEN }} 2>&1)
          DEPLOY_EXIT_CODE=$?
          
          if [ $DEPLOY_EXIT_CODE -eq 0 ]; then
            echo "✅ Deployment successful for ${{ matrix.project.name }}!"
            echo "📍 Deployment URL: $DEPLOYMENT_OUTPUT"
            echo "DEPLOYMENT_URL=$DEPLOYMENT_OUTPUT" >> $GITHUB_ENV
          else
            echo "❌ Deployment failed for ${{ matrix.project.name }} with exit code: $DEPLOY_EXIT_CODE"
            echo "📋 Error output: $DEPLOYMENT_OUTPUT"
            echo "DEPLOYMENT_URL=" >> $GITHUB_ENV
          fi
          
          echo "DEPLOY_EXIT_CODE=$DEPLOY_EXIT_CODE" >> $GITHUB_ENV
          echo "PROJECT_NAME=${{ matrix.project.name }}" >> $GITHUB_ENV
          exit $DEPLOY_EXIT_CODE

      - name: 📢 Send Success Notification
        if: success()
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"
          
          # Format commit message
          TITLE=$(echo "$COMMIT_MSG" | head -n1)
          FORMATTED_TITLE=$(echo "$TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')
          
          BODY=$(echo "$COMMIT_MSG" | tail -n +2 | sed '/^$/d')
          if [ ! -z "$BODY" ]; then
            FORMATTED_BODY=$(echo "$BODY" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g' | sed 's/^/* /')
          else
            FORMATTED_BODY="No additional details"
          fi
          
          # Determine deployment URL
          if [[ "${{ github.ref }}" == "refs/heads/dev" && -n "${{ matrix.project.dev_url }}" ]]; then
            FINAL_URL="${{ matrix.project.dev_url }}"
          else
            FINAL_URL="${{ env.DEPLOYMENT_URL }}"
          fi
          
          MENTIONS=$(echo "${{ vars.SLACK_TEST_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')
          
          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "✅ Deployment Successful: ${{ matrix.project.name }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Project:*\n${{ matrix.project.name }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n`${{ github.ref_name }}`"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Commit:*\n`${{ github.sha }}`"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Deployment URL:*\n<'"$FINAL_URL"'|View Deployment> 🔗"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Summary:*\n'"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "cc: '"$MENTIONS"'"
                  }
                ]
              }
            ]
          }' ${{ vars.DEPLOY_WEBHOOK_URL }}

      - name: 📢 Send Failure Notification
        if: failure()
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"

          TITLE=$(echo "$COMMIT_MSG" | head -n1)
          FORMATTED_TITLE=$(echo "$TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')

          MENTIONS=$(echo "${{ vars.SLACK_DEV_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')

          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "❌ Deployment Failed: ${{ matrix.project.name }}",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Project:*\n${{ matrix.project.name }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n`${{ github.ref_name }}`"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Exit Code:*\n${{ env.DEPLOY_EXIT_CODE }}"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Summary:*\n'"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Workflow Details:*\n<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run> 🔍"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "🚨 '"$MENTIONS"' Please investigate!"
                  }
                ]
              }
            ]
          }' ${{ vars.REVIEW_WEBHOOK_URL }}

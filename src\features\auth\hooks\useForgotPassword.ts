import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authApi } from "@/lib/apis/auth";
import { createI18nResolver } from "@/lib/utils";

import { ForgotPasswordPayload } from "../types";
import { ForgotPasswordFormValues, forgotPasswordSchema } from "../utils/validators/forgotPass";
import { authKeys } from "./keys";

interface UseForgotPasswordOptions {
  onSuccess?: () => void;
}

export const useForgotPassword = (options: UseForgotPasswordOptions = {}) => {
  const router = useRouter();
  const { t } = useTranslation();
  const [countdown, setCountdown] = useState(0);
  const [isCheckingDelay, setIsCheckingDelay] = useState(true);

  const form = useForm<ForgotPasswordFormValues>({
    resolver: createI18nResolver(forgotPasswordSchema, t),
    defaultValues: {
      email: "",
    },
  });

  // Lưu thời gian đếm ngược cho việc gửi lại mã đặt lại mật khẩu
  const saveCountdown = (username: string, delay = 60) => {
    try {
      const expiryTimestamp = Date.now() + delay * 1000;
      localStorage.setItem(`resend_reset_countdown_${username}`, expiryTimestamp.toString());
      setCountdown(delay);
    } catch (error) {
      console.error("Lỗi khi lưu thời gian đếm ngược đặt lại mật khẩu:", error);
    }
  };

  // Lấy thời gian đếm ngược còn lại cho đặt lại mật khẩu
  const getCountdown = (username: string): number => {
    try {
      const storedValue = localStorage.getItem(`resend_reset_countdown_${username}`);
      if (!storedValue) return 0;

      const expiryTimestamp = parseInt(storedValue, 10);
      const currentTime = Date.now();

      const remainingSeconds = Math.max(0, Math.floor((expiryTimestamp - currentTime) / 1000));
      return remainingSeconds;
    } catch (error) {
      console.error("Lỗi khi lấy thời gian đếm ngược đặt lại mật khẩu:", error);
      return 0;
    }
  };

  // Giảm thời gian đếm ngược mỗi giây
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown((prev) => Math.max(0, prev - 1));
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleFocus = () => {
    resetMutation();
  };

  const onForgotPasswordSubmit = (data: ForgotPasswordFormValues) => {
    onSubmit({ username: data.email });
  };

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    isSuccess: success,
    reset: resetMutation,
  } = useMutation({
    mutationKey: authKeys.forgotPassword(""),
    mutationFn: async (params: ForgotPasswordPayload) => {
      const { username } = params;
      if (!username) {
        throw new Error(t("auth.usernameRequired"));
      }
      return authApi.forgot({ username });
    },
    onSuccess: (_, params) => {
      toast.success(t("auth.forgotPasswordSuccess"));
      saveCountdown(params.username);
      options.onSuccess?.();
      router.push(
        `/verify-confirmation-code-reset?username=${encodeURIComponent(params.username)}`
      );
    },
  });

  // Kiểm tra thời gian chờ cho username
  const checkCountdown = (params: ForgotPasswordPayload) => {
    setIsCheckingDelay(true);
    const remainingTime = getCountdown(params.username);
    setCountdown(remainingTime);
    setIsCheckingDelay(false);
    return remainingTime;
  };

  return {
    onForgotPasswordSubmit,
    loading,
    error,
    success,
    countdown,
    isCheckingDelay,
    checkCountdown,
    handleFocus,
    form,
  };
};

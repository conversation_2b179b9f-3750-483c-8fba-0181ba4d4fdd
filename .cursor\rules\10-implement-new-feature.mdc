---
description: 
globs: 
alwaysApply: true
---
# Implementing New Features

## Overview
This rule integrates all existing guidelines for implementing new features in the application. It provides a structured approach that combines project structure, development guidelines, architecture, UI standards, internationalization, verification, API integration, feature organization, and page setup.

## Implementation Steps (In Order)

### 1. Planning and Setup
1. **Project Structure** (Refer to @01-project-structure.mdc)
   - Define feature scope and requirements
   - Plan directory structure
   - Set up necessary files in appropriate directories:
     - `src/app`: Next.js app router and pages
     - `src/features`: Feature-specific components
     - `src/components`: Reusable UI components
     - `src/hooks`: Custom React hooks
     - `src/utils`: Utility functions
     - `src/lib`: Core libraries
     - `src/types`: TypeScript types
     - `src/i18n`: Internationalization
     - `src/config`: Configuration
     - `src/constants`: Constants
     - `src/assets`: Static assets

2. **Feature Organization** (Refer to @08-feature-organization.mdc)
   - Set up feature directory structure
   - Plan component organization
   - Define feature-specific hooks
   - Prepare documentation structure

### 2. Core Implementation
3. **Application Architecture** (Refer to @03-app-architecture.mdc)
   - Set up Next.js App Router
   - Implement state management
   - Configure application flow
   - Set up error boundaries
   - Plan data fetching patterns

4. **API Integration** (Refer to @07-api-integration-standards.mdc)
   - Define query key structure
   - Implement API hooks
   - Set up cache management
   - Configure error handling
   - Define response types

### 3. UI Development
5. **UI Development Standards** (Refer to @04-ui-development-standards.mdc)
   - Implement component structure
   - Separate business logic into hooks
   - Apply clean code guidelines
   - Use semantic theme colors
   - Implement responsive design
   - Apply code splitting

6. **Page Setup** (Refer to @09-page-setup-guidelines.mdc)
   - Implement page template
   - Integrate with Sidebar
   - Set up breadcrumb navigation
   - Configure page organization
   - Ensure responsive design
   - Maintain accessibility

### 4. Internationalization
7. **Internationalization** (Refer to @05-i18n-guidelines.mdc)
   - Set up translation files
   - Implement translation keys
   - Add translations to components
   - Handle dynamic content
   - Test translations

### 5. Quality Assurance
8. **Development Guidelines** (Refer to @02-development-guidelines.mdc)
   - Implement TypeScript
   - Apply ESLint and Prettier
   - Follow naming conventions
   - Set up testing
   - Optimize performance

9. **UI Verification** (Refer to @06-ui-verification-guidelines.mdc)
   - Perform design inspection
   - Complete verification checklist
   - Test responsive design
   - Address common issues
   - Conduct quality assurance
10. **Linter check**
   - Check lint error and fix 

## Implementation Checklist
1. [ ] Planning and Setup
   - [ ] Define feature scope
   - [ ] Set up directory structure
   - [ ] Organize feature components
   - [ ] Prepare documentation

2. [ ] Core Implementation
   - [ ] Configure application architecture
   - [ ] Set up API integration
   - [ ] Implement state management
   - [ ] Configure error handling

3. [ ] UI Development
   - [ ] Implement components
   - [ ] Set up pages
   - [ ] Configure navigation
   - [ ] Ensure responsiveness

4. [ ] Internationalization
   - [ ] Add translations
   - [ ] Test language support
   - [ ] Verify dynamic content

5. [ ] Quality Assurance
   - [ ] Complete testing
   - [ ] Verify design
   - [ ] Check performance
   - [ ] Review documentation

## Best Practices
1. **Code Organization**
   - Keep related files together
   - Use proper naming conventions
   - Maintain clean directory structure

2. **Component Development**
   - Follow UI standards
   - Implement proper testing
   - Maintain accessibility

3. **API Integration**
   - Use proper query structure
   - Implement error handling
   - Manage cache effectively

4. **Internationalization**
   - Use translation keys
   - Support multiple languages
   - Handle dynamic content

5. **Performance**
   - Optimize components
   - Implement proper caching
   - Handle large datasets

## Common Pitfalls to Avoid
1. **Inconsistent Structure**
   - Avoid mixing different patterns
   - Follow established conventions
   - Maintain consistency

2. **Poor Component Design**
   - Avoid large components
   - Implement proper separation
   - Follow UI standards

3. **Missing Internationalization**
   - Always use translation keys
   - Support all languages
   - Handle dynamic content

4. **Improper API Integration**
   - Follow query structure
   - Implement proper error handling
   - Manage cache effectively

5. **Incomplete Testing**
   - Test all components
   - Verify translations
   - Check accessibility

## Documentation Requirements
1. **Component Documentation**
   - Purpose and usage
   - Props and types
   - Examples

2. **API Documentation**
   - Endpoints and methods
   - Request/response types
   - Error handling

3. **Feature Documentation**
   - Overview and purpose
   - Implementation details
   - Usage examples

## Review Process
1. **Code Review**
   - Follow all guidelines
   - Check documentation
   - Verify testing

2. **UI Review**
   - Match design specifications
   - Check responsiveness
   - Verify accessibility

3. **Performance Review**
   - Check optimization
   - Verify caching
   - Test large datasets

import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { authApi } from "@/lib/apis/auth";
import { clearAuth, setAuthToken } from "@/lib/auth";
import { transformUserInfo } from "@/lib/transforms";
import { createI18nResolver } from "@/lib/utils";

import { LoginPayload } from "../types";
import { LoginFormValues, loginSchema } from "../utils/validators/login";
import { authKeys } from "./keys";

export const useLogin = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const form = useForm<LoginFormValues>({
    resolver: createI18nResolver(loginSchema, t),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const handleGoogleLogin = () => {
    // Implement Google login logic
  };

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    reset: resetMutation,
  } = useMutation({
    mutationKey: authKeys.login(),
    mutationFn: async (data: LoginPayload) => {
      try {
        if (!data.username || !data.password) {
          throw new Error(t("validation.required"));
        }

        // Call API login
        const response = await authApi.login(data);

        // Save temporary token to call API get info
        setAuthToken({ Token: response });

        // Call API get user info
        const userInfoResponse = await authApi.getInfo(
          response.AccessToken,
          response.IdToken,
          data.username
        );

        // Transform data and save token
        const transformedData = transformUserInfo(userInfoResponse, response);
        setAuthToken(transformedData);

        return {
          loginResponse: response,
          userInfo: userInfoResponse,
          transformedData,
        };
      } catch (error) {
        clearAuth();
        throw error;
      }
    },
    onSuccess: () => {
      router.push("/dashboard");
    },
    onError: (error: any, variables: LoginPayload) => {
      clearAuth();
      const message = error instanceof Error ? error.message : t(String(error));
      if (message === "User is not confirmed") {
        router.push(`/verify-confirmation-code?username=${encodeURIComponent(variables.username)}`);
      }
    },
  });

  const handleFocus = () => {
    resetMutation();
  };

  const loginSubmit = (formData: LoginFormValues) => {
    onSubmit({
      username: formData.username,
      password: formData.password,
    });
  };

  // Reset error when form changes

  return {
    loginSubmit,
    loading,
    form,
    error,
    handleGoogleLogin,
    handleFocus,
  };
};

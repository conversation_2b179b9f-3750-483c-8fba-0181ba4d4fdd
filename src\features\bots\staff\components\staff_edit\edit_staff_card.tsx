import { ReactNode } from "react";

import { Card, CardTitle } from "@/components/ui";

interface EditStaffCardProps {
  title: string;
  description?: string;
  headerButton?: ReactNode;
  children: ReactNode;
}

export default function EditStaffCard({
  title,
  description,
  headerButton,
  children,
}: EditStaffCardProps) {
  return (
    <Card className="h-auto flex-auto space-y-4 p-4">
      <div className="flex flex-col gap-1">
        <div className="flex items-start justify-between">
          <CardTitle className="font-semibold">{title}</CardTitle>
          {headerButton}
        </div>
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
      </div>
      <div>{children}</div>
    </Card>
  );
}

import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { CustomImage } from "@/components/ui/image";

interface ProductCardProps {
  product: {
    id: string;
    name: string;
    images: Array<{ id: string; name: string; url: string }>;
    source: string;
    category: string;
    brand: string;
    tags?: string | null;
    description: string;
    shortDescription: string;
    createdAt: string;
  };
}

export const ProductCard = ({ product }: ProductCardProps) => {
  const { t } = useTranslation();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const thumbnailsRef = useRef<HTMLDivElement>(null);

  const toggleDescription = () => {
    setIsDescriptionExpanded(!isDescriptionExpanded);
  };

  const truncateDescription = (text: string, maxLength: number) => {
    if (!text) return "";
    if (text.length <= maxLength) return text;
    return isDescriptionExpanded ? text : `${text.slice(0, maxLength)}`;
  };

  const handleThumbnailClick = (index: number) => {
    setSelectedImageIndex(index);

    // Scroll the clicked thumbnail into view
    const thumbnailElements = thumbnailsRef.current?.children;
    if (thumbnailElements && thumbnailElements[index]) {
      thumbnailElements[index].scrollIntoView({
        behavior: "smooth",
        block: "nearest",
        inline: "center",
      });
    }
  };

  return (
    <div className="space-y-6 rounded-lg bg-card">
      {/* Header Section */}
      <div className="px-6">
        <h1 className="mt-4 truncate pt-4 text-base font-semibold text-foreground">
          {product.name}
        </h1>
        <p className="line-clamp-2 text-sm text-muted-foreground">{product.shortDescription}</p>
      </div>

      {/* Main Content */}
      <div className="flex flex-col gap-4 lg:flex-row">
        {/* Left Column - Image Gallery */}
        <div className="w-full lg:w-1/3">
          <div className="flex h-full flex-col rounded-lg bg-card">
            <div className="relative flex-1 p-4">
              {/* Main Image Container */}
              <div className="mx-auto flex h-full max-w-[350px] flex-col">
                {/* Main Image - Square Aspect Ratio */}
                <div className="relative aspect-square w-full overflow-hidden rounded-xl">
                  <div className="absolute inset-0">
                    <CustomImage
                      src={product.images[selectedImageIndex]?.url}
                      alt={product.name}
                      fill
                      className="rounded-xl object-cover"
                      priority
                    />
                  </div>
                </div>

                {/* Thumbnail Strip */}
                <div className="relative mt-4">
                  <div ref={thumbnailsRef} className="flex gap-2 overflow-x-auto scroll-smooth p-2">
                    {product.images.length > 0 ? (
                      product.images.map((image, index) => (
                        <button
                          key={image.id}
                          onClick={() => handleThumbnailClick(index)}
                          className={`relative aspect-square w-16 shrink-0 overflow-hidden rounded-xl transition-all
                            ${
                              selectedImageIndex === index
                                ? "opacity-100 ring-2 ring-blue-500"
                                : "opacity-70 ring-1 ring-gray-200 hover:opacity-100"
                            }`}>
                          <div className="absolute inset-0">
                            <CustomImage
                              src={image.url}
                              alt={`${product.name} - View ${index + 1}`}
                              fill
                              className="rounded-xl object-cover"
                            />
                          </div>
                        </button>
                      ))
                    ) : (
                      <button className="relative aspect-square w-16 shrink-0 overflow-hidden rounded-xl opacity-70 ring-1 ring-gray-200 transition-all">
                        <div className="absolute inset-0">
                          <CustomImage
                            src={null}
                            alt="No image available"
                            fill
                            className="rounded-xl object-cover"
                          />
                        </div>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Product Details */}
        <div className="w-full lg:w-2/3">
          <div className="h-full space-y-4 rounded-lg bg-card p-4">
            {/* Metadata Grid */}
            <div className="grid h-full grid-cols-1 gap-y-2">
              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.source")}:</span>
                <span className="flex-1 truncate pl-4 text-sm font-bold">{product.source}</span>
              </div>
              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.category")}:</span>
                <span className="flex-1 truncate pl-4 text-sm font-bold">{product.category}</span>
              </div>
              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.brand")}:</span>
                <span className="flex-1 truncate pl-4 text-sm font-bold">{product.brand}</span>
              </div>
              {product.tags && (
                <div className="flex items-center border-b py-3">
                  <span className="min-w-[164px] text-sm">{t("pages.products.tags")}:</span>
                  <div className="flex flex-wrap gap-2 pl-4">
                    {product.tags.split(",").map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag.trim()}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              <div className="flex items-center border-b py-3">
                <span className="min-w-[164px] text-sm">{t("pages.products.createdAt")}:</span>
                <span className="flex-1 truncate pl-4 text-sm font-bold">
                  {new Date(product.createdAt).toLocaleString()}
                </span>
              </div>

              {/* Description */}
              <div className="mt-4 flex-1">
                <div className="flex items-start pt-2">
                  <span className="min-w-[164px] text-sm">{t("pages.products.description")}:</span>
                  <div className="flex-1 overflow-hidden">
                    <p className="whitespace-pre-wrap break-words pl-4 text-sm font-bold">
                      {truncateDescription(product.description, 100)}
                      {product.description && product.description.length > 100 && (
                        <span
                          onClick={toggleDescription}
                          className="ml-1 cursor-pointer text-gray-400 hover:text-gray-600">
                          {isDescriptionExpanded
                            ? t("pages.products.viewLess")
                            : t("pages.products.viewMore")}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

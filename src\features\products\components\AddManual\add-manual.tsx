"use client";

// React and Next imports
import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { ChevronDown, Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

// Hooks and Types
import { useAddProduct, useUpdateProduct } from "@/features/products/hooks/product";
import type {
  CreateImage,
  CreateProduct,
  Measurements,
  Price,
  PriceVariant as PriceVariantType,
  ProductOption,
  ProductOption as ProductOptionType,
  ProductUnit as ProductUnitType,
} from "@/features/products/hooks/types";
import { validateBasicInformation } from "@/features/products/utils/validators/basic-information";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
// UI Components
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { mediaApi } from "@/lib/apis/media";
import { cn } from "@/lib/utils";

import { BasicInformation } from "../basic-information";
import { ProductMeasurements, validateMeasurements } from "../product-measurements";
// Local Components
import { ProductOptions } from "../product-options";
import { ProductPrices } from "../product-prices";
import { ProductUnits } from "../product-units";

interface FormData {
  basicInfo: {
    images: CreateImage[];
    name: string;
    description: string;
    shortDescription: string;
    brand: { id: string; name: string } | string;
    category: { id: string; name: string } | string;
    sku: string;
    tags: string | null;
    _touched?: {
      name: boolean;
      sku: boolean;
    };
  };
  options: ProductOptionType[];
  units: ProductUnitType[];
  measurements: Measurements;
  variants: PriceVariantType[];
  publish: boolean;
}

interface AddManualProps {
  isEditing?: boolean;
  productData?: any; // Replace with your product type
}

export function AddManual({ isEditing = false, productData }: AddManualProps) {
  const { t } = useTranslation();
  const router = useRouter();

  // Move navigationItems inside component to access t
  const navigationItems = useMemo(
    () => [
      { id: "basic-info", title: t("pages.products.addManual.sections.basicInfo") },
      { id: "product-options", title: t("pages.products.addManual.sections.options") },
      { id: "product-units", title: t("pages.products.addManual.sections.units") },
      { id: "product-prices", title: t("pages.products.addManual.sections.prices") },
      { id: "measurements", title: t("pages.products.addManual.sections.measurements") },
    ],
    [t]
  );

  /**
   * Extracts images associated with option values from variants.
   * IMPORTANT: Only extracts images for option1 values, not option2 or option3.
   * This is intentional to ensure consistency with how images are handled in the UI.
   */
  const extractOptionValueImages = useCallback((variants: any[]) => {
    if (!variants || !Array.isArray(variants)) return [];

    // Track images for each option1 value only
    const valueImageArrays: Record<string, CreateImage[]> = {};

    variants.forEach((variant) => {
      // Process option values and their images - only for option1
      const processOptionValue = (optionValue: string | null | undefined, images: any[]) => {
        if (!optionValue || !images || !images.length) return;

        const value = optionValue.toLowerCase();

        // Initialize array if it doesn't exist
        if (!valueImageArrays[value]) {
          valueImageArrays[value] = [];
        }

        // Add all images to the array - no deduplication
        images.forEach((img) => {
          const imageUrl = img.url || img.image || "";
          if (imageUrl) {
            valueImageArrays[value].push({
              name: img.name || "",
              image: imageUrl,
            });
          }
        });
      };

      // Process ONLY option1 with the variant's images
      if (variant.images && variant.images.length) {
        if (variant.option1) processOptionValue(variant.option1, variant.images);
        // Removed processing for option2 and option3
      }
    });

    // Convert the arrays to the expected format
    const valueImages: { value: string; images: CreateImage[] }[] = [];

    Object.keys(valueImageArrays).forEach((value) => {
      const images = valueImageArrays[value];
      if (images.length > 0) {
        valueImages.push({
          value,
          images,
        });
      }
    });

    return valueImages;
  }, []);

  // Extract unique options from variants
  const extractOptions = useCallback(
    (variants: any[]) => {
      if (!variants || !Array.isArray(variants)) return [];

      const options: ProductOptionType[] = [];

      // If product has existing options with names, use those first
      const existingOptions = productData?.options || [];

      // Extract option1 values
      const option1Values = Array.from(
        new Set(
          variants
            .map((v: any) => v.option1)
            .filter(Boolean)
            .map((v: any) => v?.toLowerCase())
        )
      );

      if (option1Values.length > 0) {
        options.push({
          // Use the name from existing options if available
          name: existingOptions[0]?.name || "",
          values: option1Values.map((value) => ({
            id: crypto.randomUUID(),
            value,
          })),
        });
      }

      // Extract option2 values
      const option2Values = Array.from(
        new Set(
          variants
            .map((v: any) => v.option2)
            .filter(Boolean)
            .map((v: any) => v?.toLowerCase())
        )
      );

      if (option2Values.length > 0) {
        options.push({
          // Use the name from existing options if available
          name: existingOptions[1]?.name || "",
          values: option2Values.map((value) => ({
            id: crypto.randomUUID(),
            value,
          })),
        });
      }

      // Extract option3 values
      const option3Values = Array.from(
        new Set(
          variants
            .map((v: any) => v.option3)
            .filter(Boolean)
            .map((v: any) => v?.toLowerCase())
        )
      );

      if (option3Values.length > 0) {
        options.push({
          // Use the name from existing options if available
          name: existingOptions[2]?.name || "",
          values: option3Values.map((value) => ({
            id: crypto.randomUUID(),
            value,
          })),
        });
      }

      return options;
    },
    [productData]
  );

  const [initialOptionValueImages, setInitialOptionValueImages] = useState<
    { value: string; images: CreateImage[] }[]
  >([]);

  const [formData, setFormData] = useState<FormData>(() => {
    if (isEditing && productData) {
      // Map product images
      const productImages =
        productData.images?.map((img: any) => ({
          name: img.name || "",
          image: img.url || img.image || "",
        })) || [];

      // Extract options and option value images
      const extractedOptions = extractOptions(productData.variants || []);
      const extractedValueImages = extractOptionValueImages(productData.variants || []);

      // Store extracted value images for use in ProductOptions component
      setInitialOptionValueImages(extractedValueImages);

      // Map variants with properly formatted images
      const mappedVariants =
        productData.variants?.map((variant: any) => {
          return {
            ...variant,
            original_sku: variant.original_sku || variant.sku, // Preserve original SKU for units
            images:
              variant.images?.map((img: any) => ({
                name: img.name || "",
                image: img.url || img.image || "",
              })) || [],
          };
        }) || [];

      // Extract unit information from child variants
      const extractedUnits: ProductUnitType[] = [];

      // Process parent-child relationships for units
      mappedVariants.forEach((variant: any) => {
        // If this is a child variant with a unit
        if (variant.parent_variant_id && variant.unit) {
          // Find the parent variant
          const parentVariant = mappedVariants.find((v: any) => v.id === variant.parent_variant_id);

          if (parentVariant) {
            // Create a unit entry for this parent-child relationship
            extractedUnits.push({
              id: crypto.randomUUID(),
              variant: parentVariant.id,
              variant_name: parentVariant.name,
              unit: variant.unit.id,
              unit_name: variant.unit.name,
              child_variant_id: variant.id,
            });
          }
        }
      });

      return {
        basicInfo: {
          images: productImages,
          name: productData.name || "",
          description: productData.description || "",
          shortDescription: productData.shortDescription || "",
          brand: productData.brand || "",
          category: productData.category || "",
          sku: productData.sku || "",
          tags: Array.isArray(productData.tags) ? productData.tags.join(",") : productData.tags,
        },
        options: extractedOptions,
        units: extractedUnits,
        measurements: productData.measurements || {
          weight_value: 0,
          weight_unit: "g",
          height_value: 0,
          height_unit: "cm",
          width_value: 0,
          width_unit: "cm",
          length_value: 0,
          length_unit: "cm",
        },
        variants: mappedVariants,
        publish: !!productData.publish,
      };
    }

    // Return default state if not editing
    return {
      basicInfo: {
        images: [],
        name: "",
        description: "",
        shortDescription: "",
        brand: "",
        category: "",
        sku: "",
        tags: null,
      },
      options: [],
      units: [],
      measurements: {
        weight_value: 0,
        weight_unit: "g",
        height_value: 0,
        height_unit: "cm",
        width_value: 0,
        width_unit: "cm",
        length_value: 0,
        length_unit: "cm",
      },
      variants: [],
      publish: false,
    };
  });
  const [expandedSections, setExpandedSections] = useState<string[]>(["basic-info"]);
  const [showValidation, setShowValidation] = useState(false);
  const [hasOptionErrors, setHasOptionErrors] = useState(false);
  const [, setHasPriceErrors] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [activeSection, setActiveSection] = useState("basic-info");

  const toggleSection = (id: string) => {
    setExpandedSections((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
    setActiveSection(id);
  };

  const handleBasicInfoChange = (values: FormData["basicInfo"]) => {
    setFormData((prev) => {
      const newFormData = {
        ...prev,
        basicInfo: {
          ...values,
          images: values.images.map((img) => ({
            name: img.name,
            image: img.image,
          })),
        },
      };

      // If we have options, regenerate variants with new name/sku but preserve existing data
      if (newFormData.options.length && newFormData.options[0].values.length) {
        // First, we need to separate child variants from parent variants to preserve them
        const childVariants = prev.variants.filter(
          (v) => v.original_sku && v.original_sku !== v.sku
        );
        const parentSkuMap = new Map<string, string>();

        // Create a map of child variant original_sku to parent_variant_id
        childVariants.forEach((child) => {
          if (child.original_sku && child.parent_variant_id) {
            parentSkuMap.set(child.original_sku, child.parent_variant_id);
          }
        });

        // Regenerate parent variants with new name/sku
        const newParentVariants = generateVariantsByOptions(
          newFormData.options,
          values.name,
          values.sku,
          prev.variants.filter((v) => !v.original_sku || v.original_sku === v.sku)
        ).map((variant) => {
          // Find matching existing variant to preserve its data
          const existingVariant = prev.variants.find(
            (v) =>
              v.option1?.toLowerCase() === variant.option1?.toLowerCase() &&
              v.option2?.toLowerCase() === variant.option2?.toLowerCase() &&
              v.option3?.toLowerCase() === variant.option3?.toLowerCase() &&
              (!v.original_sku || v.original_sku === v.sku) // Only match parent variants
          );

          return {
            ...variant,
            // Preserve existing data if available
            images: existingVariant?.images || variant.images,
            prices: existingVariant?.prices || variant.prices,
            measurements: existingVariant?.measurements || variant.measurements,
          };
        });

        // Update child variants with new parent info but preserve their data
        const updatedChildVariants = childVariants.map((child) => {
          // Find the parent by the saved parent_variant_id
          const oldParentId = child.parent_variant_id;
          const oldParentSku = child.original_sku;

          if (!oldParentSku) return child;

          // Find the new parent variant with matching option values as the old parent
          const oldParent = prev.variants.find((v) => v.id === oldParentId);
          const newParent = oldParent
            ? newParentVariants.find(
                (v) =>
                  v.option1?.toLowerCase() === oldParent.option1?.toLowerCase() &&
                  v.option2?.toLowerCase() === oldParent.option2?.toLowerCase() &&
                  v.option3?.toLowerCase() === oldParent.option3?.toLowerCase()
              )
            : null;

          if (newParent) {
            // Update the child variant with the new parent's info
            return {
              ...child,
              name:
                newParent.name && child.unit
                  ? `${newParent.name} - ${child.unit.name}`
                  : child.name,
              sku: newParent.sku && child.unit ? `${newParent.sku}-${child.unit.ratio}` : child.sku,
              original_sku: newParent.sku,
              parent_variant_id: newParent.id,
              option1: newParent.option1,
              option2: newParent.option2,
              option3: newParent.option3,
            };
          }

          return child;
        });

        // Combine parent and child variants
        newFormData.variants = [...newParentVariants, ...updatedChildVariants];
      }

      return newFormData;
    });
  };

  const generateVariantsByOptions = useCallback(
    (options: ProductOption[], name: string, sku: string, existingVariants: PriceVariantType[]) => {
      // If no options, return default variant
      if (!options.length || !options[0].values.length) {
        return [
          {
            id: uuidv4(),
            name: `${name}`,
            sku: `${sku}`,
            option1: null,
            option2: null,
            option3: null,
            images: [], // Don't use basic info images
            prices: existingVariants[0]?.prices || [],
            measurements: formData.measurements,
          },
        ];
      }

      // Generate all possible combinations of option values
      let combinations: (string | null)[][] = [[]];
      options.forEach((option) => {
        const validValues = option.values.filter((v) => v.value.trim());
        const newCombinations: (string | null)[][] = [];

        combinations.forEach((combo: (string | null)[]) => {
          validValues.forEach((value) => {
            newCombinations.push([...combo, value.value.toLowerCase()]);
          });
        });

        combinations = newCombinations;
      });

      // Create variants from combinations
      return combinations.map((combo: (string | null)[]) => {
        // Find matching existing variant to preserve its data
        const matchingVariant = existingVariants.find(
          (v) =>
            v.option1?.toLowerCase() === combo[0]?.toLowerCase() &&
            v.option2?.toLowerCase() === combo[1]?.toLowerCase() &&
            v.option3?.toLowerCase() === combo[2]?.toLowerCase()
        );

        return {
          id: matchingVariant?.id || uuidv4(),
          name: `${name} - ${combo.filter(Boolean).join(" - ")}`,
          sku: `${sku}-${combo.filter(Boolean).join("-")}`,
          option1: combo[0] || null,
          option2: combo[1] || null,
          option3: combo[2] || null,
          images: matchingVariant?.images || [], // Preserve existing images
          prices: matchingVariant?.prices || [],
          measurements: matchingVariant?.measurements || formData.measurements,
        };
      });
    },
    [formData.measurements]
  );

  /**
   * Handles changes to product options and their associated images.
   * IMPORTANT: Images are only associated with option1 values.
   * This is by design to ensure variants have the correct images.
   */
  const handleOptionsChange = (
    newOptions: ProductOption[],
    newValueImages: { value: string; images: CreateImage[] }[]
  ) => {
    // Update initialOptionValueImages state to maintain consistency
    setInitialOptionValueImages(newValueImages);

    setFormData((prev) => {
      // Generate new variants based on options
      const newVariants = generateVariantsByOptions(
        newOptions,
        prev.basicInfo.name,
        prev.basicInfo.sku,
        prev.variants
      );

      // Map through all variants and apply images from option1 values ONLY
      const updatedVariants = newVariants.map((variant) => {
        // Find images for this variant's option1 value (if it has option1)
        let variantImages: CreateImage[] = [];

        if (variant.option1) {
          // Look for matching images in newValueImages by lowercased option1 value
          const valueImageData = newValueImages.find(
            (vi) => vi.value === variant.option1?.toLowerCase()
          );

          if (valueImageData && valueImageData.images.length > 0) {
            variantImages = valueImageData.images;
          }
        }

        return {
          ...variant,
          images: variantImages.length > 0 ? variantImages : variant.images || [],
        };
      });

      return {
        ...prev,
        options: newOptions,
        variants: updatedVariants,
      };
    });
  };

  const handleUnitsChange = (units: ProductUnitType[]) => {
    setFormData((prev) => ({
      ...prev,
      units,
      variants: prev.variants,
    }));
  };

  const handlePricesChange = (groupId: string, updatedVariants: PriceVariantType[]) => {
    setFormData((prev) => ({
      ...prev,
      variants: updatedVariants.map((variant) => {
        const pricesObject = Array.isArray(variant.prices)
          ? variant.prices.reduce(
              (acc, price) => ({
                ...acc,
                [price.price_group.id]: price,
              }),
              {} as { [key: string]: Price }
            )
          : variant.prices;

        return {
          ...variant,
          prices: Object.values(pricesObject) as Price[],
        };
      }),
    }));
  };

  const handleMeasurementsChange = (updatedVariants: PriceVariantType[]) => {
    setFormData((prev) => ({
      ...prev,
      variants: updatedVariants,
    }));
  };

  const addProduct = useAddProduct({
    onSuccess: (response) => {
      toast(t("pages.products.addManual.success"));
      // Navigate to product details with the new product's ID
      router.push(`/products/${response.id}`);
    },
    onError: (error) => {
      console.error("Error creating product:", error);
      toast(t("pages.products.addManual.error"));
    },
  });

  const updateProduct = useUpdateProduct({
    onSuccess: (response) => {
      // Clear the returnToVariantList flag if it exists
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("returnToVariantList");
      }

      toast(t("pages.products.addManual.successUpdate"));
      // Navigate to product details with the updated product's ID
      router.push(`/products/${response.id}`);
    },
    onError: (error) => {
      console.error("Error updating product:", error);
      toast(t("pages.products.addManual.error"));
    },
  });

  const validateForm = useCallback(() => {
    // Reset validation states
    setShowValidation(false);

    // Set showValidation in next tick to trigger fresh validation
    setTimeout(() => {
      setShowValidation(true);
    }, 0);

    const errors: { section: string; errors: { field: string; message: string }[] }[] = [];

    // Basic Info validation using schema
    const basicInfoValidation = validateBasicInformation({
      ...formData.basicInfo,
      tags: formData.basicInfo.tags ? formData.basicInfo.tags.split(",") : null,
    });

    if (!basicInfoValidation.success) {
      errors.push({ section: "basic-info", errors: basicInfoValidation.errors });
    }

    // Options validation
    if (hasOptionErrors) {
      errors.push({
        section: "product-options",
        errors: [{ field: "duplicate-values", message: "Please fix duplicate values" }],
      });
    }

    // Measurements validation
    const measurementErrors = validateMeasurements(formData.variants);
    if (measurementErrors.length) {
      errors.push({ section: "measurements", errors: measurementErrors });
    }

    if (errors.length > 0) {
      // Scroll to first error section
      const firstSection = errors[0].section;
      const element = document.getElementById(firstSection);
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
        setExpandedSections((prev) => [...prev, firstSection]);
      }
      return false;
    }

    return true;
  }, [formData, hasOptionErrors]);

  const cleanVariantsForSubmission = (variants: PriceVariantType[]) => {
    return variants.map(({ parent_variant_id: _parent_variant_id, ...variant }) => {
      // If this is a child variant with a unit, ensure the correct SKU format
      if (variant.original_sku && variant.unit) {
        // Format should be PARENT_SKU-RATIO (using unit ratio instead of name)
        const expectedSku = `${variant.original_sku}-${variant.unit.ratio}`;
        if (variant.sku !== expectedSku) {
          variant.sku = expectedSku;
        }
      }

      // Keep original_sku in the payload for API processing
      return variant;
    });
  };

  const transformOptions = (rawOptions: any[]): any[] => {
    return rawOptions.map((option) => ({
      name: option.name || "",
      values: option.values
        .map((v: any) => v.value?.trim())
        .filter((v: any): v is string => Boolean(v)), // type guard to narrow to string
    }));
  };

  const handleSubmitClick = useCallback(async () => {
    // Validate the form
    const isValid = validateForm();
    if (isValid) {
      try {
        const cleanedVariants = cleanVariantsForSubmission(formData.variants);

        // First, process all option value images to avoid duplicate uploads
        const processedValueImages = new Map<string, CreateImage[]>();

        // Extract all unique option1 values from variants
        const option1Values = new Set<string>();
        cleanedVariants.forEach((variant) => {
          if (variant.option1) {
            option1Values.add(variant.option1.toLowerCase());
          }
        });

        // For each option value, find its images and process them once
        for (const value of Array.from(option1Values)) {
          // Find the matching option value in the first option
          if (
            formData.options.length > 0 &&
            formData.options[0].values.some((v) => v.value === value)
          ) {
            // Find images for this option value
            const valueImageData = initialOptionValueImages.find((v) => v.value === value);
            if (valueImageData && valueImageData.images.length > 0) {
              // In edit mode, we need to process base64 images
              if (isEditing) {
                const uniqueImageMap = new Map<string, CreateImage>();

                // Deduplicate images by URL first
                valueImageData.images.forEach((img) => {
                  if (!uniqueImageMap.has(img.image)) {
                    uniqueImageMap.set(img.image, img);
                  }
                });

                // Process each unique image once
                const processedImages = await Promise.all(
                  Array.from(uniqueImageMap.values()).map(async (img) => {
                    // Check if this is a base64 image that needs to be uploaded
                    if (img.image && img.image.startsWith("data:")) {
                      try {
                        // Extract the filename if present in the base64 string
                        const nameMatch = img.image.match(/;name=(.*?)(;|$)/);
                        const filename = nameMatch
                          ? decodeURIComponent(nameMatch[1])
                          : img.name || "image.jpg";

                        // Upload to API
                        const response = await mediaApi.uploadImage({
                          prefix: "media",
                          name: filename,
                          image: img.image,
                        });

                        return {
                          name: filename,
                          image: response.url,
                        };
                      } catch (error) {
                        console.error("Error uploading option value image:", error);
                        toast.error("Failed to upload an image. Please try again.");
                        return img;
                      }
                    } else {
                      // Already a URL, return as is
                      return img;
                    }
                  })
                );

                // Store processed images for this option value
                processedValueImages.set(value, processedImages);
              } else {
                // For new products, just store the image references
                const uniqueImageMap = new Map<string, CreateImage>();
                valueImageData.images.forEach((img) => {
                  if (!uniqueImageMap.has(img.image)) {
                    uniqueImageMap.set(img.image, img);
                  }
                });
                processedValueImages.set(value, Array.from(uniqueImageMap.values()));
              }
            }
          }
        }

        // Now assign the processed images to each variant based on its option1 value
        for (let i = 0; i < cleanedVariants.length; i++) {
          const variant = cleanedVariants[i];
          if (variant.option1) {
            const value = variant.option1.toLowerCase();
            const images = processedValueImages.get(value);
            if (images) {
              variant.images = images;
            }
          }
        }

        // In edit mode, check for base64 images in the product's main images
        let processedBasicImages = formData.basicInfo.images;
        if (isEditing) {
          processedBasicImages = await Promise.all(
            formData.basicInfo.images.map(async (img) => {
              if (img.image.startsWith("data:")) {
                try {
                  // Extract the filename if present in the base64 string
                  const nameMatch = img.image.match(/;name=(.*?)(;|$)/);
                  const filename = nameMatch
                    ? decodeURIComponent(nameMatch[1])
                    : img.name || "image.jpg";

                  // Upload to API
                  const response = await mediaApi.uploadImage({
                    prefix: "media",
                    name: filename,
                    image: img.image,
                  });

                  return {
                    name: filename,
                    image: response.url,
                  };
                } catch (error) {
                  console.error("Error uploading basic image:", error);
                  return img;
                }
              } else {
                return img;
              }
            })
          );
        }

        const payload: CreateProduct = {
          name: formData.basicInfo.name,
          sku: formData.basicInfo.sku,
          tags: formData.basicInfo.tags || null,
          description: formData.basicInfo.description,
          shortDescription: formData.basicInfo.shortDescription,
          brand: formData.basicInfo.brand
            ? {
                id:
                  typeof formData.basicInfo.brand === "string"
                    ? formData.basicInfo.brand
                    : formData.basicInfo.brand.id,
                name: "",
              }
            : null,
          category: formData.basicInfo.category
            ? {
                id:
                  typeof formData.basicInfo.category === "string"
                    ? formData.basicInfo.category
                    : formData.basicInfo.category.id,
                name: "",
              }
            : null,
          images: processedBasicImages,
          options: transformOptions(formData.options),
          prices: [],
          variants: cleanedVariants,
          measurements: formData.measurements,
          publish: formData.publish,
        };

        if (isEditing && productData) {
          // Transform images for update
          const transformedPayload = {
            ...payload,
            images: processedBasicImages.map((img) => ({
              id: "", // Server will handle ID
              name: img.name || "",
              url: img.image || "",
            })),
            variants: payload.variants.map((variant) => ({
              ...variant,
              // Preserve the original_sku for the API
              original_sku: variant.original_sku || variant.sku,
              // Remove slug field for child variants
              ...(variant.original_sku && variant.original_sku !== variant.sku
                ? { slug: undefined }
                : {}),
              images:
                variant.images?.map((img) => ({
                  id: "", // Server will handle ID
                  name: img.name || "",
                  url: img.image || "",
                })) || [],
            })),
          };

          updateProduct.mutateAsync({
            id: productData.id,
            data: transformedPayload,
          });
        } else {
          addProduct.mutateAsync(payload);
        }
      } catch (error) {
        console.error("Error submitting form:", error);
        toast.error("Failed to submit form. Please try again.");
      }
    }
  }, [
    validateForm,
    formData,
    addProduct,
    updateProduct,
    isEditing,
    productData,
    initialOptionValueImages,
  ]);

  const handleCancel = useCallback(() => {
    setShowCancelDialog(true);
  }, []);

  const handleLeave = useCallback(() => {
    // Check if we should return to the variant list
    const shouldReturnToVariantList =
      typeof window !== "undefined" && sessionStorage.getItem("returnToVariantList") === "true";

    if (shouldReturnToVariantList) {
      // Clear the flag and navigate to variants list
      sessionStorage.removeItem("returnToVariantList");
      router.push("/variants");
    } else if (isEditing && productData?.id) {
      // Return to product details
      router.push(`/products/${productData.id}`);
    } else {
      // Default case - go to products list
      router.push("/products");
    }
  }, [isEditing, productData, router]);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "start", // This will align to the left
      });
      setExpandedSections((prev) => [...prev, id]);
      setActiveSection(id);
    }
  };

  const handleSectionClick = (id: string) => {
    toggleSection(id);
    setActiveSection(id);
  };

  const handleSectionFocus = (section: string) => {
    setActiveSection(section);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
          }
        });
      },
      {
        rootMargin: "-60% 0px -75% 0px",
      }
    );

    // Observe all section elements
    navigationItems.forEach((item) => {
      const element = document.getElementById(item.id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => {
      // Cleanup observer
      navigationItems.forEach((item) => {
        const element = document.getElementById(item.id);
        if (element) {
          observer.unobserve(element);
        }
      });
    };
  }, [navigationItems]);

  return (
    <>
      <div className="flex h-full flex-col">
        {/* Main Content - flex */}
        <div className="flex-1 overflow-auto px-4">
          <div className="grid grid-cols-[minmax(0,1fr)_auto]">
            {/* Content Cards */}
            <div className="min-w-0">
              <div className="space-y-6">
                {/* Basic Information */}
                <div id="basic-info" className="">
                  <Card>
                    <div
                      className="flex cursor-pointer items-center justify-between p-6"
                      onClick={() => handleSectionClick("basic-info")}>
                      <h3 className="text-lg font-medium">
                        {t("pages.products.addManual.sections.basicInfo")}
                      </h3>
                      <ChevronDown
                        className={`size-5 transition-transform duration-300 ease-in-out ${
                          expandedSections.includes("basic-info") ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                    <div
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        expandedSections.includes("basic-info")
                          ? "max-h-[2000px] opacity-100"
                          : "max-h-0 opacity-0"
                      }`}>
                      <div className="p-6 pt-0">
                        <BasicInformation
                          onChange={handleBasicInfoChange}
                          onImagesChange={(images) => {
                            setFormData((prev) => ({
                              ...prev,
                              basicInfo: {
                                ...prev.basicInfo,
                                images,
                              },
                            }));
                          }}
                          initialValues={isEditing ? formData.basicInfo : undefined}
                          showValidation={showValidation}
                          onSectionFocus={() => handleSectionFocus("basic-info")}
                          isEditing={isEditing}
                        />
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Product Options */}
                <div id="product-options" className="">
                  <Card>
                    <div
                      className="flex cursor-pointer items-center justify-between p-6"
                      onClick={() => handleSectionClick("product-options")}>
                      <h3 className="text-lg font-medium">
                        {t("pages.products.addManual.sections.options")}
                      </h3>
                      <ChevronDown
                        className={`size-5 transition-transform duration-300 ease-in-out ${
                          expandedSections.includes("product-options") ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                    <div
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        expandedSections.includes("product-options")
                          ? "max-h-[2000px] opacity-100"
                          : "max-h-0 opacity-0"
                      }`}>
                      <div className="p-6 pt-0">
                        <ProductOptions
                          options={formData.options}
                          onChange={handleOptionsChange}
                          availableImages={formData.basicInfo.images}
                          showValidation={showValidation}
                          onValidationChange={setHasOptionErrors}
                          onSectionFocus={() => handleSectionFocus("product-options")}
                          initialValueImages={initialOptionValueImages}
                        />
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Product Units */}
                <div id="product-units" className="">
                  <Card>
                    <div
                      className="flex cursor-pointer items-center justify-between p-6"
                      onClick={() => handleSectionClick("product-units")}>
                      <h3 className="text-lg font-medium">
                        {t("pages.products.addManual.sections.units")}
                      </h3>
                      <ChevronDown
                        className={`size-5 transition-transform duration-300 ease-in-out ${
                          expandedSections.includes("product-units") ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                    <div
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        expandedSections.includes("product-units")
                          ? "max-h-[2000px] opacity-100"
                          : "max-h-0 opacity-0"
                      }`}>
                      <div className="p-6 pt-0">
                        <ProductUnits
                          units={formData.units}
                          options={formData.options}
                          variants={formData.variants}
                          onChange={handleUnitsChange}
                          onVariantsChange={(newVariants) => {
                            setFormData((prev) => ({
                              ...prev,
                              variants: newVariants,
                            }));
                          }}
                          onSectionFocus={() => handleSectionFocus("product-units")}
                        />
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Product Prices */}
                <div id="product-prices" className="">
                  <Card className="min-w-0 overflow-hidden">
                    <div
                      className="flex cursor-pointer items-center justify-between p-6"
                      onClick={() => handleSectionClick("product-prices")}>
                      <h3 className="text-lg font-medium">
                        {t("pages.products.addManual.sections.prices")}
                      </h3>
                      <ChevronDown
                        className={`size-5 shrink-0 transition-transform duration-300 ease-in-out ${
                          expandedSections.includes("product-prices") ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                    <div
                      className={`min-w-0 overflow-hidden transition-all duration-300 ease-in-out ${
                        expandedSections.includes("product-prices")
                          ? "max-h-full opacity-100"
                          : "max-h-0 opacity-0"
                      }`}>
                      <div className="min-w-0 p-6 pt-0">
                        <ProductPrices
                          variants={formData.variants}
                          options={formData.options}
                          onChange={handlePricesChange}
                          showValidation={showValidation}
                          onValidationChange={setHasPriceErrors}
                          onSectionFocus={() => handleSectionFocus("product-prices")}
                        />
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Measurements */}
                <div id="measurements" className="">
                  <Card>
                    <div
                      className="flex cursor-pointer items-center justify-between p-6"
                      onClick={() => handleSectionClick("measurements")}>
                      <h3 className="text-lg font-medium">
                        {t("pages.products.addManual.sections.measurements")}
                      </h3>
                      <ChevronDown
                        className={`size-5 transition-transform duration-300 ease-in-out ${
                          expandedSections.includes("measurements") ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                    <div
                      className={`overflow-hidden transition-all duration-300 ease-in-out ${
                        expandedSections.includes("measurements")
                          ? "max-h-full opacity-100"
                          : "max-h-0 opacity-0"
                      }`}>
                      <div className="p-6 pt-0">
                        <ProductMeasurements
                          variants={formData.variants}
                          options={formData.options}
                          onChange={handleMeasurementsChange}
                          showValidation={showValidation}
                          onSectionFocus={() => handleSectionFocus("measurements")}
                        />
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
              <div className="h-4"></div>
            </div>

            {/* Right Navigation */}
            <div className="hidden w-48 md:block">
              <div className="sticky top-8">
                <div className="p-4">
                  <h4 className="font-medium">{t("pages.products.addManual.onThisPage")}</h4>
                </div>
                <nav className="space-y-2 px-4">
                  {navigationItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => scrollToSection(item.id)}
                      className={cn(
                        "block w-full py-2 text-left text-sm transition-colors",
                        activeSection === item.id
                          ? "text-foreground font-medium"
                          : "text-muted-foreground hover:text-foreground"
                      )}>
                      {item.title}
                    </button>
                  ))}
                </nav>
              </div>
            </div>
          </div>
        </div>

        {/* Footer - flex-none */}
        <div className="sticky bottom-0 z-50 flex-none border-t bg-card px-6 pb-6 pt-4 shadow-2xl">
          <div className="relative flex">
            {/* Left column - Checkbox */}
            <div className="flex items-center">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="publish"
                  checked={formData.publish}
                  onCheckedChange={(checked: boolean) =>
                    setFormData((prev) => ({ ...prev, publish: checked }))
                  }
                />
                <Label htmlFor="publish">{t("pages.products.addManual.publish")}</Label>
              </div>
            </div>

            {/* Right column - Buttons */}
            <div className="ml-auto flex h-full items-center justify-end">
              <div className="flex gap-2">
                <Button
                  className="inline-flex w-24 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={handleCancel}
                  disabled={addProduct.isPending || (isEditing && updateProduct.isPending)}>
                  {t("pages.products.addManual.buttons.cancel")}
                </Button>
                <Button
                  className="inline-flex w-24 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                  onClick={handleSubmitClick}
                  disabled={addProduct.isPending || (isEditing && updateProduct.isPending)}>
                  {addProduct.isPending || (isEditing && updateProduct.isPending) ? (
                    <Loader2 className="mr-2 size-4 animate-spin" />
                  ) : isEditing ? (
                    t("pages.products.addManual.buttons.edit")
                  ) : (
                    t("pages.products.addManual.buttons.add")
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Cancel Dialog */}
      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("pages.products.addManual.dialogs.leaveTitle")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("pages.products.addManual.dialogs.leaveDesc")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowCancelDialog(false)}>
              {t("pages.products.addManual.buttons.stay")}
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleLeave}>
              {t("pages.products.addManual.buttons.leave")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

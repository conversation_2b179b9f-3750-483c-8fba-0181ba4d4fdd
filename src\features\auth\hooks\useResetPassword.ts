import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authApi } from "@/lib/apis/auth";

import { ResetPasswordFormValues } from "../utils/validators/resetPass";
import { authKeys } from "./keys";

interface ResetPasswordPayload {
  formData: ResetPasswordFormValues;
  session: string;
}

interface UseResetPasswordOptions {
  onSuccess?: () => void;
}

export const useResetPassword = (options: UseResetPasswordOptions = {}) => {
  const { t } = useTranslation();

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    isSuccess: success,
  } = useMutation({
    mutationKey: authKeys.resetPassword(""),
    mutationFn: async ({ formData, session }: ResetPasswordPayload) => {
      try {
        if (!session) {
          throw new Error(t("validation.sessionRequired"));
        }

        if (formData.newPassword !== formData.confirmPassword) {
          throw new Error(t("validation.passwordsDoNotMatch"));
        }

        return authApi.newPassword({
          session: session,
          new_password: formData.newPassword,
        });
      } catch (error) {
        const message = error instanceof Error ? error.message : t("auth.resetPasswordError");
        toast.error(message);
        throw error;
      }
    },
    onSuccess: () => {
      options.onSuccess?.();
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : t(String(error));
      toast.error(message);
    },
  });

  return {
    onSubmit,
    loading,
    error,
    success,
  };
};

import { toast } from "sonner";

import { Label } from "@/components/ui";
import { Button } from "@/components/ui/button";

import {
  useDeleteProductAttributeMapping,
  useUpdateProductAttributeMapping,
} from "./hooks/product-attribute-mapping";
import { Mapping, MappingPayload, Transformation } from "./hooks/types";

export interface MappingField {
  sourceField?: string;
  destinationField: string;
  required?: boolean;
  enabled?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  transformations?: Transformation[];
}

export interface MappingCategory {
  id: string;
  label: string;
  name: string;
  errors: number;
  mapped: number;
  total: number;
  fields: MappingField[];
}

interface ProductMappingHeaderProps {
  channelName: string;
  connectionId?: string;
  onBack?: () => void;
  onSaveChanges?: (data: MappingPayload) => void;
  onDeleteComplete?: () => void;
  categories: MappingCategory[];
}

export function ProductMappingHeader({
  channelName,
  connectionId,
  onBack,
  onSaveChanges,
  onDeleteComplete,
  categories,
}: ProductMappingHeaderProps) {
  const { mutate: deleteMapping, isPending: isDeleting } = useDeleteProductAttributeMapping();
  const { mutateAsync: updateMapping, isPending: isSaving } = useUpdateProductAttributeMapping();

  const handleBackToDefault = () => {
    if (connectionId) {
      deleteMapping(connectionId, {
        onSuccess: () => {
          if (onDeleteComplete) {
            onDeleteComplete();
          }
          if (onBack) {
            onBack();
          }
        },
      });
    } else if (onBack) {
      onBack();
    }
  };

  const handleSaveChanges = async () => {
    if (!categories) {
      toast.error("No mapping data available");
      return;
    }

    // Collect all mappings from categories
    const mappings: Mapping[] = categories.flatMap((category) =>
      category.fields
        .filter((field): field is MappingField & { sourceField: string } =>
          Boolean(field.sourceField)
        ) // Only include fields with source values
        .map((field) => ({
          source_field: field.sourceField,
          destination_field: field.destinationField,
          enabled: field.enabled ?? true,
          error_message: field.hasError ? field.errorMessage : undefined,
          transformations:
            field.transformations?.map((t) => ({
              type: t.type,
              config: t.config || {},
            })) || [],
        }))
    );

    // Validate required fields
    const requiredFields = categories.flatMap((category) =>
      category.fields.filter((field) => field.required && !field.sourceField)
    );

    if (requiredFields.length > 0) {
      // Handle required fields validation
    }

    if (connectionId) {
      const payload: MappingPayload = { mappings };
      try {
        await updateMapping({ connectionId, data: payload });
        if (onSaveChanges) {
          onSaveChanges(payload);
        }
      } catch (error) {
        // Error is already handled in the mutation
      }
    }
  };

  return (
    <div className="flex flex-col items-start justify-between gap-4 pb-4 sm:flex-row sm:items-center">
      <div className="flex items-center gap-4">
        <Label className="text-base">Product Attribute Mappings</Label>
      </div>

      <div className="flex w-full flex-col items-stretch gap-2 sm:w-auto sm:flex-row sm:items-center">
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground"
          onClick={handleBackToDefault}
          loading={isDeleting}>
          Back to default
        </Button>

        <Button variant="default" size="sm" onClick={handleSaveChanges} loading={isSaving}>
          Save changes
        </Button>
      </div>
    </div>
  );
}

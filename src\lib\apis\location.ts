import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

export interface Location {
  id: string;
  name: string;
  description?: string;
  address?: string;
  terminal_count?: number;
  phone?: string;
  open_time?: string;
  images?: { url: string }[];
  created_at?: string;
  updated_at?: string;
  company_id?: string;
}

interface PaginationParams {
  limit?: number;
  page?: number;
  query?: string;
}

export const locationApi = {
  list: async (params?: PaginationParams) => {
    return await privateApi.get<ResponseList<Location>>("/inventory/locations", {
      params,
    });
  },

  getById: async (id: string) => {
    return await privateApi.get<Location>(`/inventory/locations/${id}`);
  },
};

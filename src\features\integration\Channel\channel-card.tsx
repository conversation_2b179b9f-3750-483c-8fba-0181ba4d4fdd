import Image from "next/image";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Skeleton } from "@/components/ui";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Channel } from "@/lib/apis/types/channel";

interface ChannelCardProps {
  channel: Channel;
  onInstall?: (channel: Channel) => void;
  onConfigure?: (channel: Channel) => void;
}

export function ChannelCard({ channel, onInstall, onConfigure }: ChannelCardProps) {
  const { t } = useTranslation();
  const handleAction = () => {
    if (channel.installed) {
      onConfigure?.(channel);
    } else {
      onInstall?.(channel);
    }
  };

  return (
    <Card className="relative overflow-hidden">
      <div className="absolute right-4 top-4">
        {channel.installed && <Badge className="bg-green-500 text-white">Installed</Badge>}
      </div>
      <div className="flex h-full flex-col justify-between p-4">
        <div className="flex flex-col">
          <div className="flex items-center ">
            {channel.logo && channel.logo.trim() && (
              <Image
                src={channel.logo}
                alt={channel.name}
                width={64}
                height={64}
                className="size-16 rounded-2xl object-cover"
              />
            )}
          </div>
          <div className="flex flex-col gap-y-[6px] py-4 ">
            <h3 className="text-start text-lg font-semibold">{channel.name}</h3>
            <p className="text-start text-sm font-normal text-muted-foreground">
              {channel.description}
            </p>
          </div>
        </div>
        <div className=" flex justify-end self-end">
          <Button
            variant={channel.installed ? "outline" : "secondary"}
            size="sm"
            leftIcon={<Plus size={16} />}
            onClick={handleAction}>
            {channel.installed ? t("common.configure") : t("common.install")}
          </Button>
        </div>
      </div>
    </Card>
  );
}

export function ChannelCardSkeleton() {
  return (
    <Card className="flex flex-col space-y-4 p-4">
      <div className="flex items-center gap-4">
        <Skeleton className="size-16 rounded-lg" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      </div>
      <Skeleton className="h-20" />
      <div className="flex justify-end gap-2">
        <Skeleton className="h-9 w-24" />
      </div>
    </Card>
  );
}

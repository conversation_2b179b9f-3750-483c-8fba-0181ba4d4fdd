export const Actions = {
  EMPTY: "",
  LIST: "LIST",
  GET: "GET",
  UPDATE: "UPDATE",
  DELETE: "DELETE",
  CREATE: "CREATE",
  OTHERS: "OTHERS",
};

export const PurchaseOrderActions = {
  EMPTY: "",
  CONFIRM: "confirm",
  PAY: "pay",
  COMPLETE: "complete",
  RETURN: "return",
  IMPORT: "import",
  UPDATE: "update",
  CANCEL: "cancel",
};

export const PurchaseOrderStatuses = {
  DRAFT: "DRAFT",
  PENDING: "PENDING",
  SHIPPING: "SHIPPING",
  RETURNED: "RETURNED",
  COMPLETED: "COMPLETED",
  CANCELLED: "CANCELLED",
};

export const PurchaseOrderStatusOptions = [
  { value: "DRAFT", label: "Draft" },
  { value: "PENDING", label: "Pending" },
  { value: "SHIPPING", label: "Shipping" },
  { value: "RETURNED", label: "Returned" },
  { value: "COMPLETED", label: "Completed" },
  { value: "CANCELLED", label: "Cancelled" },
];

export const OrderActions = {
  EMPTY: "",
  CONFIRM: "confirm",
  CANCEL: "cancel",
  REQUEST_CANCEL: "request_cancel",
  REJECT_CANCEL: "reject_cancel",
  PAY: "pay",
  PACK: "pack",
  READY: "ready",
  SHIP: "ship",
  DELIVERED: "delivered",
  DELIVERED_RETURN: "delivered_return",
};

export const OrderStatusOptions = [
  { value: "DRAFT", label: "Draft" },
  { value: "PENDING", label: "Pending" },
  { value: "AWAIT_PACKING", label: "Await Packing" },
  { value: "PACKING", label: "Packing" },
  { value: "PARTIAL_PACKING", label: "Partial packing" },
  { value: "READY", label: "Ready" },
  { value: "PARTIAL_READY", label: "Partial ready" },
  { value: "SHIPPING", label: "Shipping" },
  { value: "PARTIAL_SHIPPING", label: "Partial shipping" },
  { value: "DELIVERED", label: "Delivered" },
  { value: "PARTIAL_DELIVERED", label: "Partial delivered" },
  { value: "RETURNING", label: "Returning" },
  { value: "PARTIAL_RETURNING", label: "Partial returning" },
  { value: "RETURNED", label: "Returned" },
  { value: "PARTIAL_RETURNED", label: "Partial returned" },
  { value: "COMPLETED", label: "Completed" },
  { value: "PARTIAL_COMPLETED", label: "Partial completed" },
  { value: "IN_CANCEL", label: "In cancel" },
  { value: "CANCELLED", label: "Cancelled" },
  { value: "SYNC_ERROR", label: "Sync error" },
];

export const DefaultOrderSteps = [
  { value: "DRAFT", label: "Draft" },
  { value: "PENDING", label: "Pending" },
  { value: "AWAIT_PACKING", label: "Await Packing" },
  { value: "PACKING", label: "Packing" },
  { value: "READY", label: "Ready" },
  { value: "SHIPPING", label: "Shipping" },
  { value: "DELIVERED", label: "Delivered" },
  { value: "RETURNING", label: "Returning" },
  { value: "RETURNED", label: "Returned" },
  { value: "COMPLETED", label: "Completed" },
];
export const OrderStatuses = {
  DRAFT: "DRAFT",
  PENDING: "PENDING",
  AWAIT_PACKING: "AWAIT_PACKING",
  PACKING: "PACKING",
  PARTIAL_PACKING: "PARTIAL_PACKING",
  READY: "READY",
  PARTIAL_READY: "PARTIAL_READY",
  SHIPPING: "SHIPPING",
  PARTIAL_SHIPPING: "PARTIAL_SHIPPING",
  DELIVERED: "DELIVERED",
  PARTIAL_DELIVERED: "PARTIAL_DELIVERED",
  RETURNING: "RETURNING",
  PARTIAL_RETURNING: "PARTIAL_RETURNING",
  RETURNED: "RETURNED",
  PARTIAL_RETURNED: "PARTIAL_RETURNED",
  COMPLETED: "COMPLETED",
  PARTIAL_COMPLETED: "PARTIAL_COMPLETED",
  CANCELLED: "CANCELLED",
  IN_CANCEL: "IN_CANCEL",
  SYNC_ERROR: "SYNC_ERROR",
};

export const ReturnOrderStatuses = {
  NOT_RECEIVED: "NOT_RECEIVED",
  RECEIVED: "RECEIVED",
  CANCELLED: "CANCELLED",
};

export const ReturnOrderOptions = [
  { value: "NOT_RECEIVED", label: "Not received" },
  { value: "RECEIVED", label: "Received" },
  { value: "CANCELLED", label: "Cancelled" },
];

export const OrderTypes = {
  POS: "POS",
  IMPORTED: "IMPORTED",
  ECOMMERCE: "ECOMMERCE",
};

export const OrderTypeOptions = [
  { value: "POS", label: "POS" },
  { value: "IMPORTED", label: "Imported" },
  { value: "ECOMMERCE", label: "E-Commerce" },
];

export const ReturnOrderReasonStatuses = {
  OTHER: "OTHER",
  DEFECT: "DEFECT",
  DAMAGED: "DAMAGED",
};

export const ReturnOrderReasonOptions = [
  { value: "OTHER", label: "Other reason" },
  { value: "DEFECT", label: "Defect" },
  { value: "DAMAGED", label: "Damaged" },
];

export const PaymentStatuses = {
  UNPAvalue: "UNPAvalue",
  PARTIAL_PAvalue: "PARTIAL_PAvalue",
  COD: "COD",
  PENDING_ESCROW: "PENDING_ESCROW",
  PAvalue: "PAvalue",
  PARTIAL_REFUNDED: "PARTIAL_REFUNDED",
  REFUNDED: "REFUNDED",
  VOvalueED: "VOvalueED",
};

export const PaymentStatusOptions = [
  { value: "UNPAvalue", label: "Unpavalue" },
  { value: "PARTIAL_PAvalue", label: "Partial pavalue" },
  { value: "COD", label: "COD" },
  { value: "PENDING_ESCROW", label: "Pending escrow" },
  { value: "PAvalue", label: "Pavalue" },
  { value: "PARTIAL_REFUNDED", label: "Partial refunded" },
  { value: "REFUNDED", label: "Refunded" },
  { value: "VOvalueED", label: "Vovalueed" },
];

export const DefaultPaymentSteps = [
  { value: "UNPAvalue", label: "Unpavalue" },
  { value: "COD", label: "COD" },
  { value: "PENDING_ESCROW", label: "Pending escrow" },
  { value: "PAvalue", label: "Pavalue" },
  { value: "REFUNDED", label: "Refunded" },
];

export const PackageStatuses = {
  PENDING: "PENDING",
  PACKING: "PACKING",
  READY: "READY",
  SHIPPING: "SHIPPING",
  DELIVERED: "DELIVERED",
  RETURNING: "RETURNING",
  RETURNED: "RETURNED",
  COMPLETED: "COMPLETED",
  CANCELLED: "CANCELLED",
};

export const PackageStatusOptions = [
  { value: "PENDING", label: "Pending" },
  { value: "PACKING", label: "Packing" },
  { value: "READY", label: "Ready" },
  { value: "SHIPPING", label: "Shipping" },
  { value: "DELIVERED", label: "Delivered" },
  { value: "RETURNING", label: "Returning" },
  { value: "RETURNED", label: "Returned" },
  { value: "COMPLETED", label: "Completed" },
  { value: "CANCELLED", label: "Cancelled" },
];

export const DATA_PRINT = {
  store_label: "NoLi Handmade",
  store_address: "Số 41 ngõ 63 Phố Ô Đồng Lầm, đường ven Hồ Ba Mẫu, Phương Liên, Đống Đa, Hà Nội",
  line_variant_code: "VR01",
  line_variant_label: "Áo thun màu tím",
  line_quantity: 1,
  bar_code: "SP00001",
  qr_code: "100000",
  logo_shop: "https://onexapis.s3.amazonaws.com/onexapis-B-16-9-2.png",
};

export const DiscountTypeOptions = [
  { value: "BY_ORDER_TOTAL", label: "Discount by order total" },
  { value: "BY_PRODUCT", label: "Discount by each product" },
  { value: "PRODUCT_BY_ORDER_TOTAL", label: "Discount product by order total" },
  { value: "BY_QUANTITY", label: "Discount by quantity" },
  { value: "BY_PURCHASE_PRODUCT", label: "Discount by purchased product" },
  { value: "GIFT_BY_ORDER_TOTAL", label: "Gift by order total" },
  { value: "GIFT_BY_PURCHASE_PRODUCT", label: "Gift by purchased product" },
];
export const DiscountTypes = {
  BY_ORDER_TOTAL: "BY_ORDER_TOTAL",
  BY_PRODUCT: "BY_PRODUCT",
  PRODUCT_BY_ORDER_TOTAL: "PRODUCT_BY_ORDER_TOTAL",
  BY_QUANTITY: "BY_QUANTITY",
  BY_PURCHASE_PRODUCT: "BY_PURCHASE_PRODUCT",
  GIFT_BY_ORDER_TOTAL: "GIFT_BY_ORDER_TOTAL",
  GIFT_BY_PURCHASE_PRODUCT: "GIFT_BY_PURCHASE_PRODUCT",
};

export const DayOfWeekOptions = [
  { value: 0, label: "Monday" },
  { value: 1, label: "Tuesday" },
  { value: 2, label: "Wednesday" },
  { value: 3, label: "Thursday" },
  { value: 4, label: "Frvalueay" },
  { value: 5, label: "Saturday" },
  { value: 6, label: "Sunday" },
];

export const MonthOptions = [
  { value: 1, label: "January" },
  { value: 2, label: "February" },
  { value: 3, label: "March" },
  { value: 4, label: "April" },
  { value: 5, label: "May" },
  { value: 6, label: "June" },
  { value: 7, label: "July" },
  { value: 8, label: "August" },
  { value: 9, label: "September" },
  { value: 10, label: "October" },
  { value: 11, label: "November" },
  { value: 12, label: "December" },
];

export const EffectiveProductOptions = [
  { value: "PRODUCTS", label: "Products" },
  { value: "VARIANTS", label: "Variants" },
  { value: "CATEGORIES", label: "Categories" },
  { value: "BRANDS", label: "Brands" },
];

export const EffectiveProduct = {
  PRODUCTS: "PRODUCTS",
  VARIANTS: "VARIANTS",
  CATEGORIES: "CATEGORIES",
  BRANDS: "BRANDS",
};

export const DiscountStatuses = {
  DRAFT: "DRAFT",
  RUNNING: "RUNNING",
  PAUSED: "PAUSED",
  CANCELED: "CANCELED",
};

export const DiscountStatusOptions = [
  { value: "DRAFT", label: "Draft" },
  { value: "RUNNING", label: "Running" },
  { value: "PAUSED", label: "Paused" },
  { value: "CANCELED", label: "Canceled" },
];

export const DiscountAmountType = {
  PERCENT: "PERCENT",
  VALUE: "VALUE",
};

export const ImportStatusOptions = [
  { value: "PROCESSING", label: "Processing" },
  { value: "FAILED", label: "Failed" },
  { value: "COMPLETED", label: "Completed" },
];

export const ImportTypeOptions = [
  { value: "product", label: "Product" },
  { value: "order", label: "Order" },
  { value: "stock_adjustment", label: "Stock adjustment" },
  { value: "purchase_order", label: "Purchase order" },
  { value: "customer", label: "Customer" },
  { value: "supplier", label: "Supplier" },
];

export const ImportTypes = {
  product: "Product",
  order: "Order",
  stock_adjustment: "Stock adjustment",
  purchase_order: "Purchase order",
  customer: "Customer",
  supplier: "Supplier",
};

export const RecordResponseTypeOptions = [
  { value: "success", label: "Success" },
  { value: "error", label: "Error" },
  { value: "warning", label: "Warning" },
];

export const RecordTypeOptions = [
  { value: "product", label: "Product" },
  { value: "stock_adjustment", label: "Stock adjustment" },
  { value: "order", label: "Order" },
  { value: "return_order", label: "Return Order" },
  { value: "purchase_order", label: "Purchase order" },
  { value: "customer", label: "Customer" },
  { value: "inventory", label: "Inventory" },
];

export const RecordTypes = {
  product: "Product",
  stock_adjustment: "Stock adjustment",
  order: "Order",
  return_order: "Return Order",
  purchase_order: "Purchase order",
  customer: "Customer",
  inventory: "Inventory",
};

export const EventSourceOption = [
  { value: "webhook", label: "Webhook" },
  { value: "sync_record_api", label: "Sync Record API" },
  { value: "sync_filter_api", label: "Sync Filter API" },
  { value: "scheduler", label: "Scheduler" },
];

export const ActionGroupOptions = [
  { value: "order", label: "Order" },
  { value: "product", label: "Product" },
  { value: "return_order", label: "Return Order" },
  { value: "inventory", label: "Inventory" },
  { value: "image", label: "Image" },
  { value: "webhook", label: "Webhook" },
  { value: "unknown", label: "Unknown" },
];

export const ActionTypeOptions = [
  { value: "get_order", label: "Get Order" },
  { value: "sync_order", label: "Sync Order" },
  { value: "get_product", label: "Get Product" },
  { value: "get_inventory", label: "Get Inventory" },
  { value: "sync_image", label: "Sync Image" },
  { value: "webhook", label: "Webhook" },
  { value: "get_return_order", label: "Get Return Order" },
  { value: "sync_return_order", label: "Sync Return Order" },
  { value: "sync_inventory", label: "Sync Inventory" },
  { value: "sync_product", label: "Sync Product" },
];

export const ActionType = {
  get_order: "Get Order",
  sync_order: "Sync Order",
  get_product: "Get Product",
  sync_product: "Sync Product",
  get_inventory: "Get Inventory",
  sync_image: "Sync Image",
  webhook: "Webhook",
  get_return_order: "Get Return Order",
  sync_return_order: "Sync Return Order",
};

export const RecordResponseCodeOptions = [
  { value: "error_not_found", label: "Error not found" },
  { value: "error_product_not_found", label: "Error product not found" },
  { value: "error_out_of_stock", label: "Error out of stock" },
  { value: "error_sku_existed", label: "Error sku existed" },
  { value: "error_location_not_found", label: "Error location not found" },
  { value: "error_price_invalvalue", label: "Error price invalvalue" },
  { value: "error_purchase_order_not_found", label: "Error purchase order not found" },
  { value: "error_status_invalvalue", label: "Error status invalvalue" },
];

export const ReservedVoucherOptions = [
  { value: "PURCHASE_ORDER_VOUCHER", label: "Purchase order voucher" },
  { value: "ORDER_PAYMENT_VOUCHER", label: "Order payment voucher" },
  { value: "RETURN_ORDER_VOUCHER", label: "Return order voucher" },
];

export const ReservedVoucher = {
  PURCHASE_ORDER_VOUCHER: "PURCHASE_ORDER_VOUCHER",
  ORDER_PAYMENT_VOUCHER: "ORDER_PAYMENT_VOUCHER",
  RETURN_ORDER_VOUCHER: "RETURN_ORDER_VOUCHER",
};

export const TransactionVoucherTypeOptions = [
  { value: "EXPENSE", label: "Expense" },
  { value: "PAYMENT", label: "Payment" },
];

export const PeriodOptions = [
  { value: "week", label: "Week" },
  { value: "month", label: "Month" },
  { value: "year", label: "Year" },
];

export const StockAdjustmentReasonOptions = [
  { value: "OTHER", label: "Other reason" },
  { value: "DEPRECIATION", label: "Depreciation" },
  { value: "DAMAGED", label: "Damaged" },
  { value: "RETURN", label: "Return" },
  { value: "RELOCATE", label: "Relocate" },
  { value: "PRODUCTION", label: "Production" },
];

export const StockAdjustmentReasons = {
  OTHER: "OTHER",
  DEPRECIATION: "DEPRECIATION",
  DAMAGED: "DAMAGED",
  RETURN: "RETURN",
  RELOCATE: "RELOCATE",
  PRODUCTION: "PRODUCTION",
};

export const StockAdjustmentStatuses = {
  AWAIT_CHECKING: "AWAIT_CHECKING",
  BALANCED: "BALANCED",
  DELETED: "DELETED",
};

export const StockAdjustmentStatusOptions = [
  { value: "AWAIT_CHECKING", label: "Await checking" },
  { value: "BALANCED", label: "Balanced" },
  { value: "DELETED", label: "Deleted" },
];

export const StockRelocateStatuses = {
  AWAIT_DELIVERY: "AWAIT_DELIVERY",
  DELIVERING: "DELIVERING",
  RECEIVED: "RECEIVED",
  CANCELLED: "CANCELLED",
};

export const StockRelocateStatusOptions = [
  { value: "AWAIT_DELIVERY", label: "Await delivery" },
  { value: "DELIVERING", label: "Delivering" },
  { value: "RECEIVED", label: "Received" },
  { value: "CANCELLED", label: "Cancelled" },
];

export const AccountTypeOptions = [
  { value: "BANK", label: "Bank" },
  { value: "CASH", label: "Cash" },
  { value: "DIGITAL_WALLET", label: "Digital Wallet" },
];

export const AccountTypes = {
  BANK: "BANK",
  CASH: "CASH",
  DIGITAL_WALLET: "DIGITAL_WALLET",
};

export const TransactionType = {
  PURCHASE: "PURCHASE",
  ADJUSTMENT: "ADJUSTMENT",
  RETURN: "RETURN",
  ORDER: "ORDER",
  RELOCATE: "RELOCATE",
  PACKAGE: "PACKAGE",
};

export const genderOptions = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "non_binary", label: "Non-Binary" }, // Người không thuộc vào hai giới tính nam hoặc nữ
  { value: "genderqueer", label: "Genderqueer" }, // Người có giới tính không theo chuẩn mực xã hội
  { value: "genderfluvalue", label: "Genderfluvalue" }, // Người thay đổi giới tính theo thời gian
  { value: "agender", label: "Agender" }, // Người không có giới tính
  { value: "bigender", label: "Bigender" }, // Người có hai giới tính hoặc thay đổi giữa hai giới tính
  { value: "pangender", label: "Pangender" }, // Người xác định mình với nhiều hoặc tất cả các giới tính
  { value: "two_spirit", label: "Two-Spirit" }, // Thuật ngữ bản địa Bắc Mỹ chỉ người có tinh thần và bản sắc của cả nam và nữ
  { value: "other", label: "Other" }, // Giới tính khác không được liệt kê
  { value: "prefer_not_to_say", label: "Prefer Not to Say" }, // Không muốn tiết lộ giới tính
];

export const UserRoles = {
  Admin: "Admin",
  User: "User",
  Customer: "Customer",
  Guest: "Guest",
};

export const periodOptions = [
  { label: "Today", value: "today" },
  { label: "This week", value: "this_week" },
  { label: "This month", value: "this_month" },
  { label: "This year", value: "this_year" },
  { label: "Customize", value: "custom" },
];

export const LoyalCustomerStatusOptions = [
  { value: "ACTIVATE", label: "Activate" },
  { value: "REMOVED", label: "Removed" },
];

export const PointEventTypeOptions = [
  { value: "REDEEM", label: "Redeem" },
  { value: "PLUS", label: "Plus" },
];

export const PointEventTypes = {
  REDEEM: "Redeem",
  PLUS: "Plus",
};

export const ChannelStatusOptions = [
  { value: "ACTIVE", label: "Active" },
  { value: "INACTIVE", label: "Inactive" },
  { value: "PENDING", label: "Pending" },
];

export const ChannelStatuses = {
  ACTIVE: "ACTIVE",
  INACTIVE: "INACTIVE",
  PENDING: "PENDING",
};

export const LanguageOptions = [
  { value: "vn", label: "Việt Nam" },
  { value: "en", label: "English" },
];

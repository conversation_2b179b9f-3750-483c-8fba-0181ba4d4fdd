import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";

import { useTransformations } from "../hooks/transformation";

interface TransformationDirectionProps {
  type?: string;
}

export function TransformationDirection({ type }: TransformationDirectionProps) {
  const { t } = useTranslation();
  const { transformations } = useTransformations();

  const selectedTransformation = transformations.find((transform) => transform.type === type);

  if (!selectedTransformation || !selectedTransformation.direction) return null;

  const { direction } = selectedTransformation;

  return (
    <div className="z-10 flex flex-col gap-4 rounded-md border border-border bg-muted p-4">
      <div className="flex items-center gap-2">
        <Badge variant="default" className="text-xs font-semibold">
          {selectedTransformation.label}
        </Badge>
        <Badge variant="sematic_default" className="text-xs">
          {direction.value_type}
        </Badge>
      </div>

      <div className="flex flex-col gap-2">
        <h4 className="text-sm font-medium">
          {t("pages.productMapping.advancedMapping.description")}
        </h4>
        <p className="text-sm text-muted-foreground">{selectedTransformation.description}</p>
      </div>

      <div className="flex flex-col gap-2">
        <h4 className="text-sm font-medium">
          {t("pages.productMapping.advancedMapping.exampleUsage")}
        </h4>
        <div className="rounded-md bg-card p-3">
          <code className="text-sm">{direction.example}</code>
        </div>
      </div>
    </div>
  );
}

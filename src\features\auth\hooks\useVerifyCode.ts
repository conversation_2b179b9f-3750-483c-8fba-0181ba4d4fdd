import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authApi } from "@/lib/apis/auth";

import { authKeys } from "./keys";

export const useVerifyCode = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    isSuccess: success,
    reset: resetMutation,
  } = useMutation({
    mutationKey: authKeys.verifyCode(""),
    mutationFn: async ({ username, code }: { username: string; code: string }) => {
      if (!username || !code) {
        throw new Error(t("auth.verificationCodeRequired"));
      }

      return authApi.verifyCode({ username, code });
    },
    onSuccess: () => {
      toast.success(t("auth.verificationCodeSuccess"));
      router.push("/login");
    },
  });

  return {
    onSubmit,
    loading,
    error,
    success,
    reset: resetMutation,
  };
};

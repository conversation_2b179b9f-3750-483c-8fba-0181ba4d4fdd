import { useEffect, useRef } from "react";
import { Alert<PERSON>riangle, <PERSON>R<PERSON>, Info, Settings } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Combobox } from "@/components/ui/combobox";
import { Switch } from "@/components/ui/switch";
import { TableCell, TableRow } from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { MappingField } from "./mocks/mapping-attribute";

interface ProductMappingFieldProps {
  field: MappingField;
  onChange?: (field: MappingField) => void;
  onToggle?: (enabled: boolean) => void;
  onAdvancedMapping?: (field: MappingField) => void;
  attributeData?: any;
  attributeDetail?: any;
}

const fieldOptions = [
  { id: "title", name: "Title" },
  { id: "description", name: "Description" },
  { id: "price", name: "Price" },
];

export function ProductMappingField({
  field,
  onChange,
  onToggle,
  onAdvancedMapping,
  useTableRow = false,
  attributeData,
  attributeDetail,
}: ProductMappingFieldProps & {
  useTableRow?: boolean;
  attributeData?: any;
  attributeDetail?: any;
}) {
  const {
    sourceField,
    destinationField,
    required = false,
    hasError = false,
    errorMessage = "",
    optional = false,
    enabled = true,
    hasAdvancedMapping = false,
    transformations = [],
    transformationCount = 0,
  } = field;

  // Count valid transformations with type
  const validTransformationsCount =
    transformationCount || transformations?.filter((t) => t.type).length || 0;

  const initialMappingApplied = useRef(false);
  const isRequiredAndEmpty = required && !sourceField;

  // Check if there's a mapping for this field in attributeDetail
  useEffect(() => {
    if (attributeDetail?.mappings && !initialMappingApplied.current) {
      const mapping = attributeDetail.mappings.find(
        (m: any) => m.destination_field.toLowerCase() === destinationField?.toLowerCase()
      );

      if (mapping) {
        // Only update if the values are different
        const needsUpdate =
          mapping.source_field !== sourceField ||
          mapping.enabled !== enabled ||
          !!mapping.error_message !== hasError ||
          mapping.error_message !== errorMessage;

        if (needsUpdate && onChange) {
          onChange({
            ...field,
            sourceField: mapping.source_field,
            enabled: mapping.enabled && !!mapping.source_field, // Only enable if there's a source field
            hasError: !!mapping.error_message,
            errorMessage: mapping.error_message || "",
          });
        }
        initialMappingApplied.current = true;
      }
    }
  }, [
    attributeDetail,
    destinationField,
    field,
    onChange,
    sourceField,
    enabled,
    hasError,
    errorMessage,
  ]);

  const handleToggle = (checked: boolean) => {
    if (onToggle) {
      // Only allow enabling if there's a source field
      const newEnabled = checked && !!sourceField;
      onToggle(newEnabled);

      // If trying to enable without a source field, update the field to reflect disabled state
      if (checked && !sourceField && onChange) {
        onChange({
          ...field,
          enabled: false,
        });
      }
    }
  };

  const handleFieldChange = (value: string) => {
    if (onChange) {
      // If clearing the source field, also disable the switch
      const newEnabled = value ? enabled : false;

      onChange({
        ...field,
        sourceField: value,
        enabled: newEnabled,
      });

      // If disabling due to empty value, also trigger the toggle callback
      if (!value && onToggle) {
        onToggle(false);
      }
    }
  };

  const handleClearField = () => {
    if (onChange) {
      onChange({
        ...field,
        sourceField: "",
        enabled: false,
      });

      if (onToggle) {
        onToggle(false);
      }
    }
  };

  // Get available options, including current value if it's not in the options
  const getComboboxItems = () => {
    // Extract destination attributes from attributeData
    const sourceOptions = attributeData?.source_attributes
      ? Object.entries(attributeData.source_attributes).map(([key, attr]: [string, any]) => ({
          id: key,
          name: attr.label || key,
          description: attr.description || "",
        }))
      : fieldOptions;

    const normalizedSource = sourceField?.toLowerCase();
    const isSourceInOptions = sourceOptions.some(
      (option) => option.id.toLowerCase() === normalizedSource
    );

    if (!sourceField || isSourceInOptions) {
      return sourceOptions;
    }

    return [{ id: sourceField, name: sourceField }, ...sourceOptions];
  };

  const getDestinationField = () => {
    if (!attributeData?.destination_attributes) {
      return { label: destinationField, description: "", isRequired: required };
    }

    // Try to find destination field in attributeData (case insensitive)
    const destFieldKey = Object.keys(attributeData.destination_attributes).find(
      (key) => key.toLowerCase() === destinationField?.toLowerCase()
    );

    if (destFieldKey) {
      const destField = attributeData.destination_attributes[destFieldKey];
      return {
        label: destField.label || destFieldKey,
        description: destField.description || "",
        isRequired: destField.is_required || required,
        type: destField.type || "",
      };
    }

    // Fallback to original field if not found
    return { label: destinationField, description: "", isRequired: required };
  };

  const destinationFieldInfo = getDestinationField();

  const handleAdvancedMappingClick = () => {
    if (onAdvancedMapping) {
      onAdvancedMapping(field);
    }
  };

  const comboboxProps = {
    value: sourceField,
    onValueChange: handleFieldChange,
    onClear: handleClearField,
    items: getComboboxItems(),
    placeholder: "Select a field",
    searchPlaceholder: "Search fields...",
    emptyText: "No fields found.",
    rightIcon: hasAdvancedMapping ? Settings : undefined,
    rightIconNumber: validTransformationsCount > 0 ? validTransformationsCount : undefined,
    onRightIconClick: hasAdvancedMapping ? handleAdvancedMappingClick : undefined,
    className: hasError ? "border-red-500" : "",
    ...(hasAdvancedMapping && {
      footerItem: {
        id: "advanced-mapping",
        name: "Advanced mapping",
        icon: Settings,
        onClick: handleAdvancedMappingClick,
        number: validTransformationsCount > 0 ? validTransformationsCount : undefined,
      },
    }),
  };

  if (useTableRow) {
    return (
      <>
        <TableRow>
          <TableCell>
            <div className="space-y-1">
              <Combobox {...comboboxProps} />
              {hasError && (
                <div className="flex items-center gap-1 text-xs text-red-500">
                  <span>
                    {errorMessage || "The destination field is incompatible with the source field"}
                  </span>
                </div>
              )}
            </div>
          </TableCell>

          <TableCell className="text-center">
            {hasError ? (
              <AlertTriangle className="mx-auto size-4 text-red-500" />
            ) : (
              <ArrowRight className="mx-auto size-4 text-muted-foreground" />
            )}
          </TableCell>

          <TableCell>
            <div className="flex items-center gap-2">
              <span className="font-medium">{destinationFieldInfo.label}</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="size-4 cursor-pointer text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[300px]">
                    <p className="mb-1 text-sm">{destinationFieldInfo.description}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </TableCell>

          <TableCell className="text-center">
            {destinationFieldInfo.isRequired ? (
              <Badge variant="default">Required</Badge>
            ) : optional ? (
              <Badge variant="secondary">Optional</Badge>
            ) : null}
          </TableCell>

          <TableCell className="text-center">
            <Switch
              checked={enabled && !!sourceField}
              onCheckedChange={handleToggle}
              disabled={!sourceField}
            />
          </TableCell>
        </TableRow>
      </>
    );
  }

  return (
    <div className="w-full py-4">
      <div className="grid grid-cols-[1fr_30px_1fr_100px_80px] items-center gap-4">
        <div>
          <div className="space-y-1">
            <Combobox {...comboboxProps} />
            {hasError && (
              <div className="flex items-center gap-1 text-xs text-red-500">
                <span>
                  {errorMessage || "The destination field is incompatible with the source field"}
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-center">
          {hasError ? (
            <AlertTriangle className="size-4 text-red-500" />
          ) : (
            <ArrowRight className="size-4 text-muted-foreground" />
          )}
        </div>

        <div className="flex items-center gap-2">
          <span className="font-medium">{destinationFieldInfo.label}</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="size-4 cursor-pointer text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-[300px]">
                <p className="mb-1 text-sm">{destinationFieldInfo.description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>

        <div className="flex justify-center">
          {destinationFieldInfo.isRequired ? (
            <Badge variant="blue">Required</Badge>
          ) : optional ? (
            <Badge variant="secondary">Optional</Badge>
          ) : null}
        </div>

        <div className="flex justify-center">
          <Switch
            checked={enabled && !!sourceField}
            onCheckedChange={handleToggle}
            disabled={!sourceField}
          />
        </div>
      </div>
    </div>
  );
}

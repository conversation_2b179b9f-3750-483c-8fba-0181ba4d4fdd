import { Skeleton } from "@/components/ui/skeleton";

import { CustomerDetailSkeleton } from "./customer_detail_skeleton";

export function OrderDetailSkeleton() {
  return (
    <div className="flex min-h-screen w-full flex-col">
      <div className="min-h-screen">
        <div className="flex flex-col p-4">
          <div className="flex-1">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {/* Left Column - Order Items and Payment */}
              <div className="space-y-4 md:col-span-2">
                {/* Order Items Section */}
                <div className="rounded-lg border bg-card p-4">
                  <div className="space-y-4">
                    {/* Order Header */}
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-6 w-24" />
                      <Skeleton className="h-6 w-24" />
                    </div>

                    {/* Order Items */}
                    {[...Array(3)].map((_, index) => (
                      <div key={index} className="flex gap-4 border-t pt-4">
                        <Skeleton className="size-16 rounded-md" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-4 w-1/4" />
                          <div className="flex items-center justify-between">
                            <Skeleton className="h-4 w-20" />
                            <Skeleton className="h-4 w-24" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Payment Section */}
                <div className="space-y-4 rounded-lg border bg-card p-4">
                  <div className="space-y-2">
                    {/* Payment Summary */}
                    {[...Array(4)].map((_, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    ))}
                  </div>
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-5 w-24" />
                      <Skeleton className="h-5 w-24" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Customer and Note */}
              <div className="order-first space-y-4 md:order-last">
                {/* Customer Information */}
                <div className="rounded-lg border bg-card p-4">
                  <CustomerDetailSkeleton />
                </div>

                {/* Note Section */}
                <div className="space-y-4 rounded-lg border bg-card p-4">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-24 w-full" />
                  <div className="space-y-2">
                    {[...Array(3)].map((_, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-32" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="sticky bottom-0 w-full border-t border-border bg-card p-4 pb-[24px]">
        <div className="flex flex-wrap justify-between gap-2">
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
      </div>
    </div>
  );
}

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import { useChannels } from "@/features/integration/hooks/use-channel";

import { Channel } from "@/lib/apis/types/channel";

interface UseSupportedChannelsReturn {
  channels: Channel[] | undefined;
  filteredChannels: Channel[] | undefined;
  selectedTypes: string[];
  searchQuery: string;
  isLoading: boolean;
  handleTypeChange: (types: string[]) => void;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleInstall: (channel: Channel) => void;
  handleConfigure: (channel: Channel) => void;
}

export function useSupportedChannels(): UseSupportedChannelsReturn {
  const searchParams = useSearchParams();
  const { data: channels, isLoading } = useChannels({ enabled: true });
  const router = useRouter();
  // Get initial values from URL or defaults
  const initialTypes = searchParams.get("types")?.split(",") || ["ALL"];
  const initialQuery = searchParams.get("query") || "";

  const [selectedTypes, setSelectedTypes] = useState<string[]>(initialTypes);
  const [searchQuery, setSearchQuery] = useState(initialQuery);

  const filteredChannels = useMemo(() => {
    return channels?.filter((channel) => {
      const matchesType =
        selectedTypes.includes("ALL") || selectedTypes.includes(channel.channel_type);
      const matchesSearch = channel.name.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesType && matchesSearch;
    });
  }, [channels, selectedTypes, searchQuery]);

  const createQueryString = useCallback(
    (types: string[], query: string) => {
      const params = new URLSearchParams(searchParams);
      if (types.length > 0) {
        params.set("types", types.join(","));
      } else {
        params.delete("types");
      }

      if (query) {
        params.set("query", query);
      } else {
        params.delete("query");
      }

      return params.toString();
    },
    [searchParams]
  );

  useEffect(() => {
    const queryString = createQueryString(selectedTypes, searchQuery);
    window.history.replaceState({}, "", queryString ? `?${queryString}` : window.location.pathname);
  }, [searchQuery, selectedTypes, createQueryString]);

  const handleTypeChange = (types: string[]) => {
    const lastToggled = types[types.length - 1];

    let newTypes: string[];
    if (lastToggled === "ALL") {
      newTypes = ["ALL"];
    } else if (types.includes("ALL")) {
      newTypes = types.filter((t) => t !== "ALL");
    } else {
      newTypes = types.length === 0 ? ["ALL"] : types;
    }

    setSelectedTypes(newTypes);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleInstall = (channel: Channel) => {
    router.push(`/channels/${channel.key}/install`);
  };

  const handleConfigure = (channel: Channel) => {
    // TODO: Implement configuration logic
    console.log("Configuring channel:", channel.key);
  };

  return {
    channels,
    filteredChannels,
    selectedTypes,
    searchQuery,
    isLoading,
    handleTypeChange,
    handleSearchChange,
    handleInstall,
    handleConfigure,
  };
}

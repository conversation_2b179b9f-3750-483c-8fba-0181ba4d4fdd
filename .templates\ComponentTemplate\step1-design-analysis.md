# Step 1: Design Analysis & Documentation

Before starting any implementation:

1. Design Source Verification:
   - Check `/designs` directory for component-specific design files
   - Verify design file matches component name
   - Load and analyze all design states and variations
   - Document all UI elements present in the design
   - If design files are missing or unclear, ask user for clarification
   - DO NOT proceed with assumptions about design

2. Design Documentation Setup:
   - Create ComponentName.md in component directory
   - Document exact source of design specifications
   - Note any design decisions or clarifications from user
   - Reference specific design files used
   - Create comprehensive UI element inventory:
      A. Core Elements
        • Primary content (text, images, media)
        • Interactive elements (buttons, links, inputs)
        • Navigation components
        • Status indicators
        • Icons and visual assets
     
     B. Form Elements (if applicable)
        • Input fields
        • Labels and placeholders
        • Validation indicators
        • Helper text
        • Error messages
        • Submit/action buttons
     
     C. State Indicators
        • Loading states
        • Empty states
        • Error states
        • Success states
        • Disabled states
        • Active/selected states
     
     D. Layout Components
        • Containers and wrappers
        • Grid/flex structures
        • Spacing elements
        • Dividers
        • Background elements
     
     E. Dynamic Content
        • Tooltips
        • Popovers
        • Modals/dialogs
        • Dropdown menus
        • Expandable sections
     
     F. Accessibility Elements
        • ARIA labels
        • Screen reader text
        • Focus indicators
        • Skip links
        • Keyboard navigation elements

3. Visual Measurements Documentation:
   - Create a detailed spacing map with exact pixels/rems
   - Document all component dimensions
   - Note padding and margin values
   - Record border radiuses and strokes
   - Document shadow values and effects
   - Create responsive breakpoint specifications
   - Document exact positioning of:
     • Text elements and labels
     • Input fields
     • Icons and decorative elements
     • Buttons and controls
     • Error messages
     • Helper text

4. Text Content Analysis:
   - Document all text elements:
     • Headers and titles
     • Labels and placeholders
     • Helper text and descriptions
     • Error messages
     • Button text
     • Links and actions
   - Note text variations:
     • Case (uppercase, lowercase, title case)
     • Truncation behavior
     • Text wrapping rules
     • Multilingual considerations

5. Input Field Specifications:
   - Document for each input:
     • Label positioning and styling
     • Placeholder text and styling
     • Input borders and backgrounds
     • Focus states
     • Error states
     • Helper text positioning
     • Required field indicators
     • Input restrictions/patterns
     • Validation behavior

6. Color & Typography Specification:
   - Document exact color values for all states
   - List all typography styles (size, weight, family, line-height)
   - Note text alignment and spacing
   - Document hover and active state colors
   - Create theme variation color mapping
   - Document for each text element:
     • Font family
     • Font size
     • Font weight
     • Line height
     • Letter spacing
     • Text color
     • Text decoration

7. Interactive Elements:
   - Document for each button/control:
     • Default state
     • Hover state
     • Active state
     • Disabled state
     • Loading state
     • Focus state
     • Text content
     • Icon placement
     • Dimensions
     • Spacing

8. Accessibility Requirements:
   - Document ARIA labels
   - Focus order
   - Required field indicators
   - Error message associations
   - Color contrast requirements
   - Screen reader text
   - Keyboard navigation patterns

9. Responsive Behavior:
   - Document layout changes at each breakpoint
   - Element reflow patterns
   - Typography scaling
   - Spacing adjustments
   - Touch target sizes
   - Mobile-specific interactions

✓ Output: Complete ComponentName.md with exact specifications for every UI element

Completion Criteria:
✓ Design source verified and documented
✓ All measurements documented exactly as in design
✓ All colors and typography specified from design
✓ Component structure diagrammed based on design
✓ All states and variants listed from design
✓ Visual checklist created with design references
✓ Any unclear aspects clarified with user 
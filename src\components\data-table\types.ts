export interface FilterTableProps {
  showSearch?: boolean;
  filterType?: string;
  initialValues?: Record<string, unknown>;
  searchPlaceHolder?: string;
  listFilter?: FilterType[];
  isOtherFilters?: boolean;
  isExportable?: boolean;
  isClear?: boolean;
  handleParamSearch?: (params: Record<string, unknown>) => void;
  listLoading?: boolean;
}
export enum EFilterType {
  SELECT_BOX = "selectBox",
  DATE = "date",
  SINGLE = "single",
}
export interface FilterType {
  id: string;
  type: EFilterType;
  title: string;
  dataOption?: FilterOption[];
  remote?: boolean;
  pathUrlLoad?: string;
  defaultValue?: string | number;
  isChannelFetch?: boolean;
}
export interface FilterOption {
  label: string;
  value: string | number;
  count?: number;
  noFilter?: boolean;
}

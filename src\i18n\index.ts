import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import en from "@/i18n/locales/en.json";
import vi from "@/i18n/locales/vi.json";

// Danh sách ngôn ngữ được hỗ trợ
const SUPPORTED_LANGUAGES = ["en", "vi"];

const getInitialLanguage = () => {
  // Luôn trả về ngôn ngữ mặc định cho SSR
  if (typeof window === "undefined") {
    return "en";
  }

  try {
    // Kiểm tra ngôn ngữ đã lưu trong localStorage
    const savedLanguage = localStorage.getItem("i18nextLng");
    if (savedLanguage && SUPPORTED_LANGUAGES.includes(savedLanguage)) {
      return savedLanguage;
    }

    // Lấy ngôn ngữ trình duyệt
    const browserLanguage = navigator.language.split("-")[0];
    if (SUPPORTED_LANGUAGES.includes(browserLanguage)) {
      return browserLanguage;
    }
  } catch (error) {
    console.warn("Error getting language preferences:", error);
    return "en";
  }

  return "en";
};

i18n.use(initReactI18next).init({
  resources: {
    en: { translation: en },
    vi: { translation: vi },
  },
  lng: "en", // Luôn bắt đầu với tiếng anh cho SSR
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
});

// Chỉ cập nhật ngôn ngữ ở phía client
if (typeof window !== "undefined") {
  const initialLanguage = getInitialLanguage();
  if (initialLanguage !== i18n.language) {
    i18n.changeLanguage(initialLanguage);
  }

  i18n.on("languageChanged", (lng) => {
    try {
      localStorage.setItem("i18nextLng", lng);
    } catch (error) {
      console.warn("Error saving language preference:", error);
    }
  });
}

export default i18n;

import { InfiniteData, useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { knowledgeKeys } from "@/features/bots/knowledge/hooks/keys";
import { Knowledge } from "@/features/bots/knowledge/types";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { knowledgeApi } from "@/lib/apis/knowledge";

export function useKnowledge(options: any = {}) {
  const { t } = useTranslation();
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();

  const { data, isLoading, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage, refetch } =
    useInfiniteQuery({
      queryKey: knowledgeKeys.list({ limit, ...restOptions }),
      queryFn: async ({ pageParam = 0 }) => {
        const res = await knowledgeApi.list({
          page: pageParam as number,
          limit,
          ["sort_updated_at"]: SortDirection.DESC,
          ...restOptions,
        });
        return res;
      },
      getNextPageParam: (lastPage) => {
        const totalPages = Math.floor(lastPage.total / Number(lastPage.limit));
        if (lastPage.page < totalPages) {
          return Number(lastPage.page) + 1;
        }
        return undefined;
      },
      initialPageParam: 0,
      enabled,
    });

  const knowledge = data?.pages.flatMap((page) => page.items) ?? [];
  const total = data?.pages[0]?.total ?? 0;

  const useDeleteKnowledgeMutation = useMutation({
    mutationFn: async (id: string) => {
      await knowledgeApi.delete(id);
      return id;
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<any>>(
        knowledgeKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item: Knowledge) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success(t("common.deleteSuccess"));
    },
    onError: () => {
      toast.error(t("common.deleteError"));
    },
  });

  const useDeleteListKnowledgeMutation = useMutation({
    mutationFn: async (objectData: any) => {
      await knowledgeApi.delete(objectData.listId);
      return objectData;
    },
    onSuccess: (_, objectDeletedId) => {
      queryClient.setQueryData<InfiniteData<any>>(
        knowledgeKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter(
              (item: Knowledge) => !objectDeletedId.listId.includes(item.id)
            ),
            total: page.total - objectDeletedId.listId.length,
          }));
          objectDeletedId?.handleRestRows?.();
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success(t("common.deleteSuccess"));
    },
    onError: () => {
      toast.error(t("common.deleteError"));
    },
  });

  return {
    data,
    knowledge,
    total,
    isLoading,
    refetch,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    useDeleteKnowledgeMutation,
    useDeleteListKnowledgeMutation,
  };
}

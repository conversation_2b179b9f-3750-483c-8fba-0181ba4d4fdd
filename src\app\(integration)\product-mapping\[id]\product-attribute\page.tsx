"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";

import { AdvancedMappingDialog } from "@/features/integration/Product-Mapping/AdvancedMapping/advanced-mapping-dialog";
import { useProductDetail } from "@/features/integration/Product-Mapping/hooks/product-mapping";
import {
  useProductAttributeMapping,
  useProductAttributeMappingDetail,
} from "@/features/integration/Product-Mapping/ProductAttributeMapping/hooks/product-attribute-mapping";
import {
  MappingAttribute,
  Transformation,
} from "@/features/integration/Product-Mapping/ProductAttributeMapping/hooks/types";
import {
  MappingCategory,
  MappingField,
  mockCategories,
} from "@/features/integration/Product-Mapping/ProductAttributeMapping/mocks/mapping-attribute";
import { ProductMappingHeader } from "@/features/integration/Product-Mapping/ProductAttributeMapping/product-mapping-header";
import { ProductMappingList } from "@/features/integration/Product-Mapping/ProductAttributeMapping/product-mapping-list";
import { mockChannels } from "@/features/integration/Product-Mapping/ProductMappingList/mocks/channels";
import { mockMappingProducts } from "@/features/integration/Product-Mapping/ProductMappingList/mocks/mapping-products";

import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function ProductAttributeMappingPage() {
  const params = useParams();
  const router = useRouter();
  const channelId = params.id as string;
  const [categories, setCategories] = useState<MappingCategory[]>(mockCategories);
  const [showAdvancedMapping, setShowAdvancedMapping] = useState(false);
  const [selectedField, setSelectedField] = useState<{
    categoryId: string;
    fieldIndex: number;
    field: MappingField;
  } | null>(null);
  const [sourceOptions, setSourceOptions] = useState<Array<MappingAttribute>>();
  const [activeCategory, setActiveCategory] = useState<string>("");

  // Extract the sync_record_id and connection_id from URL parameters
  const syncRecordId = decodeURIComponent(channelId); // Using channelId as sync_record_id and decoding it
  const searchParams = useSearchParams();
  const connectionId = searchParams.get("connection_id");

  // Fetch product detail data
  const {
    productDetail,
    isLoading: isProductLoading,
    isError: isProductError,
    refetch: refetchProduct,
  } = useProductDetail(syncRecordId);

  // Fetch attribute mapping data
  const {
    data: attributeResponse,
    isLoading: isAttributeLoading,
    isError: isAttributeError,
    refetch: refetchAttribute,
    mutate: mutateAttribute,
  } = useProductAttributeMapping(connectionId || undefined, syncRecordId);

  // Determine overall loading state
  const isLoading = isProductLoading || isAttributeLoading;

  const {
    data: attributeDetail,
    isLoading: isAttributeDetailLoading,
    isError: isAttributeDetailError,
    refetch: refetchAttributeDetail,
  } = useProductAttributeMappingDetail(connectionId || undefined);

  // Extract standard_source_data and source fields from the product detail
  const sourceData = attributeResponse?.master_data;

  // Extract fields directly from standard_source_data based on the API response structure
  const sourceFields = sourceData
    ? Object.entries(sourceData)
        .filter(([key]) => key !== "variants")
        .map(([key]) => key)
    : [];

  // Store the full field data for reference
  const sourceFieldsData = sourceData
    ? Object.entries(sourceData)
        .filter(([key]) => key !== "variants")
        .map(([key, value]) => ({
          field: key,
          value: value,
          displayValue: typeof value === "object" ? JSON.stringify(value) : String(value),
        }))
    : [];

  // Generate mapping fields from destination attributes
  const generateMappingFields = useCallback(() => {
    // Type assertion for attributeResponse - it has destination_attributes based on console log
    const attrData = attributeResponse as unknown as {
      destination_attributes: Record<
        string,
        {
          label: string;
          description: string;
          is_required: boolean;
          type: string;
        }
      >;
    };

    if (!attrData || !attrData.destination_attributes) {
      return mockCategories; // Fallback to mock data if no destination attributes
    }

    const destAttributes = attrData.destination_attributes;

    // Get existing mappings from attributeDetail
    const existingMappings = attributeDetail?.mappings || [];

    // Create a map of existing mappings for quick lookup
    const existingMappingsMap = new Map(
      existingMappings.map((m: any) => [m.destination_field.toLowerCase(), m])
    );

    const fieldsList = Object.entries(destAttributes).map(([key, attr]) => {
      // Find existing mapping for this field
      const existingMapping = existingMappingsMap.get(key.toLowerCase());

      // For name field, set the source field and value from master_data
      const isNameField = key.toLowerCase() === "name";
      const sourceFieldValue = isNameField
        ? sourceData?.name || ""
        : existingMapping?.source_field || "";

      return {
        sourceField: sourceFieldValue,
        destinationField: key,
        required: attr.is_required === true,
        optional: attr.is_required === false,
        enabled: existingMapping?.enabled ?? true,
        hasAdvancedMapping: true,
        hasError: !!existingMapping?.error_message,
        errorMessage: existingMapping?.error_message || "",
        value: isNameField ? sourceData?.name : existingMapping?.value || "",
        transformations: existingMapping?.transformations || [],
      };
    });

    // For now put all fields in the product attributes category
    const newCategories: MappingCategory[] = [
      {
        id: "product",
        name: "Product attributes",
        label: "Product attributes",
        errors: fieldsList.filter((f) => f.hasError).length,
        mapped: fieldsList.filter((f) => f.sourceField).length,
        total: fieldsList.length,
        fields: fieldsList,
      },
      {
        id: "category",
        name: "Category attributes",
        label: "Category attributes",
        errors: 0,
        mapped: 0,
        total: 0,
        fields: [],
      },
      {
        id: "pricing",
        name: "Pricing attributes",
        label: "Pricing attributes",
        errors: 0,
        mapped: 0,
        total: 0,
        fields: [],
      },
    ];

    // Return all categories, even empty ones
    return newCategories;
  }, [attributeResponse, attributeDetail]);

  useEffect(() => {
    // Fetch product details when component mounts
    if (syncRecordId) {
      refetchProduct();
    }
  }, [syncRecordId, refetchProduct]);

  // Process attribute data when it loads
  useEffect(() => {
    if (connectionId && syncRecordId) {
      mutateAttribute();
    }
  }, [connectionId, syncRecordId, mutateAttribute]);

  useEffect(() => {
    if (attributeResponse) {
      console.log("Setting up categories from destination attributes");
      try {
        const generatedCategories = generateMappingFields();
        setCategories(generatedCategories);
        // Set initial active category if not set
        if (!activeCategory && generatedCategories.length > 0) {
          setActiveCategory(generatedCategories[0].id);
        }
      } catch (error) {
        console.error("Error generating mapping fields:", error);
      }
    }
  }, [attributeResponse, generateMappingFields, activeCategory]);

  // Handle tab change
  const handleTabChange = useCallback(
    (categoryId: string) => {
      setActiveCategory(categoryId);
      // Refetch all data when tab changes
      refetchProduct();
      refetchAttribute();
      refetchAttributeDetail();
    },
    [refetchProduct, refetchAttribute, refetchAttributeDetail]
  );

  // Find channel from mock data
  const channel = mockChannels.find((c) => c.key === channelId) || {
    name: "Unknown Channel",
    key: channelId,
  };

  const handleFieldChange = (categoryId: string, fieldIndex: number, updatedField: any) => {
    setCategories((prevCategories) =>
      prevCategories.map((category) => {
        if (category.id === categoryId) {
          const updatedFields = [...category.fields];
          updatedFields[fieldIndex] = {
            ...updatedFields[fieldIndex],
            ...updatedField,
          };
          return { ...category, fields: updatedFields };
        }
        return category;
      })
    );
  };

  const handleFieldToggle = (categoryId: string, fieldIndex: number, enabled: boolean) => {
    handleFieldChange(categoryId, fieldIndex, { enabled });
  };

  const handleAdvancedMapping = useCallback(
    (categoryId: string, fieldIndex: number, field: MappingField) => {
      setSelectedField({ categoryId, fieldIndex, field });
      setShowAdvancedMapping(true);
    },
    []
  );

  const handleAdvancedMappingClose = useCallback(() => {
    setShowAdvancedMapping(false);
    setSelectedField(null);
  }, []);

  const handleAdvancedMappingSave = useCallback(
    (mappingData: {
      sourceField: string;
      value: string;
      transformations?: Array<{
        type: string;
        config: Record<string, any>;
      }>;
      transformationCount?: number;
    }) => {
      if (selectedField) {
        const { categoryId, fieldIndex, field } = selectedField;
        const transformations: Transformation[] =
          mappingData.transformations?.map((t) => ({
            type: t.type,
            config: t.config || {},
          })) || [];

        // Check if there are any valid transformations with types
        const hasValidTransformations = transformations.some((t) => t.type);
        const transformationCount = mappingData.transformationCount || 0;

        handleFieldChange(categoryId, fieldIndex, {
          ...field,
          sourceField: mappingData.sourceField,
          value: mappingData.value,
          hasAdvancedMapping: true, // Always keep the advanced mapping button
          transformations,
          transformationCount,
        });
      }
      handleAdvancedMappingClose();
    },
    [selectedField, handleFieldChange, handleAdvancedMappingClose]
  );

  const handleBackToDefault = () => {
    // Reset to default mappings
    const generatedCategories = generateMappingFields();
    setCategories(generatedCategories);
  };

  // Handle deletion completion
  const handleDeleteComplete = useCallback(() => {
    // Refetch all data after deletion
    refetchProduct();
    refetchAttribute();
    refetchAttributeDetail();
  }, [refetchProduct, refetchAttribute, refetchAttributeDetail]);

  const handleSettings = () => {};

  return (
    <div className="mx-auto p-6">
      {/* Header Section */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <ProductMappingHeader
            channelName={channel.name}
            connectionId={connectionId || undefined}
            onBack={handleBackToDefault}
            onDeleteComplete={handleDeleteComplete}
            categories={categories}
          />
          {/* Mapping Section */}
          {isLoading ? (
            <div className="my-4">
              <Skeleton className="h-96 w-full" />
            </div>
          ) : (
            <ProductMappingList
              categories={categories}
              onFieldChange={handleFieldChange}
              onFieldToggle={handleFieldToggle}
              onAdvancedMapping={handleAdvancedMapping}
              onTabChange={handleTabChange}
              attributeData={attributeResponse}
              attributeDetail={attributeDetail}
            />
          )}
          {showAdvancedMapping && selectedField && (
            <AdvancedMappingDialog
              open={showAdvancedMapping}
              onOpenChange={handleAdvancedMappingClose}
              product={sourceData || mockMappingProducts[0].source_product}
              syncRecordId={syncRecordId}
              connectionId={connectionId || undefined}
              sourceData={sourceData}
              sourceFields={sourceFields}
              selectedField={{
                sourceField: selectedField.field.sourceField,
                destinationField: selectedField.field.destinationField,
                transformations:
                  selectedField.field.transformations?.map((t) => ({
                    type: t.type || "",
                    config: t.config || {},
                  })) || [],
              }}
              onSave={handleAdvancedMappingSave}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}

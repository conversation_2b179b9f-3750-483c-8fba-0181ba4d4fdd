import { useQuery } from "@tanstack/react-query";

import { ColorSettings } from "@/features/settings/theme-settings/color-settings/types";

import { ENDPOINTS } from "@/constants/endpoints";
import { LogoImageData } from "@/lib/apis/setting";

import { privateApi } from "../api_helper";

type SettingValueType = ColorSettings | LogoImageData | Record<string, any>;

interface SettingValue {
  setting_name: string;
  company_id: string;
  updated_at: string;
  created_at: string;
  setting_value: SettingValueType;
  id: string;
  user: {
    name_staff: string;
    id: string;
  };
}

export interface Version {
  id: string;
  user: {
    name_staff: string;
    id: string;
  } | null;
  data: {
    list: {
      print_template: Array<{
        template: string;
        company_id: string;
        updated_at: string;
        size: string;
        created_at: string;
        template_type: string;
        id: string;
        user: {
          name_staff: string;
          id: string;
        };
        location_id: string;
      }>;
      location: Array<{
        country: string;
        city: string;
        created_at: string;
        open_time: string;
        description: string;
        ward: string | null;
        province_code: string;
        default_shipping: boolean;
        province: string;
        updated_at: string;
        meta_data: any | null;
        id: string;
        first_name: string | null;
        zip: string | null;
        images: any[];
        company_id: string;
        address2: string;
        address1: string;
        last_name: string | null;
        country_code: string;
        phone: string;
        district: string | null;
        name: string;
        default_billing: boolean;
        user: any | null;
      }>;
    };
    dict: {
      [key: string]: SettingValue;
    };
  };
  updated_at: string;
  version: string;
  created_at: string;
  company_id: string;
}

export const versionApi = {
  getVersion: async () => {
    return privateApi.get<Version>(ENDPOINTS.VERSION.GET_VERSION);
  },
  updateVersion: async (data: Version) => {
    return privateApi.put<Version>(ENDPOINTS.VERSION.GET_VERSION, data);
  },
};

export const useVersion = () => {
  const query = useQuery({
    queryKey: ["version"],
    queryFn: versionApi.getVersion,
  });

  const getSettingValue = <T extends SettingValueType>(key: string): T | undefined => {
    return query.data?.data?.dict?.[key]?.setting_value as T | undefined;
  };

  return {
    ...query,
    getSettingValue,
  };
};

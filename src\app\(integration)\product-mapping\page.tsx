"use client";

import { useEffect, use<PERSON>em<PERSON>, useState } from "react";
import { FolderSync, Triangle<PERSON><PERSON><PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useConnection } from "@/features/integration/hooks/connection";
import { useProductMappings } from "@/features/integration/Product-Mapping/hooks/product-mapping";
import { useSyncProduct } from "@/features/integration/Product-Mapping/hooks/sync-product";
import ChannelTabs from "@/features/integration/Product-Mapping/ProductMappingList/channel-tabs";
import { getColumns } from "@/features/integration/Product-Mapping/ProductMappingList/columns";
import { ProductMappingStatus } from "@/features/integration/Product-Mapping/ProductMappingList/constant/filter-options";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Label } from "@/components/ui/label";
import { ConnectionStatus } from "@/lib/apis/connection";

export default function ProductMappingPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const [isSyncDialogOpen, setIsSyncDialogOpen] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);

  // Get connections for tabs
  const { connections, isLoading: isConnectionsLoading } = useConnection({
    status: ConnectionStatus.ACTIVE,
    limit: 20,
  });

  // Initialize selected connection to an empty string
  const [selectedConnectionId, setSelectedConnectionId] = useState<string>("");

  // Set the first connection ID when connections are loaded
  useEffect(() => {
    if (connections.length > 0 && !selectedConnectionId) {
      setSelectedConnectionId(connections[0].id);
    }
  }, [connections, selectedConnectionId]);

  const options = useMemo(
    () => ({
      limit: Number(getInitialParams.limit),
      ...getInitialParams,
      // Use the selected connection ID
      connection_id: selectedConnectionId,
      enabled: !!selectedConnectionId, // Only fetch when a connection is selected
    }),
    [getInitialParams, selectedConnectionId]
  );

  const { mappingProducts, total, isLoading, isFetching, refetch } = useProductMappings(options);
  const { handleSync, isSyncing } = useSyncProduct(selectedConnectionId);

  const isTableLoading = isLoading || isFetching || isConnectionsLoading || !selectedConnectionId;

  // Find the selected connection for name display
  const selectedConnection = connections.find((conn) => conn.id === selectedConnectionId);

  const handleSyncClick = () => {
    if (selectedProducts.length === 0) {
      setIsSyncDialogOpen(true);
    } else {
      // Extract ids from selected products
      const selectedIds = selectedProducts.map((product) => product.id);
      handleSync(selectedIds);
    }
  };

  const handleConfirmSync = () => {
    // Sync all products without passing specific IDs
    handleSync().then(() => {
      // Only close the dialog after the sync operation is complete
      setIsSyncDialogOpen(false);
    });
  };

  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      isClear: false,
      isOtherFilters: false,
      showSearch: true,
      filterType: "products",
      searchPlaceHolder: t("pages.productMappingList.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "mapping_status",
          title: "Status",
          type: EFilterType.SINGLE,
          dataOption: ProductMappingStatus,
          defaultValue: "ALL",
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("pages.productMappingList.groupButton.syncButton"),
        icon: FolderSync,
        onClick: handleSyncClick,
        disabled: isSyncing,
        isLoading: isSyncing,
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <div className="flex max-w-full flex-col overflow-hidden">
      <div className="mx-6">
        <ChannelTabs
          selectedConnectionId={selectedConnectionId}
          onConnectionChange={setSelectedConnectionId}
        />
      </div>
      <TableCard className="max-h-[calc(100vh-128px)]">
        <TableHeader
          title={t("pages.productMappingList.title")}
          description={t("pages.productMappingList.description")}
          filterProps={filterConfig as FilterTableProps}
          filterType="products"
          data={mappingProducts || []}
          isExportable={true}
          isSaveFilters={false}
          rightComponent={<GroupButton {...groupButtonConfig} />}
        />
        <TableContainer
          data={mappingProducts || []}
          columns={getColumns(t, selectedConnection?.name || "")}
          loading={isTableLoading || isSyncing}
          total={total}
          pageSize={Number(getInitialParams.limit)}
          currentPage={Number(getInitialParams.page)}
          allowDelete={false}
          onSelectionChange={setSelectedProducts}
        />
      </TableCard>

      <ConfirmDialog
        open={isSyncDialogOpen}
        onOpenChange={setIsSyncDialogOpen}
        title={t("pages.productMappingList.alert.title")}
        loading={isSyncing}
        isControl={true}
        description=""
        longDescription={
          <>
            <div className="text-sm">{t("pages.productMappingList.alert.description")}</div>
            <div className="mt-4 space-y-2 border-l-2 border-muted-foreground pl-4 text-sm">
              <div className="flex items-center font-semibold text-destructive">
                <TriangleAlert className="mr-2 size-4" /> {t("pages.productMappingList.alert.note")}
              </div>
              <div className="text-muted-foreground">
                {t("pages.productMappingList.alert.noteDescription")}
              </div>
            </div>
            <Label>{t("pages.productMappingList.alert.areYouSure")}</Label>
          </>
        }
        confirmText={t("pages.productMappingList.alert.confirm")}
        cancelText={t("pages.productMappingList.alert.cancel")}
        onConfirm={handleConfirmSync}
      />
    </div>
  );
}

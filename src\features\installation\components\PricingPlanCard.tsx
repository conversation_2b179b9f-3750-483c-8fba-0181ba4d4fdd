import React, { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { Check, ChevronDown, ChevronUp } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslation } from "react-i18next";

import Crown from "@/assets/images/crown.svg";
import { Button } from "@/components/ui";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import { Plan, PlanFeature } from "../hooks/type";

interface PricingPlanCardProps {
  plan: Plan;
  integrations: Record<string, number>;
  handleIncrement: (planId: string, e: React.MouseEvent) => void;
  handleDecrement: (planId: string, e: React.MouseEvent) => void;
  getUndiscountPrice: (price: number) => string;
  getPrice: (price: number, salePrice: number) => string;
  planType: string;
  isPopular: boolean;
  onClick: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  isCompany: boolean;
}

export const PricingPlanCard: React.FC<PricingPlanCardProps> = ({
  plan,
  integrations,
  handleIncrement,
  handleDecrement,
  getUndiscountPrice,
  getPrice,
  planType,
  isPopular,
  onClick,
  isLoading = false,
  disabled = false,
  isCompany = true,
}) => {
  const { t } = useTranslation();
  const { theme, systemTheme } = useTheme();
  const currentMode = theme === "system" ? systemTheme : theme;
  const isDark = currentMode === "dark";
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const visibleFeatures: PlanFeature[] = showAllFeatures
    ? plan.features || []
    : plan.features?.slice(0, 5) || [];

  // Prices for animation
  const regularPrice = parseFloat(plan.price);
  const salePrice = parseFloat(plan.sale_price || plan.price);
  const displayPrice = planType === "annually" && salePrice !== 0 ? salePrice : regularPrice;

  // Animation variants for feature list container
  const featureContainerVariants = {
    open: {
      height: "auto",
      opacity: 1,
      transition: {
        height: { duration: 0.3, ease: "easeInOut" },
        opacity: { duration: 0.2 },
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
    closed: {
      height: 0,
      opacity: 0,
      transition: {
        height: { duration: 0.3, ease: "easeInOut" },
        opacity: { duration: 0.2 },
        staggerChildren: 0.03,
        staggerDirection: -1,
        when: "afterChildren",
      },
    },
  };

  // Animation variants for individual features
  const featureItemVariants = {
    open: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.2 },
    },
    closed: {
      opacity: 0,
      y: -10,
      transition: { duration: 0.1 },
    },
  };

  // Animation for button
  const buttonVariants = {
    initial: { scale: 1 },
    hover: { scale: 1.02 },
    tap: { scale: 0.98 },
  };

  // Icon animation
  const iconVariants = {
    open: { rotate: 180 },
    closed: { rotate: 0 },
  };

  return (
    <motion.div
      key={plan.id}
      initial={{ scale: 1 }}
      whileHover={{
        scale: 1.03,
        boxShadow: isPopular
          ? "0px 0px 40px -8px rgba(255,145,11,0.30)"
          : "0px 10px 30px -8px rgba(0,0,0,0.1)",
      }}
      transition={{
        type: "spring",
        stiffness: 500,
        damping: 15,
        mass: 0.6,
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className={`flex size-full flex-col rounded-2xl
      ${
        isPopular
          ? "border-2 border-primary bg-prime shadow-[0px_0px_32px_-8px_rgb(255,145,11,10.0)]"
          : "border border-border bg-card"
      } 
      transition-all duration-100
    `}>
      <div className="flex flex-col px-4">
        <div className="flex py-4">
          <h3 className="pb-4 text-xl font-bold">{plan.name}</h3>
          {isPopular && (
            <Badge
              variant="gradient"
              className="ml-auto flex h-6 items-center gap-2 rounded-lg border-none">
              <Crown className="size-4 shrink-0 border-none fill-primary" />
              <span className="shrink-0 truncate text-xs">{t("pages.choosePlan.mostPopular")}</span>
            </Badge>
          )}
        </div>

        <div className="overflow-hidden pt-4">
          {planType == "annually" && parseFloat(plan.sale_price || plan.price) != 0 && (
            <>
              <span className="text-muted-foreground line-through">${parseFloat(plan.price)}</span>
              &nbsp;
            </>
          )}
          <div className="inline-flex items-baseline">
            <span className="text-4xl font-bold">$</span>
            <AnimatePresence mode="wait">
              <motion.span
                key={planType}
                className="text-4xl font-bold"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}>
                {getPrice(parseFloat(plan.price), parseFloat(plan.sale_price))}
              </motion.span>
            </AnimatePresence>
            <span className="text-muted-foreground">/{t("common.time.month")}</span>
          </div>
        </div>
        <div className="flex grow flex-col py-4">
          {/* <div className="mb-4 flex items-center justify-between">
            <div className="flex flex-col gap-1">
              <h4
                className={`text-sm font-semibold ${isPopular ? "text-white dark:text-black" : "text-foreground"}`}>
                No. of Integrations
              </h4>
              <p className="max-w-32 text-xs text-muted-foreground">
                Choose to connect multiple stores
              </p>
            </div>

            <IntegrationCounter
              planId={plan.id}
              integrations={integrations}
              handleIncrement={handleIncrement}
              handleDecrement={handleDecrement}
              disabled={disabled}
              isPopular={isPopular}
            />
          </div> */}

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 500, damping: 10 }}>
            <Button
              variant="secondary"
              className={`w-full ${
                // Popular Individual Card
                isPopular && !isCompany
                  ? "bg-primary text-neutral-200 hover:bg-primary-hover"
                  : // Non-Popular Company Card
                    // !isPopular && isCompany
                    // ? "bg-semi text-white hover:bg-semi/80 dark:text-black"
                    // : // Non-Popular Individual Card (default)
                    ""
              }`}
              onClick={onClick}>
              {t("pages.choosePlan.getStarted")}
            </Button>
          </motion.div>
        </div>
        <Separator />
        <div className="grow overflow-hidden">
          {/* First 5 features always shown */}
          <ul className="space-y-3 py-4 text-sm text-foreground">
            {plan.features?.slice(0, 5).map((feature: PlanFeature, index: number) => (
              <motion.li
                key={`fixed-${feature.description}-${index}`}
                className="flex items-start"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.05, duration: 0.2 }}>
                <Check className="mr-2 size-5 shrink-0 text-sematic-success" />
                <span className="text-foreground">{feature.description}</span>
              </motion.li>
            ))}
          </ul>

          {/* Extra features with animation */}
          {(plan.features?.length || 0) > 5 && (
            <>
              <div className="relative overflow-hidden">
                <AnimatePresence initial={false}>
                  {showAllFeatures && (
                    <motion.div
                      key="extra-features"
                      initial="closed"
                      animate="open"
                      exit="closed"
                      variants={featureContainerVariants}
                      className="z-10 overflow-hidden">
                      <ul className="space-y-3 pb-2 pt-1 text-sm text-foreground">
                        {plan.features?.slice(5).map((feature: PlanFeature, index: number) => (
                          <motion.li
                            variants={featureItemVariants}
                            custom={index}
                            key={`extra-${feature.description}-${index}`}
                            className="flex items-start">
                            <Check className="mr-2 size-5 shrink-0 text-sematic-success" />
                            <span className="text-foreground">{feature.description}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              <motion.button
                initial={buttonVariants.initial}
                whileHover={buttonVariants.hover}
                whileTap={buttonVariants.tap}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowAllFeatures(!showAllFeatures);
                }}
                className="ml-2 mt-2 flex w-full items-center justify-start gap-1 text-sm text-muted-foreground hover:text-foreground">
                <span>{showAllFeatures ? "Show less" : "View all features"}</span>
                <motion.div
                  animate={showAllFeatures ? "open" : "closed"}
                  variants={iconVariants}
                  transition={{ duration: 0.3 }}>
                  {showAllFeatures ? (
                    <ChevronUp className="size-4" />
                  ) : (
                    <ChevronDown className="size-4" />
                  )}
                </motion.div>
              </motion.button>
            </>
          )}
        </div>
      </div>
    </motion.div>
  );
};

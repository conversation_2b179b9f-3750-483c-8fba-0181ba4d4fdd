"use client";

import { UseFormReturn } from "react-hook-form";

import { Card, CardContent } from "@/components/ui/card";

import { EditStaff } from "../staff_info/edit_staff";
import { Integration } from "../staff_info/integration";

interface StaffInfoTabProps {
  form: UseFormReturn<any>;
  staffId: string;
  staffImage?: string;
}

export default function StaffInfoTab({ form, staffId, staffImage }: StaffInfoTabProps) {
  const handleAuthorize = (provider: string) => {
    console.log(`Authorizing ${provider}`);
    // Implement authorization logic
  };

  const handleEmbedCode = () => {
    console.log("Getting embed code");
    // Implement embed code generation
  };

  const handleThemeColorChange = (color: string) => {
    console.log("Theme color changed to:", color);
    // Implement theme color change
  };

  return (
    <div className="space-y-4">
      {/* Staff Info Section */}
      <Card>
        <CardContent className="p-4">
          <EditStaff form={form} />
        </CardContent>
      </Card>

      {/* Integration Section */}
      <Card>
        <CardContent className="p-4">
          <Integration
            staffId={staffId}
            onAuthorize={handleAuthorize}
            onEmbedCode={handleEmbedCode}
            onThemeColorChange={handleThemeColorChange}
            staffImage={staffImage}
          />
        </CardContent>
      </Card>
    </div>
  );
}

"use client";

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";

// Custom variant for the toggle buttons to match the image
const toggleButtonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-all",
  {
    variants: {
      variant: {
        default: "bg-neutral-100 hover:bg-neutral-100",
        selected: "bg-neutral-100 text-foreground",
        ghost: "bg-neutral-100 text-muted-foreground hover:bg-neutral-200 hover:text-foreground",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 px-3 py-1.5",
        lg: "h-10 px-5 py-2.5",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ToggleButtonOption {
  id: string;
  label: string;
  value: string;
}

export interface ToggleButtonProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "onValueChange"> {
  options: ToggleButtonOption[];
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  isMultiSelect?: boolean;
  size?: VariantProps<typeof toggleButtonVariants>["size"];
}

export function ToggleButton({
  className,
  options,
  value = [],
  onValueChange,
  isMultiSelect = false,
  size,
  ...props
}: ToggleButtonProps) {
  // Convert single value to array for consistent handling
  const selectedValues = Array.isArray(value) ? value : value ? [value] : [];

  const handleValueChange = (newValue: string[]) => {
    if (onValueChange) {
      if (isMultiSelect) {
        onValueChange(newValue);
      } else {
        // For single select, just return the first value or empty string
        onValueChange(newValue[0] || "");
      }
    }
  };

  // Render appropriate toggle group based on selection mode
  if (isMultiSelect) {
    return (
      <ToggleGroup
        type="multiple"
        value={selectedValues}
        onValueChange={handleValueChange}
        className={cn("flex flex-wrap gap-2", className)}>
        {options.map((option) => {
          const isSelected = selectedValues.includes(option.value);
          return (
            <ToggleGroupItem
              key={option.id}
              value={option.value}
              className={cn(
                toggleButtonVariants({
                  variant: isSelected ? "selected" : "default",
                  size,
                })
              )}>
              {option.label}
            </ToggleGroupItem>
          );
        })}
      </ToggleGroup>
    );
  } else {
    return (
      <ToggleGroup
        type="single"
        value={selectedValues[0] || ""}
        onValueChange={(value) => handleValueChange(value ? [value] : [])}
        className={cn("flex flex-wrap gap-2", className)}>
        {options.map((option) => {
          const isSelected = selectedValues.includes(option.value);
          return (
            <ToggleGroupItem
              key={option.id}
              value={option.value}
              className={cn(
                toggleButtonVariants({
                  variant: isSelected ? "selected" : "default",
                  size,
                })
              )}>
              {option.label}
            </ToggleGroupItem>
          );
        })}
      </ToggleGroup>
    );
  }
}

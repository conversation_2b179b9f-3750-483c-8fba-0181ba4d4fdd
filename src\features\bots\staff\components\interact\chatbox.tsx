"use client";

import { useRef } from "react";

import Footer from "@/features/xbots/components/footer";
import MessageList from "@/features/xbots/components/message-list";
import { useMessage } from "@/features/xbots/hooks/use-message";

import defaultAvatar from "@/assets/images/staff/default_avatar.png";
import { CustomImage } from "@/components/ui/image";
import { uppercaseToTitleCase } from "@/utils/helpers/text-formater";

import { VirtualStaffModel } from "../../hooks/type";

interface ChatBoxProps {
  customHeader?: React.ReactNode;
  staff: VirtualStaffModel;
  isLoading?: boolean;
  isDefaultHeader?: boolean;
  receiverRoles?: string[];
  senderRoles?: string[];
}

export default function ChatBox({
  staff,
  isLoading = false,
  customHeader,
  isDefaultHeader,
  receiverRoles,
  senderRoles,
}: ChatBoxProps) {
  const {
    messages,
    input,
    isLoading: isMessageLoading,
    handleInputChange,
    sendMessage,
    streamingMessage,
    isStreaming,
    handleKeyDown,
    isSending,
  } = useMessage({ staffId: staff?.id, role: "staff" });
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // const { hasNextPage, fetchNextPage, isFetchingNextPage } = useGetListMessenger(staff?.id);

  return (
    <div className="flex max-h-[450px] min-h-[450px] min-w-[250px] flex-1 flex-col overflow-hidden lg:max-h-full lg:min-h-full">
      {customHeader}
      {isDefaultHeader && <ChatHeader staff={staff} />}
      <div
        ref={chatContainerRef}
        className="flex w-full flex-auto flex-col-reverse overflow-y-auto  overflow-x-hidden">
        <MessageList
          messages={messages}
          className="text-[14px]"
          streamingMessage={streamingMessage}
          isStreaming={isStreaming}
          isSending={isSending}
          isLoading={isLoading}
          receiverRoles={receiverRoles}
          senderRoles={senderRoles}
          staffAvatar={staff?.image?.url}
          // isFetchingNextPage={isFetchingNextPage}
        />
      </div>
      <Footer
        className="bg-card"
        input={input}
        handleKeyDown={handleKeyDown}
        isLoading={isLoading || isMessageLoading || isStreaming || isSending}
        handleInputChange={handleInputChange}
        handleSubmit={() => sendMessage()}
      />
    </div>
  );
}

const ChatHeader = ({ staff }: { staff: VirtualStaffModel }) => {
  return (
    <div className="flex items-center justify-between bg-muted">
      <div className="flex w-full items-center gap-3 p-4">
        <div className="relative">
          <CustomImage
            defaultImage={defaultAvatar.src}
            src={staff?.image?.url || defaultAvatar.src}
            alt={staff?.name || ""}
            width={40}
            height={40}
            className="size-10 flex-none rounded-lg object-cover"
          />
          <span className="absolute -bottom-1 -right-1 size-4 rounded-full border-4 border-card bg-sematic-success" />
        </div>
        <div className="flex max-w-[164px] flex-col justify-between ">
          <span className="flex items-center gap-1 truncate text-base font-semibold text-accent-foreground">
            {staff?.name}
          </span>
          <span className="truncate text-xs text-muted-foreground">
            {uppercaseToTitleCase(staff?.role || "")}
          </span>
        </div>
      </div>
    </div>
  );
};

import { useCallback, useState } from "react";

import {
  DestinationProductData,
  MappingProduct,
  ProductVariant,
} from "@/features/integration/Product-Mapping/hooks/types";
import {
  useDestinationProductDetail,
  useDestinationProducts,
} from "@/features/integration/Product-Mapping/ProductSyncSelect/hooks/product-selection";

// Types for our destination products
export interface DestinationProduct {
  id: string;
  name: string;
  image: string;
  price: number;
}

export interface DestinationVariant {
  id: string;
  name: string;
  image: string;
  price: number;
  color: string;
}

// Custom hook to get destination products from API instead of mocks
export const useDestinationProductList = () => {
  const { destinationProducts, isLoading, isError } = useDestinationProducts();
  return {
    destinationProducts,
    isLoading,
    isError,
  };
};

// For backward compatibility with existing code
export const getDestinationProducts = () => {
  console.warn("getDestinationProducts is deprecated, use useDestinationProductList instead");
  return []; // Return empty array for SSR and initial render
};

// Get source product variants for display
export const getSourceVariants = (product: MappingProduct): ProductVariant[] => {
  if (
    !product.standard_source_data.variants ||
    product.standard_source_data.variants.length === 0
  ) {
    // If no variants, create a default variant based on the product
    return [
      {
        id: `${product.standard_source_data.id}-default`,
        name: "Default",
        price: product.price,
        images: product.standard_source_data.images?.[0]?.url || "/placeholder.svg",
      },
    ];
  }

  // Map actual variants to our display format
  return product.standard_source_data.variants.map((variant) => ({
    id: variant.id,
    name: variant.name,
    images:
      variant.images?.[0]?.url ||
      product.standard_source_data.images?.[0]?.url ||
      "/placeholder.svg",
    price: variant.prices?.[0]?.price || product.price,
  }));
};

// Custom hook to get product variants
export const useDestinationVariants = (connectionId?: string, productId?: string) => {
  const { variants, isLoading, isError } = useDestinationProductDetail({
    connectionId,
    productId,
    enabled: !!connectionId && !!productId,
  });

  return {
    variants,
    isLoading,
    isError,
  };
};

// Get destination product variants for a selected product (updated to use actual API data)
export const getDestinationVariants = (
  productId: string,
  destinationProducts: DestinationProductData[]
): ProductVariant[] => {
  // Find the selected product in our destination products data
  const selectedProduct = destinationProducts.find((p) => p.id === productId);

  if (!selectedProduct) {
    return [
      {
        id: `${productId}-default`,
        name: "Default",
        price: 0,
      },
    ];
  }

  // Create an empty array to store the variants
  let variants: ProductVariant[] = [];

  // Check if the product has variants in standard_data
  if (
    selectedProduct.standard_data?.variants &&
    selectedProduct.standard_data.variants.length > 0
  ) {
    // Map the variants from the standard_data
    variants = selectedProduct.standard_data.variants.map((variant) => {
      return {
        id: variant.id || "",
        name: variant.name || "Unnamed variant",
        price: typeof variant.price === "string" ? parseFloat(variant.price) : variant.price || 0,
        images: variant.images || "",
      };
    });
  } else if (selectedProduct.variants && selectedProduct.variants.length > 0) {
    // Use variants directly from the product (from the top level)
    variants = selectedProduct.variants.map((variant) => {
      return {
        id: variant.id || "",
        name: variant.name || (variant.title as string) || "Unnamed variant",
        price:
          typeof variant.price === "string"
            ? parseFloat(variant.price)
            : Number(variant.price) || 0,
        images: (variant.image as string) || (variant.images as string) || "",
      };
    });
  } else {
    // If no variants are found, create a default one
    variants = [
      {
        id: `${productId}-default`,
        name: "Default",
        price: selectedProduct.standard_data?.price
          ? parseFloat(selectedProduct.standard_data.price)
          : 0,
        images: (selectedProduct.standard_data?.image as string) || "",
      },
    ];
  }

  return variants;
};

// Hook for product mapping functionality
export const useProductSelection = (
  product: MappingProduct,
  onClose: () => void,
  onMapCallback?: (destinationProductId: string) => Promise<void>
) => {
  const [selectedDestProduct, setSelectedDestProduct] = useState<DestinationProductData | null>(
    null
  );
  const [mapVariants, setMapVariants] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [mappedVariants, setMappedVariants] = useState<Record<string, string>>({}); // sourceVariantId -> destVariantId
  const [selectedSourceVariant, setSelectedSourceVariant] = useState<string | null>(null);
  // Track active source variant for mapping operations
  const [activeSourceForMapping, setActiveSourceForMapping] = useState<string | null>(null);

  // Get source product variants
  const sourceVariants = getSourceVariants(product);

  // Get destination products from API using our new hook
  const { destinationProducts, isLoading: isLoadingDestProducts } = useDestinationProductList();

  // Get variants for the selected destination product
  const { variants: selectedProductVariants, isLoading: isLoadingVariants } =
    useDestinationVariants(selectedDestProduct?.connection_id, selectedDestProduct?.id);

  // Track if user has manually unselected a source variant
  const [manuallyUnselected, setManuallyUnselected] = useState(false);

  // // Auto-select the first source variant when entering variant mapping view
  // useEffect(() => {
  //   // Only auto-select if:
  //   // 1. We have a selected destination product
  //   // 2. We have source variants available
  //   // 3. No source variant is currently selected
  //   // 4. User hasn't manually unselected a variant
  //   // 5. We're not in the loading state
  //   if (
  //     selectedDestProduct &&
  //     sourceVariants.length > 0 &&
  //     !selectedSourceVariant &&
  //     !manuallyUnselected &&
  //     !isLoadingVariants
  //   ) {
  //     console.log("Auto-selecting first source variant:", sourceVariants[0].id);
  //     // Use immediate timeout to ensure this runs after the current render cycle
  //     setTimeout(() => {
  //       setSelectedSourceVariant(sourceVariants[0].id);
  //       setActiveSourceForMapping(sourceVariants[0].id);
  //     }, 0);
  //   }
  // }, [
  //   selectedDestProduct,
  //   sourceVariants,
  //   selectedSourceVariant,
  //   manuallyUnselected,
  //   isLoadingVariants,
  // ]);

  // // Also trigger auto-selection when destination product variants finish loading
  // useEffect(() => {
  //   if (
  //     selectedDestProduct &&
  //     sourceVariants.length > 0 &&
  //     !selectedSourceVariant &&
  //     !manuallyUnselected &&
  //     selectedProductVariants.length > 0 &&
  //     !isLoadingVariants
  //   ) {
  //     console.log(
  //       "Auto-selecting first source variant after variants loaded:",
  //       sourceVariants[0].id
  //     );
  //     setSelectedSourceVariant(sourceVariants[0].id);
  //     setActiveSourceForMapping(sourceVariants[0].id);
  //   }
  // }, [
  //   selectedProductVariants,
  //   selectedDestProduct,
  //   sourceVariants,
  //   selectedSourceVariant,
  //   manuallyUnselected,
  //   isLoadingVariants,
  // ]);

  // Handle product selection
  const handleProductSelect = (destProduct: DestinationProductData) => {
    setSelectedDestProduct(destProduct);
    setManuallyUnselected(false); // Reset when changing products
  };

  // Handle back button
  const handleBack = () => {
    setSelectedDestProduct(null);
    setSelectedSourceVariant(null);
    setActiveSourceForMapping(null);
  };

  // Handle map action
  const handleMap = async () => {
    if (!selectedDestProduct || !onMapCallback) return;

    try {
      setIsLoading(true);
      await onMapCallback(selectedDestProduct.id);
      onClose();
    } catch (error) {
      console.error("Error mapping product:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle variant mapping option change
  const handleVariantMappingChange = (checked: boolean) => {
    setMapVariants(checked);
  };

  // Handle source variant selection
  const handleSourceVariantSelect = (variantId: string) => {
    // Toggle selection if clicking the same variant
    if (selectedSourceVariant === variantId) {
      setSelectedSourceVariant(null);
      setActiveSourceForMapping(null);
      setManuallyUnselected(true); // Mark that user manually unselected
    } else {
      setSelectedSourceVariant(variantId);
      setActiveSourceForMapping(variantId);
    }
  };

  // Handle clearing selection (when clicking outside)
  const clearSelection = () => {
    setSelectedSourceVariant(null);
    setActiveSourceForMapping(null);
    setManuallyUnselected(true); // Mark that user manually unselected
  };

  // Handle destination variant mapping
  const handleMapVariant = (sourceVariantId: string, destVariantId: string) => {
    // Special case for clearing all mappings
    if (sourceVariantId === "__clear_all__" && destVariantId === "__clear_all__") {
      setMappedVariants({});
      return;
    }

    setMappedVariants((prev) => {
      const newMappedVariants = { ...prev };

      // Check if the destination is already mapped to another source
      const existingSourceId = Object.entries(newMappedVariants).find(
        ([_, dstId]) => dstId === destVariantId
      )?.[0];

      if (existingSourceId) {
        // Remove the existing mapping
        delete newMappedVariants[existingSourceId];
      }

      // Check if the source is already mapped to another destination
      if (newMappedVariants[sourceVariantId]) {
        // Remove the existing mapping
        delete newMappedVariants[sourceVariantId];
      }

      // Create the new mapping
      newMappedVariants[sourceVariantId] = destVariantId;

      return newMappedVariants;
    });

    // Keep the source variant active for further mapping operations
    setActiveSourceForMapping(sourceVariantId);
  };

  // Handle unmapping a variant
  const handleUnmapVariant = (sourceVariantId: string) => {
    setMappedVariants((prev) => {
      const newMappedVariants = { ...prev };
      delete newMappedVariants[sourceVariantId];
      return newMappedVariants;
    });
  };

  // Handle unmapping a destination variant
  const handleUnmapDestVariant = (destVariantId: string) => {
    // Only allow unmapping if the selected source variant is the one that mapped to this destination variant
    if (!selectedSourceVariant) return;

    // Check if the selected source variant is mapped to this destination variant
    if (mappedVariants[selectedSourceVariant] !== destVariantId) return;

    setMappedVariants((prev) => {
      const newMappedVariants = { ...prev };
      delete newMappedVariants[selectedSourceVariant];
      return newMappedVariants;
    });
  };

  // Check if a destination variant is already mapped
  const isDestVariantMapped = (destVariantId: string) => {
    return Object.values(mappedVariants).includes(destVariantId);
  };

  // Check if a destination variant is mapped to the currently active source
  const isDestinationMappedToActiveSource = (destVariantId: string) => {
    return (
      activeSourceForMapping !== null && mappedVariants[activeSourceForMapping] === destVariantId
    );
  };

  // Get the current view (selection or mapping)
  const isVariantMappingView = selectedDestProduct !== null;

  // Wrapped getDestinationVariants to include destination products data
  const getDestinationVariantsForSelected = useCallback(
    (productId: string) => {
      // If we have detailed product variants from the API, use those first
      if (selectedProductVariants.length > 0 && selectedDestProduct?.id === productId) {
        console.log("Using API variants:", selectedProductVariants);
        return selectedProductVariants;
      }
      // Otherwise fall back to variants from the product list
      console.log("Using product list variants for", productId);
      return getDestinationVariants(productId, destinationProducts);
    },
    [destinationProducts, selectedDestProduct, selectedProductVariants]
  );

  return {
    selectedDestProduct,
    mapVariants,
    isLoading,
    isLoadingDestProducts,
    isLoadingVariants,
    isVariantMappingView,
    sourceVariants,
    destinationProducts,
    selectedProductVariants,
    mappedVariants,
    selectedSourceVariant,
    activeSourceForMapping,
    handleProductSelect,
    handleBack,
    handleMap,
    handleVariantMappingChange,
    handleSourceVariantSelect,
    handleMapVariant,
    handleUnmapVariant,
    handleUnmapDestVariant,
    isDestVariantMapped,
    isDestinationMappedToActiveSource,
    getDestinationVariants: getDestinationVariantsForSelected,
    clearSelection,
  };
};

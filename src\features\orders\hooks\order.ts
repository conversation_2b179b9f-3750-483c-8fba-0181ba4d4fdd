import { useInfiniteQuery, useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { orderApi } from "@/lib/apis/order";

import { IGetOrdersParams, orderKeys } from "./keys";
import { Order } from "./types";

interface UseOrdersOptions extends Partial<IGetOrdersParams> {
  enabled?: boolean;
}

export function useOrders(options: UseOrdersOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;

  const query = useInfiniteQuery({
    queryKey: orderKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      orderApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const orders = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    orders,
    total,
  };
}

export function useOrderDetail(id: string) {
  const query = useQuery({
    queryKey: orderKeys.detail(id),
    queryFn: () => orderApi.getById(id),
  });

  const order = query.data;

  return { ...query, order };
}

export function useCreateOrder() {
  return useMutation({
    mutationFn: async (data: Partial<Order>) => {
      console.log("Sending order data:", data);
      const response = await orderApi.create(data);
      console.log("Order creation response:", response);
      return response;
    },
    onError: (error: any) => {
      console.error("Error creating order:", error);
      toast.error(error.response?.data?.message || "Failed to create order. Please try again.");
    },
    onSuccess: (data) => {
      console.log("Order created successfully:", data);
    },
  });
}

export function useUpdateOrder() {
  return useMutation({
    mutationFn: async (data: Partial<Order>) => {
      const response = await orderApi.update(data.id as string, data);
      return response;
    },
    onError: (error: any) => {
      console.error("Error updating order:", error);
      toast.error(error.response?.data?.message || "Failed to update order. Please try again.");
    },
    onSuccess: (data) => {
      console.log("Order updated successfully:", data);
    },
  });
}

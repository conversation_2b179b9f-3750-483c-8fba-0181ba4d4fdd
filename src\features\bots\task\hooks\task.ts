import { useInfiniteQuery } from "@tanstack/react-query";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { taskApi } from "@/lib/apis/task";

import { IGetTasksParams, taskKeys } from "./keys";

interface UseTasksOptions extends Partial<IGetTasksParams> {
  enabled?: boolean;
}

export function useTasks(options: UseTasksOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;

  const query = useInfiniteQuery({
    queryKey: taskKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      taskApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const tasks = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    tasks,
    total,
  };
}

import { privateApi } from "../api_helper";

const inventoryApi = {
  getInventory: async (productId: string) => {
    const response = await privateApi.get(`/products/${productId}/inventory`);
    return response.data;
  },

  getInventoryItems: async (id: string, sortCreatedAt: "asc" | "desc" = "desc") => {
    try {
      const response = await privateApi.get(`/inventory/inventory_items`, {
        params: {
          id,
          sort_created_at: sortCreatedAt,
        },
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  getInventoryTransactions: async (
    itemId: string,
    transactionId: string,
    sortCreatedAt: "asc" | "desc" = "desc"
  ) => {
    try {
      const response = await privateApi.get(
        `/inventory/inventory_items/${itemId}/inventory_transactions/${transactionId}`,
        {
          params: {
            sort_created_at: sortCreatedAt,
          },
        }
      );
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default inventoryApi;

import { useEffect, useMemo, useState } from "react";
import { useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { QUERY_KEYS } from "@/features/products/hooks/keys";
import type {
  PriceVariant,
  ProductOption,
  ProductUnit,
  UnitResponse,
} from "@/features/products/hooks/types";

import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { productApi } from "@/lib/apis/product";

interface ProductUnitsProps {
  units: ProductUnit[];
  options: ProductOption[];
  variants: PriceVariant[];
  onChange: (units: ProductUnit[]) => void;
  onVariantsChange: (variants: PriceVariant[]) => void;
  onSectionFocus?: () => void;
}

export function ProductUnits({
  units = [],
  options = [],
  variants = [],
  onChange,
  onVariantsChange,
  onSectionFocus,
}: ProductUnitsProps) {
  const queryClient = useQueryClient();
  const [unitsList, setUnitsList] = useState<ProductUnit[]>(units);
  const [newUnit, setNewUnit] = useState<{ name: string; ratio: number }>({
    name: "",
    ratio: 0,
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [unitToDelete, setUnitToDelete] = useState<number | null>(null);
  const { t } = useTranslation();
  // Update query to handle pagination
  const {
    data: unitsData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery<UnitResponse>({
    queryKey: QUERY_KEYS.UNITS,
    queryFn: async ({ pageParam = 0 }) => {
      const response = await productApi.getUnits({ limit: 20, page: pageParam as number });
      return response as unknown as UnitResponse;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      if (lastPage.items.length < 20) return undefined;
      return lastPage.page + 1;
    },
  });

  // Flatten all items from all pages
  const allUnits = useMemo(() => {
    return unitsData?.pages.flatMap((page) => page.items) ?? [];
  }, [unitsData]);

  // Check if we need to initialize units from variants
  useEffect(() => {
    if (units.length === 0 && variants.length > 0) {
      // Look for child variants with units and original_sku
      const childVariantsWithUnits = variants.filter((v) => v.unit && v.original_sku);

      if (childVariantsWithUnits.length > 0) {
        const newUnits: ProductUnit[] = [];

        // Process each child variant
        childVariantsWithUnits.forEach((childVariant) => {
          // Find parent variant based on original_sku
          const parentVariant = variants.find(
            (v) => !v.parent_variant_id && v.sku === childVariant.original_sku
          );

          if (parentVariant && childVariant.unit) {
            // Create a unit entry for this relationship
            newUnits.push({
              id: Math.random().toString(),
              variant: parentVariant.id,
              variant_name: parentVariant.name,
              unit: childVariant.unit.id,
              unit_name: childVariant.unit.name,
              child_variant_id: childVariant.id,
            });
          }
        });

        if (newUnits.length > 0) {
          setUnitsList(newUnits);
          onChange(newUnits);
        }
      }
    } else if (units.length > 0 && unitsList.length === 0) {
      // Initialize from provided units
      setUnitsList(units);
    }
  }, [units, variants, onChange, unitsList.length]);

  // Create unit mutation
  const createUnitMutation = useMutation({
    mutationFn: (data: { name: string; ratio: number }) => productApi.createUnit(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.UNITS });
      setIsDialogOpen(false);
      setNewUnit({ name: "", ratio: 0 });
    },
    onError: (error) => {
      console.error("Error in unit creation:", error);
      // You might want to show an error toast here
    },
  });

  const handleAddUnit = async () => {
    if (!newUnit.name || !newUnit.ratio) return;

    try {
      // Create unit in API with both name and ratio
      const response = await createUnitMutation.mutateAsync({
        name: newUnit.name,
        ratio: newUnit.ratio,
      });

      // Then add the new unit to our local list
      const unitToAdd: ProductUnit = {
        id: Math.random().toString(),
        variant: "",
        unit: "",
      };

      onChange([...unitsList, unitToAdd]);

      // Reset form and close dialog
      setNewUnit({ name: "", ratio: 0 });
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error creating unit:", error);
      // You might want to show an error toast here
    }
  };

  const handleVariantChange = (index: number, variantId: string) => {
    const currentUnit = unitsList[index];
    const selectedUnit = allUnits.find((u) => u.id === currentUnit.unit);

    // Check if trying to select "all" when other cards have variants selected
    if (variantId === "all") {
      // Get all base variants
      const baseVariants = variants.filter((v) => !v.parent_variant_id);

      // Remove current child variant but keep its data
      const currentChildVariant = variants.find((v) => v.id === currentUnit.child_variant_id);
      const cleanVariants = variants.filter((v) => v.id !== currentUnit.child_variant_id);

      // If unit is already selected, create child variants for all base variants
      if (selectedUnit) {
        // Find variants that don't already have this unit
        const variantsNeedingUnit = baseVariants.filter(
          (parentVariant) =>
            !variants.some(
              (v) => v.parent_variant_id === parentVariant.id && v.unit?.id === selectedUnit.id
            )
        );

        const childVariants = variantsNeedingUnit.map((parentVariant) => ({
          ...parentVariant,
          id: Math.random().toString(),
          name: `${parentVariant.name} - ${selectedUnit.name}`,
          sku: `${parentVariant.sku}-${selectedUnit.ratio}`,
          original_sku: parentVariant.sku,
          parent_variant_id: parentVariant.id,
          unit: {
            id: selectedUnit.id,
            name: selectedUnit.name,
            ratio: selectedUnit.ratio,
          },
          prices: currentChildVariant?.prices || [],
        }));

        const newVariants = [...cleanVariants, ...childVariants];

        // Update unit card for "All" selection with child variant IDs
        const updatedUnits = unitsList.map((u, i) =>
          i === index
            ? {
                ...u,
                variant: "all",
                variant_name: "All Variants",
                child_variant_id: undefined,
                child_variant_ids: childVariants.map((v) => v.id),
              }
            : u
        );

        setUnitsList(updatedUnits);
        onChange(updatedUnits);
        onVariantsChange(newVariants);
        return;
      }

      // If no unit selected yet, just update the unit card
      const updatedUnits = unitsList.map((u, i) =>
        i === index
          ? {
              ...u,
              variant: "all",
              variant_name: "All Variants",
              child_variant_id: undefined,
              child_variant_ids: [],
            }
          : u
      );

      setUnitsList(updatedUnits);
      onChange(updatedUnits);
      onVariantsChange(cleanVariants);
      return;
    }

    // For switching between specific variants
    if (currentUnit.child_variant_id) {
      // Remove the existing child variant
      const cleanVariants = variants.filter((v) => v.id !== currentUnit.child_variant_id);

      const selectedParentVariant = variants.find((v) => v.id === variantId);
      if (!selectedParentVariant) return;

      // Create new child variant for the new parent
      const childVariant: PriceVariant = {
        ...selectedParentVariant,
        id: Math.random().toString(),
        name: selectedUnit
          ? `${selectedParentVariant.name} - ${selectedUnit.name}`
          : selectedParentVariant.name,
        sku: selectedUnit
          ? `${selectedParentVariant.sku}-${selectedUnit.ratio}`
          : selectedParentVariant.sku,
        original_sku: selectedParentVariant.sku,
        parent_variant_id: variantId,
        unit: selectedUnit
          ? {
              id: selectedUnit.id,
              name: selectedUnit.name,
              ratio: selectedUnit.ratio,
            }
          : undefined,
        prices: [],
      };

      const newVariants = [...cleanVariants, childVariant];

      // Update unit card
      const updatedUnits = unitsList.map((u, i) =>
        i === index
          ? {
              ...u,
              variant: variantId,
              variant_name: selectedParentVariant.name,
              child_variant_id: childVariant.id,
            }
          : u
      );

      setUnitsList(updatedUnits);
      onChange(updatedUnits);
      onVariantsChange(newVariants);
      return;
    }

    // Add this block to handle switching from "All" to specific variant
    if (currentUnit.variant === "all" && currentUnit.child_variant_ids?.length) {
      // Remove all child variants created by "All"
      const cleanVariants = variants.filter((v) => !currentUnit.child_variant_ids?.includes(v.id));

      const selectedParentVariant = variants.find((v) => v.id === variantId);
      if (!selectedParentVariant) return;

      // Create new child variant for the selected parent
      const childVariant: PriceVariant = {
        ...selectedParentVariant,
        id: Math.random().toString(),
        name: selectedUnit
          ? `${selectedParentVariant.name} (${selectedUnit.name})`
          : selectedParentVariant.name,
        sku: selectedUnit
          ? `${selectedParentVariant.sku}-${selectedUnit.ratio}`
          : selectedParentVariant.sku,
        original_sku: selectedParentVariant.sku,
        parent_variant_id: variantId,
        unit: selectedUnit
          ? {
              id: selectedUnit.id,
              name: selectedUnit.name,
              ratio: selectedUnit.ratio,
            }
          : undefined,
        prices: [],
      };

      const newVariants = [...cleanVariants, childVariant];

      // Update unit card
      const updatedUnits = unitsList.map((u, i) =>
        i === index
          ? {
              ...u,
              variant: variantId,
              variant_name: selectedParentVariant.name,
              child_variant_id: childVariant.id,
              child_variant_ids: undefined,
            }
          : u
      );

      setUnitsList(updatedUnits);
      onChange(updatedUnits);
      onVariantsChange(newVariants);
      return;
    }

    // Check if the variant still exists in the current variants list
    const variantExists = variants.some((v) => v.id === currentUnit.variant);

    // If variant doesn't exist anymore or no child variant, create new one
    if (!variantExists || !currentUnit.child_variant_id) {
      const parentVariant = variants.find((v) => v.id === variantId);
      if (!parentVariant) return;

      const childVariant: PriceVariant = {
        ...parentVariant,
        id: Math.random().toString(),
        name: selectedUnit ? `${parentVariant.name} (${selectedUnit.name})` : parentVariant.name,
        sku: selectedUnit ? `${parentVariant.sku}-${selectedUnit.ratio}` : parentVariant.sku,
        original_sku: parentVariant.sku,
        parent_variant_id: variantId,
        unit: selectedUnit
          ? {
              id: selectedUnit.id,
              name: selectedUnit.name,
              ratio: selectedUnit.ratio,
            }
          : undefined,
        prices: [],
      };

      // Remove old child variant if it exists
      const cleanVariants = variants.filter((v) => v.id !== currentUnit.child_variant_id);
      const newVariants = [...cleanVariants, childVariant];

      // Update unit card with new info
      const updatedUnits = unitsList.map((u, i) =>
        i === index
          ? {
              ...u,
              variant: variantId,
              variant_name: variants.find((v) => v.id === variantId)?.name,
              child_variant_id: childVariant.id,
            }
          : u
      );

      setUnitsList(updatedUnits);
      onChange(updatedUnits);
      onVariantsChange(newVariants);
      return;
    }

    // Update existing child variant
    const updatedVariants = variants.map((v) => {
      if (v.id === currentUnit.child_variant_id) {
        const selectedParentVariant = variants.find((pv) => pv.id === variantId);
        if (!selectedParentVariant) return v;

        return {
          ...v,
          name: selectedUnit
            ? `${selectedParentVariant.name} (${selectedUnit.name})`
            : selectedParentVariant.name,
          sku: selectedUnit
            ? `${selectedParentVariant.sku}-${selectedUnit.ratio}`
            : selectedParentVariant.sku,
          original_sku: selectedParentVariant.sku,
          parent_variant_id: variantId,
          option1: selectedParentVariant.option1,
          option2: selectedParentVariant.option2,
          unit: v.unit,
          prices: v.prices,
        };
      }
      return v;
    });

    const updatedUnits = unitsList.map((u, i) =>
      i === index
        ? {
            ...u,
            variant: variantId,
            variant_name: variants.find((v) => v.id === variantId)?.name,
          }
        : u
    );

    setUnitsList(updatedUnits);
    onChange(updatedUnits);
    onVariantsChange(updatedVariants);
  };

  const handleUnitChange = (index: number, unitData: any) => {
    const unitId = typeof unitData === "string" ? unitData : unitData.id;
    const unit = allUnits.find((u) => u.id === unitId);
    if (!unit) return;

    const currentUnit = unitsList[index];

    // If "All" variants is selected, check if any variants already have this unit
    if (currentUnit.variant === "all") {
      const hasExistingUnitVariants = variants.some(
        (v) =>
          v.unit?.id === unit.id && // Has the same unit
          v.parent_variant_id // Is a child variant
      );

      if (hasExistingUnitVariants) {
        toast({
          title: "Cannot assign unit to all variants",
          description: "Some variants already have this unit assigned",
          variant: "destructive",
        });
        return;
      }
    }

    // Handle "All" variants case
    if (currentUnit.variant === "all") {
      const baseVariants = variants.filter((v) => !v.parent_variant_id);

      // Remove old child variants
      const cleanVariants = variants.filter((v) => !currentUnit.child_variant_ids?.includes(v.id));

      // Create new child variants for each base variant
      const childVariants = baseVariants.map((parentVariant) => ({
        ...parentVariant,
        id: Math.random().toString(),
        name: `${parentVariant.name} (${unit.name})`,
        sku: `${parentVariant.sku}-${unit.ratio}`,
        original_sku: parentVariant.sku,
        parent_variant_id: parentVariant.id,
        unit: {
          id: unit.id,
          name: unit.name,
          ratio: unit.ratio,
        },
        prices: [],
      }));

      const newVariants = [...cleanVariants, ...childVariants];

      // Update unit card
      const updatedUnits = unitsList.map((u, i) =>
        i === index
          ? {
              ...u,
              unit: unit.id,
              unit_name: unit.name,
              child_variant_ids: childVariants.map((v) => v.id),
            }
          : u
      );

      setUnitsList(updatedUnits);
      onChange(updatedUnits);
      onVariantsChange(newVariants);
      return;
    }

    if (!currentUnit.child_variant_id) return; // Must select variant first

    const parentVariant = variants.find((v) => v.id === currentUnit.variant);
    if (!parentVariant) return;

    // Check if another unit card already uses this unit for the same parent
    const isDuplicate = unitsList.some(
      (u, i) =>
        i !== index && // Not the current card
        u.variant === currentUnit.variant && // Same parent variant
        u.unit === unit.id // Same unit
    );

    if (isDuplicate) {
      toast({
        title: "Unit already exists",
        description: "This variant already has a child with this unit",
        variant: "destructive",
      });
      return;
    }

    // Update existing child variant with new unit
    const updatedVariants = variants.map((v) => {
      if (v.id === currentUnit.child_variant_id) {
        return {
          ...v,
          name: `${parentVariant.name} - ${unit.name}`,
          sku: `${parentVariant.sku}-${unit.ratio}`,
          original_sku: parentVariant.sku,
          unit: {
            id: unit.id,
            name: unit.name,
            ratio: unit.ratio,
          },
          option1: parentVariant.option1,
          option2: parentVariant.option2,
        };
      }
      return v;
    });

    // Update unit card info
    const updatedUnits = unitsList.map((u, i) =>
      i === index
        ? {
            ...u,
            unit: unit.id,
            unit_name: unit.name,
          }
        : u
    );

    setUnitsList(updatedUnits);
    onChange(updatedUnits);
    onVariantsChange(updatedVariants);
  };

  const handleLoadMore = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };

  const handleDeleteUnit = (index: number) => {
    setUnitToDelete(index);
    setShowDeleteAlert(true);
  };

  const handleConfirmedDelete = () => {
    if (unitToDelete === null) return;

    const unitToDeleteItem = unitsList[unitToDelete];

    // Remove the unit card and its child variants
    const updatedUnits = unitsList.filter((_, i) => i !== unitToDelete);
    const updatedVariants = variants.filter((v) => {
      if (unitToDeleteItem.variant === "all") {
        // Remove all child variants created by this "all" selection
        return !unitToDeleteItem.child_variant_ids?.includes(v.id);
      }
      // Remove single child variant
      return v.id !== unitToDeleteItem.child_variant_id;
    });

    setUnitsList(updatedUnits);
    onChange(updatedUnits);
    onVariantsChange(updatedVariants);
    setShowDeleteAlert(false);
    setUnitToDelete(null);
  };

  return (
    <div
      className="space-y-4"
      onClick={() => onSectionFocus?.()}
      onFocus={() => onSectionFocus?.()}>
      <div className="text-sm text-muted-foreground">
        {t("pages.products.addManual.sections.createVariant")}
      </div>

      {unitsList.map((unit, index) => (
        <div key={`${unit.id}-${index}`} className="rounded-lg bg-background p-4">
          <div className="grid grid-cols-[minmax(0,1fr)_minmax(0,1fr)_auto] gap-4">
            {/* Variant Combobox */}
            <div>
              <div className="mb-1.5 whitespace-nowrap text-sm font-medium">
                {t("pages.products.addManual.sections.variant")}
              </div>
              <Combobox
                value={unit.variant || ""}
                onValueChange={(value) => handleVariantChange(index, value)}
                items={[
                  {
                    id: "all",
                    name: t("pages.products.addManual.sections.all"),
                    displayValue: t("pages.products.addManual.sections.all"),
                    disabled: unitsList.some((u, i) => i !== index && u.variant === "all"),
                  },
                  ...variants
                    .filter((v) => !v.parent_variant_id)
                    .map((variantOption) => {
                      // Check if this is already used by another unit card
                      const isUsedByOther = unitsList.some(
                        (u, i) => i !== index && u.variant === variantOption.id
                      );

                      // If a variant has a child with original_sku, we should show it as selected
                      const hasChildWithUnit = variants.some(
                        (v) => v.original_sku === variantOption.sku && v.unit
                      );

                      return {
                        id: variantOption.id,
                        name: variantOption.sku,
                        displayValue: variantOption.sku,
                        disabled: isUsedByOther && unit.variant !== variantOption.id,
                      };
                    }),
                ]}
                placeholder={t("pages.products.addManual.sections.variantPlaceholder")}
                searchPlaceholder={t("pages.products.addManual.sections.variantSearchPlaceholder")}
                emptyText={t("pages.products.addManual.sections.variantEmptyText")}
              />
            </div>

            {/* Unit Combobox */}
            <div>
              <div className="mb-1.5 whitespace-nowrap text-sm font-medium">
                {t("pages.products.addManual.sections.units")}
              </div>
              <Combobox
                value={unit.unit || ""}
                onValueChange={(value) => handleUnitChange(index, value)}
                items={allUnits.map((unitOption) => {
                  const isUsedByAll = unitsList.some(
                    (u, i) => i !== index && u.variant === "all" && u.unit === unitOption.id
                  );

                  const isUsedByAnyVariant = variants.some(
                    (v) => v.parent_variant_id && v.unit?.id === unitOption.id
                  );

                  const isUsedBySameVariant = unitsList.some(
                    (u, i) =>
                      i !== index &&
                      u.variant === unit.variant &&
                      u.variant !== "all" &&
                      u.unit === unitOption.id
                  );

                  const isDisabled =
                    isUsedByAll ||
                    (unit.variant === "all" && isUsedByAnyVariant) ||
                    isUsedBySameVariant;

                  return {
                    id: unitOption.id,
                    name: unitOption.name,
                    displayValue: unitOption.name,
                    disabled: isDisabled && unit.unit !== unitOption.id,
                  };
                })}
                placeholder={t("pages.products.addManual.sections.unitPlaceholder")}
                searchPlaceholder={t("pages.products.addManual.sections.unitSearchPlaceholder")}
                emptyText={t("pages.products.addManual.sections.unitEmptyText")}
                onCreateNew={() => setIsDialogOpen(true)}
                onLoadMore={handleLoadMore}
                hasNextPage={hasNextPage}
                isLoadingMore={isFetchingNextPage}
              />
            </div>

            {/* Delete button */}
            <div className="flex items-end">
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteUnit(index);
                }}
                className="text-destructive hover:bg-destructive/10 hover:text-destructive">
                <Trash2 className="size-6 stroke-[1]" />
              </Button>
            </div>
          </div>
        </div>
      ))}

      <Button
        variant="outline"
        className="bg-bg-primary"
        onClick={(e) => {
          e.stopPropagation();
          const newUnit: ProductUnit = {
            id: Math.random().toString(),
            variant: "",
            unit: "",
          };
          setUnitsList([...unitsList, newUnit]);
        }}>
        <Plus className="size-4" />
        {t("pages.products.addManual.sections.addType")}
      </Button>

      {/* Keep the dialog for creating new units */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("pages.products.addManual.sections.addUnit")}</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("pages.products.addManual.sections.name")}
                </label>
                <Input
                  placeholder="KG"
                  value={newUnit.name}
                  onChange={(e) =>
                    setNewUnit((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("pages.products.addManual.sections.ratio")}
                </label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder={t("pages.products.addManual.sections.ratioPlaceholder")}
                  value={newUnit.ratio || ""}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    if (value >= 0) {
                      // Only update if value is non-negative
                      setNewUnit((prev) => ({
                        ...prev,
                        ratio: value,
                      }));
                    }
                  }}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              {t("pages.products.addManual.sections.cancel")}
            </Button>
            <Button onClick={handleAddUnit}>{t("pages.products.addManual.sections.add")}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ConfirmDialog
        open={showDeleteAlert}
        onOpenChange={() => {}}
        variant="destructive"
        title={t("common.areYouSure")}
        description={t("pages.products.descriptionDeleteValueOption")}
        cancelText={t("common.cancel")}
        confirmText={t("common.delete")}
        onCancel={() => setShowDeleteAlert(false)}
        onConfirm={handleConfirmedDelete}
      />
    </div>
  );
}

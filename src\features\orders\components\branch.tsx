"use client";

import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";

import { Combobox } from "@/components/ui/combobox";
import { Location, locationApi } from "@/lib/apis/location";

interface BranchProps {
  onBranchSelect?: (branch: Location) => void;
  initialSelectedBranch?: Location | null;
  disabled?: boolean;
}

export function Branch({ onBranchSelect, initialSelectedBranch, disabled }: BranchProps) {
  const [selectedBranchId, setSelectedBranchId] = useState<string>(initialSelectedBranch?.id || "");

  // Fetch locations
  const { data: locationsData } = useQuery({
    queryKey: ["locations"],
    queryFn: async () => {
      const response = await locationApi.list({ limit: 20 });
      return response;
    },
  });

  const { t } = useTranslation();

  const locations = locationsData?.items || [];

  // Set default location when data is loaded
  useEffect(() => {
    if (locations.length > 0 && !selectedBranchId && !initialSelectedBranch) {
      setSelectedBranchId(locations[0].id);
      onBranchSelect?.(locations[0]);
    } else if (initialSelectedBranch && !selectedBranchId) {
      setSelectedBranchId(initialSelectedBranch.id);
      onBranchSelect?.(initialSelectedBranch);
    }
  }, [locations, selectedBranchId, onBranchSelect, initialSelectedBranch]);

  const handleBranchSelect = (branchId: string) => {
    const branch = locations.find((b) => b.id === branchId);
    if (branch) {
      setSelectedBranchId(branchId);
      onBranchSelect?.(branch);
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-sm font-medium">{t("pages.orders.branch")}</h2>

      <Combobox
        value={selectedBranchId}
        onValueChange={handleBranchSelect}
        items={locations.map((branch) => ({
          id: branch.id,
          name: branch.name,
          displayValue: branch.name,
        }))}
        placeholder={t("pages.orders.loading")}
        searchPlaceholder={t("pages.orders.searchBranch")}
        emptyText={t("pages.orders.noBranchFound")}
      />
    </div>
  );
}

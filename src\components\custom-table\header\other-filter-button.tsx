import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface OtherFilterButtonProps {
  label: string;
  value: string;
  isSelected: boolean;
  isFullWidth?: boolean;
  onClick: (value: string | string[]) => void;
  className?: string;
}

export function OtherFilterButton({
  label,
  value,
  isSelected,
  isFullWidth = false,
  onClick,
  className,
}: OtherFilterButtonProps) {
  return (
    <Button
      variant="secondary"
      className={cn(
        "hover:bg-muted",
        isFullWidth ? "w-full " : "w-fit",
        isSelected && "border !border-primary !bg-neutral-100 hover:!bg-accent",
        className
      )}
      onClick={() => onClick(value)}>
      {label}
    </Button>
  );
}

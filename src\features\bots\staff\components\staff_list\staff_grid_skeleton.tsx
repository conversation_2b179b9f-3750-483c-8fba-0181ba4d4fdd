import { Card } from "@/components/ui";
import { Skeleton } from "@/components/ui/skeleton";

export default function StaffGridSkeleton({ count = 8 }: { count?: number }) {
  return (
    <div className="flex flex-col gap-6">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: count }).map((_, i) => (
          <Card key={i} className="flex flex-col gap-3  p-4">
            <div className="flex items-center gap-3">
              <Skeleton className="h-14 w-14 rounded-lg bg-muted" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-24 rounded" />
                <Skeleton className="h-3 w-16 rounded" />
              </div>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              <Skeleton className="h-5 w-12 rounded" />
              <Skeleton className="h-5 w-16 rounded" />
              <Skeleton className="h-5 w-10 rounded" />
            </div>
            <Skeleton className="mt-4 h-8 w-full rounded" />
          </Card>
        ))}
      </div>
    </div>
  );
}

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";

interface UploadImageParams {
  prefix: string;
  name: string;
  image: string;
}

interface UploadImageResponse {
  company_id: string;
  updated_at: string;
  id: string;
  created_at: string;
  name: string;
  url: string;
  user: null | {
    id: string;
    email: string;
    name: string;
  };
}

export const mediaApi = {
  uploadImage: async (data: UploadImageParams) => {
    const response = await privateApi.post<UploadImageResponse>(ENDPOINTS.MEDIA.UPLOAD_IMAGE, data);
    return response;
  },
};

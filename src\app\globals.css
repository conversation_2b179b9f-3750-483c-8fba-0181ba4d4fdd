@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

html::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
* {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 9999px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
}
/* Hide scrollbar for Firefox */
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none; /* IE and Edge */
}
.hide-scrollbar {
  overflow-x: auto; /* Ensure horizontal scrolling is enabled */
  white-space: nowrap; /* Prevent wrapping of tabs */
}
@layer base {
  :root {
    --primary: 33 96% 52%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 33 76% 62%;
    --primary-light: 225 62% 95%;
    --primary-dark: 227 59% 11%;
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --muted: 240 6% 90%;
    --muted-50: 0 0% 98% / 50%;
    --semi:10 10% 0%;
    --reverse-neutral: 240 10% 26%;
    --dark-primary: 33 4% 98%;

    /* Neutral */
    --50: 0 0% 98%;
    --100: 240 5% 96%;
    --200: 240 6% 90%;
    --300: 240 5% 84%;
    --400: 240 5% 65%;
    --500: 240 4% 46%;
    --600: 240 5% 34%;
    --700: 240 5% 26%;
    --800: 240 4% 16%;
    --900: 240 6% 10%;
    --950: 240 10% 4%;

    /* Sematic */
    --sematic-foreground: 0 0% 100%;
    --destructive: 0 72% 51%;
    --destructive-hover: 0 84% 60%;
    --bg-destructive-10: 0 72% 51% / 10%;
    --bg-destructive-20: 0 72% 51% / 20%;
    --success: 142 76% 36%;
    --success-hover: 142 71% 45%;
    --warning: 38 92% 50%;
    --warning-hover-2: 48 96% 56%;
    --info: 199 89% 48%;
    --info-hover: 198 93% 60%;
    --blue: 226 71% 40%;

    /* Secondary */
    --secondary: 33 100% 52%;
    --secondary-hover: 33 100% 62%;
    --secondary-light: 33 100% 95%;
    --secondary-dark: 34 93% 5%;
    --secondary-foreground: 0 0% 0%;

    --bg-primary: 240 5% 96%;
    --bg-secondary: 0 0% 100%;
    --bg-muted-40: 0 0% 98% / 40%;
    --bg-muted-50: 0 0% 98% / 50%;
    --bg-accent-50: 0 0% 98% / 50%;
    --muted-foreground: 240 4% 46%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --border: 240 5% 84%;
    --border-muted-40: 240 6% 90% / 40%;
    --border-destructive-50: 0 96% 89%;
    --input: 240 6% 90%;
    --accent: 240 1% 90%;
    --accent-foreground: 240 4% 16%;
    --ring: 240 5% 65%;

    --destructive-foreground: 210 40% 98%;

    --success-foreground: 0 0% 100%;

    /* Other variables */

    --table-row-hover: 240 7.7% 94.9%;

    --radius: 0.5rem;

    /* Sidebar specific */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 271 91% 65%;
    --chart-5: 84 81% 44%;

    /* Data visualization */
    --data-1: 173 80% 40%;
    --data-2: 25 95% 53%;
    --data-3: 199 89% 48%;
    --data-4: 271 91% 65%;
    --data-5: 84 81% 44%;
  }

  .dark {
    --primary: 33 96% 52%;
    --primary-foreground: 0 0% 0%;
    --primary-hover: 33 76% 62%;
    --primary-light: 227 59% 11%;
    --primary-dark: 225 62% 95%;
    --background: 229 84% 5%;
    --foreground: 0 0% 100%;
    --muted: 240 4% 16%;
    --muted-50: 217 33% 17% / 0.5;
    --semi: 10 10% 100%;
    --reverse-neutral: 240 2% 84%;
    --dark-primary: 34 96% 5%;

    /* Neutral */
    --50: 240 10% 4%;
    --100: 240 6% 10%;
    --200: 240 4% 16%;
    --300: 240 5% 26%;
    --400: 240 5% 34%;
    --500: 240 4% 46%;
    --600: 240 5% 65%;
    --700: 240 5% 84%;
    --800: 240 6% 90%;
    --900: 240 5% 96%;
    --950: 0 0% 98%;

    /* Sematic */
    --sematic-foreground: 0 0% 0%;
    --destructive: 0 91% 71%;
    --destructive-hover: 0 94% 82%;
    --bg-destructive-10: 0 63% 31% / 10%;
    --bg-destructive-20: 0 63% 31% / 20%;
    --success: 142 69% 58%;
    --success-hover: 142 77% 73%;
    --warning: 48 96% 53%;
    --warning-hover-2: 50 98% 64%;
    --info: 199 95% 74%;
    --info-hover: 201 94% 86%;
    --blue: 213 94% 68%;

    /* Secondary */
    --secondary: 33 100% 62%;
    --secondary-hover: 33 100% 71%;
    --secondary-light: 34 93% 5%;
    --secondary-dark: 33 100% 95%;
    --secondary-foreground: 0 0% 100%;

    --bg-primary: 240 10% 4%;
    --bg-secondary: 240 10% 4%;
    --bg-muted-40: 240 6% 10% / 40%;
    --bg-muted-50: 240 6% 10% / 50%;
    --bg-accent-50: 240 6% 10% / 50%;
    --muted-foreground: 240 4% 46%;
    --card: 240 6% 10%;
    --card-foreground: 0 0% 100%;
    --popover: 240 4% 16%;
    --popover-foreground: 0 0% 100%;
    --border: 240 5% 26%;
    --border-muted-40: 240 5% 26% / 40%;
    --border-destructive-50: 0 70% 35%;
    --input: 240 4% 16%;
    --accent: 240 18% 4%;
    --accent-foreground: 240 6% 90%;
    --ring: 240 5% 34%;

    --destructive-foreground: 210 40% 98%;

    --success-foreground: 0 0% 100%;

    --table-row-hover: 240 4.2% 13.9%;

    /* Sidebar specific dark mode */
    --sidebar-background: 229 84% 5%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217 33% 17%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 20% 65%;
    --sidebar-ring: 215 20% 65%;

    /* Chart colors dark mode */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Data visualization dark */
    --data-1: 201 96% 42%;
    --data-2: 142 71% 55%;
    --data-3: 355 78% 66%;
    --data-4: 27 87% 77%;
    --data-5: 248 83% 68%;
    --data-6: 173 58% 49%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none; /* IE and Edge */
}

.input:-webkit-autofill,
.input:-webkit-autofill:hover,
.input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  -webkit-text-fill-color: hsl(var(--foreground)) !important;
  caret-color: hsl(var(--foreground)) !important;
  transition: background-color 5000s ease-in-out 0s;
}

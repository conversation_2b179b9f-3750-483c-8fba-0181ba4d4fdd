import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get component name from command line argument
const componentName = process.argv[2];

if (!componentName) {
  console.error('Please provide a component name');
  process.exit(1);
}

// Define paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const templateDir = path.join(__dirname, '..', '.templates', 'ComponentTemplate');
const targetDir = path.join(__dirname, '..', 'src', 'components', componentName);

// Create target directory if it doesn't exist
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Create designs directory
const designsDir = path.join(targetDir, 'designs');
if (!fs.existsSync(designsDir)) {
  fs.mkdirSync(designsDir, { recursive: true });
}

// Copy template files
const templateFiles = fs.readdirSync(templateDir);

// File name mapping
const fileNameMap = {
  'Component.tsx': `${componentName}.tsx`,
  'Component.test.tsx': `${componentName}.test.tsx`,
  'README.md': `${componentName}.md`,
  'ai-prompts.md': 'ai-prompts.md'
};

templateFiles.forEach(file => {
  // Skip ai-prompts.md
  if (file === 'ai-prompts.md') {
    return;
  }

  const sourcePath = path.join(templateDir, file);
  const targetPath = path.join(targetDir, fileNameMap[file] || file);

  // Read the template file
  let content = fs.readFileSync(sourcePath, 'utf8');

  // Replace placeholder with actual component name
  content = content.replace(/ComponentTemplate/g, componentName);

  // Write the file
  fs.writeFileSync(targetPath, content);
});

console.log(`
✨ Component ${componentName} created successfully!

Next steps:
1. Add your design files to: src/components/${componentName}/designs/
2. Update ${componentName}.md with your component's requirements
3. Follow the development process:
   - Analyze designs and update requirements
   - Generate and implement test cases
   - Implement the component
   - Register component in demo system
   
You can check your component at:
- Demo: http://localhost:3000/demo/${componentName}

Run tests with: npm test ${componentName}
`); 
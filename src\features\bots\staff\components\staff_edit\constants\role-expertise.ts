export const ROLE_EXPERTISE: Record<string, string[]> = {
  VIRTUAL_STAFF: [],
  CUSTOMER_SUPPORT_AGENT: [
    "PRODUCT_SERVICE_KNOWLEDGE",
    "ISSUE_DIAGNOSIS_TROUBLESHOOTING",
    "<PERSON><PERSON>AT<PERSON><PERSON>IC_COMMUNICATION",
    "MU<PERSON><PERSON>INGUAL_SUPPORT",
    "TICKETING_SYSTEM_INTEGRATION",
    "ESCALATION_MANAGEMENT",
  ],
  SALES_ASSISTANT: [
    "LEAD_QUALIFICATION",
    "PRODUCT_RECOMMENDATION",
    "OBJECTION_HANDLING",
    "PRICING_PROMOTION_STRATEGIES",
    "CRM_INTEGRATION",
    "UPSELLING_CROSS_SELLING",
  ],
  VIRTUAL_PERSONAL_ASSISTANT: [
    "CALENDAR_SCHEDULE_MANAGEMENT",
    "EMAIL_INBOX_TRIAGE",
    "TASK_REMINDER_AUTOMATION",
    "TRAVEL_PLANNING",
    "CONTACT_MANAGEMENT",
    "NOTE_TAKING_SUMMARY",
  ],
  TECH<PERSON>CAL_SUPPORT_SPECIALIST: [
    "SYSTEM_DIAGNOSTICS",
    "ERROR_LOG_ANALYSIS",
    "API_SDK_GUIDANCE",
    "SOFTWARE_INSTALLATION_SETUP",
    "CONFIGURATION_MANAGEMENT",
    "REMOTE_TROUBLESHOOTING",
  ],
  HR_RECRUITMENT_ASSISTANT: [
    "JOB_DESCRIPTION_DRAFTING",
    "RESUME_SCREENING",
    "CANDIDATE_PRE_QUALIFICATION",
    "INTERVIEW_SCHEDULING",
    "ASSESSMENT_TESTING_SUPPORT",
    "ONBOARDING_WORKFLOW_AUTOMATION",
  ],
  MARKETING_ASSISTANT: [
    "CAMPAIGN_PLANNING",
    "SOCIAL_MEDIA_MANAGEMENT",
    "CONTENT_CALENDAR_GENERATION",
    "AUDIENCE_SEGMENTATION",
    "PERFORMANCE_ANALYTICS",
    "AB_TESTING_SUPPORT",
  ],
  CONTENT_CREATOR: [
    "TOPIC_RESEARCH",
    "SEO_OPTIMIZATION",
    "TONE_STYLE_ADAPTATION",
    "DRAFT_GENERATION",
    "REVISION_EDITING",
    "MULTIMEDIA_INTEGRATION",
  ],
  DATA_ANALYST: [
    "DATA_CLEANING_ETL",
    "STATISTICAL_ANALYSIS",
    "TREND_IDENTIFICATION",
    "VISUALIZATION_RECOMMENDATIONS",
    "DASHBOARD_INTEGRATION",
    "REPORTING_AUTOMATION",
  ],
  EDUCATIONAL_TUTOR: [
    "CURRICULUM_BREAKDOWN",
    "CONCEPT_EXPLANATION",
    "PRACTICE_QUESTION_GENERATION",
    "ADAPTIVE_LEARNING_PATHS",
    "PROGRESS_ASSESSMENT",
    "FEEDBACK_ENCOURAGEMENT",
  ],
  SCHEDULING_ASSISTANT: [
    "AVAILABILITY_MATCHING",
    "TIME_ZONE_HANDLING",
    "CONFLICT_DETECTION",
    "RESCHEDULING_PROPOSALS",
    "REMINDER_NOTIFICATIONS",
    "CALENDAR_INTEGRATION",
  ],
  RESEARCH_ASSISTANT: [
    "LITERATURE_SEARCH",
    "SOURCE_SUMMARIZATION",
    "CITATION_FORMATTING",
    "DATA_EXTRACTION",
    "HYPOTHESIS_GENERATION",
    "REPORT_STRUCTURING",
  ],
  FINANCIAL_ADVISOR: [
    "BUDGET_FORECASTING",
    "INVESTMENT_STRATEGY_GUIDANCE",
    "RISK_ASSESSMENT",
    "PORTFOLIO_ANALYSIS",
    "MARKET_TREND_SUMMARIES",
    "REGULATORY_COMPLIANCE_AWARENESS",
  ],
  VIRTUAL_TRAVEL_AGENT: [
    "ITINERARY_PLANNING",
    "BOOKING_MANAGEMENT",
    "PRICE_COMPARISON",
    "VISA_ENTRY_REQUIREMENTS",
    "DESTINATION_RECOMMENDATIONS",
    "REAL_TIME_TRAVEL_ALERTS",
  ],
  LEGAL_ASSISTANT: [
    "CONTRACT_REVIEW_ANALYSIS",
    "LEGAL_RESEARCH_SUMMARIZATION",
    "COMPLIANCE_REGULATORY_CHECKS",
    "DOCUMENT_DRAFTING",
    "CASE_LAW_RETRIEVAL",
    "COURT_DEADLINE_TRACKING",
  ],
  CODE_REVIEW_SPECIALIST: [
    "STATIC_CODE_ANALYSIS",
    "SECURITY_VULNERABILITY_DETECTION",
    "CODING_STANDARDS_ENFORCEMENT",
    "PERFORMANCE_OPTIMIZATION_SUGGESTIONS",
    "DOCUMENTATION_REVIEW",
    "REFACTORING_RECOMMENDATIONS",
  ],
  HEALTHCARE_COACH: [
    "LIFESTYLE_ASSESSMENT",
    "HEALTH_GOAL_TRACKING",
    "NUTRITION_PLANNING",
    "EXERCISE_GUIDANCE",
    "APPOINTMENT_REMINDERS",
    "WELLNESS_TIPS_EDUCATION",
  ],
  MENTAL_HEALTH_COMPANION: [
    "MOOD_TRACKING",
    "CBT_BASED_PROMPTING",
    "RELAXATION_MINDFULNESS_EXERCISES",
    "CRISIS_RESOURCE_REFERRALS",
    "PERSONALIZED_COPING_STRATEGIES",
    "EMPATHETIC_CONVERSATIONAL_SUPPORT",
  ],
  VIRTUAL_EVENT_PLANNER: [
    "VENUE_SOURCING",
    "GUEST_LIST_MANAGEMENT",
    "SCHEDULE_COORDINATION",
    "VENDOR_COMMUNICATION",
    "BUDGET_TRACKING",
    "EVENT_PROMOTION",
  ],
  REAL_ESTATE_ADVISOR: [
    "PROPERTY_VALUATION_ANALYSIS",
    "MARKET_TREND_REPORTING",
    "MORTGAGE_FINANCING_CALCULATIONS",
    "NEIGHBORHOOD_INSIGHTS",
    "PROPERTY_SEARCH_ASSISTANCE",
    "LEGAL_COMPLIANCE_GUIDANCE",
  ],
  SECURITY_ANALYST: [
    "THREAT_INTELLIGENCE_GATHERING",
    "VULNERABILITY_SCANNING",
    "INCIDENT_RESPONSE_GUIDANCE",
    "SECURITY_POLICY_RECOMMENDATIONS",
    "COMPLIANCE_REPORTING",
    "RISK_ASSESSMENT",
  ],
  UX_UI_DESIGNER_AGENT: [
    "USER_RESEARCH_SYNTHESIS",
    "WIREFRAME_PROTOTYPE_GENERATION",
    "ACCESSIBILITY_COMPLIANCE_CHECKING",
    "VISUAL_DESIGN_GUIDANCE",
    "USABILITY_TESTING_SUGGESTIONS",
    "INTERACTION_FLOW_OPTIMIZATION",
  ],
  PROJECT_MANAGEMENT_ASSISTANT: [
    "TASK_BREAKDOWN_SCHEDULING",
    "RESOURCE_ALLOCATION_SUGGESTIONS",
    "MILESTONE_TRACKING",
    "RISK_IDENTIFICATION_MITIGATION",
    "STAKEHOLDER_COMMUNICATION_SUMMARIES",
    "PROJECT_DOCUMENTATION_GENERATION",
  ],
};

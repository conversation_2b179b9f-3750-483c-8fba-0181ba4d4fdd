"use client";

import { motion, Variants } from "framer-motion";

function LoadingThreeDotsJumping() {
  const dotVariants: Variants = {
    jump: {
      y: -3,
      transition: {
        duration: 0.8,
        repeat: Infinity,
        repeatType: "mirror",
        ease: "easeInOut",
      },
    },
  };

  return (
    <motion.div
      animate="jump"
      transition={{ staggerChildren: -0.2, staggerDirection: -1 }}
      className="flex  justify-center items-center gap-1.5">
      <motion.div className="size-2 rounded-full bg-current" variants={dotVariants} />
      <motion.div className="size-2 rounded-full bg-current" variants={dotVariants} />
      <motion.div className="size-2 rounded-full bg-current" variants={dotVariants} />
    </motion.div>
  );
}

export default LoadingThreeDotsJumping;

import { z } from "zod";

const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const loginSchema = z.object({
  username: z
    .string()
    .min(1, "validation.usernameRequired")
    .superRefine((value, ctx) => {
      if (!value) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "validation.usernameRequired",
        });
        return;
      }

      if (value.includes("@")) {
        if (!emailRegex.test(value)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "validation.invalidEmail",
          });
        }
      } else if (value.length < 3) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "validation.invalidUsername",
        });
      }
    }),
  password: z.string().min(1, "validation.passwordRequired"),
});

export type LoginFormValues = z.infer<typeof loginSchema>;

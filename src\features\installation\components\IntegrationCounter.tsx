import React from "react";

import { Button } from "@/components/ui/button";

interface IntegrationCounterProps {
  planId: string;
  integrations: Record<string, number>;
  handleIncrement: (plan: string, e: React.MouseEvent) => void;
  handleDecrement: (plan: string, e: React.MouseEvent) => void;
  minQuantity?: number;
  disabled?: boolean;
  isPopular?: boolean;
}

export const IntegrationCounter: React.FC<IntegrationCounterProps> = ({
  planId,
  integrations,
  handleIncrement,
  handleDecrement,
  minQuantity = 1,
  disabled = false,
  isPopular = false,
}) => {
  const canDecrement = integrations[planId] > minQuantity;

  return (
    <div
      className={`inline-flex h-10 items-center rounded-lg border bg-transparent text-foreground ${
        isPopular ? "border-reverseNeutral" : "border-neutral-200"
      }`}>
      <Button
        variant="ghost"
        size="icon"
        onClick={(e) => {
          if (canDecrement && !disabled) {
            handleDecrement(planId, e);
          }
        }}
        disabled={!canDecrement || disabled}
        className={`ml-1 aspect-square size-8 rounded-lg ${
          isPopular ? "bg-reverseNeutral" : "bg-neutral-200 hover:bg-neutral-300"
        }
          ${!canDecrement || disabled ? "cursor-not-allowed" : ""}`}>
        −
      </Button>

      <div
        className={`w-6 grow px-1 text-center text-sm font-medium ${
          isPopular ? "text-white dark:text-black" : "text-foreground"
        }`}>
        {integrations[planId]}
      </div>

      <Button
        variant="ghost"
        size="icon"
        onClick={(e) => {
          if (!disabled) {
            handleIncrement(planId, e);
          }
        }}
        disabled={disabled}
        className={`mr-1 aspect-square size-8 rounded-lg ${
          isPopular ? "bg-reverseNeutral" : "bg-neutral-200 hover:bg-neutral-300"
        }
          ${disabled ? "cursor-not-allowed" : ""}`}>
        +
      </Button>
    </div>
  );
};

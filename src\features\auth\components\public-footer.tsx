"use client";

import { useTranslation } from "react-i18next";

export function Footer({ className }: { className?: string }) {
  const { t } = useTranslation();

  return (
    <div
      className={`relative z-10 text-center text-sm text-muted-foreground lg:text-right ${className}`}>
      {t("footer.crafted")}{" "}
      <span role="img" aria-label={t("footer.heart")} className="text-red-500">
        ❤️
      </span>{" "}
      {t("footer.by")} {t("footer.team")}
    </div>
  );
}

import { useQuery } from "@tanstack/react-query";

import inventoryApi from "@/lib/apis/inventory";

import { inventoryKeys } from "./key";

export function useInventory(productId: string) {
  return useQuery({
    queryKey: inventoryKeys.detail(productId),
    queryFn: () => {
      return inventoryApi.getInventory(productId);
    },
    enabled: Bo<PERSON><PERSON>(productId),
  });
}

export function useInventoryItems(sku: string, sortCreatedAt: "asc" | "desc" = "desc") {
  const enabled = Boolean(sku);

  return useQuery({
    queryKey: [...inventoryKeys.items(sku), { sort: sortCreatedAt }],
    queryFn: () => {
      return inventoryApi.getInventoryItems(sku, sortCreatedAt);
    },
    enabled,
  });
}

export function useInventoryTransactions(itemId: string, sortCreatedAt: "asc" | "desc" = "desc") {
  const enabled = Boolean(itemId);

  return useQuery({
    queryKey: [...inventoryKeys.transactions(itemId), { sort: sortCreatedAt }],
    queryFn: () => {
      return inventoryApi.getInventoryTransactions(itemId, "", sortCreatedAt);
    },
    enabled,
  });
}

---
description: 
globs: 
alwaysApply: false
---
# Development Guidelines

## Code Style and Quality

- TypeScript is used throughout the project for type safety
- <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> are configured for code quality and formatting
- Follow the existing component structure and naming conventions
- Use the provided hooks and utilities from `src/hooks` and `src/utils`

## Component Organization

- Place reusable UI components in `src/components`
- Feature-specific components go in `src/features`
- Follow the established component file structure:
    - One component per file
    - Use TypeScript interfaces for props
    - Include relevant tests
    - Keep components focused and maintainable

## Testing Requirements

- Write unit tests for new components and utilities
- Place E2E tests in the `E2Etests` directory
- Use Jest and React Testing Library for unit tests
- Follow the existing test patterns and naming conventions

## Performance Guidelines

- Optimize images and assets
- Use proper code splitting and lazy loading
- Follow Next.js best practices for performance
- Implement proper caching strategies

## Internationalization

- Use the i18n system for all user-facing text
- Add new translations to `src/i18n/locales/en.json` and `src/i18n/locales/vi.json`
- Follow the established translation key structure


import React, { useMemo } from "react";
import {
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
} from "recharts";

export interface RadarChartData {
  subject: string;
  value: number;
}

interface CustomRadarChartProps {
  width?: number;
  height?: number;
  radarData?: RadarChartData[];
  layers?: number;
  color?: string;
}

const CustomRadarChart: React.FC<CustomRadarChartProps> = ({
  width = 350,
  height = 230,
  radarData,
}) => {
  const formatRadarData = useMemo(
    () =>
      radarData?.map((item) => ({
        ...item,
        firstLayer: 25,
        secondLayer: 50,
        thirdLayer: 75,
        fourthLayer: 100,
      })),
    [radarData]
  );
  return (
    <ResponsiveContainer width={width} height={height}>
      <RadarChart cx="50%" cy="50%" outerRadius="80%" className="text-wrap" data={formatRadarData}>
        <PolarGrid stroke="rgba(212,212,216,0.2)" />
        <PolarAngleAxis dataKey="subject" tick={{ fontSize: 12, fill: "#888" }} />
        <PolarRadiusAxis angle={20} domain={[0, 100]} tick={false} axisLine={false} />
        <Radar name="Score" dataKey="value" stroke="#fa900f " fill="rgba(250,144,15,0.5)" />
        <Radar name="Score" dataKey="firstLayer" fill="#fa900f" opacity={0.2} />
        <Radar name="Score" dataKey="secondLayer" fill="#fa900f" opacity={0.2} />
        <Radar name="Score" dataKey="thirdLayer" fill="#fa900f" opacity={0.2} />
        <Radar name="Score" dataKey="fourthLayer" fill="#fa900f" opacity={0.2} />
      </RadarChart>
    </ResponsiveContainer>
  );
};

export default CustomRadarChart;

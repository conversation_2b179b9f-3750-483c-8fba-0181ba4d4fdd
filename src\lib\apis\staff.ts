import { CreateStaffPayload, VirtualStaffModel } from "@/features/bots/staff/hooks/type";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi, publicApi } from "../api_helper";
import { ResponseAxiosDetail, ResponseList } from "./types/common";

export const staffApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<VirtualStaffModel>>(ENDPOINTS.STAFF.LIST, {
      params,
    });
  },

  create: async (data: Partial<CreateStaffPayload>) => {
    return await privateApi.post<VirtualStaffModel>(ENDPOINTS.STAFF.CREATE, data);
  },

  update: async (id: string, data: Record<string, any>) => {
    const url = ENDPOINTS.STAFF.UPDATE.replace(":id", id);
    return await privateApi.put<VirtualStaffModel>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.STAFF.DELETE.replace(":id", id);
    return await privateApi.delete(url);
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.STAFF.DETAIL.replace(":id", id);
    return await privateApi.get<ResponseAxiosDetail<VirtualStaffModel>>(url);
  },

  getPublicById: async (id: string) => {
    const url = ENDPOINTS.STAFF.PUBLIC_DETAIL.replace(":id", id);
    return await publicApi.get<ResponseAxiosDetail<VirtualStaffModel>>(url);
  },
};

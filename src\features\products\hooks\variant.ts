import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { variantApi } from "@/lib/apis/product";

import { variantKeys } from "./keys";

interface UseVariantsOptions {
  query?: string;
  enabled?: boolean;
}

export function useVariants(options: UseVariantsOptions = {}) {
  const { query = "", enabled = true } = options;

  const result = useInfiniteQuery({
    queryKey: variantKeys.list({ query }),
    queryFn: async ({ pageParam = 0 }) => {
      // First load - just use query parameter
      if (pageParam === 0) {
        const response = await variantApi.list({ query });
        return { ...response, pageRequested: 0 };
      }

      // Paginated requests - follow correct sequence
      // First paginated request is page 1, then 2, 3, etc.
      const page = Math.max(1, Number(pageParam));

      const response = await variantApi.list({
        query,
        limit: 20,
        page,
      });

      return { ...response, pageRequested: page };
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage?.total) return undefined;

      const totalItems = lastPage.total;
      const itemsPerPage = 20;
      const totalPages = Math.ceil(totalItems / itemsPerPage);

      // First request was without page - next should be page 1
      if (lastPage.pageRequested === 0) {
        return 1;
      }

      // For pages 1 and up, increment as normal
      const currentPage = Number(lastPage.pageRequested);
      const hasMore = currentPage < totalPages;
      const nextPage = hasMore ? currentPage + 1 : undefined;

      return nextPage;
    },
    initialPageParam: 0,
    enabled,
  });

  const variants = result.data?.pages.flatMap((page) => page.items) ?? [];

  return {
    ...result,
    variants,
  };
}

export function useVariant(id: string) {
  return useQuery({
    queryKey: variantKeys.detail(id),
    queryFn: async () => {
      const response = await variantApi.list({ id });
      return response.items[0];
    },
    enabled: !!id,
  });
}

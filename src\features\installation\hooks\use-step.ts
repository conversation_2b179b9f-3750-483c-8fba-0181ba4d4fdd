import { useState } from "react";

interface UseStepsReturn {
  activeStep: number;
  setActiveStep: (step: number) => void;
  steps: string[];
  isFirstStep: boolean;
  isLastStep: boolean;
}

export const useSteps = (initialSteps: string[], defaultActiveStep?: number): UseStepsReturn => {
  // If no default step provided, start at index 0
  const initialActiveStep = defaultActiveStep ?? 0;

  // Validate that the default step is within the steps array
  if (
    defaultActiveStep !== undefined &&
    (defaultActiveStep < 0 || defaultActiveStep >= initialSteps.length)
  ) {
    throw new Error("Default active step must be a valid index in the steps array");
  }

  const [activeStep, setActiveStep] = useState<number>(initialActiveStep);
  const [steps] = useState<string[]>(initialSteps);

  const safeSetActiveStep = (step: number) => {
    // Ensure only steps within the array index can be set as active
    if (step >= 0 && step < steps.length) {
      setActiveStep(step);
    } else {
      throw new Error("Cannot set an active step outside of the steps array bounds");
    }
  };

  return {
    activeStep,
    setActiveStep: safeSetActiveStep,
    steps,
    isFirstStep: activeStep === 0,
    isLastStep: activeStep === steps.length - 1,
  };
};

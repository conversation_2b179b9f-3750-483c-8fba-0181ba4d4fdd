import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatPrice, parsePrice } from "@/utils/helpers/price-formater";

interface AdjustPriceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentPrice: number;
  currentDiscount?: number | string;
  onPriceChange: (prices: { originalPrice: number; discountedPrice: number }) => void;
  trigger?: React.ReactNode;
}

export function AdjustPriceDialog({
  open,
  onOpenChange,
  currentPrice,
  currentDiscount = 0,
  onPriceChange,
  trigger,
}: AdjustPriceDialogProps) {
  const { t } = useTranslation();
  const [price, setPrice] = useState(currentPrice.toString());
  const [discountType, setDiscountType] = useState<"value" | "percent">("value");
  const [discountValue, setDiscountValue] = useState(currentDiscount.toString());

  const handleApply = () => {
    const originalPrice = parsePrice(price);
    let discountedPrice = originalPrice;

    if (discountType === "value") {
      discountedPrice = Math.max(0, originalPrice - parsePrice(discountValue));
    } else {
      const percent = parseFloat(discountValue) || 0;
      discountedPrice = Math.max(0, originalPrice * (1 - percent / 100));
    }

    onPriceChange({ originalPrice, discountedPrice });
    onOpenChange(false);
  };
  // Add useEffect to apply initial discount
  useEffect(() => {
    if (currentDiscount && Number(currentDiscount) > 0) {
      handleApply();
    }
  }, []); // Run once on mount

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      {trigger && <PopoverTrigger asChild>{trigger}</PopoverTrigger>}
      <PopoverContent className="w-[350px]">
        <div className="space-y-4">
          <h4 className="font-medium">{t("pages.orders.adjustPrice")}</h4>
          <div className="grid gap-3">
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">{t("pages.orders.price")}</span>
                <Input
                  type="text"
                  value={formatPrice(price)}
                  onChange={(e) => setPrice(e.target.value)}
                  suffix="đ"
                  className="w-auto"
                />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">{t("pages.orders.discount")}</span>
                <div className="flex w-[240px] items-center gap-2">
                  <Input
                    value={formatPrice(discountValue)}
                    onChange={(e) => setDiscountValue(e.target.value)}
                    suffix={discountType === "value" ? "đ" : "%"}
                    className="flex-1"
                  />
                  <Tabs
                    value={discountType}
                    onValueChange={(value) => setDiscountType(value as "value" | "percent")}
                    className="w-auto">
                    <TabsList className="h-10 grid-cols-2">
                      <TabsTrigger value="value" className="text-xs">
                        {t("pages.orders.value")}
                      </TabsTrigger>
                      <TabsTrigger value="percent" className="text-xs">
                        %
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" size="sm" onClick={() => onOpenChange(false)}>
                {t("common.cancel")}
              </Button>
              <Button type="button" size="sm" onClick={handleApply}>
                {t("common.apply")}
              </Button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

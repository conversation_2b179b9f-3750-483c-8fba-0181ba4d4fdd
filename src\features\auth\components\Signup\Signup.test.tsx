import { fireEvent, render, screen, waitFor } from "@testing-library/react";

import "@testing-library/jest-dom";

import { I18nextProvider } from "react-i18next";

import i18n from "@/i18n";

import { Signup } from "./Signup";

jest.mock("@/assets/images/vcare-text.svg", () => "mock-svg");
jest.mock("@/assets/images/logo.png", () => "mock-logo");
jest.mock("@/assets/images/bg.png", () => "mock-bg");

// ✅ Mock Next.js Router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(), // Mock navigation
    replace: jest.fn(),
    prefetch: jest.fn(),
    pathname: "/signup",
  }),
}));

describe("Component", () => {
  it("renders correctly", () => {
    const { container } = render(
      <I18nextProvider i18n={i18n}>
        <Signup />
      </I18nextProvider>
    );

    // Find the input by its name attribute
    const email = container.querySelector('input[name="email"]');
    expect(email).toBeInTheDocument();

    const username = container.querySelector('input[name="username"]');
    expect(username).toBeInTheDocument();

    const password = container.querySelector('input[name="password"]');
    expect(password).toBeInTheDocument();

    const confirmPassword = container.querySelector('input[name="confirmPassword"]');
    expect(confirmPassword).toBeInTheDocument();

    expect(screen.getByRole("button", { name: "Sign up" })).toBeInTheDocument();
  });
});

describe("Validate", () => {
  it("validate for empty fields", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <Signup />
    //   </Provider>
    // );

    fireEvent.click(screen.getByRole("button", { name: "Sign up" }));

    await waitFor(() => {
      expect(screen.getByText("Invalid email address")).toBeInTheDocument();
      expect(screen.getByText("Username must be at least 3 characters")).toBeInTheDocument();
      expect(screen.getByText("Password must be at least 8 characters")).toBeInTheDocument();
    });
  });

  it("validate for email", async () => {
    const { container } = render(
      <I18nextProvider i18n={i18n}>
        <Signup />
      </I18nextProvider>
    );

    // Find the input by its name attribute
    const email = container.querySelector('input[name="email"]');
    if (email) {
      // Check if email is not null
      fireEvent.change(email, {
        target: { value: "ab" },
      });
    }

    fireEvent.click(screen.getByRole("button", { name: "Sign up" }));

    expect(email).toBeInvalid();
  });

  it("validate password don't match", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <Signup />
    //   </Provider>
    // );

    const password = screen.getByLabelText("Password");
    const confirmPassword = screen.getByLabelText("Confirm Password");
    const submitButton = screen.getByRole("button", { name: "Sign up" });

    fireEvent.change(password, { target: { value: "adminadmin" } });
    fireEvent.change(confirmPassword, { target: { value: "adminadmin2" } });

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/passwords don't match/i)).toBeInTheDocument();
    });
  });

  it("shows validation error for short username", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <Signup />
    //   </Provider>
    // );

    fireEvent.change(screen.getByLabelText("Username"), {
      target: { value: "ab" },
    });

    const submitButton = screen.getByRole("button", { name: "Sign up" });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText("Username must be at least 3 characters")).toBeInTheDocument();
    });
  });

  it("validate for submit", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <Signup />
    //   </Provider>
    // );

    fireEvent.change(screen.getByLabelText("Email"), {
      target: { value: "<EMAIL> " },
    });
    fireEvent.change(screen.getByLabelText("Username"), { target: { value: "trannhon" } });
    fireEvent.change(screen.getByLabelText("Password"), { target: { value: "Admin@123" } });
    fireEvent.change(screen.getByLabelText("Confirm Password"), { target: { value: "Admin@123" } });

    const submitButton = screen.getByRole("button", { name: "Sign up" });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(submitButton).toHaveTextContent("Loading");
    });
  });
});

// npm test -- features/auth/components/Signup/Signup.test.tsx

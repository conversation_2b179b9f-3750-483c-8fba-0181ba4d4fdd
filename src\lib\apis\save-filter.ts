/* eslint-disable @typescript-eslint/no-unused-vars */
import { SAVE_FILTER_ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";

export interface SaveFilterPayload {
  name: string;
  type: string;
  filters: Record<string, unknown>;
}

export interface SaveFilterItem {
  id: string;
  company_id: string;
  user_id: string;
  name: string;
  icon?: string;
  type: string;
  filters: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface SaveFilterResponse {
  total: number;
  page: number;
  limit: number;
  items: SaveFilterItem[];
}

export const saveFilterApi = {
  get: async ({ id, params }: { id: string; params?: Record<string, unknown> }) => {
    return privateApi.get<SaveFilterResponse>(`${SAVE_FILTER_ENDPOINTS.GET_LIST}/${id}`, {
      params,
    });
  },

  create: async (data: SaveFilterPayload) => {
    return privateApi.post<SaveFilterItem>(SAVE_FILTER_ENDPOINTS.CREATE, data);
  },

  update: async (data: SaveFilterItem[], filterType: string) => {
    return privateApi.put<SaveFilterItem[]>(`${SAVE_FILTER_ENDPOINTS.UPDATE}/${filterType}`, data);
  },

  delete: async ({ id }: { id: string }) => {
    return privateApi.delete(`${SAVE_FILTER_ENDPOINTS.DELETE}/${id}`);
  },
};

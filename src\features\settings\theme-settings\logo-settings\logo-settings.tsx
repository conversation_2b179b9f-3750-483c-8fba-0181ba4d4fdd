import { t } from "i18next";

import { useLogoSetting } from "@/features/settings/hooks/logo-setting";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";

import { logoConfigs, LogoType } from "./logo-config";

interface LogoUploadCardProps {
  type: LogoType;
  title: string;
  description: string;
  form: any; // TODO: Add proper form type
}

const LogoUploadCard = ({ type, title, description, form }: LogoUploadCardProps) => (
  <Card>
    <CardHeader>
      <CardTitle>{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <FormField
        control={form.control}
        name={type}
        render={({ field }) => {
          return (
            <FormItem>
              <FormLabel className="sr-only">{title}</FormLabel>
              <FormControl>
                <ImageUpload value={field.value} onChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    </CardContent>
  </Card>
);

export default function LogoSetting() {
  const { form, onSubmit, handleReset, isLoading } = useLogoSetting();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {logoConfigs.map((config) => (
            <div key={config.type}>
              <LogoUploadCard
                type={config.type}
                title={t(config.titleKey)}
                description={t(config.descriptionKey)}
                form={form}
              />
            </div>
          ))}
        </div>

        <div className="flex justify-end space-x-4">
          <Button disabled={isLoading} type="button" variant="outline" onClick={handleReset}>
            {t("common.reset")}
          </Button>
          <Button disabled={isLoading} loading={isLoading} type="submit">
            {t("common.save")}
          </Button>
        </div>
      </form>
    </Form>
  );
}

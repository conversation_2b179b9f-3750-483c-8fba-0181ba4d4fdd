"use client";

import { useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";

import { useBrands, useCreateBrand, useUpdateBrand } from "@/features/brand/hooks/brand";
import { Brand } from "@/features/brand/hooks/types";

import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { uploadImage } from "@/lib/apis/images";

interface AddBrandDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: {
    name: string;
    image: { id: string; name: string; url: string } | null;
  }) => Promise<any>;
  initialName?: string;
  onClose: () => void;
  brandToEdit?: Brand;
  isEdit?: boolean;
}

export function AddBrandDialog({
  open = true,
  onOpenChange,
  onSubmit,
  initialName = "",
  onClose,
  brandToEdit,
  isEdit = false,
}: AddBrandDialogProps) {
  const [name, setName] = useState("");
  const [image, setImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isOpen, setIsOpen] = useState(open);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { refetch } = useBrands({ enabled: false });

  const createBrand = useCreateBrand({
    onSuccess: () => {
      refetch();
      handleClose();
    },
  });

  const updateBrand = useUpdateBrand({
    onSuccess: () => {
      refetch();
      handleClose();
    },
  });

  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  useEffect(() => {
    if (isOpen) {
      if (isEdit && brandToEdit) {
        setName(brandToEdit.name);
        setImage(brandToEdit.image?.url || null);
      } else if (initialName) {
        setName(initialName);
      }
    }
  }, [isOpen, initialName, isEdit, brandToEdit]);

  const handleImageChange = async (base64: string | null) => {
    if (!base64) {
      setImage(null);
      return;
    }

    try {
      setIsUploading(true);
      const fileName = `brand_${Date.now()}.png`;
      const uploadedImage = await uploadImage({
        name: fileName,
        image: base64,
        prefix: "media",
      });
      setImage(uploadedImage.url);
    } catch (error) {
      console.error("Failed to upload image:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      if (onSubmit) {
        const result = await onSubmit({
          name,
          image: image ? { id: "1", name: "image", url: image } : null,
        });

        handleClose();

        return result;
      } else if (isEdit && brandToEdit) {
        const imageObject = image
          ? {
              url: image,
              id: brandToEdit.image?.id || crypto.randomUUID(),
              name: brandToEdit.image?.name || `brand_${Date.now()}.png`,
            }
          : null;

        await updateBrand.mutateAsync({
          id: brandToEdit.id,
          data: {
            name,
            image: imageObject,
          },
        });
      } else {
        const id = crypto.randomUUID();
        await createBrand.mutateAsync({
          name,
          image: image
            ? {
                url: image,
                id,
                name: `brand_${Date.now()}.png`,
              }
            : null,
        });
      }
    } catch (error) {
      console.error("Failed to save brand:", error);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    if (onOpenChange) {
      onOpenChange(false);
    }
    onClose();
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(state) => {
        if (!state) handleClose();
        else if (onOpenChange) onOpenChange(state);
        setIsOpen(state);
      }}>
      <DialogContent>
        <DialogHeader className="pb-6">
          <DialogTitle>{isEdit ? t("common.edit") : t("common.add")}</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col">
          <div className="space-y-2">
            <Label className="text-sm">{t("pages.products.name")}</Label>
            <Input
              placeholder={t("pages.products.name")}
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div className="space-y-2 py-6">
            <Label>{t("pages.products.addManual.basicInfo.images")}</Label>
            <ImageUpload value={image} onChange={handleImageChange} className="h-[200px]" />
          </div>
          <Button
            className="w-24 self-end"
            onClick={handleSubmit}
            loading={createBrand.isPending || updateBrand.isPending}
            uploading={isUploading}
            disabled={!name.trim()}>
            {isEdit ? t("common.update") : t("common.add")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

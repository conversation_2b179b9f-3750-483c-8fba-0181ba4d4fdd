import { Globe, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";

import { ClipboardCopy } from "@/components/clipboard-copy";
import { Form, FormField, Input, Label } from "@/components/ui";
import { Button } from "@/components/ui/button";

interface URLUploaderProps {
  urls: string[];
  setUrls: (urls: string[]) => void;
  form: any;
  isUploading: boolean;
}

const URLUploader = ({ urls, setUrls, form, isUploading }: URLUploaderProps) => {
  const { t } = useTranslation();
  // Kiểm tra URL hợp lệ
  const isValidUrl = (urlString: string) => {
    try {
      new URL(urlString);
      return true;
    } catch (e) {
      return false;
    }
  };
  // Thêm URL nếu hợp lệ và chưa trùng
  const handleAddUrl = () => {
    const trimmed = form.getValues().url.trim();
    if (!trimmed) return;
    if (!isValidUrl(trimmed)) {
      form.setError("url", {
        message: t("pages.knowledge.upload.invalidUrl"),
      });
      return;
    }
    if (urls.includes(trimmed)) {
      form.setError("url", {
        message: t("pages.knowledge.upload.urlAlreadyAdded"),
      });
      return;
    }
    setUrls([...urls, trimmed]);
    form.setValue("url", "");
  };
  const handleRemoveUrl = (idx: number) => {
    setUrls(urls.filter((_, i) => i !== idx));
  };
  return (
    <div className="flex h-full flex-1 flex-col p-1">
      <Label className="mb-2 flex items-center gap-2 text-sm font-medium">
        <Globe className="size-4" /> {t("pages.knowledge.upload.urlImport")}
      </Label>
      <div className="my-2 text-sm font-normal text-muted-foreground">
        {t("pages.knowledge.upload.urlImportDescription")}
      </div>
      <div className="flex items-start gap-2">
        <div className="flex-1">
          <Form {...form}>
            <FormField
              control={form.control}
              name="url"
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  rightIcon={<ClipboardCopy text={field.value} variant="secondary" size="xs" />}
                  placeholder="https://www.example.com"
                  disabled={isUploading}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleAddUrl();
                    }
                  }}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Form>
        </div>
        <Button type="button" variant="default" onClick={handleAddUrl} disabled={isUploading}>
          {t("common.add")}
        </Button>
      </div>
      <div className="mt-4 flex-1 overflow-y-auto">
        {urls.map((item: string, idx: number) => (
          <div
            key={idx}
            className="mb-2 flex items-center justify-between rounded border bg-card px-3 py-2">
            <div className="flex items-center gap-2">
              <Globe className="size-4 text-gray-500" />
              <span className="text-sm">{item}</span>
            </div>
            <div className="flex items-center gap-2">
              <ClipboardCopy text={item} variant="ghost" size="xs" />
              <Button type="button" variant="ghost" size="xs" onClick={() => handleRemoveUrl(idx)}>
                <Trash className="size-4 text-destructive" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default URLUploader;

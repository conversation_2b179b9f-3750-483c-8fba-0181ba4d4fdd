"use client";

import { useMemo, useState } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { AddBrandDialog } from "@/features/brand/components/add-brand-dialog";
import { columns } from "@/features/brand/components/BrandList/column";
import { useBrands } from "@/features/brand/hooks/brand";
import { Brand } from "@/features/brand/hooks/types";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";

export default function BrandsPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );
  const [showAddBrand, setShowAddBrand] = useState(false);
  const [brandToEdit, setBrandToEdit] = useState<Brand | undefined>(undefined);
  const { brands, total, isLoading, isFetching, refetch, useDeleteBrandMutation } =
    useBrands(options);

  const isTableLoading = isLoading || isFetching;
  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "brands",
      searchPlaceHolder: t("pages.products.filters.search.placeholderBrand"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "created_at",
          type: "date",
          title: t("pages.products.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: "date",
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as unknown as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);
  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("common.add"),
        icon: PlusIcon,
        onClick: () => {
          setBrandToEdit(undefined);
          setShowAddBrand(true);
        },
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  const handleBrandDialogClose = () => {
    setShowAddBrand(false);
    setBrandToEdit(undefined);
    // Ensure data is refreshed when dialog closes
    // refetch();
  };

  const handleEditBrand = (brand: Brand) => {
    setBrandToEdit(brand);
    setShowAddBrand(true);
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.products.brand")}
        filterType="brands"
        data={brands || []}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(useDeleteBrandMutation, isFetching, t, handleEditBrand)}
        data={brands || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
      />
      {showAddBrand && (
        <AddBrandDialog
          onClose={handleBrandDialogClose}
          brandToEdit={brandToEdit}
          isEdit={!!brandToEdit}
        />
      )}
    </TableCard>
  );
}

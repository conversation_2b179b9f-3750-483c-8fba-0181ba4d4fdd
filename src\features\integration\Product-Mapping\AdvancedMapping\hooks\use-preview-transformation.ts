import { useEffect, useState } from "react";

import { useHandleTransformation } from "./transformation";
import { Transformation, TransformationPayload } from "./types";

interface UsePreviewTransformationProps {
  sourceValue: string;
  transformedValue: string;
  sourceField: string;
  transformations: Transformation[];
  syncRecordId?: string;
  connectionId?: string;
  activeTab: "output" | "rule";
}

export const usePreviewTransformation = ({
  sourceValue,
  transformedValue,
  sourceField,
  transformations,
  syncRecordId = "",
  connectionId = "",
  activeTab,
}: UsePreviewTransformationProps) => {
  // State for tracking API calls and results
  const [isLoading, setIsLoading] = useState(false);
  const [transformationOutput, setTransformationOutput] = useState<string[]>([]);

  // Use the transformation mutation hook
  const {
    executeTransformation,
    data: transformationResponse,
    isLoading: isMutationLoading,
    reset: resetMutation,
  } = useHandleTransformation();

  // Execute transformation when necessary inputs change
  useEffect(() => {
    // Only create payload when on output tab with valid data
    if (activeTab === "output" && sourceField && sourceValue) {
      // Filter out transformations that have a valid type - ignore empty ones
      const validTransformations = transformations
        .filter((t) => t.type && t.type !== "")
        .map((t) => ({
          type: t.type || "",
          config: t.config || {},
        }));

      // Only call API if we have at least one valid transformation
      if (validTransformations.length > 0) {
        console.log(
          "Creating transformation payload with valid transformations:",
          validTransformations
        );

        const payload: TransformationPayload = {
          sync_record_id: syncRecordId,
          connection_id: connectionId,
          source_field: sourceField,
          transformations: validTransformations,
        };

        // Execute the mutation
        setIsLoading(true);
        console.log("Executing transformation with payload:", payload);

        executeTransformation(payload)
          .then((response) => {
            console.log("Transformation successful:", response.outputs);
            // Extract outputs from the response
            if (response?.outputs) {
              setTransformationOutput(response.outputs);
            } else if (response?.transformedValue) {
              // Handle legacy format
              setTransformationOutput([response.transformedValue]);
            }
          })
          .catch((error) => {
            console.error("Transformation failed:", error);
            // Reset outputs on error
            setTransformationOutput([]);
          })
          .finally(() => {
            setIsLoading(false);
          });
      } else {
        // Reset outputs if we have no valid transformations
        console.log("No valid transformations found, resetting output");
        setTransformationOutput([]);
        resetMutation();
      }
    }
  }, [
    activeTab,
    sourceField,
    sourceValue,
    transformations,
    syncRecordId,
    connectionId,
    executeTransformation,
    resetMutation,
  ]);

  // Helper functions to access transformation results
  const getPreviousOutput = (index: number): string => {
    // For first transformation, use source value
    if (index === 0) return sourceValue;

    // For subsequent transformations, use output from previous step
    // Subtract 1 because index is 1-based but array is 0-based
    return index <= transformationOutput.length ? transformationOutput[index - 1] : sourceValue;
  };

  const getChainOutput = (transformation: Transformation, index: number): string => {
    // Check if this transformation has a valid type
    const hasValidType = transformation.type && transformation.type !== "";

    // If it's not a valid transformation (type not selected), return source value
    if (!hasValidType) return sourceValue;

    // Get this transformation's output if available
    return index < transformationOutput.length ? transformationOutput[index] : sourceValue;
  };

  const getFinalOutput = (): string => {
    // Return the last transformation output if available
    return transformationOutput.length > 0
      ? transformationOutput[transformationOutput.length - 1]
      : transformedValue || sourceValue || "null";
  };

  return {
    isLoading: isLoading || isMutationLoading,
    getPreviousOutput,
    getChainOutput,
    getFinalOutput,
    transformationResponse,
  };
};

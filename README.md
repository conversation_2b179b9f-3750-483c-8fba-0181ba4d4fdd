# X Healthcare Data Reports - AI-Guided Development Workflow

## Tech Stack
- **Next.js 14** - React framework with App Router
- **TypeScript** - Static type checking
- **shadcn/ui** - Re-usable components
- **Tailwind CSS** - Utility-first CSS
- **Jest & Testing Library** - Testing framework

## Project Structure
```
optiwarehouse-app/
├── .vscode/                           # VSCode settings
├── .next/                             # Next.js build output
├── .src/                              # Source code
│   ├── app/                           # Next.js App Router pages
│   │   ├── ([GroupPageName])/         # Group pages
│   │   ├── [PageName]/                # Page
│   │   ├── global.css                 # Global styles
│   │   ├── layout.tsx                 # Layout component
│   │   ├── not-found.tsx              # 404 page
│   │   └── page.tsx                   # Home page
│   ├── assets/                        # Static assets
│   ├── components/                    # React components
│   │   ├── ui/                        # shadcn/ui components
│   │   ├── [ComponentName]/           # Custom components
│   │   └── provider.tsx               # Provider component
│   ├── config/                        # Configuration files
│   ├── constants/                     # Constant values
│   │   └── endpoints/                 # API endpoints
│   ├── features/                      # Feature-specific components
│   │   └── [FeatureName]/             # Feature
│   │      ├── components/             # Feature components
│   │      ├── hooks/                  # Feature hooks
│   │      ├── types/                  # Feature types
│   │      ├── store/                  # Feature store
│   │      ├── __tests__/              # Feature tests
│   │      └── utils/                  # Feature utils
│   │          └── validators/         # Feature validators (form validation)
│   ├── hooks/                         # Custom React hooks (for all features)
│   ├── i18n/                          # Language files config
│   │   └── locales/                   # Language files
│   │      └── [locale].json           # Language file
│   ├── lib/                           # Utility functions
│   │   ├── apis/                      # API functions
│   │   ├── auth.ts                    # Authentication functions
│   │   └── utils.ts                   # Utility functions
│   ├── store/                         # Redux store
│   │   ├── rootReducer.ts             # Root reducer
│   │   ├── rootSaga.ts                # Root saga
│   │   └── index.ts                   # Store configuration
│   └── middleware.ts                  # Middleware
├── .env                               # Environment variables (local)
├── .env.example                       # Environment variables example
├── .eslint.config.js                  # ESLint configuration
├── .gitignore                         # Git ignore file
├── components.json                    # shadcn/ui configuration
├── __mocks__/                         # Mock files
│   ├── fileMock.js                    # File mock
│   └── ...                            # Other mock files
├── .jest/                             # Jest configuration
│   ├── setEnvVars.js                  # Jest setup file
│   └── ...                            # Other Jest files
├── jest.config.js                     # Jest configuration
├── jest.setup.js                      # Jest setup file
├── jest/                              # Jest configuration
│   ├── setEnvVars.js                  # Jest setup file
├── public/                            # Public assets
├── scripts/                           # Scripts
│   ├── create-component.js            # Create component script
│   └── create-page.js                 # Create page script
├── next.config.js                     # Next.js configuration
├── package.json                       # Project dependencies  
├── postcss.config.js                  # PostCSS configuration
├── README.md                          # Project documentation
├── tailwind.config.ts                 # Tailwind CSS configuration
└── tsconfig.json                      # TypeScript configuration
```

## Setup Requirements
- Node.js 18.17 or later
- npm 9.x or later
- Git

## Getting Started
1. Clone the repository
2. Copy .env.example to .env and update the variables
3. Install dependencies:
   ```bash
   npm install
   ```
4. Create your first component:
   ```bash
   npm run create-component MyComponent
   ```
5. Follow the AI workflow using Cursor's AI agent as described below

## Component Development Standards

### TypeScript Best Practices
1. **Type Definitions**
   ```typescript
   // Use explicit type definitions
   interface ComponentProps {
     variant: 'primary' | 'secondary';
     size?: 'sm' | 'md' | 'lg';
     onClick?: () => void;
     children: React.ReactNode;
   }

   // Use proper React.FC typing
   const Component: React.FC<ComponentProps> = ({
     variant,
     size = 'md',
     onClick,
     children
   }) => {
     // Implementation
   };
   ```

2. **Type Safety**
   - Use strict TypeScript configuration
   - Avoid `any` types
   - Implement proper error boundaries
   - Use discriminated unions for complex states

### shadcn/ui Integration
1. **Component Usage**
   ```typescript
   import { Button } from "@/components/ui/button"
   import { Card } from "@/components/ui/card"
   
   // Extend shadcn/ui components
   interface CustomButtonProps extends React.ComponentPropsWithoutRef<typeof Button> {
     customProp?: string;
   }
   ```

2. **Styling Guidelines**
   - Use cn() utility for class merging
   - Follow shadcn/ui's component patterns
   - Maintain consistent styling variables
   - Use CSS variables for theming

### Next.js Patterns
1. **Client Components**
   ```typescript
   'use client'
   
   import { useState } from 'react'
   import { useRouter } from 'next/navigation'
   ```

2. **Server Components**
   ```typescript
   import { headers } from 'next/headers'
   import { Suspense } from 'react'
   ```

3. **Image Optimization**
   ```typescript
   import Image from 'next/image'
   
   // Use proper image handling
   <Image
     src={src}
     alt={alt}
     width={width}
     height={height}
     placeholder="blur"
     loading="lazy"
   />
   ```

### Component Template Structure
```typescript
// [ComponentName]/[ComponentName].tsx
'use client'

import React from 'react'
import { cn } from "@/lib/utils"
import { 
  useCallback,
  useState,
  type ComponentPropsWithoutRef 
} from 'react'

interface ComponentProps {
  // Props definition
}

export const Component: React.FC<ComponentProps> = ({
  // Props destructuring
}) => {
  // Implementation
}
```

### Testing Standards
```typescript
// [ComponentName]/[ComponentName].test.tsx
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

describe('Component', () => {
  it('renders with default props', () => {
    // Test implementation
  })

  it('handles user interactions', async () => {
    const user = userEvent.setup()
    // Test implementation
  })
})
```

## AI-Guided Development Process

### Overview
This project uses an AI-guided development workflow that emphasizes test-driven development (TDD) and consistent component creation. The workflow is designed to ensure high-quality, well-tested components with proper documentation.

### AI-Guided Component Development Workflow

#### Creating a New Component
```bash
npm run create-component ComponentName
```
This command will:
1. Create a new component directory in `components/`
2. Generate all necessary files from templates
3. Set up the AI workflow structure

#### Component Structure
Each component folder contains:
```
components/ComponentName/
├── ComponentName.tsx        # Component implementation
├── ComponentName.test.tsx   # Test cases
├── README.md               # Requirements and documentation
└── ai-prompts.md          # AI workflow guide
```

#### Development Steps

##### Step 1: Design & Requirements
1. Add your design image/mockup to the component folder
2. Update `README.md` with:
   - Design reference
   - Component requirements
   - Props interface
   - Test cases to implement
   - Implementation notes

##### Step 2: Test Case Generation
Use the AI prompt in `ai-prompts.md` to:
1. Analyze the design and requirements
2. Generate comprehensive test cases covering:
   - Basic rendering
   - Props validation
   - User interactions
   - Responsive behavior
   - Edge cases
3. Implement the test cases in `ComponentName.test.tsx`

##### Step 3: Component Implementation
Use the AI prompt to implement the component:
1. Create props interface
2. Implement layout matching design
3. Add functionality and interactions
4. Ensure responsive design
5. Handle edge cases
6. Add TypeScript types

##### Step 4: Test & Iterate
1. Run tests:
   ```bash
   npm test ComponentName
   ```
2. If tests fail:
   - Use AI to analyze failures
   - Get implementation suggestions
   - Update component
   - Rerun tests
3. If tests pass:
   - Verify visual match with design
   - Check performance
   - Review accessibility

##### Step 5: Completion Checklist
- [ ] All test cases pass
- [ ] Component visually matches design
- [ ] Props interface is complete
- [ ] TypeScript types are correct
- [ ] Responsive behavior works
- [ ] No console errors/warnings
- [ ] Code is clean and documented

## Example
See the `ProductCard` component for a complete example of this workflow:
- [ProductCard Component](./components/ProductCard/README.md)
- [ProductCard Tests](./components/ProductCard/ProductCard.test.tsx)
- [ProductCard Implementation](./components/ProductCard/ProductCard.tsx)

## Best Practices

1. **Component Architecture**
   - Use Server Components by default
   - Mark with 'use client' only when needed
   - Implement proper error boundaries
   - Use React Suspense for loading states
   - Keep components small and focused
   - Follow composition over inheritance

2. **TypeScript Usage**
   - Enable strict mode in tsconfig
   - Use proper type imports/exports
   - Implement proper error handling
   - Use utility types appropriately
   - Avoid type assertions unless necessary
   - Create reusable type definitions

3. **shadcn/ui Integration**
   - Follow component composition patterns
   - Use proper variant handling
   - Implement consistent styling
   - Maintain accessibility standards
   - Extend components using composition
   - Use proper theme variables

4. **Performance Optimization**
   - Implement proper code splitting
   - Use Next.js Image optimization
   - Implement proper caching strategies
   - Monitor and optimize bundle size
   - Use React.memo when needed
   - Implement proper loading states

5. **Testing Strategy**
   - Write comprehensive unit tests
   - Implement integration tests
   - Use proper mocking strategies
   - Test accessibility features
   - Test responsive behavior
   - Test error states

6. **State Management**
   - Use React Query for server state
   - Implement proper loading states
   - Handle error states gracefully
   - Use proper form validation
   - Implement proper data fetching

7. **Code Quality**
   - Follow ESLint rules
   - Use Prettier for formatting
   - Implement proper error handling
   - Write meaningful comments
   - Use proper naming conventions

## Available Scripts
- `npm run create-component ComponentName` - Create new component
- `npm test ComponentName` - Run component tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Generate test coverage report

## Workflow Benefits
- Consistent component structure
- Test-driven development
- Comprehensive documentation
- Type safety
- Accessibility considerations
- Responsive design
- Edge case handling

## Contributing
1. Create a new component using the workflow
2. Follow the AI-guided development steps
3. Ensure all tests pass
4. Update documentation
5. Submit for review

## Using ai-prompts.md with Cursor AI

### Step-by-Step Guide

1. **Prepare Your Component**
   ```bash
   npm run create-component MyComponent
   ```
   This creates your component folder with all necessary files, including `ai-prompts.md`

2. **Add Design Assets**
   - Place your design image/mockup in the component folder
   - Update the README.md with initial requirements

3. **Start AI-Guided Development**
   Open your component folder in Cursor and follow these steps:

   a) **Generate Test Cases**
   ```
   Message to AI: "I need to generate test cases for my new MyComponent. 
   Here's my design [reference your design]. 
   Please follow the Step 1 prompt from ai-prompts.md to help me create comprehensive test cases."
   ```

   b) **Implement Component**
   ```
   Message to AI: "Now that we have the test cases, please help me implement MyComponent.
   Please follow the Step 2 prompt from ai-prompts.md for the implementation."
   ```

   c) **Test & Debug**
   ```
   Message to AI: "Let's run and verify the tests following Step 3 from ai-prompts.md.
   Here are my test results: [paste test output]"
   ```

   d) **Complete Checklist**
   ```
   Message to AI: "Can you help me verify the completion checklist from Step 4 in ai-prompts.md?"
   ```

### Working with AI Prompts

Each step in `ai-prompts.md` is designed to guide specific aspects of development:

1. **Test Generation (Step 1)**
   - Share your design and requirements
   - Let AI analyze and suggest test cases
   - Review and refine the generated tests
   - AI will help implement the test file

2. **Implementation (Step 2)**
   - AI will guide you through component creation
   - Follow the structured implementation steps
   - Get suggestions for props interface
   - Receive guidance on layout and functionality

3. **Testing (Step 3)**
   - Run tests and share results with AI
   - Get help debugging failed tests
   - Receive suggestions for improvements
   - Iterate until all tests pass

4. **Completion (Step 4)**
   - Use the checklist with AI's help
   - Verify all requirements are met
   - Get final improvement suggestions
   - Ensure code quality and documentation

### Example Workflow Conversation

```
You: I've just created a new Button component using create-component. Here's my design [attach design].
     Can you help me follow Step 1 from ai-prompts.md to generate test cases?

AI: I'll help you generate test cases based on Step 1. First, let me analyze your design...
    [AI provides test implementation]

You: Great! Now let's move to Step 2. Can you help me implement the component?

AI: Following Step 2 from ai-prompts.md, I'll help you implement the Button component...
    [AI provides component implementation]

You: The tests are running now. Here are the results [paste test output].
     Can you help me follow Step 3 to address these failures?

AI: Let's analyze the test results following Step 3...
    [AI provides debugging help]

You: Tests are now passing. Can you help me verify the completion checklist from Step 4?

AI: I'll help you go through each item in the completion checklist...
    [AI helps verify completion]
```

### Tips for Using ai-prompts.md

1. **Follow Sequential Steps**
   - Complete each step before moving to the next
   - Don't skip steps even if they seem obvious
   - Keep the AI informed of your progress

2. **Provide Context**
   - Always reference which step you're on
   - Share relevant files and outputs
   - Explain any specific requirements or constraints

3. **Iterate with AI**
   - Use the prompts as conversation starters
   - Ask for clarification when needed
   - Let AI guide you through complex issues

4. **Document Progress**
   - Keep track of completed steps
   - Note any deviations from the standard process
   - Document important decisions made with AI

Remember: The prompts in `ai-prompts.md` are your guide for structured development. The AI agent will help you interpret and follow these prompts effectively.

## Autonomous Development Mode with AI

### Starting Autonomous Development
After creating your component, you can let the AI agent handle the entire development cycle automatically:

```
Message to AI: "I need to develop [ComponentName] in autonomous mode. 
Here's my design [attach design] and requirements. 
Please follow ai-prompts.md steps continuously until all tests pass and the component is complete.
Please update me on progress after each major step."
```

### How Autonomous Mode Works
1. **Initial Setup**
   ```
   You: "Create ProductCard component in autonomous mode. Here's the design [design]
        and these are the requirements [requirements]."
   
   AI: "I'll handle the complete development cycle. I'll start with test generation
       and keep you updated after each major step."
   ```

2. **Continuous Development Flow**
   The AI will:
   - Generate and implement test cases
   - Create the component implementation
   - Run tests automatically
   - Fix failing tests
   - Iterate until all tests pass
   - Verify the completion checklist

3. **Progress Updates**
   The AI will provide updates like:
   ```
   AI: "Step 1 complete: Test cases generated and implemented.
       Moving to component implementation..."

   AI: "Step 2 complete: Initial implementation done.
       Running tests..."

   AI: "Tests failed. Analyzing and fixing issues...
       [test error details]
       Implementing fixes..."

   AI: "All tests passing. Verifying completion checklist..."
   ```

### Monitoring Autonomous Development
1. **Review Points**
   - After test case generation
   - After initial implementation
   - When tests fail
   - Before final completion

2. **Intervention Points**
   You can intervene by saying:
   ```
   "Pause the autonomous development, I need to review the current state."
   "Modify the test cases to include [specific test]."
   "Adjust the implementation to handle [specific case]."
   "Continue autonomous development from current state."
   ```

3. **Progress Tracking**
   The AI will maintain a progress log:
   ```
   AI: "Development Progress:
       ✓ Test cases generated
       ✓ Initial implementation
       × Tests failing (2/6 passing)
       → Fixing test failures
       → Remaining: 4 failing tests"
   ```

### Best Practices for Autonomous Mode

1. **Clear Initial Requirements**
   - Provide detailed design specifications
   - List all required functionality
   - Specify any constraints or dependencies
   - Include performance requirements

2. **Monitoring Guidelines**
   - Review progress updates regularly
   - Check test coverage reports
   - Verify implementation quality
   - Validate accessibility features

3. **When to Use Autonomous Mode**
   Best for:
   - Components with clear requirements
   - Standard UI patterns
   - Well-defined behaviors
   - Components similar to existing ones

   Not recommended for:
   - Complex business logic
   - Critical security features
   - Experimental features
   - Heavy external integrations

4. **Quality Control**
   The AI will verify:
   - Test coverage
   - TypeScript types
   - Accessibility standards
   - Performance metrics
   - Code style consistency

### Example Autonomous Session

```
You: "Create Button component in autonomous mode. Here's the design [design].
     Requirements: primary/secondary variants, disabled state, loading state."

AI: "Starting autonomous development for Button component.
     Step 1: Generating test cases..."

[After a few moments]

AI: "Test cases generated and implemented:
     ✓ Renders primary variant
     ✓ Renders secondary variant
     ✓ Handles disabled state
     ✓ Shows loading spinner
     Moving to implementation..."

[AI continues development]

AI: "Initial implementation complete.
     Running tests... 3/4 tests passing.
     Fixing failing test: loading spinner visibility..."

[AI fixes issues]

AI: "All tests passing. Verifying completion checklist...
     Component is ready for review."
```

Remember: Even in autonomous mode, you should review the final output and test the component in your application context.

## GitHub Pull Request Rules

1. **Branch Naming**
   - Format: `[JIRA-TICKET]-description`
   - Example: `OPWH-123-add-button-component`

2. **Commit Messages**
   - Start with Jira ticket: `[OPWH-123] Add button component`
   - Use present tense and imperative mood
   - Keep messages clear and descriptive

3. **Pull Request Title**
   - Include Jira ticket: `[OPWH-123] Add reusable button component`
   - Should clearly describe the change

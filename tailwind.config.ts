import type { Config } from "tailwindcss";
import animate from "tailwindcss-animate";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/features/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        bg: {
          DEFAULT: "hsl(var())",
          primary: "hsl(var( --bg-primary))",
          secondary: "hsl(var(--bg-secondary))",
          muted: {
            DEFAULT: "hsl(var())",
            40: "hsl(var( --bg-muted-40))",
            50: "hsl(var( --bg-muted-50))",
          },
          accent: {
            DEFAULT: "hsl(var())",
            50: "hsl(var(--bg-accent-50))",
          },
          destructive: {
            DEFAULT: "hsl(var())",
            10: "hsl(var(--bg-destructive-10))",
            20: "hsl(var(--bg-destructive-20))",
          },
        },
        background: "hsl(var(--bg-primary))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
          hover: "hsl(var(--primary-hover))",
          light: "hsl(var(--primary-light))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        success: {
          DEFAULT: "hsl(var(--success))",
        },
        warning: {
          DEFAULT: "hsl(var(--warning))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
          50: "hsla(var(--muted-50))",
        },
        sematic: {
          DEFAULT: "hsl(var())",
          foreground: "hsl(var(--sematic-foreground))",
          info: "hsl(var(--info))",
          blue: "hsl(var(--blue))",
          success: "hsl(var(--success))",
          warning: "hsl(var(--warning))",
        },
        semi: {
          DEFAULT: "hsl(var(--semi))",
        },
        reverseNeutral: {
          DEFAULT: "hsl(var(--reverse-neutral))",
        },
        prime: {
          DEFAULT: "hsl(var(--dark-primary))",
        },
        neutral: {
          DEFAULT: "hsl(var())",
          50: "hsl(var(--50))",
          100: "hsl(var(--100))",
          200: "hsl(var(--200))",
          300: "hsl(var(--300))",
          400: "hsl(var(--400))",
          500: "hsl(var(--500))",
          600: "hsl(var(--600))",
          700: "hsl(var(--700))",
          800: "hsl(var(--800))",
          900: "hsl(var(--900))",
          950: "hsl(var(--950))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
          hover: "hsl(var(--destructive-hover))",
        },
        border: {
          DEFAULT: "hsl(var(--border))",
          destructive: {
            DEFAULT: "hsl(var())",
            50: "hsl(var(--border-destructive-50))",
          },
        },

        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        table: {
          hover: "hsl(var(--table-row-hover))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      screens: {
        xxs: "320px",
        xs: "375px",
        nav: "1371px",
        card: "990px",
        small_card: "480px",
        screen_card: "2560px",
      },
    },
  },
  plugins: [animate],
};

export default config;

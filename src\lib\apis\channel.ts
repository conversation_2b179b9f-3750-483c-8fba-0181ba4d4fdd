import { SetupFieldsResponse } from "@/features/integration/hooks/install-channel";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi, publicApi } from "../api_helper";
import {
  ChannelResponse,
  ConnectionDetail,
  ConnectionPayload,
  ConnectionResponse,
  InstallConnectionParams,
  InstallConnectionResponse,
  SyncSettingsPayload,
  SyncSettingsResponse,
} from "./types/channel";

export const channelApi = {
  list: async (params?: Record<string, unknown>) => {
    const response: ChannelResponse = await privateApi.get<ChannelResponse>(
      ENDPOINTS.CHANNEL.LIST,
      { params }
    );
    return response.connection_types;
  },
  getConnectionSetupFields: async (channelKey: string) => {
    return await privateApi.get<SetupFieldsResponse>(
      ENDPOINTS.CHANNEL.CONNECTION_SETUP_FIELDS(channelKey),
      { params: { channel_key: channelKey } }
    );
  },
  createConnection: async (payload: ConnectionPayload) => {
    return await privateApi.post<ConnectionResponse>(
      ENDPOINTS.CHANNEL.CONNECTION_SETUP(payload.connection_type),
      payload
    );
  },
  installConnection: async (params: InstallConnectionParams) => {
    return await publicApi.post<InstallConnectionResponse>(
      ENDPOINTS.CHANNEL.INSTALL_CONNECTION,
      params
    );
  },
  getConnection: async (connectionId: string) => {
    return await privateApi.get<ConnectionDetail>(ENDPOINTS.CHANNEL.GET_CONNECTION(connectionId));
  },
  syncSettings: async (payload: SyncSettingsPayload) => {
    return await privateApi.post<SyncSettingsResponse>(
      ENDPOINTS.CHANNEL.CONNECTION_SYNC_SETTINGS,
      payload
    );
  },
};

import React, { useState } from "react";

import { AvatarStatus } from "@/components/ui/avatar-status";

interface HeaderProps {
  name: string;
  jobTitle: string;
  avatar?: string;
  botId?: string;
  status?: "online" | "offline" | "away" | "busy";
  isEmbedded?: boolean;
  isConversation: boolean;
}

const Header: React.FC<HeaderProps> = ({
  name,
  jobTitle,
  avatar = "/avatars/default.png",
  botId,
  isEmbedded = false,
  isConversation = false,
}) => {
  const [copied, setCopied] = useState(false);

  return (
    <div className={`flex items-center justify-between bg-muted p-4`}>
      <div
        className={`flex h-full items-center overflow-hidden ${isEmbedded ? "" : "pb-1"} flex-1`}>
        <div className={`relative ${isEmbedded ? "mb-1 mr-2 size-8" : "mr-2 size-10"}`}>
          <AvatarStatus
            src={avatar}
            alt={name}
            fill
            className="object-cover"
            status="online"
            statusColor="#10b981"
            statusSize={isEmbedded ? 16 : 16}
          />
        </div>
        <div className="min-w-0 flex-1">
          <h2 className="truncate font-semibold">{name}</h2>
          {/* <p className="truncate text-sm">{jobTitle}</p> */}
        </div>
      </div>
    </div>
  );
};

export default Header;

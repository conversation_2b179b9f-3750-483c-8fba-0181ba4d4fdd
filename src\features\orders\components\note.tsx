"use client";

import { useEffect, useState } from "react";
// Add date-fns for formatting
import { format, parseISO } from "date-fns";
import { TagInput } from "emblor";
import { Check, Copy } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";

interface Tag {
  id: string;
  text: string;
}

interface NoteProps {
  initialNote?: string;
  initialTags?: string[];
  onNoteChange?: (note: string) => void;
  onTagsChange?: (tags: string[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  additionalInfo?: {
    staff?: {
      id: string;
      name: string;
    };
    branch?: {
      name: string;
    };
    updated_at?: string;
  };
}

export function Note({
  initialNote = "",
  initialTags = [],
  onNoteChange,
  onTagsChange,
  disabled,
  readOnly = false,
  additionalInfo,
}: NoteProps) {
  const [note, setNote] = useState(initialNote);
  const [tags, setTags] = useState<Tag[]>(
    initialTags.map((tag) => ({ id: crypto.randomUUID(), text: tag }))
  );
  const [activeTagIndex, setActiveTagIndex] = useState<number | null>(null);
  const MAX_TAGS = 10;
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    setNote(initialNote);
  }, [initialNote]);

  useEffect(() => {
    setTags(initialTags.map((tag) => ({ id: crypto.randomUUID(), text: tag })));
  }, [initialTags]);

  const handleNoteChange = (value: string) => {
    setNote(value);
    onNoteChange?.(value);
  };

  const handleTagsChange = (value: Tag[] | ((prev: Tag[]) => Tag[])) => {
    const newTags = typeof value === "function" ? value(tags) : value;
    if (newTags.length <= MAX_TAGS) {
      setTags(newTags);
      onTagsChange?.(newTags.map((tag) => tag.text));
    }
  };

  const handleCopyToClipboard = (id: string) => {
    navigator.clipboard.writeText(id);
  };

  const formatDateTime = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      return format(date, "dd/MM/yyyy HH:mm:ss");
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  return (
    <div className="space-y-4">
      {readOnly && additionalInfo && (
        <div className="space-y-4">
          <h3 className="pb-4 text-sm font-normal text-muted-foreground">Additional Information</h3>
          <div className="space-y-6">
            {additionalInfo.branch && (
              <div className="flex justify-between">
                <span className="text-sm font-medium">Branch</span>
                <span className="truncate pl-2 text-right text-base">
                  {additionalInfo.branch.name}
                </span>
              </div>
            )}
            {additionalInfo.staff && (
              <div className="flex justify-between">
                <span className="text-sm font-medium">Staff</span>
                <span className="truncate pl-2 text-right text-base">
                  {additionalInfo.staff.name}
                </span>
              </div>
            )}
            {additionalInfo.updated_at && (
              <div className="flex justify-between">
                <span className="text-sm font-medium">Time</span>
                <span className="truncate pl-2 text-right text-base">
                  {formatDateTime(additionalInfo.updated_at)}
                </span>
              </div>
            )}
            {additionalInfo.staff && (
              <div className="flex flex-wrap items-start justify-between">
                <span className="w-full text-sm font-medium">Reference</span>
                <div className="flex w-full items-center justify-between">
                  <span className="break-all text-base">{additionalInfo.staff.id}</span>
                  {copied ? (
                    <Check className="ml-2 size-4 text-green-500" />
                  ) : (
                    <Copy
                      className="ml-2 size-4 cursor-pointer text-primary"
                      onClick={() => {
                        if (additionalInfo?.staff) {
                          handleCopyToClipboard(additionalInfo.staff.id);
                          setCopied(true);
                          // Reset copied state after 20 seconds
                          setTimeout(() => setCopied(false), 20000);
                        }
                      }}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className={readOnly ? "border-t pt-4" : ""}>
        <div className="space-y-2">
          <h2 className="text-sm font-medium">{t("pages.orders.note")}</h2>
          {readOnly ? (
            <p className="text-base">{note || "--"}</p>
          ) : (
            <Textarea
              placeholder={t("pages.orders.notePlaceholder")}
              value={note}
              onChange={(e) => handleNoteChange(e.target.value)}
              className="min-h-[100px] resize-none"
              disabled={disabled}
            />
          )}
        </div>

        <div className="mt-4 space-y-2">
          <h2 className="text-sm font-medium">{t("pages.orders.tags")}</h2>
          <div className="flex flex-col space-y-2">
            {readOnly ? (
              <div className="flex max-w-full flex-wrap gap-1">
                {tags.flatMap((tag) =>
                  (tag.text || "--").split(",").map((subTag, subIndex) => (
                    <Badge
                      key={`${tag.id}-${subIndex}`}
                      variant="secondary"
                      className="max-w-[200px]">
                      <span className="truncate">{subTag.trim() || "--"}</span>
                    </Badge>
                  ))
                )}
              </div>
            ) : (
              <TagInput
                placeholder={t("pages.orders.tagsPlaceholder")}
                tags={tags}
                setTags={handleTagsChange}
                activeTagIndex={activeTagIndex}
                setActiveTagIndex={setActiveTagIndex}
                styleClasses={{
                  tag: {
                    body: "bg-muted text-bg-background py-0.5 truncate",
                  },
                  input: "bg-transparent p-0 ps-0 shadow-none",
                  inlineTagsContainer: "bg-transparent rounded-md text-sm gap-1",
                }}
                truncate={20}
                maxLength={30}
                maxTags={MAX_TAGS}
                shape={"rounded"}
                disabled={disabled}
              />
            )}
            {!readOnly && (
              <span className="text-xs text-muted-foreground">
                {tags.length}/{MAX_TAGS}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

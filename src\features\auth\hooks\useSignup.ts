import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { authApi } from "@/lib/apis/auth";
import { createI18nResolver } from "@/lib/utils";

import { SignupFormValues, signupSchema } from "../utils/validators/signup";
import { authKeys } from "./keys";

export const useSignup = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const form = useForm<SignupFormValues>({
    resolver: createI18nResolver(signupSchema, t),
    defaultValues: {
      email: "",
      username: "",
      password: "",
      confirmPassword: "",
    },
  });

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    isSuccess: success,
    reset: resetMutation,
  } = useMutation({
    mutationKey: authKeys.signup(),
    mutationFn: async (data: SignupFormValues) => {
      try {
        if (!data.username || !data.password || !data.email) {
          throw new Error(t("validation.required"));
        }

        const response = await authApi.signup({
          email: data.email,
          username: data.username,
          password: data.password,
          confirmPassword: data.confirmPassword,
        });

        return response;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      router.push(`/verify-confirmation-code?username=${encodeURIComponent(variables.username)}`);
    },
  });

  const handleFocus = () => {
    resetMutation();
  };

  const signupSubmit = (formData: SignupFormValues) => {
    onSubmit(formData);
  };

  return {
    signupSubmit,
    loading,
    form,
    error,
    success,
    handleFocus,
  };
};

import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface AddVoucherDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApplyVoucher: (code: string) => void;
  trigger?: React.ReactNode;
}

export function AddVoucherDialog({
  open,
  onOpenChange,
  onApplyVoucher,
  trigger,
}: AddVoucherDialogProps) {
  const { t } = useTranslation();
  const [voucherCode, setVoucherCode] = useState("");

  const handleApply = () => {
    if (voucherCode.trim()) {
      onApplyVoucher(voucherCode.trim());
      setVoucherCode("");
      onOpenChange(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      {trigger && <PopoverTrigger asChild>{trigger}</PopoverTrigger>}
      <PopoverContent className="w-80">
        <h2 className="text-base font-medium">{t("pages.orders.addVoucher")}</h2>
        <div className="space-y-4">
          <div className="py-4">
            <div className="grid grid-cols-[100px_1fr] items-center gap-2">
              <Label htmlFor="voucher-code">{t("pages.orders.voucherCode")}</Label>
              <Input
                id="voucher-code"
                value={voucherCode}
                onChange={(e) => setVoucherCode(e.target.value)}
                placeholder={t("pages.orders.voucherCodePlaceholder")}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleApply();
                  }
                }}
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              {t("common.cancel")}
            </Button>
            <Button onClick={handleApply}>{t("common.apply")}</Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export interface IGetStaffParams {
  page?: number;
  limit?: number;
  query?: string;
  department_id?: string;
  role?: string;
  [key: string]: unknown;
}

export const staffKeys = {
  all: () => ["staff"] as const,
  lists: () => [...staffKeys.all(), "list"] as const,
  list: (params: IGetStaffParams) => [...staffKeys.lists(), params] as const,
  details: () => [...staffKeys.all(), "detail"] as const,
  detail: (id: string) => [...staffKeys.details(), id] as const,
};

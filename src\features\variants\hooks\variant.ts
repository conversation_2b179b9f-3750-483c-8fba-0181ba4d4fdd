import { InfiniteData, useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { ResponseList } from "@/lib/apis/types/common";
import { variantApi } from "@/lib/apis/variant";

import { IGetVariantParams, variantKeys } from "./keys";
import { Variant } from "./types";

interface UseVariantsOptions extends Partial<IGetVariantParams> {
  enabled?: boolean;
}

export function useVariants(options: UseVariantsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const query = useInfiniteQuery({
    queryKey: variantKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      variantApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const variants = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteVariantMutation = useMutation({
    mutationFn: async (id: string) => {
      return variantApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Variant>>>(
        variantKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success(t("pages.products.deletedSuccessfully"));
    },
    onError: () => {
      toast.error(t("pages.products.deletionFailed"));
    },
  });
  return {
    ...query,
    variants,
    total,
    useDeleteVariantMutation,
  };
}

import { nanoid } from "nanoid";

export interface MockMessage {
  id: string;
  content: string;
  role: "user" | "assistant";
  timestamp: Date;
}

export interface MockBot {
  id: string;
  name: string;
  description: string;
  avatar?: string;
}

// Sample bots data
export const mockBots: MockBot[] = [
  {
    id: "customer-support",
    name: "Customer Support Agent",
    description: "Virtual assistant for handling customer inquiries and support requests.",
    avatar: "/avatars/support.png",
  },
  {
    id: "sales-rep",
    name: "Sales Representative",
    description: "AI assistant that can answer questions about products and help with purchases.",
    avatar: "/avatars/sales.png",
  },
  {
    id: "technician",
    name: "Technical Support",
    description: "Technical assistant that can troubleshoot issues and provide solutions.",
    avatar: "/avatars/tech.png",
  },
];

// Predefined responses based on input keywords
const responsePatterns = [
  {
    keywords: ["hello", "hi", "hey", "greetings"],
    responses: [
      "Hello! How can I assist you today?",
      "Hi there! What can I help you with?",
      "Greetings! How may I be of service?",
    ],
  },
  {
    keywords: ["price", "cost", "pricing", "payment", "subscribe", "subscription"],
    responses: [
      "Our pricing plans start at $9.99/month for the basic plan, $19.99/month for premium, and $29.99/month for enterprise.",
      "We offer several pricing tiers to meet your needs. Would you like me to break down what features come with each?",
      "You can find detailed pricing information on our website. Can I help you understand any specific plan?",
    ],
  },
  {
    keywords: ["feature", "features", "capabilities", "can", "do"],
    responses: [
      "Our platform offers a wide range of features including data analytics, automated reporting, and custom dashboards.",
      "Some of our key features include integration with 100+ tools, automated workflows, and AI-powered insights.",
      "Our service comes with real-time analytics, team collaboration tools, and customizable workflows.",
    ],
  },
  {
    keywords: ["issue", "problem", "error", "bug", "not working", "fix"],
    responses: [
      "I'm sorry you're experiencing issues. Could you provide more details about what's happening?",
      "Let's troubleshoot this together. First, have you tried refreshing the page or clearing your cache?",
      "I understand how frustrating technical issues can be. Let me help you resolve this as quickly as possible.",
    ],
  },
  {
    keywords: ["thanks", "thank you", "appreciate"],
    responses: [
      "You're welcome! Is there anything else I can help you with?",
      "Happy to help! Let me know if you have any other questions.",
      "It's my pleasure to assist. Feel free to reach out if you need anything else.",
    ],
  },
];

// Default responses if no keywords match
const defaultResponses = [
  "I'm here to help. Could you provide more details about your question?",
  "Interesting question! Could you elaborate a bit more so I can better assist you?",
  "I'd be happy to help with that. Can you share a bit more information?",
  "Thanks for reaching out. Let me know more about what you're looking for.",
];

// Function to generate a mock response based on input message
export const generateMockResponse = (message: string): string => {
  const lowerMessage = message.toLowerCase();

  // Check for keyword matches
  for (const pattern of responsePatterns) {
    if (pattern.keywords.some((keyword) => lowerMessage.includes(keyword))) {
      // Randomly select one of the matching responses
      const randomIndex = Math.floor(Math.random() * pattern.responses.length);
      return pattern.responses[randomIndex];
    }
  }

  // If no keywords match, return a random default response
  const randomIndex = Math.floor(Math.random() * defaultResponses.length);
  return defaultResponses[randomIndex];
};

// Function to get a mock bot by ID
export const getMockBot = (id: string): MockBot | undefined => {
  return mockBots.find((bot) => bot.id === id);
};

// Function to create a mock message
export const createMockMessage = (content: string, role: "user" | "assistant"): MockMessage => {
  return {
    id: nanoid(),
    content,
    role,
    timestamp: new Date(),
  };
};

export interface IGetProductMappingParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}

export const productMappingKeys = {
  all: () => ["productMapping"] as const,
  lists: () => [...productMappingKeys.all(), "list"] as const,
  list: (params: IGetProductMappingParams) => [...productMappingKeys.lists(), params] as const,
  details: () => [...productMappingKeys.all(), "detail"] as const,
  detail: (id: string) => [...productMappingKeys.details(), id] as const,
};

export const syncProductKeys = {
  all: () => ["syncProduct"] as const,
  sync: (connectionId: string) => [...syncProductKeys.all(), connectionId] as const,
};

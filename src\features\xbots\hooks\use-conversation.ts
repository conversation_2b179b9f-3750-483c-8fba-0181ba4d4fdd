import { useInfiniteQuery, useMutation } from "@tanstack/react-query";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { conversationApi } from "@/lib/apis/conversation";

interface IGetConversationParams {
  query?: string;
  [key: string]: unknown;
}

interface CONVERSATION {
  role: string;
  company_id: string;
  created_at: string;
  updated_at: string;
  customer_name: string;
  customer_phone_number: string;
  id: string;
  assignee_id: string;
}

interface CREATE_CONVERSATION {
  customer_name: string;
  customer_phone_number: string;
  assignee_id: string;
  staff_id?: string;
}

const conversationKeys = {
  all: () => ["chat"] as const,
  lists: () => [...conversationKeys.all(), "list"] as const,
  list: (params: IGetConversationParams) => [...conversationKeys.lists(), params] as const,
  details: () => [...conversationKeys.all(), "detail"] as const,
  detail: (id: string) => [...conversationKeys.details(), id] as const,
};

export const useGetChatList = (option: IGetConversationParams) => {
  const result = useInfiniteQuery({
    queryKey: conversationKeys.list(option),
    queryFn: async () => {
      const response = await conversationApi.list({
        ...option,
        ["sort_updated_at"]: SortDirection.DESC,
      });
      return response;
    },
    getNextPageParam: () => {
      return undefined;
    },
    initialPageParam: 0,
  });

  const chatList = result.data?.pages.flatMap((page) => page.items) ?? [];
  return {
    chatList,
    isLoading: result.isLoading,
    isFetching: result.isFetching,
    isError: result.isError,
    error: result.error,
  };
};

export const useGetListMessenger = (id?: string) => {
  const result = useInfiniteQuery({
    queryKey: id ? conversationKeys.detail(id) : ["conversation-detail", "undefined"],
    queryFn: async ({ pageParam = 0 }) => {
      if (!id) return { items: [], total: 0, page: pageParam, limit: 20 };
      const response = await conversationApi.getMessenger(id, {
        sort_updated_at: SortDirection.DESC,
        page: pageParam,
      });
      return response;
    },
    enabled: !!id,
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.total) return undefined;
      const nextPage = allPages.length;
      const totalPages = Math.ceil(lastPage.total / (lastPage.limit || 20));
      return nextPage < totalPages ? nextPage : undefined;
    },
    initialPageParam: 0,
  });

  const listMessenger = result.data?.pages.flatMap((page) => page.items) ?? [];

  return {
    listMessenger,
    isLoading: result.isLoading,
    isFetching: result.isFetching,
    isError: result.isError,
    error: result.error,
    hasNextPage: result.hasNextPage,
    fetchNextPage: result.fetchNextPage,
    isFetchingNextPage: result.isFetchingNextPage,
  };
};

export const useCreateConversation = () => {
  const mutation = useMutation({
    mutationFn: async (payload: CREATE_CONVERSATION) => {
      const response = await conversationApi.createConversation(payload);
      return response;
    },
  });

  return {
    ...mutation,
    isPending: mutation.isPending,
    mutate: mutation.mutate,
    mutateAsync: mutation.mutateAsync,
  };
};

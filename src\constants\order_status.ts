export const ORDER_STATUS = {
  SHIPPING: {
    label: "SHIPPING",
    variant: "blue",
  },
  READY: {
    label: "READY",
    variant: "blue",
  },
  COMPLETED: {
    label: "COMPLETED",
    variant: "green",
  },
  AWAIT_PACKING: {
    label: "AWAIT_PACKING",
    variant: "blue",
  },
  CANCELLED: {
    label: "CANCELLED",
    variant: "destructive",
  },
  DRAFT: {
    label: "DRAFT",
    variant: "secondary",
  },
  DELIVERED: {
    label: "DELIVERED",
    variant: "green",
  },
  PENDING: {
    label: "PENDING",
    variant: "yellow",
  },
  CONFIRMED: {
    label: "CONFIRMED",
    variant: "green",
  },
} as const;

export type OrderStatus = keyof typeof ORDER_STATUS;

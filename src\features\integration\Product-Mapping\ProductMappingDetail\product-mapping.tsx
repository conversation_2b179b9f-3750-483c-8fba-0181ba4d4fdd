import { useTranslation } from "react-i18next";

import { CustomImage } from "@/components/ui/image";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";

import { ProductMappingDetail } from "../hooks/types";

interface ProductMappingProps {
  productMappingDetail: ProductMappingDetail | any;
}

export const ProductMapping = ({ productMappingDetail }: ProductMappingProps) => {
  const { t } = useTranslation();

  // Handle the case when productMappingDetail is an array (success response from API)
  const actualData =
    Array.isArray(productMappingDetail) && productMappingDetail.length > 0
      ? productMappingDetail[0]
      : productMappingDetail;

  // Check if data exists in a success response object
  const dataFromSuccessResponse =
    productMappingDetail?.success &&
    Array.isArray(productMappingDetail.data) &&
    productMappingDetail.data.length > 0
      ? productMappingDetail.data[0]
      : null;

  // Use the appropriate data source
  const productData = dataFromSuccessResponse || actualData;

  const sourceData = productData?.standard_source_data;
  const destinationData = productData?.standard_destination_data;

  const renderProductDetails = (data: any) => {
    if (!data) {
      console.log("Product data is null");
      return null;
    }

    // Extract images or use empty array as fallback
    const images = data.images || [];

    return (
      <Table>
        <TableBody>
          <TableRow className="py-3">
            <TableCell className="w-28 min-w-28 px-0 text-left">{t("product.image")}</TableCell>
            <TableCell className="px-0 text-left">
              <div className="flex gap-2">
                {images.length > 0 ? (
                  images.map((img: any, index: number) => (
                    <div key={index} className="size-12 overflow-hidden rounded">
                      <CustomImage
                        src={img.url}
                        alt="Product thumbnail"
                        width={48}
                        height={48}
                        className="object-fill"
                      />
                    </div>
                  ))
                ) : (
                  <div className="size-12 overflow-hidden rounded bg-gray-200"></div>
                )}
              </div>
            </TableCell>
          </TableRow>
          <TableRow className="py-3">
            <TableCell className="w-40 min-w-40 px-0 text-left">{t("product.title")}</TableCell>
            <TableCell className="px-0 text-left font-medium">{data.name || "--"}</TableCell>
          </TableRow>
          <TableRow className="py-3">
            <TableCell className="w-40 min-w-40 px-0 text-left">
              {t("product.description")}
            </TableCell>
            <TableCell className="px-0 text-left font-medium">{data.description || "--"}</TableCell>
          </TableRow>
          <TableRow className="py-3">
            <TableCell className="w-40 min-w-40 px-0 text-left">{t("product.price")}</TableCell>
            <TableCell className="px-0 text-left font-medium">${data.price || "--"}</TableCell>
          </TableRow>
          <TableRow className="py-3">
            <TableCell className="w-40 min-w-40 px-0 text-left">{t("product.sku")}</TableCell>
            <TableCell className="px-0 text-left font-medium">{data.sku || "--"}</TableCell>
          </TableRow>
          <TableRow className="py-3">
            <TableCell className="w-40 min-w-40 px-0 text-left">{t("product.brand")}</TableCell>
            <TableCell className="px-0 text-left font-medium">{data.brand || "--"}</TableCell>
          </TableRow>
          <TableRow className="py-3">
            <TableCell className="w-40 min-w-40 px-0 text-left">{t("product.category")}</TableCell>
            <TableCell className="px-0 text-left font-medium">{data.category || "--"}</TableCell>
          </TableRow>
          <TableRow className="py-3">
            <TableCell className="w-40 min-w-40 px-0 text-left">{t("product.inventory")}</TableCell>
            <TableCell className="px-0 text-left font-medium">
              {data.variants?.reduce(
                (sum: number, variant: any) => sum + (variant.inventories?.available || 0),
                0
              ) || 0}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );
  };

  return (
    <div className="grid md:grid-cols-2">
      {/* Left Column - Source */}
      <div className="border-r pr-4">{renderProductDetails(sourceData)}</div>

      {/* Right Column - Destination */}
      <div className="md:pl-4">
        {destinationData ? (
          renderProductDetails(destinationData)
        ) : (
          <div className="flex h-full items-center justify-center py-8 text-muted-foreground">
            {t("product.notMapped")}
          </div>
        )}
      </div>
    </div>
  );
};

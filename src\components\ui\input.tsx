import * as React from "react";
import { Eye, EyeOff } from "lucide-react";

import { cn } from "@/lib/utils";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  label?: string;
  rightElement?: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerClassName?: string;
  description?: string;
  suffix?: string;
}

const PasswordToggle = ({ show, onToggle }: { show: boolean; onToggle: () => void }) => (
  <button
    type="button"
    onClick={onToggle}
    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground">
    {show ? <EyeOff className="size-4" /> : <Eye className="size-4" />}
  </button>
);

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      error,
      label,
      rightElement,
      leftIcon,
      rightIcon,
      containerClassName,
      value,
      defaultValue,
      description,
      suffix,
      onChange,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const isPasswordType = type === "password";

    return (
      <div className={cn("space-y-2", containerClassName)}>
        {(!!label || !!rightElement) && (
          <div className="flex items-center justify-between">
            {!!label && (
              <label
                className={cn(
                  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                  error && "text-destructive"
                )}
                htmlFor={props.id}>
                {label}
              </label>
            )}
            {rightElement && rightElement}
          </div>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          <input
            type={isPasswordType ? (showPassword ? "text" : "password") : type}
            className={cn(
              "input bg-transparent font-normal text-foreground border border-border flex h-10 w-full rounded-md px-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
              error ? "focus-visible:ring-destructive" : "focus-visible:ring-primary",
              leftIcon && "pl-8",
              isPasswordType && "pr-8",
              rightIcon && "pr-14",
              suffix && "pr-8",
              className
            )}
            ref={ref}
            value={value}
            defaultValue={defaultValue}
            onChange={onChange}
            {...props}
          />
          {isPasswordType ? (
            <PasswordToggle show={showPassword} onToggle={() => setShowPassword(!showPassword)} />
          ) : (
            <>
              {suffix && (
                <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 pr-2 text-sm text-muted-foreground">
                  {suffix}
                </span>
              )}
              {rightIcon && (
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                  {rightIcon}
                </div>
              )}
            </>
          )}
        </div>
        {!!description && <p className="text-sm text-muted-foreground">{description}</p>}
        {!!error && <p className="text-sm font-medium text-destructive">{error}</p>}
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };

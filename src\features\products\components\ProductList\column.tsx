import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";

import { Product } from "../../hooks/types";

export const columns = (
  useDeleteProductMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Product>[] => [
  {
    id: "product",
    accessorKey: "product",
    header: t("pages.products.headers.productInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Product> }) => {
      const product = row.original;
      return (
        <div className="flex items-center gap-4  truncate">
          <ImageColumn
            src={product?.images[0]?.url || ""}
            alt={product?.name}
            width={0}
            iszoomable={true}
            height={0}
            sizes="100vw"
            className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
          />
          <div className="flex flex-auto flex-col justify-between gap-1 truncate ">
            <TextColumn text={product?.name} className=" font-medium" />
            <TextColumn text={`SKU: ${product?.sku}`} className="text-xs text-muted-foreground" />
          </div>
        </div>
      );
    },
  },
  {
    id: "category",
    accessorKey: "category",
    sorter: true,
    sortKey: "category.id",
    header: t("pages.products.headers.category"),
    cell: ({ row }: { row: Row<Product> }) => <TextColumn text={row?.original?.category?.name} />,
  },
  {
    id: "brand",
    accessorKey: "brand",
    sorter: true,
    sortKey: "brand.name",
    header: t("pages.products.headers.brand"),

    cell: ({ row }: { row: Row<Product> }) => <TextColumn text={row?.original?.brand?.name} />,
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    sorter: true,
    sortKey: "updated_at",
    header: t("pages.products.headers.updatedAt"),
    cell: ({ row }: { row: Row<Product> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.products.headers.createdAt"),
    cell: ({ row }: { row: Row<Product> }) => (
      <ActionCell
        useDeleteProductMutation={useDeleteProductMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];

const ActionCell = ({
  useDeleteProductMutation,
  row,
  isDeleting,
}: {
  useDeleteProductMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Product>;
}) => {
  const router = useRouter();
  const product = row.original;
  // const { refetch } = useProducts();
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    // router.push(`/products/${product.id}`);

    startTransition(() => {
      router.push(`/products/${product.id}`);
    });
  }, [router, product.id]);

  const handleEdit = useCallback(() => {
    router.push(`/products/${product.id}/edit`);
  }, [router, product.id]);

  const handleDelete = useCallback(async () => {
    return useDeleteProductMutation.mutateAsync(product.id);
  }, [useDeleteProductMutation, product.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

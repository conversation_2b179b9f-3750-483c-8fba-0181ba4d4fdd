import { useState } from "react";
import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { departmentKeys } from "@/features/bots/department/hooks/keys";
import { CreateStaffPayload, VirtualStaffModel } from "@/features/bots/staff/hooks/type";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { staffApi } from "@/lib/apis/staff";
import { ResponseList } from "@/lib/apis/types/common";

import { IGetStaffParams, staffKeys } from "./keys";

export function useStaff(options: IGetStaffParams = {}) {
  const {
    limit = 20,
    enabled = true,
    ...restOptions
  } = options as IGetStaffParams & { enabled?: boolean };
  const queryClient = useQueryClient();
  const query = useInfiniteQuery({
    queryKey: staffKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      staffApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return +lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });
  const loadMore = () => {
    query.fetchNextPage();
  };

  const staff = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteStaffMutation = useMutation({
    mutationFn: async (id: string) => {
      return staffApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<VirtualStaffModel>>>(
        staffKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Staff deleted successfully");
    },
    onError: () => {
      toast.error("Staff deletion failed");
    },
  });

  const useDeleteListStaffMutation = useMutation({
    mutationFn: async (objectData: any) => {
      // Implement batch delete if your API supports it
      // For now, delete one by one
      return Promise.all(objectData.listId.map((id: string) => staffApi.delete(id)));
    },
    onSuccess: (_, objectDeletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<VirtualStaffModel>>>(
        staffKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => !objectDeletedId.listId.includes(item.id)),
            total: page.total - objectDeletedId.listId.length,
          }));
          objectDeletedId?.handleRestRows?.();
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Staff deleted successfully");
    },
    onError: () => {
      toast.error("Staff deletion failed");
    },
  });

  return {
    ...query,
    staff,
    total,
    useDeleteStaffMutation,
    useDeleteListStaffMutation,
    loadMore,
  };
}

export function useStaffDetail(id: string) {
  const query = useQuery({
    queryKey: staffKeys.detail(id),
    queryFn: async () => {
      const response = await staffApi.getById(id);
      return response.data;
    },
    enabled: !!id,
  });

  return {
    ...query,
    refetch: query.refetch,
  };
}

export function usePublicStaffDetail(id: string) {
  return useQuery({
    queryKey: staffKeys.detail(id),
    queryFn: async () => {
      const response = await staffApi.getPublicById(id);
      return response.data;
    },
    enabled: !!id,
  });
}

export function useCreateStaff() {
  const queryClient = useQueryClient();
  const [isTimeoutLoading, setIsTimeoutLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: async (data: Partial<CreateStaffPayload>) => {
      const response = await staffApi.create(data);
      return response;
    },
    onError: (error: any) => {
      setIsTimeoutLoading(false);
      toast.error(error.response?.data?.message || "Failed to create staff. Please try again.");
    },
    onSuccess: (newStaff) => {
      toast.success("Staff created successfully");
      setIsTimeoutLoading(true);

      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: staffKeys.list({}) });
        queryClient.invalidateQueries({ queryKey: departmentKeys.list({}) });
        setIsTimeoutLoading(false);
      }, 2000);
    },
  });

  return {
    ...mutation,
    isPending: mutation.isPending || isTimeoutLoading,
  };
}

interface UseUpdateStaffOptions {
  onSuccess?: (data: VirtualStaffModel) => void;
  onError?: (error: Error) => void;
}

export function useUpdateStaff(options: UseUpdateStaffOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Record<string, any> }) => {
      const response = await staffApi.update(id, data);
      return response;
    },
    onSuccess: (updatedStaff) => {
      queryClient.setQueryData(staffKeys.detail(updatedStaff.id), updatedStaff);
      onSuccess?.(updatedStaff);
    },
    onError,
  });
}

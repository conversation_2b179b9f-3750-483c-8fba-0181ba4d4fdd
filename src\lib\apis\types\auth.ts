export interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}

export interface UserInfoResponse {
  UserAttributes: {
    [key: string]: string;
  }[];
  Username?: string;
}

export interface AuthUser {
  Username?: string;
  UserAttributes?: {
    [key: string]: string;
  };
  Token?: TokenData;
}

export interface TokenData {
  AccessToken: string;
  ExpiresIn: number;
  TokenType: string;
  RefreshToken: string;
  IdToken: string;
}

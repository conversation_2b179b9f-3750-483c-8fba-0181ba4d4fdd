"use client";

import { useState } from "react";

import <PERSON><PERSON>ogo from "@/assets/images/facebook.png";
import Zalo<PERSON>ogo from "@/assets/images/zalo.png";

import { EmbedWidget } from "./embed_widget";
import { SocialPlatform } from "./social_integration";

interface IntegrationProps {
  staffId: string;
  onAuthorize?: (provider: string) => void;
  onEmbedCode?: () => void;
  onThemeColorChange?: (color: string) => void;
  staffImage?: string;
}

export function Integration({
  staffId,
  onAuthorize,
  onEmbedCode,
  onThemeColorChange,
  staffImage,
}: IntegrationProps) {
  const [platforms, setPlatforms] = useState({
    zalo: true,
    facebook: true,
  });

  const handleToggle = (platform: keyof typeof platforms) => (enabled: boolean) => {
    setPlatforms((prev) => ({
      ...prev,
      [platform]: enabled,
    }));
  };

  const socialPlatforms = [
    {
      id: "zalo",
      name: "<PERSON><PERSON>A",
      logo: <PERSON>alo<PERSON><PERSON>,
    },
    {
      id: "facebook",
      name: "Facebook",
      logo: <PERSON><PERSON><PERSON>,
    },
  ];

  return (
    <div className="space-y-2">
      <h2 className="text-base font-semibold">Integration</h2>

      <div className="space-y-4">
        {socialPlatforms.map((platform) => (
          <SocialPlatform
            key={platform.id}
            name={platform.name}
            logo={platform.logo}
            enabled={platforms[platform.id as keyof typeof platforms]}
            onToggle={handleToggle(platform.id as keyof typeof platforms)}
            onAuthorize={() => onAuthorize?.(platform.id)}
          />
        ))}
        <EmbedWidget
          staffId={staffId}
          onThemeColorChange={onThemeColorChange}
          staffImage={staffImage}
        />
      </div>
    </div>
  );
}

export interface IGetDepartmentsParams {
  limit?: number;
  page?: number;
  search?: string;
  sort_updated_at?: string;
}

export const departmentKeys = {
  all: ["departments"] as const,
  lists: () => [...departmentKeys.all, "list"] as const,
  list: (params: IGetDepartmentsParams) => [...departmentKeys.lists(), params] as const,
  details: () => [...departmentKeys.all, "detail"] as const,
  detail: (id: string) => [...departmentKeys.details(), id] as const,
};

"use client";

import { useMemo, useState } from "react";
import Image from "next/image";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { useInstallChannel } from "@/features/integration/hooks/install-channel";
import { useChannels } from "@/features/integration/hooks/use-channel";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardTitle } from "@/components/ui/card";
import { Form, FormField } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import InstallChannelSkeleton from "../../../../../features/integration/Channel/InstallChannel/skeleton";

export default function InstallChannelPage() {
  const router = useRouter();
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const { channel_key } = useParams();
  const { formFields, initialValues, validationSchema, isLoading, handleSubmit } =
    useInstallChannel(channel_key as string);
  const { data: channels } = useChannels({ enabled: true });
  const currentChannel = useMemo(
    () => channels?.find((channel) => channel.key === channel_key),
    [channels, channel_key]
  );

  const form = useForm<z.infer<typeof validationSchema>>({
    resolver: zodResolver(validationSchema),
    defaultValues: initialValues,
  });

  const onSubmit = async (data: z.infer<typeof validationSchema>) => {
    await handleSubmit(data);
  };

  if (isLoading) {
    return <InstallChannelSkeleton />;
  }

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex max-h-full min-h-full flex-col justify-between overflow-hidden">
          <Card className="m-4 mt-0  h-fit overflow-auto p-4 ">
            <div className="flex flex-col gap-4">
              <CardTitle className="text-base">Install {currentChannel?.name}</CardTitle>
              <div className=" flex items-center gap-4">
                <Image
                  src={currentChannel?.logo || ""}
                  alt={`${currentChannel?.name || "Channel"} Logo`}
                  width={64}
                  height={64}
                  className="rounded-lg"
                />
                <div className="flex flex-col justify-between gap-2 p-4">
                  <h1 className="text-lg font-semibold">{currentChannel?.name}</h1>
                  <p className="text-sm text-muted-foreground">{currentChannel?.description}</p>
                </div>
              </div>

              <div className="space-y-4">
                {formFields.map((field) => (
                  <FormField
                    key={field.name}
                    control={form.control}
                    name={field.name}
                    render={({ field: formField, fieldState }) => {
                      return (
                        <Input
                          label={field?.label as string}
                          {...formField}
                          type={field.type}
                          placeholder={field.placeholder}
                          error={fieldState.error?.message}
                        />
                      );
                    }}
                  />
                ))}
              </div>
            </div>
          </Card>
          <Card className="flex w-full flex-none justify-end gap-2 rounded-none border-x-0 p-6 pt-4">
            <Button variant="outline" type="button" onClick={() => setShowCancelDialog(true)}>
              Cancel
            </Button>
            <Button type="submit" loading={isLoading || form?.formState?.isSubmitting}>
              Install
            </Button>
          </Card>
        </form>
      </Form>

      <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Cancel Installation?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel? Any unsaved changes will be lost.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowCancelDialog(false)}>Stay</AlertDialogCancel>
            <AlertDialogAction onClick={() => router.push("/channels/supported-channels")}>
              Leave
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

"use client";

import { useEffect, useState } from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { CustomerAddress } from "@/features/customer/hooks/type";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface SelectAddressDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  addresses: CustomerAddress[];
  selectedAddressId?: string;
  onSelect: (address: CustomerAddress) => void;
  onEdit?: (address: CustomerAddress) => void;
  onAddAddress?: () => void;
  title?: string;
  defaultShippingKey?: "default_shipping" | "default_billing";
  customer?: {
    first_name?: string;
    last_name?: string;
    phone?: string;
  };
}

export function SelectAddressDialog({
  open,
  onOpenChange,
  addresses,
  onSelect,
  onEdit,
  onAddAddress,
  title = "Shipping Address",
  defaultShippingKey = "default_shipping",
  customer,
}: SelectAddressDialogProps) {
  // Create a unique key for each address based on its content
  const getAddressKey = (address: CustomerAddress) => {
    return `${address.address1}-${address.district}-${address.ward}-${address.province}`;
  };

  // Find the initially selected address
  const [selectedAddress, setSelectedAddress] = useState<CustomerAddress | null>(null);
  const [selectedKey, setSelectedKey] = useState<string>("");
  const { t } = useTranslation();
  // Reset selection and find default address when dialog opens
  // Modified useEffect to prioritize selected address over default
  useEffect(() => {
    if (open) {
      // If we have a selectedAddress from props, use that
      if (selectedAddress) {
        setSelectedAddress(selectedAddress);
        setSelectedKey(getAddressKey(selectedAddress));
      }
      // Only fall back to default if we don't have a selected address
      else {
        const defaultAddress = addresses.find((addr) => addr[defaultShippingKey]);
        if (defaultAddress) {
          setSelectedAddress(defaultAddress);
          setSelectedKey(getAddressKey(defaultAddress));
        } else {
          setSelectedAddress(null);
          setSelectedKey("");
        }
      }
    }
  }, [open, addresses, defaultShippingKey, selectedAddress]);

  const handleSelect = () => {
    if (selectedAddress) {
      // Create a new address object with the correct default flag set
      // Deep clone the address to ensure we're not modifying the original
      const addressToSave = JSON.parse(JSON.stringify(selectedAddress));

      // Set the appropriate flag to true and log it clearly
      addressToSave[defaultShippingKey] = true;

      // For billing addresses, ensure default_billing is true
      if (defaultShippingKey === "default_billing") {
        addressToSave.default_billing = true;
      }

      // For shipping addresses, ensure default_shipping is true
      if (defaultShippingKey === "default_shipping") {
        addressToSave.default_shipping = true;
      }
      onSelect(addressToSave);
      onOpenChange(false);
    }
  };

  const handleAddressSelect = (key: string) => {
    const address = addresses.find((addr) => getAddressKey(addr) === key);
    if (address) {
      setSelectedAddress(address);
      setSelectedKey(key);
    }
  };

  const handleEdit = (address: CustomerAddress) => {
    if (onEdit) {
      onEdit(address);
      onOpenChange(false);
    }
  };

  const getFullAddress = (address: CustomerAddress) => {
    const parts = [
      address.address1,
      address.ward,
      address.district,
      address.city,
      address.province,
    ].filter(Boolean);
    return parts.join(", ");
  };

  const getDisplayName = (address: CustomerAddress) => {
    // First check for recipient name
    if (address.name) return address.name;

    // If no recipient name, use customer name
    return `${customer?.first_name || address.first_name || ""}`.trim() || "—";
  };
  const getPhone = (address: CustomerAddress) => {
    if (address.phone) return address.phone;
    return customer?.phone || "";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-h-[90vh] max-w-xl flex-col gap-0 overflow-hidden p-0">
        <DialogHeader className="flex-none px-6 pb-4 pt-6">
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-6 py-4">
          {addresses.length > 0 ? (
            <RadioGroup value={selectedKey} onValueChange={handleAddressSelect}>
              {addresses.map((address) => {
                const addressKey = getAddressKey(address);
                return (
                  <div
                    key={addressKey}
                    onClick={() => handleAddressSelect(addressKey)}
                    className={`flex cursor-pointer items-center space-x-3 border-b pb-4`}>
                    <RadioGroupItem value={addressKey} id={`address-${addressKey}`} />
                    <label htmlFor={`address-${addressKey}`} className="flex-1 cursor-pointer">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{getDisplayName(address)}</span>
                          <div className="h-4 w-px bg-border" />
                          <span className="text-sm text-muted-foreground">{getPhone(address)}</span>
                        </div>
                        {onEdit && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="text-primary"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(address);
                            }}>
                            {t("pages.orders.edit")}
                          </Button>
                        )}
                      </div>
                      <p className="mt-1 text-sm text-muted-foreground">
                        {getFullAddress(address)}
                      </p>
                      {address[defaultShippingKey] && (
                        <div className="mt-2">
                          <Badge className="text-xs">
                            {defaultShippingKey === "default_shipping"
                              ? t("pages.orders.defaultShippingAddress")
                              : t("pages.orders.defaultBillingAddress")}
                          </Badge>
                        </div>
                      )}
                    </label>
                  </div>
                );
              })}
            </RadioGroup>
          ) : (
            <div className="py-6 text-center text-sm text-muted-foreground">
              {t("pages.orders.noAddressesFound")}
            </div>
          )}
        </div>

        <div className="flex-none border-t bg-card">
          <div className="px-6 pb-6 pt-4">
            <div className="flex items-center justify-between gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  onAddAddress?.();
                  onOpenChange(false);
                }}
                className="gap-2">
                <Plus className="size-4" />
                {t("pages.orders.addAddress")}
              </Button>

              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  className="min-w-[80px]"
                  onClick={() => onOpenChange(false)}>
                  {t("pages.orders.cancel")}
                </Button>
                <Button
                  type="button"
                  className="min-w-[80px]"
                  onClick={handleSelect}
                  disabled={!selectedAddress}>
                  {t("pages.orders.save")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

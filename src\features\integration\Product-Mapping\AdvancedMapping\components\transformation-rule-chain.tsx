import { capitalize } from "lodash";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";

import { Transformation } from "../hooks/types";

interface TransformationRuleChainProps {
  transformation: Transformation;
  index: number;
  sourceField: string;
  sourceData?: any;
}

export function TransformationRuleChain({
  transformation,
  index,
  sourceField,
  sourceData,
}: TransformationRuleChainProps) {
  const { t } = useTranslation();

  const ruleConfiguration = {
    type: transformation.type || "direct",
    sourceFields: [sourceField],
    config: transformation.config || {},
  };

  // Use real source data if available, otherwise use mockup data
  const productData = sourceData || {
    title: "Blue Denim Jacket",
    description: "Classic denim jacket in blue",
    price: 49.99,
    brand: "Fashion Brand",
    size: "medium",
    dimensions: "10x20x5",
    tags: ["denim", "blue", "jacket", "fashion"],
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">
            {t("pages.productMapping.advancedMapping.transformationChain")}
          </h4>
          <Badge variant="sematic_info" className="border-none px-2">
            {index + 1}
          </Badge>
          {transformation.type && (
            <Badge variant="sematic_default" className="border-none bg-muted px-2">
              {capitalize(transformation.type)}
            </Badge>
          )}
        </div>
        <div className="rounded-md border bg-muted p-2">
          <pre className="whitespace-pre-wrap break-all text-sm">
            {JSON.stringify(ruleConfiguration, null, 2)}
          </pre>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="text-sm font-medium">
          {t("pages.productMapping.advancedMapping.sampleData")}
        </h4>
        <div className="space-y-4 rounded-md border bg-muted p-2">
          <pre className="whitespace-pre-wrap break-all text-sm">
            {JSON.stringify(productData, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}

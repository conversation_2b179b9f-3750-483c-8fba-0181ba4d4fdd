import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseAxiosDetail, ResponseList } from "./types/common";

export interface ObjectData {
  reason: string;
  statusDescription: string;
  event_type: string;
  shopOrderId: string;
  orderId: number;
  trackingUrl: string;
  depotId: number;
  deliveryDate: string;
  status: string;
}

export interface FetchEvent {
  id: string;
  company_id: string;
  status: string;
  action_type: string;
  created_at: string;
  updated_at: string;
  event_source: string;
  channel?: string;
  continuation_token: string;
  is_batch: boolean;
  action_group: string;
  connection_id: string;
  event_time: string;
  batch_id: string;
  object_id: string;
  retry_count: number;
}

export const fetchEventApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<FetchEvent>>(
      ENDPOINTS.INTEGRATION_ENDPOINTS.FETCH_EVENT_LIST,
      { params }
    );
  },
  detail: async (id: string) => {
    return await privateApi.get<ResponseAxiosDetail<FetchEvent[]>>(
      ENDPOINTS.INTEGRATION_ENDPOINTS.FETCH_EVENT_DETAIL(id)
    );
  },
};

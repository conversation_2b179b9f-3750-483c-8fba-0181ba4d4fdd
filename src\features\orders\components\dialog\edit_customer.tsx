"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import * as z from "zod";

import {
  Customer,
  useAddCustomer,
  useCustomerGroups,
  useUpdateCustomer,
} from "@/features/customer/hooks/customer";
import { CustomerAddress } from "@/features/customer/hooks/type";
import { AddressData } from "@/features/orders/components/address";
import { AddressList } from "@/features/orders/components/address_list";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Combobox } from "@/components/ui/combobox";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface EditCustomerDialogProps {
  customer?: Customer;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  initialEditAddress?: CustomerAddress | null;
  focusFirstName?: boolean;
  mode?: "create" | "edit";
}

const genders = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];

// Define the form schema with zod
const customerFormSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().optional(),
  phone: z.string().min(1, "Phone number is required"),
  email: z.string().min(1, "Email is required").email(),
  gender: z.string().optional(),
  birthday: z.date().optional().nullable(),
  customer_group_id: z.string().optional(),
  company_name: z.string().optional(),
});

type CustomerFormValues = z.infer<typeof customerFormSchema>;

export function EditCustomerDialog({
  customer,
  open,
  onOpenChange,
  onSuccess,
  initialEditAddress,
  focusFirstName = false,
  mode = "edit",
}: EditCustomerDialogProps) {
  const { t } = useTranslation();
  const [customerAddresses, setCustomerAddresses] = useState<AddressData[]>([]);

  const { data: customerGroups = [], isLoading: isLoadingGroups } = useCustomerGroups(open);
  const { mutate: updateCustomer, isPending: isUpdatingMutation } = useUpdateCustomer();
  const { mutate: createCustomer, isPending: isCreatingMutation } = useAddCustomer();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Combine mutation loading state with our local loading state
  const isUpdating = isUpdatingMutation || isSubmitting;
  const isCreating = isCreatingMutation || isSubmitting;

  // Set up form with zod validation
  const form = useZodForm({
    schema: customerFormSchema,
    defaultValues: {
      first_name: customer?.first_name || "",
      last_name: customer?.last_name || "",
      phone: customer?.phone || "",
      email: customer?.email || "",
      gender: customer?.gender || "",
      birthday: customer?.birthday ? new Date(customer.birthday) : null,
      customer_group_id: customer?.customer_group?.id || "",
      company_name: customer?.company_name || "",
    },
  });

  // Initialize customer data when dialog opens
  useEffect(() => {
    if (open) {
      form.reset({
        first_name: customer?.first_name || "",
        last_name: customer?.last_name || "",
        phone: customer?.phone || "",
        email: customer?.email || "",
        gender: customer?.gender || "",
        birthday: customer?.birthday ? new Date(customer.birthday) : null,
        customer_group_id: customer?.customer_group?.id || "",
        company_name: customer?.company_name || "",
      });

      // Add delay to ensure form is rendered
      setTimeout(() => {
        if (focusFirstName) {
          // Focus on first name input when editing from customer component
          const firstNameInput = document.querySelector('input[placeholder="Enter first name"]');
          if (firstNameInput) {
            (firstNameInput as HTMLInputElement).focus();
          }
        } else if (initialEditAddress) {
          // Focus on address when editing from select address dialog
          const addressInputs = document.querySelectorAll('input[data-input-type="address"]');
          const matchingInput = Array.from(addressInputs).find(
            (input) => (input as HTMLInputElement).value === initialEditAddress.address1
          );

          if (matchingInput) {
            // First scroll to the addresses section
            const addressesSection = document.querySelector(".space-y-2.pb-2");
            if (addressesSection) {
              addressesSection.scrollIntoView({ behavior: "smooth", block: "start" });
            }

            // Then scroll to and focus the specific input
            matchingInput.scrollIntoView({ behavior: "smooth", block: "center" });
            (matchingInput as HTMLInputElement).focus();

            // Add highlight effect
            const addressContainer = matchingInput.closest(".rounded-lg");
            if (addressContainer) {
              addressContainer.classList.add("ring-2", "ring-primary");
              setTimeout(() => {
                addressContainer.classList.remove("ring-2", "ring-primary");
              }, 2000);
            }
          }
        }
      }, 300);
    }
  }, [customer, open, form, initialEditAddress, focusFirstName]);

  const handleAddressChange = (addresses: AddressData[]) => {
    setCustomerAddresses(addresses);
  };

  // Convert AddressData to CustomerAddress format
  const formatAddressForSubmission = (addresses: AddressData[]) => {
    return addresses.map((addr) => ({
      address1: addr.address_line1,
      province: addr.province,
      district: addr.district,
      ward: addr.ward,
      name: addr.recipient_name || "",
      phone: addr.recipient_phone || "",
      default_shipping: addr.is_shipping_default,
      default_billing: addr.is_billing_default,
      ...(addr.recipient_name && { first_name: addr.recipient_name }),
      ...(addr.recipient_name && { last_name: "" }),
      ...(addr.recipient_phone && { phone: addr.recipient_phone }),
    }));
  };

  // Handle form submission
  const onSubmit = (data: CustomerFormValues) => {
    console.log("Customer group selected:", data.customer_group_id);
    // Check for duplicate addresses
    const addressMap = new Map();
    let hasDuplicates = false;

    customerAddresses.forEach((addr) => {
      const key = `${addr.address_line1.toLowerCase()}-${addr.province.toLowerCase()}-${addr.district.toLowerCase()}-${addr.ward.toLowerCase()}`;

      if (addressMap.has(key)) {
        hasDuplicates = true;
      }
      addressMap.set(key, addr);
    });

    if (hasDuplicates) {
      toast.error("Duplicate addresses found. Please remove duplicate addresses before saving.");
      return;
    }

    const formattedAddresses = formatAddressForSubmission(customerAddresses);

    // Find the full customer group object from the selected ID
    const selectedCustomerGroup = data.customer_group_id
      ? customerGroups.find((group) => group.id === data.customer_group_id)
      : null;

    // Filter out unknown fields from the customer group object
    const filteredCustomerGroup = selectedCustomerGroup
      ? {
          id: selectedCustomerGroup.id,
          name: selectedCustomerGroup.name,
          discount_percent: selectedCustomerGroup.discount_percent,
          min_order_amount: selectedCustomerGroup.min_order_amount,
          max_discount_amount_per_order: selectedCustomerGroup.max_discount_amount_per_order,
          min_purchased_amount: selectedCustomerGroup.min_purchased_amount,
          default_price_group: selectedCustomerGroup.default_price_group,
          default_payment_method: selectedCustomerGroup.default_payment_method,
          next_group: selectedCustomerGroup.next_group,
          image: selectedCustomerGroup.image,
        }
      : null;

    // Prepare payload
    const payload = {
      first_name: data.first_name,
      last_name: data.last_name,
      phone: data.phone,
      email: data.email,
      gender: data.gender,
      birthday: data.birthday ? format(data.birthday, "yyyy-MM-dd") : undefined,
      company_name: data.company_name,
      // Include the filtered customer group instead of the raw one
      customer_group: filteredCustomerGroup || undefined,
      customer_group_id: data.customer_group_id,
      addresses: formattedAddresses as unknown as CustomerAddress[],
    };
    console.log("Payload:", payload);

    // Set local loading state to true
    setIsSubmitting(true);

    if (mode === "create") {
      createCustomer(payload, {
        onSuccess: () => {
          toast.success("Customer created successfully");
          setTimeout(() => {
            setIsSubmitting(false);
            onOpenChange(false);
            onSuccess?.();
          }, 5000);
        },
        onError: (error: any) => {
          setIsSubmitting(false);
          if (error === "Customer phone already existed") {
            toast.error(t("validation.phoneNumberAlreadyExists"));
          } else {
            toast.error("Failed to create customer");
          }
        },
      });
    } else {
      updateCustomer(
        { ...payload, id: customer!.id },
        {
          onSuccess: () => {
            toast.success("Customer updated successfully");
            setTimeout(() => {
              setIsSubmitting(false);
              onOpenChange(false);
              onSuccess?.();
            }, 5000);
          },
          onError: (error: any) => {
            setIsSubmitting(false);
            if (error?.error === "error_customer_phone_existed") {
              toast.error(t("validation.phoneNumberAlreadyExists"));
            } else {
              toast.error("Failed to update customer");
            }
          },
        }
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-w-screen-lg flex-none flex-col gap-0 overflow-hidden p-0">
        <DialogHeader className="flex-none p-6">
          <DialogTitle>
            {mode === "create" ? t("pages.orders.addCustomer") : t("pages.orders.editCustomer")}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit(onSubmit)(e);
            }}
            className="flex-1 overflow-y-auto px-6 py-2">
            <div className="space-y-6">
              {/* First Row */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <FormField
                    control={form.control}
                    name="first_name"
                    render={({ field, formState }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          {t("pages.orders.Name")}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder={t("pages.orders.enterName")}
                            className={formState.errors.first_name ? "ring-1 ring-destructive" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {/* Phone Number */}
                <div className="space-y-2">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field, formState }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          {t("pages.orders.phoneNumber")}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="tel"
                            data-input-type="phone"
                            placeholder={t("pages.orders.enterPhoneNumber")}
                            onKeyPress={(e) => {
                              if (
                                !/[0-9]/.test(e.key) &&
                                e.key !== "Backspace" &&
                                e.key !== "Delete" &&
                                e.key !== "ArrowLeft" &&
                                e.key !== "ArrowRight"
                              ) {
                                e.preventDefault();
                              }
                            }}
                            onChange={(e) => {
                              const value = e.target.value.replace(/[^0-9]/g, "");
                              field.onChange(value);
                            }}
                            className={formState.errors.phone ? "ring-1 ring-destructive" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-12 gap-4">
                <div className="col-span-6 space-y-2">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field, formState }) => (
                      <FormItem>
                        <FormLabel>
                          {t("pages.orders.email")}
                          <span className="text-destructive"> *</span>
                        </FormLabel>

                        <FormControl>
                          <Input
                            type="email"
                            {...field}
                            placeholder={t("pages.orders.enterEmail")}
                            className={formState.errors.email ? "ring-1 ring-destructive" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="col-span-3 space-y-2">
                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("pages.orders.gender")}</FormLabel>
                        <Combobox
                          value={field.value || ""}
                          onValueChange={field.onChange}
                          items={genders.map((gender) => ({
                            id: gender.value,
                            name: gender.label,
                          }))}
                          placeholder={t("pages.orders.selectGender")}
                          searchPlaceholder="Search genders..."
                          emptyText="No genders found."
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="col-span-3 space-y-2">
                  <FormField
                    control={form.control}
                    name="birthday"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("pages.orders.birthday")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              type="button"
                              variant="outline"
                              className={cn(
                                "w-full justify-between text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}>
                              <span>
                                {field.value
                                  ? format(field.value, "PPP")
                                  : t("pages.orders.pickADate")}
                              </span>
                              <CalendarIcon className="size-4 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value || undefined}
                              onSelect={(date) => {
                                field.onChange(date);
                              }}
                              disabled={(date) => date > new Date()}
                              initialFocus
                              className="rounded-md bg-popover"
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Customer Group */}
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="customer_group_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("pages.orders.customerGroup")}</FormLabel>
                      <Combobox
                        value={field.value || ""}
                        onValueChange={(value) => {
                          console.log("Customer group selected:", value);
                          field.onChange(value);
                        }}
                        items={customerGroups.map((group) => ({
                          id: group.id,
                          name: group.name,
                        }))}
                        placeholder={t("pages.orders.selectCustomerGroup")}
                        searchPlaceholder="Search customer groups..."
                        emptyText="No customer groups found."
                        isLoading={isLoadingGroups}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Company name */}
              <div className="space-y-2">
                <FormField
                  control={form.control}
                  name="company_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("pages.orders.companyName")}</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder={t("pages.orders.enterCompanyName")} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Address Section */}
              <div className="space-y-2 pb-2">
                {customerAddresses.length > 0 && (
                  <div className="font-medium">{t("pages.orders.addresses")}</div>
                )}
                <AddressList customer={customer} onChange={handleAddressChange} />
              </div>
            </div>
          </form>
        </Form>

        <div className="flex-none border-t bg-card">
          <div className="px-6 pb-6 pt-4">
            <div className="flex items-center justify-end gap-2">
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  {t("pages.orders.cancel")}
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={isUpdating || isCreating}
                loading={isUpdating || isCreating}
                onClick={() => form.handleSubmit(onSubmit)()}>
                {isUpdating || isCreating
                  ? t("pages.orders.submit")
                  : mode === "create"
                    ? t("pages.orders.submit")
                    : t("pages.orders.submit")}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

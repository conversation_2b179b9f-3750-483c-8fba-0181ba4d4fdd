import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { Row } from "@tanstack/react-table";

import { DateColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";

import { Task } from "../hooks/type";

export const columns = (t: any): CustomColumn<Task>[] => [
  {
    id: "name",
    accessorKey: "name",
    header: t("nav.order"),
    sorter: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Task> }) => {
      const task = row.original;
      return (
        <div
          className="flex cursor-pointer items-center gap-4 text-primary hover:underline"
          onClick={() => {
            window.location.href = `/tasks/${task.id}`;
          }}>
          <TextColumn text={`${task?.name}`} />
        </div>
      );
    },
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.orders.filters.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Task> }) => <DateColumn date={row?.original?.updated_at} />,
  },

  {
    id: "actions",
    header: t("common.actions"),
    cell: ({ row }: { row: Row<Task> }) => <ActionCell row={row} />,
  },
];

const ActionCell = ({ row }: { row: Row<Task> }) => {
  const router = useRouter();
  const task = row.original;
  const handleView = useCallback(() => {
    router.push(`/tasks/${task.id}` as any);
  }, [router, task.id]);

  const handleEdit = useCallback(() => {
    router.push(`/tasks/${task.id}/edit` as any);
  }, [router, task.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
      ]}
    />
  );
};

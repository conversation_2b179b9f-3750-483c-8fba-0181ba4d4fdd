"use client";

import { Switch } from "@/components/ui/switch";

interface SyncSettingsSectionProps {
  title: string;
  settings: {
    key: "products" | "inventory" | "orders";
    title: string;
    description: string;
  }[];
  syncSettings: {
    products: boolean;
    inventory: boolean;
    orders: boolean;
  };
  onToggle: (key: "products" | "inventory" | "orders") => void;
  disabled?: boolean;
}

export const SyncSettingsSection: React.FC<SyncSettingsSectionProps> = ({
  title,
  settings,
  syncSettings,
  onToggle,
  disabled = false,
}) => {
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">{title}</h2>
      <div className="space-y-3">
        {settings.map((setting) => (
          <div key={setting.key} className="flex items-center justify-between">
            <div>
              <p className="font-medium">{setting.title}</p>
              <p className="text-sm text-muted-foreground">{setting.description}</p>
            </div>
            <Switch
              checked={syncSettings[setting.key]}
              onCheckedChange={() => onToggle(setting.key)}
              disabled={disabled}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

import { District, Province, Ward } from "@/features/customer/hooks/type";

import { publicApi } from "../api_helper";
import { ResponseList } from "./types/common";

interface PaginationParams {
  limit?: number;
  page?: number;
  query?: string;
}

export const addressProvinceApi = {
  list: async (params?: PaginationParams) => {
    return await publicApi.get<ResponseList<Province>>("/static/get_provinces", { params });
  },
};

export const addressDistrictApi = {
  list: async (params?: PaginationParams) => {
    return await publicApi.get<ResponseList<District>>("/static/get_districts", { params });
  },
};

export const addressWardApi = {
  list: async (params?: PaginationParams) => {
    return await publicApi.get<ResponseList<Ward>>("/static/get_wards", { params });
  },
};

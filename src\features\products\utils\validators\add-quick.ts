import * as z from "zod";

const imageSchema = z.object({
  name: z.string(),
  image: z.string().startsWith("data:", "validation.invalidImageFormat"),
});

export const addQuickProductSchema = z.object({
  name: z.string().min(1, "validation.nameRequired").max(250, "validation.nameTooLong"),
  sku: z
    .string()
    .regex(/^[a-zA-Z0-9\-_]+$/, "validation.skuFormat")
    .min(1, "validation.skuRequired"),
  images: z.array(imageSchema).optional(),
  price: z
    .string()
    .min(1, "validation.priceRequired")
    .refine((val) => !isNaN(Number(val)), "validation.invalidPrice")
    .refine((val) => Number(val) > 0, "validation.priceMustBePositive"),
  brand: z.string().optional(),
  category: z.string().optional(),
  publish: z.boolean().default(false),
  tags: z.string().nullable(),
});

export type AddQuickProductValues = z.infer<typeof addQuickProductSchema>;

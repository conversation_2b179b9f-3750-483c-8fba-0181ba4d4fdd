export interface ICommonParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}

export const syncRecordKeys = {
  all: () => ["syncRecord"] as const,
  lists: () => [...syncRecordKeys.all(), "list"] as const,
  list: (params: ICommonParams) => [...syncRecordKeys.lists(), params] as const,
  details: () => [...syncRecordKeys.all(), "detail"] as const,
  detail: (id: string) => [...syncRecordKeys.details(), id] as const,
};

export const fetchEventKeys = {
  all: () => ["fetchEvent"] as const,
  lists: () => [...fetchEventKeys.all(), "list"] as const,
  list: (params: ICommonParams) => [...fetchEventKeys.lists(), params] as const,
  details: () => [...fetchEventKeys.all(), "detail"] as const,
  detail: (id: string) => [...fetchEventKeys.details(), id] as const,
};

export const connectionKeys = {
  all: () => ["connection"] as const,
  lists: () => [...connectionKeys.all(), "list"] as const,
  list: (params: ICommonParams) => [...connectionKeys.lists(), params] as const,
};

import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";

export const CustomerDetailSkeleton = () => {
  const { t } = useTranslation();
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card className="md:col-span-2">
        <CardHeader className="px-6 py-4">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {t("pages.customer.details.customerDetails")}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Left side - Customer information */}
            <div>
              <Table>
                <TableBody>
                  {[...Array(7)].map((_, i) => (
                    <TableRow key={i} className="border-b">
                      <TableCell className="py-3 pr-4 text-sm font-medium" width="120">
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell className="py-3 text-sm">
                        <Skeleton className="h-4 w-full max-w-md" />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Right side - Points and tags */}
            <div className="flex flex-col">
              <div className="rounded-md border border-dashed p-4">
                <div className="mb-4 flex items-center justify-between">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-6 w-24" />
                </div>

                <div className="mb-4 flex items-center justify-between">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-4 w-10" />
                </div>

                <Separator className="my-4" />

                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-10" />
                </div>
              </div>

              <div className="mt-4">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-10" />
                  <div className="flex flex-wrap justify-end gap-1">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-12" />
                    <Skeleton className="h-6 w-14" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export const PurchaseInfoSkeleton = () => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">
          <Skeleton className="h-5 w-36" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex border-b py-2.5">
              <div className="w-2/3 text-sm font-medium text-muted-foreground">
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="w-1/3 text-right font-medium">
                <Skeleton className="ml-auto h-4 w-12" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const SaleSuggestionSkeleton = () => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-base font-medium">
          <Skeleton className="h-5 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex border-b py-2.5">
              <div className="w-2/3 text-sm font-medium text-muted-foreground">
                <Skeleton className="h-4 w-28" />
              </div>
              <div className="w-1/3 text-right font-medium">
                <Skeleton className="ml-auto h-4 w-14" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const OrderHistorySkeleton = () => {
  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-base font-medium">
          <Skeleton className="h-5 w-24" />
        </CardTitle>
        <Skeleton className="h-9 w-20" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between border-b pb-4">
            <div className="flex space-x-2">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
            <Skeleton className="h-8 w-32" />
          </div>

          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between border-b py-3">
                <div className="flex flex-1 flex-col space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <div className="flex w-1/4 justify-end">
                  <Skeleton className="h-4 w-16" />
                </div>
                <div className="flex w-1/4 justify-end">
                  <Skeleton className="h-4 w-20" />
                </div>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between pt-2">
            <Skeleton className="h-8 w-24" />
            <div className="flex space-x-1">
              <Skeleton className="size-8" />
              <Skeleton className="size-8" />
              <Skeleton className="size-8" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const CustomersDetailSkeleton = () => {
  return (
    <div className="space-y-4 px-4 pb-20">
      <CustomerDetailSkeleton />

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <PurchaseInfoSkeleton />
        <SaleSuggestionSkeleton />
      </div>

      <OrderHistorySkeleton />

      {/* Skeleton for the sticky footer */}
      <div className="fixed inset-x-0 bottom-0 z-30 border-t bg-card p-4">
        <div className="container mx-auto flex justify-end space-x-4">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
    </div>
  );
};

export default CustomersDetailSkeleton;

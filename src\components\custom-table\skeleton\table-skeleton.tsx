import { ColumnDef } from "@tanstack/react-table";

import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

import { TableCell, TableRow } from "../../ui/table";

interface TableSkeletonProps<TData, TValue> {
  mainColumn?: string;
  columns: ColumnDef<TData, TValue>[];
}

const getSkeletonWidth = (columnId: string | undefined) => {
  switch (columnId) {
    case "select":
    case "expand":
      return "w-[48px]";
    case "actions":
      return "w-fit";
    default:
      return "w-[120px]";
  }
};

export function TableSkeleton<TData, TValue>({
  columns,
  mainColumn,
}: TableSkeletonProps<TData, TValue>) {
  return Array.from({ length: 10 }).map((_, index) => (
    <TableRow className="overflow-x-auto" key={index}>
      {columns.map((column, colIndex) => (
        <TableCell
          key={`${index}-${colIndex}`}
          className={cn(
            column.id === "select"
              ? "w-[48px] sticky left-0 z-20 bg-card group-hover:bg-table-hover transition-colors "
              : "",
            column.id === "actions"
              ? "w-fit sticky right-0 z-20 bg-card text-center group-hover:bg-table-hover transition-colors "
              : "",
            column.id === mainColumn ? "max-w-[400px]" : ""
          )}>
          {column.id === "select" || column.id === "expand" ? (
            <Skeleton className="size-8" />
          ) : column.id === mainColumn ? (
            <div className="flex items-center gap-4">
              <Skeleton className="size-12 flex-none rounded" />
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-[180px]" />
                <Skeleton className="h-4 w-[120px]" />
              </div>
            </div>
          ) : column.id === "actions" ? (
            <div className="flex items-center justify-center gap-2">
              <Skeleton className="size-8 rounded" />
            </div>
          ) : (
            <Skeleton className={cn("h-4", getSkeletonWidth(column.id))} />
          )}
        </TableCell>
      ))}
    </TableRow>
  ));
}

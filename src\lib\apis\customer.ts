import { Customer, CustomerGroup } from "@/features/customer/hooks/type";

import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

interface PaginationParams {
  limit?: number;
  page?: number;
  query?: string;
}

export const customerApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Customer>>("/actor/customers", {
      params,
    });
  },

  create: async (data: Partial<Customer>) => {
    return await privateApi.post<Customer>("/actor/customers", data);
  },

  update: async (id: string, data: Partial<Customer>) => {
    return await privateApi.put<Customer>(`/actor/customers/${id}`, data);
  },

  delete: async (id: string) => {
    return await privateApi.delete(`/actor/customers/${id}`);
  },

  getById: async (id: string) => {
    return await privateApi.get<Customer>(`/actor/customers/${id}`);
  },
};

export const customerGroupApi = {
  list: async (params?: PaginationParams) => {
    return await privateApi.get<ResponseList<CustomerGroup>>("/actor/customer_groups", {
      params,
    });
  },
};

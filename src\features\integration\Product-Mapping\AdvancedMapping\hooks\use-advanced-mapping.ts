import { useEffect, useState } from "react";

import { Transformation } from "./types";

export interface AdvancedMappingState {
  sourceField: string;
  transformations: Transformation[];
  activeTab: "output" | "rule";
}

export interface UseAdvancedMappingProps {
  initialSourceField?: string;
  initialTransformations?: Transformation[];
}

export function useAdvancedMapping({
  initialSourceField = "",
  initialTransformations = [],
}: UseAdvancedMappingProps = {}) {
  const [sourceField, setSourceField] = useState<string>(initialSourceField);
  const [transformations, setTransformations] = useState<Transformation[]>(() => {
    // Initialize with initial transformations if available, otherwise create empty one
    if (initialTransformations.length > 0) {
      const initialState = initialTransformations.map((t) => ({
        id: Date.now().toString(),
        type: t.type || "",
        config: t.config || {},
      }));
      return initialState;
    }
    return [{ id: Date.now().toString(), type: "", config: {} }];
  });
  const [activeTab, setActiveTab] = useState<"output" | "rule">("output");

  // Update transformations when initialTransformations change
  useEffect(() => {
    if (initialTransformations.length > 0) {
      const newTransformations = initialTransformations.map((t) => ({
        id: Date.now().toString(),
        type: t.type || "",
        config: t.config || {},
      }));
      setTransformations(newTransformations);
    }
  }, [initialTransformations]); // Only depend on initialTransformations changes

  const handleAddTransformation = () => {
    setTransformations([...transformations, { id: Date.now().toString(), type: "", config: {} }]);
  };

  const handleTransformationTypeChange = (transformationId: string, type: string) => {
    setTransformations(
      transformations.map((t) => {
        if (t.id === transformationId) {
          return { ...t, type, config: {} };
        }
        return t;
      })
    );
  };

  const handleRemoveTransformation = (transformationId: string) => {
    const updatedTransformations = transformations.filter((t) => t.id !== transformationId);
    // Ensure there's always at least one transformation
    if (updatedTransformations.length === 0) {
      setTransformations([{ id: Date.now().toString(), type: "", config: {} }]);
    } else {
      setTransformations(updatedTransformations);
    }
  };

  const handleConfigChange = (transformationId: string, config: any) => {
    setTransformations(
      transformations.map((t) => (t.id === transformationId ? { ...t, config } : t))
    );
  };

  return {
    sourceField,
    setSourceField,
    transformations,
    activeTab,
    setActiveTab,
    handleAddTransformation,
    handleTransformationTypeChange,
    handleRemoveTransformation,
    handleConfigChange,
  };
}

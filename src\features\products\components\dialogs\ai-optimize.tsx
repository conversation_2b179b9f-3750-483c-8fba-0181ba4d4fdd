import { useCallback, useEffect, useState } from "react";
import { RefreshCw } from "lucide-react";

import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { optimizeProduct } from "@/lib/apis/product";

interface AIOptimizeProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productName: string;
  description: string;
  onReplace?: (title: string, description: string) => void;
  hasOptimized: boolean;
  onOptimized: () => void;
  initialData?: {
    title: string;
    description: string;
    improvements: {
      title: string;
      description: string;
    };
    seo_impact: string | { original: string; optimized: string };
    readability_score: string | { original: string; optimized: string };
  } | null;
}

export function AIOptimize({
  open,
  onOpenChange,
  productName,
  description,
  onReplace,
  onOptimized,
  initialData,
}: AIOptimizeProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [currentInput, setCurrentInput] = useState({
    title: productName,
    description: description,
  });
  const [optimizedData, setOptimizedData] = useState(initialData);

  // Update inputs when props change
  useEffect(() => {
    setCurrentInput({
      title: productName,
      description: description,
    });
  }, [productName, description]);

  // Update optimizedData when initialData changes
  useEffect(() => {
    if (initialData) {
      setOptimizedData(initialData);
    }
  }, [initialData]);

  // Update optimize handler
  const handleOptimize = useCallback(async () => {
    try {
      setLoading(true);
      const response = await optimizeProduct({
        title: currentInput.title,
        description: currentInput.description,
        target_marketplace: "amazon" as "amazon" | "ebay" | "general",
        max_title_length: 200,
      });

      setOptimizedData({
        title: response?.result?.optimized?.title || "",
        description: response?.result?.optimized?.description || "",
        improvements: {
          title: response?.result?.analysis?.improvements?.title || "",
          description: response?.result?.analysis?.improvements?.description || "",
        },
        seo_impact: response?.result?.analysis?.seo_impact || "",
        readability_score: response?.result?.analysis?.readability_score || "",
      });

      // Notify parent of completion
      onOptimized();
    } catch {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to optimize content. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  }, [currentInput.description, currentInput.title, onOptimized, toast]);

  const handleReplace = () => {
    if (optimizedData && onReplace) {
      onReplace(optimizedData.title, optimizedData.description);
      onOpenChange(false);
    }
  };

  // Helper function to render any type of score/impact data
  const renderAnalysisData = (data: unknown): JSX.Element | string => {
    if (!data) return "---";
    if (typeof data === "string") return data;
    if (typeof data !== "object") return String(data);

    return (
      <div className="space-y-2">
        {Object.entries(data as Record<string, unknown>).map(([key, value]) => {
          if (value == null) return null;

          if (typeof value === "object") {
            return (
              <div key={key} className="border-l-2 border-muted pl-3">
                <div className="text-sm font-medium capitalize">{key.replace(/_/g, " ")}:</div>
                <div className="text-sm text-muted-foreground">{renderAnalysisData(value)}</div>
              </div>
            );
          }

          return (
            <div key={key} className="space-y-1">
              <span className="text-sm font-medium capitalize">{key.replace(/_/g, " ")}: </span>
              <span className="text-sm">{String(value)}</span>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex h-[90vh] flex-col sm:max-w-[1500px]">
        <DialogHeader>
          <DialogTitle>Optimize Content</DialogTitle>
        </DialogHeader>

        {/* Make the main content area scrollable */}
        <div className="flex-1 overflow-y-auto pr-2">
          {/* Headers in same row */}
          <div className="mb-4 grid grid-cols-[1fr,auto,1fr] gap-8">
            <h3 className="text-sm font-medium text-muted-foreground">Your content</h3>
            <div /> {/* Empty div for spacing */}
            <h3 className="text-sm font-medium text-muted-foreground">AI Suggestions</h3>
          </div>

          {/* Main content grid */}
          <div className="grid grid-cols-[1fr,auto,1fr] gap-8">
            {/* Left Column */}
            <div className="flex flex-col">
              {/* Title section with fixed height */}
              <div className="h-[90px]">
                <div className="flex h-8 items-center">
                  <h4 className="text-sm font-medium">Product Name</h4>
                </div>
                <Input
                  value={currentInput.title}
                  onChange={(e) => setCurrentInput((prev) => ({ ...prev, title: e.target.value }))}
                  className="h-[50px]"
                />
              </div>
              {/* Description section */}
              <div className="flex-1">
                <div className="flex h-8 items-center">
                  <h4 className="text-sm font-medium">Description</h4>
                </div>
                <Textarea
                  value={currentInput.description}
                  onChange={(e) =>
                    setCurrentInput((prev) => ({ ...prev, description: e.target.value }))
                  }
                  className="min-h-[120px] text-sm"
                />
              </div>
            </div>

            {/* Middle Column - Optimize Button */}
            <div className="flex items-start justify-center pt-12">
              <Button
                size="icon"
                onClick={handleOptimize}
                disabled={loading}
                className="sticky top-4 size-12">
                <RefreshCw className={`size-6 ${loading ? "animate-spin" : ""}`} />
              </Button>
            </div>

            {/* Right Column */}
            <div className="flex flex-col">
              {/* Title section with same fixed height */}
              <div className="h-[90px]">
                <div className="flex h-8 items-center">
                  <h4 className="text-sm font-medium">Optimized Title</h4>
                </div>
                <div className="h-[50px] overflow-auto rounded-md border p-2 text-sm">
                  {optimizedData?.title || "Click optimize to generate suggestions"}
                </div>
              </div>
              {/* Description section */}
              <div className="flex-1">
                <div className="flex h-8 items-center">
                  <h4 className="text-sm font-medium">Optimized Description</h4>
                </div>
                <div className="min-h-[120px] overflow-auto rounded-md border p-2 text-sm">
                  {optimizedData?.description || "Click optimize to generate suggestions"}
                </div>
              </div>
            </div>
          </div>

          {/* Analysis section */}
          <div className="mt-6">
            <h3 className="mb-4 text-sm font-medium">Analysis</h3>
            <div className="space-y-3 rounded-lg border p-4">
              <div className="flex">
                <span className="min-w-[140px] text-sm font-bold">Title Improvements:</span>
                <span className="flex-1 text-sm">
                  {optimizedData?.improvements?.title || "---"}
                </span>
              </div>

              <div className="flex">
                <span className="min-w-[140px] text-sm font-bold">Description Improvements:</span>
                <span className="flex-1 text-sm">
                  {optimizedData?.improvements?.description || "---"}
                </span>
              </div>

              <div className="flex">
                <span className="min-w-[140px] text-sm font-bold">Readability Score:</span>
                <span className="flex-1 text-sm">
                  {renderAnalysisData(optimizedData?.readability_score)}
                </span>
              </div>

              <div className="flex">
                <span className="min-w-[140px] text-sm font-bold">SEO Impact:</span>
                <span className="flex-1 text-sm">
                  {renderAnalysisData(optimizedData?.seo_impact)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer stays at bottom */}
        <div className="mt-6 flex justify-end gap-2 border-t pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleReplace} disabled={!optimizedData}>
            Replace
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

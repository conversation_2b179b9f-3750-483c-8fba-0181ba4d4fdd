import { fireEvent, render, screen, waitFor } from "@testing-library/react";

import "@testing-library/jest-dom";

import { I18nextProvider } from "react-i18next";

import i18n from "@/i18n";

import { ForgotPassword } from "./ForgotPassword";

jest.mock("@/assets/images/vcare-text.svg", () => "mock-svg");
jest.mock("@/assets/images/logo.png", () => "mock-logo");
jest.mock("@/assets/images/bg.png", () => "mock-bg");

// ✅ Mock Next.js Router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(), // Mock navigation
    replace: jest.fn(),
    prefetch: jest.fn(),
    pathname: "/forgot-password",
  }),
}));

describe("Component", () => {
  it("renders correctly", () => {
    const { container } = render(
      <I18nextProvider i18n={i18n}>
        <ForgotPassword />
      </I18nextProvider>
    );

    const email = container.querySelector('input[name="email"]');
    expect(email).toBeInTheDocument();

    expect(screen.getByRole("button", { name: "Send instructions" })).toBeInTheDocument();
  });
});

describe("Validate", () => {
  it("validate for empty email", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <ForgotPassword />
    //   </Provider>
    // );

    fireEvent.click(screen.getByRole("button", { name: "Send instructions" }));

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    });
  });

  it("validate for submit", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <ForgotPassword />
    //   </Provider>
    // );

    fireEvent.change(screen.getByLabelText("Email"), { target: { value: "<EMAIL> " } });

    const submitButton = screen.getByRole("button", { name: "Send instructions" });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(submitButton).toHaveTextContent(/sending/i);
    });
  });
});

// npm test -- features/auth/components/ForgotPassword/ForgotPassword.test.tsx

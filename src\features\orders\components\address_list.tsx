"use client";

import { useEffect, useState } from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Customer } from "@/features/customer/hooks/customer";

import { Button } from "@/components/ui/button";

import { Address, AddressData } from "./address";

interface AddressListProps {
  customer?: Customer;
  onChange?: (addresses: AddressData[]) => void;
  focusAddressForm?: boolean;
}

export function AddressList({ customer, onChange, focusAddressForm = false }: AddressListProps) {
  const [addresses, setAddresses] = useState<AddressData[]>([]);
  const [showRecipientInfo, setShowRecipientInfo] = useState<Record<string, boolean>>({});
  const { t } = useTranslation();
  // Add helper function to check for duplicate addresses
  const isDuplicateAddress = (address: AddressData, excludeId?: string) => {
    return addresses.some((existingAddr) => {
      if (excludeId && existingAddr.id === excludeId) {
        return false; // Skip comparing with self when editing
      }

      return (
        existingAddr.address_line1.toLowerCase() === address.address_line1.toLowerCase() &&
        existingAddr.province.toLowerCase() === address.province.toLowerCase() &&
        existingAddr.district.toLowerCase() === address.district.toLowerCase() &&
        existingAddr.ward.toLowerCase() === address.ward.toLowerCase()
      );
    });
  };

  // Initialize addresses from customer data if available
  useEffect(() => {
    if (customer?.addresses && customer.addresses.length > 0) {
      const formattedAddresses = customer.addresses.map((addr, index) => {
        // Determine if we need to show recipient info
        // Either if there's a name or phone in the address, or if there's first_name/last_name
        const hasRecipientInfo = !!(addr.name || addr.phone || addr.first_name || addr.last_name);
        const id = `address-${addr.id || index}`;

        // Always set showRecipientInfo based on if we have recipient data
        setShowRecipientInfo((prev) => ({
          ...prev,
          [id]: hasRecipientInfo,
        }));

        return {
          id,
          address_line1: addr.address1 || "",
          province: addr.province || "",
          province_code: addr.province_code || "",
          district: addr.district || "",
          district_code: addr.district_code || "",
          ward: addr.ward || "",
          ward_code: addr.ward_code || "",
          recipient_name: addr.name || "",
          recipient_phone: addr.phone || "",
          is_shipping_default: addr.default_shipping || false,
          is_billing_default: addr.default_billing || false,
          first_name: addr.first_name || "",
          last_name: addr.last_name || "",
        };
      });

      setAddresses(formattedAddresses);
    }
  }, [customer]);

  // Notify parent when addresses change
  useEffect(() => {
    onChange?.(addresses);
  }, [addresses, onChange]);

  const handleAddAddress = () => {
    const newAddress: AddressData = {
      id: `address-${Date.now()}`,
      address_line1: "",
      province: "",
      district: "",
      ward: "",
      is_shipping_default: addresses.length === 0,
      is_billing_default: addresses.length === 0,
    };
    setAddresses([...addresses, newAddress]);
    setShowRecipientInfo({ ...showRecipientInfo, [newAddress.id]: false });

    // Focus on the address input after a short delay to ensure it's rendered
    if (focusAddressForm) {
      setTimeout(() => {
        const addressInput = document.querySelector('input[placeholder="Enter address"]');
        if (addressInput) {
          (addressInput as HTMLInputElement).focus();
        }
      }, 100);
    }
  };

  const handleDeleteAddress = (id: string) => {
    const newAddresses = addresses.filter((address) => address.id !== id);

    // If we delete the default address and we have other addresses, set a new default
    if (newAddresses.length > 0) {
      const deletedAddress = addresses.find((a) => a.id === id);
      if (deletedAddress?.is_shipping_default) {
        newAddresses[0].is_shipping_default = true;
      }
      if (deletedAddress?.is_billing_default) {
        newAddresses[0].is_billing_default = true;
      }
    }

    setAddresses(newAddresses);

    // Clean up the recipient info state
    const newShowRecipientInfo = { ...showRecipientInfo };
    delete newShowRecipientInfo[id];
    setShowRecipientInfo(newShowRecipientInfo);
  };

  const handleChangeAddress = (id: string, data: Partial<AddressData>) => {
    // Check for duplicates when address fields are changed
    const updatedAddresses = addresses.map((address) => {
      if (address.id === id) {
        const updatedAddress = { ...address, ...data };

        // Only check for duplicates if all required fields are filled
        if (
          updatedAddress.address_line1 &&
          updatedAddress.province &&
          updatedAddress.district &&
          updatedAddress.ward
        ) {
          if (isDuplicateAddress(updatedAddress, id)) {
            toast.error("This address already exists");
            return address; // Return original address if duplicate
          }
        }
        return updatedAddress;
      }

      // If this address is being set as default, unset default on other addresses
      if (data.is_shipping_default && address.id !== id) {
        return { ...address, is_shipping_default: false };
      }
      if (data.is_billing_default && address.id !== id) {
        return { ...address, is_billing_default: false };
      }

      return address;
    });

    setAddresses(updatedAddresses);
  };

  const toggleRecipientInfo = (id: string) => {
    setShowRecipientInfo({
      ...showRecipientInfo,
      [id]: !showRecipientInfo[id],
    });
  };

  return (
    <div className="space-y-4">
      {addresses.length > 0 && (
        <div className="space-y-4">
          {addresses.map((address, index) => (
            <Address
              key={address.id}
              address={address}
              index={index}
              onDelete={handleDeleteAddress}
              onChange={handleChangeAddress}
              showRecipientInfo={showRecipientInfo[address.id]}
              toggleRecipientInfo={() => toggleRecipientInfo(address.id)}
              customer={customer}
            />
          ))}
        </div>
      )}

      <div className="flex justify-end">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleAddAddress}
          className="gap-2">
          <Plus className="size-4" />
          {t("pages.orders.addAddress")}
        </Button>
      </div>
    </div>
  );
}

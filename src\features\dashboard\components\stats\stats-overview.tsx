import { ChartLine, CircleDollarSign, Microscope, Users } from "lucide-react";
import { useTranslation } from "react-i18next";

import { StatsCard } from "@/features/dashboard/components/stats/stats-card";

interface StatsData {
  totalFacilities: {
    value: number;
    change: number;
  };
  totalPatients: {
    value: number;
    change: number;
  };
  averageOccupancy: {
    value: number;
    change: number;
  };
  totalRevenue: {
    value: number;
    change: number;
  };
}

interface StatsOverviewProps {
  data: StatsData;
}

export function StatsOverview({ data }: StatsOverviewProps) {
  const { t } = useTranslation();

  const formatValue = (key: keyof StatsData, value: number) => {
    switch (key) {
      case "totalRevenue":
        return `₫${value.toLocaleString()}`;
      case "averageOccupancy":
        return `${value}%`;
      default:
        return value.toLocaleString();
    }
  };

  const formatChange = (change: number) => {
    return `${change.toFixed(2)}%`;
  };

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-4">
      <StatsCard
        title={t("pages.overview.stats.totalFacilities")}
        value={formatValue("totalFacilities", data.totalFacilities.value)}
        change={formatChange(data.totalFacilities.change)}
        icon={<Microscope className="text-orange-500" size={20} />}
      />
      <StatsCard
        title={t("pages.overview.stats.totalPatients")}
        value={formatValue("totalPatients", data.totalPatients.value)}
        change={formatChange(data.totalPatients.change)}
        icon={<Users className="text-green-500" size={20} />}
      />
      <StatsCard
        title={t("pages.overview.stats.averageOccupancy")}
        value={formatValue("averageOccupancy", data.averageOccupancy.value)}
        change={formatChange(data.averageOccupancy.change)}
        icon={<ChartLine className="text-sky-500" size={20} />}
      />
      <StatsCard
        title={t("pages.overview.stats.totalRevenue")}
        value={formatValue("totalRevenue", data.totalRevenue.value)}
        change={formatChange(data.totalRevenue.change)}
        icon={<CircleDollarSign className="text-red-500" size={20} />}
      />
    </div>
  );
}

import { useState } from "react";
import { ChevronsUpDown } from "lucide-react";

import { useStaff } from "@/features/bots/staff/hooks/staff";

import defaultAvatar from "@/assets/images/staff/default_avatar.png";
import { Command, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CustomImage } from "@/components/ui/image";
import useDebounce from "@/hooks/use-debounce";
import { uppercaseToTitleCase } from "@/utils/helpers/text-formater";

import { VirtualStaffModel } from "../../hooks/type";

interface StaffSelectionProps {
  currentStaff: VirtualStaffModel;
  onSelect: (staff: VirtualStaffModel) => void;
}

export default function StaffSelection({ currentStaff, onSelect }: StaffSelectionProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  console.log()
  const { staff: staffList, isPending, loadMore } = useStaff({ query: debouncedSearchQuery });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex cursor-pointer items-center gap-2">
          <div className="flex min-w-[220px] items-center gap-3">
            <div className="relative">
              <CustomImage
                src={currentStaff?.image?.url || defaultAvatar.src}
                alt={currentStaff?.name || ""}
                width={40}
                height={40}
                className="size-10 flex-none rounded-lg object-cover"
              />
              <span className="absolute -bottom-1 -right-1 size-4 rounded-full border-4 border-card bg-sematic-success" />
            </div>
            <div className="flex max-w-[164px] flex-col ">
              <span className="flex items-center gap-1 truncate text-base font-semibold text-accent-foreground">
                {currentStaff?.name}
              </span>
              <span className="truncate text-sm text-muted-foreground">
                {uppercaseToTitleCase(currentStaff?.role || "")}
              </span>
            </div>
          </div>
          <ChevronsUpDown size={16} className="text-muted-foreground" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="">
        <Command>
          <CommandInput
            placeholder="Search staff..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList
            onScroll={(event) => {
              const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
              if (scrollHeight - scrollTop <= clientHeight * 1.5) {
                loadMore();
              }
            }}>
            {staffList.map((staff, index) => (
              <CommandItem
                key={staff?.id || index}
                onSelect={() => onSelect(staff!)}
                className={`flex items-center gap-2  ${staff?.id === currentStaff?.id ? "bg-muted dark:bg-bg-secondary" : ""}`}>
                <CustomImage
                  src={staff?.image?.url || defaultAvatar.src}
                  alt={staff?.name || ""}
                  width={28}
                  height={28}
                  className="rounded-md object-cover"
                />
                <div className="flex flex-col">
                  <span className="text-sm font-medium">{staff?.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {uppercaseToTitleCase(staff?.role || "")}
                  </span>
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

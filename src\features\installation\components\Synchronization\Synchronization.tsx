"use client";

import Loading from "@/app/loading";
import { AlertCircle } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslation } from "react-i18next";

import Illustration from "@/assets/images/illustration.svg";
import LogoShopify from "@/assets/images/logo-shopify.png";
import LogoTiktok from "@/assets/images/logo-tiktok.png";
import LogoTextOptiwarehouseDark from "@/assets/images/logotextv2-dark.svg";
import LogoTextOptiwarehouseLight from "@/assets/images/logotextv2.svg";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

import { useSynchronization } from "../../hooks/use-synchronization";
import { PlatformIntegration } from "../PlatformIntegration";
import { SyncSettingsSection } from "../SyncSettingsSection";

export const Synchronization = () => {
  const { t } = useTranslation();
  const { theme, systemTheme } = useTheme();

  const {
    error,
    isLoadingSource,
    isLoadingDestination,
    sourceConnection,
    destinationConnection,
    settingsConfig,
    syncSettings,
    isSuccess,
    message,
    isConnecting,
    handleSwitchToggle,
    onSyncSetting,
  } = useSynchronization();

  const currentMode = theme === "system" ? systemTheme : theme;
  const isDark = currentMode === "dark";
  const CustomLogo = isDark ? LogoTextOptiwarehouseDark : LogoTextOptiwarehouseLight;

  // Loading
  if (isLoadingSource || isLoadingDestination) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Loading />
      </div>
    );
  }

  // Success screen
  if (isSuccess) {
    return (
      <div className="flex min-h-screen flex-col items-center bg-bg-primary px-4 py-12">
        <div className="flex flex-col items-center">
          <CustomLogo
            className="left-20 top-5 xl:absolute"
            alt="Optiwarehouse Logo"
            width="200"
            height="100"
            viewBox="0 0 129 64"
          />
        </div>

        <div className="w-full max-w-3xl text-center">
          <h1 className="mb-2 text-center text-3xl font-bold text-foreground">
            {t("pages.synchronization.success.completeTitle")}
          </h1>
          <p className="mb-10 text-center text-muted-foreground">{message}</p>

          <Illustration alt="Optiwarehouse Logo" className="mx-auto" />

          <Button
            variant={"default"}
            onClick={() => (window.location.href = "/dashboard")}
            className="max-w-[408px]">
            {t("pages.synchronization.success.gotoDashboard")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center bg-bg-primary px-4 py-12">
      <div className="flex flex-col items-center">
        <CustomLogo
          className="left-20 top-5 xl:absolute"
          alt="Optiwarehouse Logo"
          width="200"
          height="100"
          viewBox="0 0 129 64"
        />
      </div>

      {/* Main Content */}
      <div className="w-full max-w-3xl">
        <h1 className="mb-2 text-center text-3xl font-bold text-foreground">
          {error
            ? t("pages.synchronization.title.error")
            : t("pages.synchronization.title.success", {
                source: sourceConnection?.name,
                destination: destinationConnection?.name,
              })}
        </h1>
        <p className="mb-10 text-center text-muted-foreground">
          {t("pages.synchronization.description")}
        </p>

        {/* Connection Error */}
        {error && (
          <Card className="mb-6 border-destructive bg-destructive/10 p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="size-4 text-destructive" />
              <div className="font-medium text-destructive">
                {t("pages.synchronization.error.connectionError")}
              </div>
            </div>
            <div className="mt-2 text-sm text-destructive">{t(error)}</div>
          </Card>
        )}

        {/* Hiển thị phần còn lại chỉ khi không có lỗi */}
        {!error && (
          <>
            {/* Integration Card */}
            {!isLoadingSource && !isLoadingDestination && (
              <div
                className="mb-10 overflow-hidden rounded-lg border border-transparent bg-card shadow-xl"
                style={{
                  background: !isDark
                    ? "linear-gradient(#FFFFFF, #FFFFFF) padding-box, linear-gradient(225deg, #ff7b00, #2b6cb0) border-box"
                    : "linear-gradient(#18181B, #18181B) padding-box, linear-gradient(225deg, #ff7b00, #2b6cb0) border-box",
                  borderWidth: "2px",
                  borderStyle: "solid",
                }}>
                <PlatformIntegration
                  leftPlatform={{
                    name: sourceConnection?.name || t("pages.synchronization.platforms.source"),
                    logo: sourceConnection?.logo ? sourceConnection.logo : LogoShopify,
                    logoAlt: t("pages.synchronization.platforms.sourceLogoAlt", {
                      name: sourceConnection?.name,
                    }),
                  }}
                  rightPlatform={{
                    name:
                      destinationConnection?.name ||
                      t("pages.synchronization.platforms.destination"),
                    logo: destinationConnection?.logo ? destinationConnection.logo : LogoTiktok,
                    logoAlt: t("pages.synchronization.platforms.destinationLogoAlt", {
                      name: destinationConnection?.name,
                    }),
                  }}
                  syncSettings={
                    syncSettings as {
                      products: boolean;
                      inventory: boolean;
                      orders: boolean;
                    }
                  }
                />
              </div>
            )}

            {/* Sync Settings */}
            {!isLoadingSource && !isLoadingDestination && (
              <div className="space-y-4">
                <SyncSettingsSection
                  title={t("pages.synchronization.syncSetting.title")}
                  settings={settingsConfig}
                  syncSettings={syncSettings}
                  onToggle={handleSwitchToggle}
                  disabled={!!error || isLoadingSource || isLoadingDestination}
                />

                {/* Connect Button */}
                <Button
                  variant={"default"}
                  onClick={() =>
                    onSyncSetting(sourceConnection?.id || "", destinationConnection?.id || "")
                  }
                  className="w-full"
                  loading={isConnecting}
                  disabled={!!error || isLoadingSource || isLoadingDestination}>
                  {t("pages.synchronization.syncSetting.buttonTitle", {
                    destination: destinationConnection?.name,
                  })}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

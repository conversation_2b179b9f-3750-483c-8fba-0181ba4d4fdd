<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>XBot Chat Embed Example</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 40px 20px;
      }

      header {
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 40px;
      }

      h1 {
        margin: 0;
        color: #333;
      }

      .content {
        background-color: #fff;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .section {
        margin-bottom: 30px;
      }

      .section h2 {
        color: #444;
        margin-top: 0;
      }

      .section p {
        color: #666;
        line-height: 1.6;
      }

      .footer {
        margin-top: 40px;
        text-align: center;
        color: #777;
        font-size: 14px;
      }

      #debug-console {
        background-color: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-family: monospace;
        margin-top: 20px;
        padding: 10px;
        height: 200px;
        overflow: auto;
      }
    </style>
  </head>
  <body>
    <header>
      <div class="container">
        <h1>XBot Chat Integration Example</h1>
      </div>
    </header>

    <div class="container">
      <div class="content">
        <div class="section">
          <h2>Welcome to our Website</h2>
          <p>
            This is an example page showing how to embed the XBot Chat widget into your website. The
            chat button should appear in the bottom right corner of your screen.
          </p>
        </div>

        <div class="section">
          <h2>How It Works</h2>
          <p>
            Simply click on the chat icon to open the support chat. Our virtual assistant is ready
            to help answer your questions 24/7.
          </p>
          <p>
            The chat widget is embedded using a simple script that creates an iframe to load the
            chat interface. It's designed to be non-intrusive and mobile-friendly.
          </p>
        </div>

        <div class="section">
          <h2>Features</h2>
          <ul>
            <li>Responsive design that works on desktop and mobile</li>
            <li>Customizable appearance</li>
            <li>Persistent chat sessions</li>
            <li>Easy to implement with a single script tag</li>
          </ul>
        </div>

        <div class="section">
          <h2>Debug Information</h2>
          <div id="debug-console"></div>
          <button id="check-script" style="margin-top: 10px">Check Script Status</button>
        </div>
      </div>

      <div class="footer">
        <p>&copy; 2023 XBot Chat Example. All rights reserved.</p>
      </div>
    </div>

    <!-- XBot Chat Widget Embed Code -->
    <!-- Virtual Staff Widget Container -->
    <!-- Virtual Staff Widget Container -->
<div id="xbot-container"></div>

<!-- Virtual Staff Widget Script -->
<script src="http://localhost:3000/xbot-embed.js" data-bot-id="6bc06ee2-d9d6-4e6d-a812-ac6096676bf6" data-theme-color="#FF9500" data-image="https://optiwarehouse-staging.s3.amazonaws.com/9e61d187-426a-45ec-914d-7aea8ca7d42d/images/virtual_staffs/default_avatar.png"></script>
</html>

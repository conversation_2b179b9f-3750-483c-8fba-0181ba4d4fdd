"use client";

import { useMemo, useState } from "react";
import { FileUp, PlusIcon, Zap } from "lucide-react";
import { useTranslation } from "react-i18next";

import { AddBulkProduct } from "@/features/products/components/add-bulk";
import { AddQuickProduct } from "@/features/products/components/add-quick";
import { columns } from "@/features/products/components/ProductList/column";
import { useProducts } from "@/features/products/hooks/product";
import { Product } from "@/features/products/hooks/types";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function ProductsPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const [showQuickAdd, setShowQuickAdd] = useState(false);
  const [showBulkAdd, setShowBulkAdd] = useState(false);
  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );
  const {
    products,
    total,
    isLoading,
    isFetching,
    refetch,
    useDeleteProductMutation,
    useDeleteListProductMutation,
  } = useProducts(options);

  const isTableLoading = isLoading || isFetching;
  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "products",
      searchPlaceHolder: t("pages.products.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "source.channel_name",
          type: "selectBox",
          title: t("pages.products.filters.source"),
          defaultValue: getInitialParams["source.channel_name"],
          isChannelFetch: true,
          remote: true,
        },
        {
          id: "category.id",
          type: "selectBox",
          title: t("pages.products.filters.category"),
          remote: true,
          pathUrlLoad: "product/categories",
          defaultValue: getInitialParams["category.id"],
        },
        {
          id: "brand.id",
          type: "selectBox",
          title: t("pages.products.filters.brand"),
          remote: true,
          pathUrlLoad: "product/brands",
          defaultValue: getInitialParams["brand.id"],
        },
        {
          id: "created_at",
          type: "date",
          title: t("pages.products.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: "date",
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);
  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "dropdown" as const,
        title: t("common.add"),
        icon: PlusIcon,
        items: [
          {
            type: "add_manual",
            title: t("pages.products.actions.addManual.title"),
            icon: PlusIcon,
            href: authProtectedPaths.PRODUCTS_NEW,
          },
          {
            type: "add_quick",
            title: t("pages.products.actions.addQuick"),
            icon: Zap,
            onClick: () => setShowQuickAdd(true),
          },
          {
            type: "add_bulk",
            title: t("pages.products.actions.addBulk"),
            icon: FileUp,
            onClick: () => setShowBulkAdd(true),
          },
        ],
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.products.title")}
        filterType="products"
        data={products || []}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(useDeleteProductMutation, isFetching, t)}
        data={products || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          const newList = listIndexId.map((row: any) => (products?.[row] as unknown as Product).id);
          useDeleteListProductMutation.mutate({
            listId: newList,
            handleRestRows,
          });
        }}
      />

      {showQuickAdd && <AddQuickProduct onClose={() => setShowQuickAdd(false)} />}
      {showBulkAdd && <AddBulkProduct onClose={() => setShowBulkAdd(false)} />}
    </TableCard>
  );
}

import { t } from "i18next";

import { useColorSetting, useUpdateColor } from "@/features/settings/hooks/color-setting";

import { Button } from "@/components/ui/button";

import { ColorSettingCard } from "./color-setting-card";
import { DATA_DEFAULT } from "./constants";

export default function ColorSetting() {
  const { localColors, handleColorChange, handleReset, isLoading, setLocalColors } =
    useColorSetting();
  const { updateColorMutation, isLoading: isUpdateLoading } = useUpdateColor();

  const handleSetDefault = () => {
    setLocalColors(DATA_DEFAULT);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {(["light", "dark"] as const).map((mode) => (
          <div key={mode} className="space-y-4 rounded-md border bg-muted p-4">
            <h3 className="text-lg font-semibold">
              {t(mode === "light" ? "Light Mode" : "Dark Mode")}
            </h3>
            <ColorSettingCard
              mode={mode}
              colors={localColors[mode]}
              onColorChange={handleColorChange}
            />
          </div>
        ))}
      </div>

      <div className="flex justify-end space-x-4">
        <Button variant="link" onClick={handleSetDefault} disabled={isLoading || isUpdateLoading}>
          {t("common.setAsDefault")}
        </Button>
        <Button variant="outline" onClick={handleReset} disabled={isLoading || isUpdateLoading}>
          {t("common.reset")}
        </Button>
        <Button
          disabled={isLoading || isUpdateLoading}
          loading={isLoading || isUpdateLoading}
          onClick={() => updateColorMutation(localColors)}>
          {t("common.save")}
        </Button>
      </div>
    </div>
  );
}

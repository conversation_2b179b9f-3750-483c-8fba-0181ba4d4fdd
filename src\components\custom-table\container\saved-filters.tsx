"use client";

import { memo, useCallback, useEffect, useRef, useState } from "react";
import { MoreVertical, Settings } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useSavedFilters } from "@/components/custom-table/hooks/use-saved-filters";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

import { MAX_DISPLAY_TABS } from "../constant";
import LucideIcon from "./lucide-icon";
import SaveFilterItem from "./save-filter-item";
import { SettingsDialog } from "./settings-dialog";

interface SavedFiltersProps {
  filterType: string;
  onFilterChange?: (filters: Record<string, unknown>) => void;
}

function SavedFilters({ filterType, onFilterChange }: SavedFiltersProps) {
  const {
    data,
    isLoading,
    isUpdating,
    isDeleting,
    activeTab,
    editingIndex,
    editValue,
    setEditValue,
    handleTabChange: originalHandleTabChange,
    handleEditFilter,
    handleAcceptFilter,
    handleConfirmDelete,
    handleCancelEdit,
    updateFilters,
    updateIcon,
  } = useSavedFilters({
    filterType,
    onFilterChange,
  });

  const { t } = useTranslation();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [selectedTabs, setSelectedTabs] = useState<Record<string, boolean>>({});
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  const scrollToActiveTab = useCallback(() => {
    if (activeTab && tabsContainerRef.current) {
      const activeTabIndex = data.findIndex((filter) => filter.id === activeTab);
      if (activeTabIndex >= 0 && tabRefs.current[activeTabIndex]) {
        const container = tabsContainerRef.current;
        const tab = tabRefs.current[activeTabIndex];

        // Calculate scroll position to center the tab
        const tabRect = tab.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        const scrollLeft = tab.offsetLeft - containerRect.width / 2 + tabRect.width / 2;

        // Smooth scroll to the tab
        container.scrollTo({
          left: scrollLeft,
          behavior: "smooth",
        });
      }
    }
  }, [activeTab, data]);

  // Custom tab change handler that also scrolls
  const handleTabChange = useCallback(
    (tabId: string) => {
      originalHandleTabChange(tabId);
      // We don't scroll here as the useEffect will handle it
    },
    [originalHandleTabChange]
  );

  const handleTabSelect = (id: string, checked: boolean) => {
    const selectedCount = Object.values(selectedTabs).filter(Boolean).length;
    if (checked && selectedCount >= 6) return;
    setSelectedTabs((prev) => ({ ...prev, [id]: checked }));
  };

  const handleIconChange = (id: string, icon: string) => {
    updateIcon(id, icon);
  };

  // Scroll to active tab when it changes
  useEffect(() => {
    scrollToActiveTab();
  }, [activeTab, data, scrollToActiveTab]);

  // Initial scroll to active tab on mount
  useEffect(() => {
    if (data.length > 0) {
      scrollToActiveTab();
    }
  }, [data.length, scrollToActiveTab]);

  // if (isLoading || isUpdating) {
  //   return <SavedFilterSkeleton />;
  // }
  return (
    <div className="flex flex-1 overflow-hidden">
      <div ref={tabsContainerRef} className="hide-scrollbar flex items-end gap-2 overflow-x-auto">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="flex items-end">
          <TabsList className="items-end rounded-none border-b border-transparent bg-transparent p-0">
            {data?.slice(0, MAX_DISPLAY_TABS + 1).map((filter, index) => (
              <TabsTrigger
                ref={(el: HTMLButtonElement | null) => {
                  tabRefs.current[index] = el;
                }}
                disabled={isUpdating}
                key={filter.id}
                value={filter.id}
                className={cn(
                  "group relative h-9 rounded-none border-b-2 border-transparent text-sm font-medium data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-foreground data-[state=active]:shadow-none px-5 items-end"
                )}>
                <SaveFilterItem
                  filter={filter}
                  editingIndex={editingIndex}
                  editValue={editValue}
                  setEditValue={setEditValue}
                  handleAcceptFilter={handleAcceptFilter}
                  handleEditFilter={handleEditFilter}
                  handleCancelEdit={handleCancelEdit}
                />
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
      {data.length > 1 && (
        <div className="ml-1 h-fit shrink-0 border-l">
          <Popover>
            <PopoverTrigger asChild>
              <button className="p-2">
                <MoreVertical size={16} />
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-fit min-w-[200px] p-1" align="end">
              <div className="max-h-[300px] space-y-0.5 overflow-y-auto">
                {data?.map((filter, index) => (
                  <button
                    key={filter.id}
                    className={cn(
                      "flex w-full items-center gap-4 rounded-sm px-2 py-1.5 text-sm hover:bg-accent ",
                      activeTab === filter.id && "bg-muted",
                      index > MAX_DISPLAY_TABS && "text-muted-foreground"
                    )}
                    onClick={() => {
                      handleTabChange(filter.id);
                      // Close popover after selection
                      const closeEvent = new Event("click", { bubbles: true });
                      document.dispatchEvent(closeEvent);
                    }}>
                    <div className="flex flex-1 items-center gap-2">
                      <LucideIcon
                        iconName={filter.icon || "Circle"}
                        className={!filter.icon ? "opacity-30" : ""}
                      />
                      <span className="max-w-[150px] flex-1 truncate text-left">{filter.name}</span>
                    </div>
                  </button>
                ))}
              </div>
              <div className="mt-1 border-t pt-1">
                <button
                  className="relative flex w-full items-center rounded-sm px-2 py-1.5 text-sm hover:bg-accent "
                  onClick={() => setIsSettingsOpen(true)}>
                  <Settings className="size-4 text-muted-foreground" />
                  <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-center">
                    {t("table.savedFilters.settings.settings")}
                  </span>
                </button>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      )}

      <SettingsDialog
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
        data={data}
        isUpdating={isUpdating}
        isDeleting={isDeleting}
        selectedTabs={selectedTabs}
        onTabSelect={handleTabSelect}
        handleUpdateFilters={updateFilters}
        onIconChange={handleIconChange}
        handleConfirmDelete={handleConfirmDelete}
      />
    </div>
  );
}

export default memo(SavedFilters);

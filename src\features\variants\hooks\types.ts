import { Brand } from "@/features/brand/hooks/types";
import { Category } from "@/features/category/hooks/types";
import { InventoryItem } from "@/features/inventory/hooks/types";
import { Price } from "@/features/prices/hooks/types";
import { Image } from "@/features/products/hooks/types";
import { Unit } from "@/features/units/hooks/types";

export interface Variant {
  id: string;
  name: string;
  original_sku: string;
  sku: string;
  product_id: string;
  prices: Price[];
  images?: Image[] | null;
  barcode: string;
  brand: Brand;
  category?: Category | null;
  created_at: string;
  updated_at: string;
  company_id: string;
  slug: string;
  unit?: Unit | null;
  inventories?: InventoryItem[] | null;
}

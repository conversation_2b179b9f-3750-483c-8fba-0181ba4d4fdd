"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON>, ResponsiveC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Axis } from "recharts";

import { Card } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface TopData {
  code: string;
  name: string;
  value: number;
}

type DataType = "diagnoses" | "medications";

interface TopTwentyProps {
  data: {
    diagnoses: TopData[];
    medications: TopData[];
  };
}

export function TopTwenty({ data }: TopTwentyProps) {
  const { t } = useTranslation();
  const [activeType, setActiveType] = useState<DataType>("diagnoses");

  return (
    <Card className="p-6">
      <div className="mb-6 flex flex-1 items-center justify-between">
        <h2 className="text-lg font-semibold">{t("pages.overview.topTwenty.title")}</h2>
        <Tabs
          defaultValue="diagnoses"
          value={activeType}
          onValueChange={(value) => setActiveType(value as DataType)}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="diagnoses">
              <span className="truncate">{t("pages.overview.topTwenty.icdDiagnoses")}</span>
            </TabsTrigger>
            <TabsTrigger value="medications">
              <span className="truncate">
                {t("pages.overview.topTwenty.prescribedMedications")}
              </span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={activeType === "diagnoses" ? data.diagnoses : data.medications}
          layout="vertical">
          <XAxis
            type="number"
            tickLine={false}
            axisLine={false}
            stroke="hsl(var(--muted-foreground))"
            fontSize={12}
            tickFormatter={(value) => value.toString()}
          />
          <YAxis
            type="category"
            dataKey={(data) => `${data.code} ${data.name}`}
            tickLine={false}
            axisLine={false}
            stroke="hsl(var(--muted-foreground))"
            fontSize={12}
            width={120}
            tickFormatter={(value) => {
              const [code, ...nameParts] = value.split(" ");
              return `${code}\n${nameParts.join(" ")}`;
            }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: "hsl(var(--background))",
              border: "1px solid hsl(var(--border))",
              borderRadius: "var(--radius)",
              padding: "8px 12px",
            }}
            cursor={{ fill: "hsl(var(--accent))" }}
          />
          <Bar
            dataKey="value"
            fill={activeType === "diagnoses" ? "hsl(var(--data-1))" : "hsl(var(--data-2))"}
            radius={4}
            barSize={81}
          />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );
}

"use client";

import { Controller, UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Switch } from "@/components/ui/switch";

import EditStaffCard from "../edit_staff_card";

function SkillSwitch({ label, control, name }: { label: string; control: any; name: string }) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { value, onChange } }) => (
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">{label}</span>
          <Switch checked={value} onCheckedChange={onChange} />
        </div>
      )}
    />
  );
}

interface SkillsTabProps {
  form: UseFormReturn<any>;
}

export default function SkillsTab({ form }: SkillsTabProps) {
  const { t } = useTranslation();

  return (
    <EditStaffCard
      title={t("pages.staff.skills.tab")}
      description={t("pages.staff.skills.description")}>
      <div className="space-y-4">
        <SkillSwitch label="Products" control={form.control} name="tools.products" />
        <SkillSwitch label="Orders" control={form.control} name="tools.orders" />
        <SkillSwitch label="Inventory" control={form.control} name="tools.inventory" />
      </div>
    </EditStaffCard>
  );
}

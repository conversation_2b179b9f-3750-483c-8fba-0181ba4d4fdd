"use client";

import { MoveHorizontal } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Combobox } from "@/components/ui/combobox";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";

import {
  COMMUNICATION_TONE_OPTIONS,
  LANGUAGE_OPTIONS,
  RESPONSE_LENGTH_OPTIONS,
} from "../constants/interaction_style";
import EditStaffCard from "../edit_staff_card";

interface PersonalitySliderProps {
  value: number;
  onChange: (value: number) => void;
  leftLabel: string;
  rightLabel: string;
  className?: string;
}

function PersonalitySlider({
  value,
  onChange,
  leftLabel,
  rightLabel,
  className = "",
}: PersonalitySliderProps) {
  return (
    <div className={`${className} space-y-[6px]`}>
      <div className="mb-1 flex items-center justify-between">
        <div className="flex gap-2">
          <span className="text-xs font-medium text-muted-foreground">{leftLabel}</span>
          <MoveHorizontal size={16} />
          <span className="text-xs font-medium text-muted-foreground">{rightLabel}</span>
        </div>
        <span className="text-xs font-medium leading-5 text-muted-foreground">{value}%</span>
      </div>
      <Slider
        value={[value]}
        onValueChange={(values) => onChange(values[0])}
        max={100}
        step={1}
        trackClassName="h-2"
        thumbClassName="size-5"
        className="w-full"
      />
    </div>
  );
}

interface InteractionStyleTabProps {
  form: UseFormReturn<any>;
}

export default function InteractionStyleTab({ form }: InteractionStyleTabProps) {
  const { t } = useTranslation();
  return (
    <EditStaffCard title={t("pages.staff.interactionStyle.tab")}>
      <Form {...form}>
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="personality.tone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("pages.staff.interactionStyle.communicationTone")}</FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value}
                    onValueChange={field.onChange}
                    items={COMMUNICATION_TONE_OPTIONS}
                    placeholder="Select Variant"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="personality.language"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("pages.staff.interactionStyle.languagePreferences")}</FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value}
                    onValueChange={field.onChange}
                    items={LANGUAGE_OPTIONS}
                    placeholder="Select Variant"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="personality.response_length"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("pages.staff.interactionStyle.responseLength")}</FormLabel>
                <FormControl>
                  <Combobox
                    value={field.value}
                    onValueChange={field.onChange}
                    items={RESPONSE_LENGTH_OPTIONS}
                    placeholder="Select Variant"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <div className="space-y-2">
            <p className="font-medium">{t("pages.staff.interactionStyle.personalityTraits")}</p>

            <FormField
              control={form.control}
              name="personality.personal_trait.formality"
              render={({ field }) => (
                <FormItem className="mb-4">
                  <FormControl>
                    <PersonalitySlider
                      value={field.value}
                      onChange={field.onChange}
                      leftLabel={t("pages.staff.interactionStyle.formal")}
                      rightLabel={t("pages.staff.interactionStyle.casual")}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="personality.personal_trait.detailed"
              render={({ field }) => (
                <FormItem className="mb-4">
                  <FormControl>
                    <PersonalitySlider
                      value={field.value}
                      onChange={field.onChange}
                      leftLabel={t("pages.staff.interactionStyle.detailed")}
                      rightLabel={t("pages.staff.interactionStyle.concise")}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="personality.personal_trait.creativity"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <PersonalitySlider
                      value={field.value}
                      onChange={field.onChange}
                      leftLabel={t("pages.staff.interactionStyle.creative")}
                      rightLabel={t("pages.staff.interactionStyle.analytical")}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="personality.ethical_constraints"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel>{t("pages.staff.interactionStyle.ethicalConstraints")}</FormLabel>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </div>
                <span className="text-sm text-muted-foreground">
                  {t("pages.staff.interactionStyle.contentFiltering")}
                </span>
              </FormItem>
            )}
          />
        </div>
      </Form>
    </EditStaffCard>
  );
}

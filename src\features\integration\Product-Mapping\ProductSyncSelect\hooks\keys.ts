import { ICommonParams } from "@/features/integration/hooks/keys";

export interface IProductDetailParams {
  connection_id?: string;
  product_id?: string;
}

export const productSyncSelectKeys = {
  all: () => ["productSyncSelect"] as const,
  lists: () => [...productSyncSelectKeys.all(), "list"] as const,
  list: (params: ICommonParams) => [...productSyncSelectKeys.lists(), params] as const,
  details: () => [...productSyncSelectKeys.all(), "detail"] as const,
  detail: (params: IProductDetailParams) =>
    [...productSyncSelectKeys.details(), params.connection_id, params.product_id] as const,
};

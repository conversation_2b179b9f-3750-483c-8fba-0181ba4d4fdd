"use client";

import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Card } from "@/components/ui/card";

interface CostData {
  name: string;
  insurance: number;
  service: number;
  specialCare: number;
}

interface InsuranceData {
  name: string;
  value: number;
}

interface TreatmentCostsProps {
  data: {
    costs: CostData[];
    insurance: InsuranceData[];
  };
}

export function TreatmentCosts({ data }: TreatmentCostsProps) {
  const { t } = useTranslation();

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Average Treatment Costs */}
      <Card className="p-6 ">
        <h2 className="mb-6 text-lg font-semibold">
          {t("pages.overview.costs.averageTreatmentCosts")}
        </h2>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data.costs}>
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "var(--radius)",
                padding: "8px 12px",
              }}
              cursor={{ fill: "hsl(var(--accent))" }}
            />
            <Bar dataKey="insurance" fill="hsl(var(--data-1))" radius={4} barSize={24} />
            <Bar dataKey="service" fill="hsl(var(--data-2))" radius={4} barSize={24} />
            <Bar dataKey="specialCare" fill="hsl(var(--data-3))" radius={4} barSize={24} />
          </BarChart>
        </ResponsiveContainer>
      </Card>

      {/* Insurance Payments */}
      <Card className="p-6">
        <h2 className="mb-6 text-lg font-semibold">
          {t("pages.overview.costs.insurancePayments")}
        </h2>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data.insurance}>
            <defs>
              <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--data-1))" stopOpacity={0.8} />
                <stop offset="95%" stopColor="hsl(var(--data-1))" stopOpacity={0} />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="name"
              tickLine={false}
              axisLine={false}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "var(--radius)",
                padding: "8px 12px",
              }}
            />
            <Area
              type="monotone"
              dataKey="value"
              stroke="hsl(var(--data-1))"
              fillOpacity={1}
              fill="url(#colorValue)"
            />
          </AreaChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
}

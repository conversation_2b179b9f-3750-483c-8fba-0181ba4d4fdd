"use client";

import type * as React from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { AnimatePresence, motion } from "framer-motion";
import type { LucideIcon } from "lucide-react";
import { useTheme } from "next-themes";

import LogoTextDark from "@/assets/images/logotextv2-dark.svg";
import LogoText from "@/assets/images/logotextv2.svg";
import LogoDark from "@/assets/images/logov2-dark.svg";
import Logo from "@/assets/images/logov2.svg";
import LogoOXBDark from "@/assets/logos/logo-OXB-dark.svg";
import LogoOXBIcon from "@/assets/logos/logo-OXB-icon.svg";
import LogoOXBLight from "@/assets/logos/logo-OXB-light.svg";
import { BranchSwitcher } from "@/components/Sidebar/components/branch-switcher";
import { NavGroup } from "@/components/Sidebar/components/nav-group";
import { NavUser } from "@/components/Sidebar/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarSeparator,
  useSidebar,
} from "@/components/ui/sidebar";
import { useVersion } from "@/lib/apis/version";
import { IS_ONE_X_BOT } from "@/utils/constants/common";

import { ScrollArea } from "../ui/scroll-area";
import { data } from "./data";

// Define types for the sidebar data structure
interface SubNavItem {
  title: string;
  url: string;
  isActive?: boolean;
}

interface NavItem {
  title: string;
  url?: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: SubNavItem[];
}

type NavItems = NavItem[];

const LogoComponent = ({ isOpen, isTablet }: { isOpen: boolean; isTablet: boolean }) => {
  const { theme, systemTheme } = useTheme();
  const { getSettingValue, isLoading } = useVersion();
  const shopInfo = getSettingValue<Record<string, any>>("shop_info");

  if (isLoading) return null;

  const currentMode = theme === "system" ? systemTheme : theme;
  const isDark = currentMode === "dark";
  const maxWidth = isOpen || true ? 300 : 48;
  const customLogo = isOpen
    ? isDark
      ? shopInfo?.darkLogo?.url
      : shopInfo?.lightLogo?.url
    : isDark
      ? shopInfo?.darkIcon?.url
      : shopInfo?.lightIcon?.url;

  const DefaultLogo = isOpen ? (isDark ? LogoTextDark : LogoText) : isDark ? LogoDark : Logo;
  const LogoOneXBot =
    isOpen || isTablet ? (isDark ? LogoOXBDark : LogoOXBLight) : isDark ? LogoOXBIcon : LogoOXBIcon;

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={`${isOpen}-${isDark}-${customLogo}`}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.1 }}
        className="flex size-full items-center justify-center">
        {IS_ONE_X_BOT ? (
          <LogoOneXBot
            maxWidth={maxWidth}
            height={48}
            className="max-h-[48px] max-w-full object-contain"
          />
        ) : customLogo ? (
          <Image
            src={
              isTablet ? (isDark ? shopInfo?.darkLogo?.url : shopInfo?.lightLogo?.url) : customLogo
            }
            alt="Logo"
            width={maxWidth}
            height={48}
            className="max-h-[48px] max-w-full object-contain"
          />
        ) : (
          <DefaultLogo
            width={maxWidth}
            height={48}
            className="max-h-[48px] max-w-full object-contain"
          />
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { open, isTablet, openTablet } = useSidebar();
  const pathname = usePathname();

  const getActiveItems = (items: NavItems): NavItems => {
    return items.map((item) => {
      if (item.items) {
        // For items with subitems
        const activeSubItem = item.items.some((subItem) => pathname.startsWith(subItem.url));
        return {
          ...item,
          isActive: activeSubItem,
          items: item.items.map((subItem) => ({
            ...subItem,
            isActive: pathname.startsWith(subItem.url),
          })),
        };
      }
      // For items without subitems
      return {
        ...item,
        isActive: pathname.startsWith(item.url as string),
      };
    });
  };

  const activeOverview = getActiveItems(
    data.overview.map((item) => ({
      ...item,
      icon: item.icon as unknown as LucideIcon,
    }))
  );
  const activeNavMain = getActiveItems(
    data.navMain.map((item) => ({
      ...item,
      icon: item.icon as unknown as LucideIcon,
    }))
  );
  const activeOperations = getActiveItems(
    data.operations.map((item) => ({
      ...item,
      icon: item.icon as unknown as LucideIcon,
    }))
  );
  const activeVirtualStaff = getActiveItems(
    data.virtual_staff?.map((item) => ({
      ...item,
      icon: item.icon as unknown as LucideIcon,
    })) || []
  );
  return (
    <Sidebar className="z-50" collapsible="icon" {...props}>
      <SidebarHeader className="px-0">
        <div className="flex h-16 shrink-0 items-center justify-center px-1">
          <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
            <LogoComponent isOpen={open} isTablet={isTablet && openTablet} />
          </Link>
        </div>
      </SidebarHeader>
      <ScrollArea className="flex-1">
        <SidebarContent className="flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-auto">
          <BranchSwitcher branches={data.branches} />
          <NavGroup items={activeOverview} />
          {!open && !isTablet && <SidebarSeparator />}
          <NavGroup items={activeNavMain} />
          {!open && !isTablet && <SidebarSeparator />}
          <NavGroup items={activeOperations} groupTitle="groups.operations" />
          {!open && !isTablet && <SidebarSeparator />}
          {IS_ONE_X_BOT && (
            <NavGroup items={activeVirtualStaff} groupTitle="groups.virtual_staff" />
          )}
        </SidebarContent>
      </ScrollArea>

      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}

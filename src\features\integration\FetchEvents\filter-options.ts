import { FilterOption } from "@/components/data-table/types";

export const ActionTypeOptions: FilterOption[] = [
  { value: "get_order", label: "Get Order" },
  { value: "sync_order", label: "Sync Order" },
  { value: "get_product", label: "Get Product" },
  { value: "get_inventory", label: "Get Inventory" },
  { value: "sync_image", label: "Sync Image" },
  { value: "webhook", label: "Webhook" },
  { value: "get_return_order", label: "Get Return Order" },
  { value: "sync_return_order", label: "Sync Return Order" },
  { value: "sync_inventory", label: "Sync Inventory" },
  { value: "sync_product", label: "Sync Product" },
  { value: "sync_purchase_order", label: "Sync Purchase Order" },
  { value: "get_purchase_order", label: "Get Purchase Order" },
];

export const ActionGroupOptions: FilterOption[] = [
  { value: "order", label: "Order" },
  { value: "product", label: "Product" },
  { value: "return_order", label: "Return Order" },
  { value: "inventory", label: "Inventory" },
  { value: "image", label: "Image" },
  { value: "webhook", label: "Webhook" },
  { value: "purchase_order", label: "Purchase Order" },

  { value: "unknown", label: "Unknown" },
];

export const EventSourceOption: FilterOption[] = [
  { value: "webhook", label: "Webhook" },
  { value: "sync_record_api", label: "Sync Record API" },
  { value: "sync_filter_api", label: "Sync Filter API" },
  { value: "scheduler", label: "Scheduler" },
];

export const StatusOptions: FilterOption[] = [
  { value: "PENDING", label: "Pending" },
  { value: "PROCESSING", label: "Processing" },
  { value: "FAILED", label: "Failed" },
  { value: "COMPLETED", label: "Completed" },
];

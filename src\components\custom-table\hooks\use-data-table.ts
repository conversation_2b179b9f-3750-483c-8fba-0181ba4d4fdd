"use client";

import { useCallback, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { DateRange } from "react-day-picker";
import * as XLSX from "xlsx";

import { CustomColumn } from "@/components/data-table/data-table";

export enum SortDirection {
  ASC = "asc",
  DESC = "desc",
}

export interface SortConfig {
  sorter?: boolean;
  defaultSortOrder?: SortDirection;
  sortKey?: string;
}

export interface ColumnSort {
  key: string;
  direction: SortDirection;
}

export interface ExportConfig<TData> {
  data: TData[];
  filename?: string;
  dateRange?: DateRange;
}

export default function useDatatable(customParams = {}) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const getInitialParams = useMemo(() => {
    const params: Record<string, unknown> = {
      page: 0,
      limit: 20,
      ...customParams,
    };
    searchParams?.forEach((value, key) => {
      params[key] = value;
    });
    return params;
  }, [searchParams]);
  const updateURL = useCallback(
    (params: Record<string, unknown>) => {
      params = {
        ...params,
        page: params.page || 0,
        limit: params.limit || 20,
      };
      const url = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          url.set(key, String(value));
        }
      });
      router.push(`?${url.toString()}`);
    },
    [router]
  );

  const handleParamSearch = useCallback(
    async (params: Record<string, unknown>) => {
      updateURL({ ...params });
    },
    [updateURL]
  );

  const handleSort = useCallback(
    (column: CustomColumn<unknown>) => {
      const sortKey = column.sortKey || column.id;
      let currentDirection: SortDirection | null = null;
      let currentSortKey: string | null = null;

      Object.entries(getInitialParams).forEach(([key, value]) => {
        if (key.startsWith("sort_")) {
          currentSortKey = key.replace("sort_", "");
          currentDirection = value as SortDirection;
        }
      });

      if (currentSortKey === sortKey) {
        if (currentDirection === SortDirection.ASC) {
          currentDirection = SortDirection.DESC;
        } else if (currentDirection === SortDirection.DESC) {
          currentDirection = null;
        } else {
          currentDirection = SortDirection.ASC;
        }
      } else {
        currentSortKey = sortKey!;
        currentDirection = SortDirection.ASC;
      }

      const newParams: Record<string, unknown> = { ...getInitialParams };
      Object.keys(newParams).forEach((key) => {
        if (key.startsWith("sort_")) {
          delete newParams[key];
        }
      });

      if (currentDirection) {
        newParams[`sort_${currentSortKey}`] = currentDirection;
      }

      handleParamSearch(newParams);
    },
    [getInitialParams, handleParamSearch]
  );

  const handlePageSizeChange = useCallback(
    (value: number) => {
      const params = { ...getInitialParams, page: 0, limit: value };
      handleParamSearch(params);
    },
    [getInitialParams, handleParamSearch]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      const params = { ...getInitialParams, page };
      handleParamSearch(params);
    },
    [getInitialParams, handleParamSearch]
  );

  const handleExport = useCallback(
    <TData>({ data, filename = "exportedData" }: ExportConfig<TData>) => {
      const date = new Date();
      const formattedDate = date.toLocaleDateString("en-CA");
      const hours = date.getHours();
      const minutes = date.getMinutes();
      const ampm = hours >= 12 ? "pm" : "am";
      const formattedTime = `${hours % 12 || 12}-${minutes.toString().padStart(2, "0")}${ampm}`;
      const finalFilename = `${filename}_${formattedDate}_${formattedTime}`;

      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

      XLSX.writeFile(wb, `${finalFilename}.xlsx`);
    },
    []
  );

  return {
    getInitialParams,
    handleParamSearch,
    handleSort,
    handlePageSizeChange,
    handlePageChange,
    handleExport,
  };
}

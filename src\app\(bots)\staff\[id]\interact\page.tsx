"use client";

import { useParams } from "next/navigation";



import ChatBox from "@/features/bots/staff/components/interact/chatbox";
import StaffHeader from "@/features/bots/staff/components/interact/header";
import StaffRadarChart from "@/features/bots/staff/components/interact/interact_radarchart";
import StaffKnowledgePanel from "@/features/bots/staff/components/interact/knowledge_panel";
import { RadarChartData } from "@/features/bots/staff/components/radar_chart";
import { useStaffDetail } from "@/features/bots/staff/hooks/staff";



import { Card, Separator } from "@/components/ui";
import Empty from "@/components/ui/empty";





export default function InteractPage() {
  const params = useParams();
  const { data: staff, isLoading, refetch } = useStaffDetail(params.id as string);
  const radarData: RadarChartData[] = [
    { subject: "Consistency", value: 80 },
    { subject: "Coverage", value: 60 },
    { subject: "Accuracy", value: 70 },
    { subject: "Friendliness", value: 50 },
    { subject: "Response Time", value: 75 },
    { subject: "Relevance", value: 64 },
  ];

  if (!staff && !isLoading) {
    return (
      <div>
        <Empty />
      </div>
    );
  }

  return (
    <Card className="m-4 mt-0 flex flex-auto flex-col lg:h-[calc(100vh-90px)] lg:overflow-y-hidden">
      <StaffHeader virtualStaff={staff!} />
      <Separator />
      <div className="flex flex-col overflow-hidden lg:flex-auto lg:flex-row">
        <ChatBox receiverRoles={["VIRTUAL_STAFF"]} staff={staff!} isLoading={isLoading} />
        <Separator orientation="vertical" className="hidden lg:block" />
        <Separator orientation="horizontal" className="lg:hidden" />
        <div className="flex flex-1 flex-col">
          <StaffKnowledgePanel staff={staff!} isLoading={isLoading} onRefetch={refetch} />
          <Separator />
          <StaffRadarChart radarData={radarData} />
        </div>
      </div>
    </Card>
  );
}
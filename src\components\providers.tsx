"use client";

import { usePathname } from "next/navigation";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { I18nextProvider } from "react-i18next";

import { AppSidebar } from "@/components/Sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { noSidebarPaths, publicPaths } from "@/constants/paths";
import i18n from "@/i18n";

import { Header } from "./Layout/header";
import ThemeProvider from "./theme-provider";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

export function Providers({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isPublicPath = Object.values(publicPaths).some((path) => {
    const routePattern = path.split(":")[0].replace(/\/$/, "");
    return pathname.startsWith(routePattern);
  });
  const isNoSidebarPath = Object.values(noSidebarPaths).some((path) => {
    const routePattern = path.split(":")[0].replace(/\/$/, "");
    return pathname.startsWith(routePattern);
  });
  return (
    <QueryClientProvider client={queryClient}>
      <I18nextProvider i18n={i18n}>
        <NextThemesProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange>
          {isPublicPath || isNoSidebarPath ? (
            children
          ) : (
            <ThemeProvider>
              <SidebarProvider>
                <div className="flex h-screen w-full">
                  <AppSidebar />
                  <main className="relative flex flex-1 flex-col overflow-hidden">
                    <Header
                      breadcrumb={[
                        {
                          label: "pages.overview.title",
                          href: "/dashboard",
                        },
                      ]}
                    />
                    <div className="flex-1 overflow-auto">{children}</div>
                  </main>
                </div>
              </SidebarProvider>
            </ThemeProvider>
          )}
        </NextThemesProvider>
      </I18nextProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

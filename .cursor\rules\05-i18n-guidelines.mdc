---
description: 
globs: 
alwaysApply: false
---
# Internationalization (i18n) Guidelines

## Translation File Structure
```typescript
// src/i18n/locales/en.json
{
  "common": {
    "buttons": {
      "submit": "Submit",
      "cancel": "Cancel",
      "save": "Save",
      "delete": "Delete"
    },
    "messages": {
      "success": "Operation successful",
      "error": "An error occurred",
      "loading": "Loading..."
    }
  },
  "auth": {
    "login": {
      "title": "Login",
      "email": "Email",
      "password": "Password"
    }
  }
}

// src/i18n/locales/vi.json
{
  "common": {
    "buttons": {
      "submit": "<PERSON>á<PERSON> nhận",
      "cancel": "Hủy bỏ",
      "save": "Lư<PERSON>",
      "delete": "Xóa"
    },
    "messages": {
      "success": "Thao tác thành công",
      "error": "Đã xảy ra lỗi",
      "loading": "Đang tải..."
    }
  },
  "auth": {
    "login": {
      "title": "<PERSON><PERSON>ng nhập",
      "email": "Email",
      "password": "<PERSON><PERSON>t khẩu"
    }
  }
}
```

## Translation Key Guidelines
1. Key Structure:
   - Use nested objects for better organization
   - Format: `section.subsection.key`
   - Example: `common.buttons.submit`

2. Naming Conventions:
   - Use camelCase for keys
   - Group related translations
   - Keep keys descriptive and consistent
   - Use plural suffix when needed (_plural)

## Usage in Components
```typescript
// Component usage example
import { useTranslation } from 'next-i18next';

export const LoginForm = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('auth.login.title')}</h1>
      <input 
        placeholder={t('auth.login.email')} 
        type="email" 
      />
      <input 
        placeholder={t('auth.login.password')} 
        type="password" 
      />
      <button>{t('common.buttons.submit')}</button>
    </div>
  );
};
```

## Translation Management
1. Organization:
   - Group translations by feature/module
   - Keep common phrases in `common` namespace
   - Use consistent terminology across languages

2. Key Categories:
   ```typescript
   {
     "common": {
       "buttons": {},      // Button labels
       "messages": {},     // System messages
       "labels": {},       // Form labels
       "placeholders": {}, // Input placeholders
       "errors": {},      // Error messages
       "titles": {}       // Page/section titles
     },
     "features": {
       "featureName": {
         "actions": {},    // Feature-specific actions
         "messages": {},   // Feature-specific messages
         "labels": {}      // Feature-specific labels
       }
     }
   }
   ```

## Adding New Translations
1. Process:
   - Add key to both `en.json` and `vi.json`
   - Follow existing naming patterns
   - Add comments for context if needed
   - Update type definitions if using TypeScript

2. Example:
   ```typescript
   // Adding new feature translations
   {
     "userProfile": {
       "title": "User Profile" | "Hồ sơ người dùng",
       "fields": {
         "fullName": "Full Name" | "Họ và tên",
         "phoneNumber": "Phone Number" | "Số điện thoại",
         "address": "Address" | "Địa chỉ"
       },
       "messages": {
         "updateSuccess": "Profile updated successfully" | "Cập nhật hồ sơ thành công",
         "updateError": "Failed to update profile" | "Cập nhật hồ sơ thất bại"
       }
     }
   }
   ```

## Best Practices
1. Translation Keys:
   - Keep keys in English
   - Use descriptive names
   - Avoid abbreviations
   - Group related translations

2. Content Guidelines:
   - Maintain consistent tone
   - Consider text length variations
   - Handle pluralization properly
   - Use variables for dynamic content

3. Code Integration:
   - Use translation hooks consistently
   - Handle missing translations gracefully
   - Implement fallback texts
   - Add type safety for keys

## Dynamic Content
```typescript
// Using variables in translations
{
  "notifications": {
    "welcome": "Welcome, {{name}}!" | "Xin chào, {{name}}!",
    "itemCount": "{{count}} items" | "{{count}} mục",
    "lastLogin": "Last login: {{date}}" | "Lần đăng nhập cuối: {{date}}"
  }
}

// Usage in component
const Welcome = ({ name }) => {
  const { t } = useTranslation();
  return (
    <div>
      {t('notifications.welcome', { name })}
    </div>
  );
};
```

## Testing Translations
1. Requirements:
   - Test for missing translations
   - Verify dynamic content
   - Check text overflow
   - Test RTL support if needed

2. Example Test:
   ```typescript
   describe('Translations', () => {
     it('should have matching keys in en and vi', () => {
       const enKeys = getAllKeys(en);
       const viKeys = getAllKeys(vi);
       expect(enKeys).toEqual(viKeys);
     });
   });
   ```

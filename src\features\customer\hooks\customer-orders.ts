import { useInfiniteQuery } from "@tanstack/react-query";

import { Order } from "@/features/orders/hooks/types";

import { orderApi } from "@/lib/apis/order";
import { ResponseList } from "@/lib/apis/types/common";

export const CUSTOMER_ORDER_KEYS = {
  all: ["customer", "orders"] as const,
  lists: () => [...CUSTOMER_ORDER_KEYS.all, "list"] as const,
  list: (filters: Record<string, any>) => [...CUSTOMER_ORDER_KEYS.lists(), filters] as const,
};

interface UseCustomerOrdersOptions {
  customerId: string;
  limit?: number;
  enabled?: boolean;
  page?: number;
  [key: string]: any;
}

export function useCustomerOrders(options: UseCustomerOrdersOptions) {
  const { customerId, limit = 20, enabled = true, ...restOptions } = options;

  const query = useInfiniteQuery<ResponseList<Order>>({
    queryKey: CUSTOMER_ORDER_KEYS.list({ customerId, limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      orderApi.list({
        page: pageParam as number,
        limit,
        "customer.id": customerId,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled: enabled && !!customerId,
  });

  const orders = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    orders,
    total,
  };
}

import { useCallback, useEffect, useMemo, useState } from "react";
import { Table } from "@tanstack/react-table";

export function useColumnView<TData>(table: Table<TData>) {
  const [searchQuery, setSearchQuery] = useState("");
  const [columnOrder, setColumnOrder] = useState<string[]>([]);
  const [tempVisibility, setTempVisibility] = useState<Record<string, boolean>>(
    table.getState().columnVisibility
  );

  const allColumnIds = table.getAllColumns().map((column) => column.id);
  const hasSelectColumn = allColumnIds.includes("select");
  const hasExpandColumn = allColumnIds.includes("expand");
  const hasActionColumn = allColumnIds.includes("actions");

  const { specialStartColumns, specialEndColumns } = useMemo(() => {
    const start: string[] = [];
    const end: string[] = [];

    if (hasSelectColumn) start.push("select");
    if (hasExpandColumn) start.push("expand");
    if (hasActionColumn) end.push("actions");

    return { specialStartColumns: start, specialEndColumns: end };
  }, [hasSelectColumn, hasExpandColumn, hasActionColumn]);

  const displayColumnOrder = columnOrder.filter(
    (id) => id !== "select" && id !== "expand" && id !== "actions"
  );

  useEffect(() => {
    const initialOrder = table.getState().columnOrder.length
      ? table.getState().columnOrder
      : [
          ...specialStartColumns,
          ...table
            .getAllColumns()
            .filter(
              (column) =>
                column.id !== "select" && column.id !== "expand" && column.id !== "actions"
            )
            .map((column) => column.id),
          ...specialEndColumns,
        ];
    setColumnOrder(initialOrder);
  }, [table, specialStartColumns, specialEndColumns]);

  const moveColumnToTop = useCallback(
    (columnId: string) => {
      if (searchQuery) {
        setSearchQuery("");
      }
      const updatedOrder = [...displayColumnOrder];
      const index = updatedOrder.indexOf(columnId);
      if (index > 0) {
        updatedOrder.splice(index, 1);
        updatedOrder.unshift(columnId);
        setColumnOrder([...specialStartColumns, ...updatedOrder, ...specialEndColumns]);
      }
    },
    [displayColumnOrder, searchQuery, specialStartColumns, specialEndColumns]
  );

  const handleToggleAll = useCallback(
    (value: boolean) => {
      const newState: Record<string, boolean> = {};
      displayColumnOrder.forEach((id) => {
        newState[id] = value;
      });
      setTempVisibility(newState);
    },
    [displayColumnOrder]
  );

  const handleToggleColumn = useCallback((columnId: string, value: boolean) => {
    setTempVisibility((prev) => ({
      ...prev,
      [columnId]: value,
    }));
  }, []);

  const handleSave = useCallback(() => {
    table.setColumnVisibility(tempVisibility);
    const finalOrder = [...specialStartColumns, ...displayColumnOrder, ...specialEndColumns];
    table.setColumnOrder(finalOrder);
    return true;
  }, [table, tempVisibility, specialStartColumns, displayColumnOrder, specialEndColumns]);

  const handleCancel = useCallback(() => {
    setTempVisibility(table.getState().columnVisibility);
  }, [table]);

  return {
    searchQuery,
    setSearchQuery,
    columnOrder,
    setColumnOrder,
    tempVisibility,
    displayColumnOrder,
    moveColumnToTop,
    handleToggleAll,
    handleToggleColumn,
    handleSave,
    handleCancel,
    specialStartColumns,
    specialEndColumns,
  };
}

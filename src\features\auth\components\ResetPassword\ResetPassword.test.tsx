import { fireEvent, render, screen, waitFor } from "@testing-library/react";

import "@testing-library/jest-dom";

import { I18nextProvider } from "react-i18next";

import i18n from "@/i18n";

import { ResetPassword } from "./ResetPassword";

jest.mock("@/assets/images/vcare-text.svg", () => "mock-svg");
jest.mock("@/assets/images/logo.png", () => "mock-logo");
jest.mock("@/assets/images/bg.png", () => "mock-bg");

// ✅ Mock Next.js Router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(), // Mock navigation
    replace: jest.fn(),
    prefetch: jest.fn(),
    pathname: "/reset-password",
  }),

  useSearchParams: jest.fn(() => ({
    get: (key: string) => {
      if (key === "email") return "<EMAIL>"; // ✅ Mock query parameter
      return null;
    },
  })),
}));

describe("Component", () => {
  it("renders correctly", () => {
    const { container } = render(
      <I18nextProvider i18n={i18n}>
        <ResetPassword />
      </I18nextProvider>
    );

    expect(container.querySelector('input[name="code"]')).toBeInTheDocument();
    expect(container.querySelector('input[name="newPassword"]')).toBeInTheDocument();
    expect(container.querySelector('input[name="confirmPassword"]')).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Reset password" })).toBeInTheDocument();
  });
});

describe("Validate", () => {
  it("validate for empty fields", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <I18nextProvider i18n={i18n}>
    //       <ResetPassword />
    //     </I18nextProvider>
    //   </Provider>
    // );

    fireEvent.click(screen.getByRole("button", { name: "Reset password" }));

    await waitFor(() => {
      expect(screen.getByText("Please confirm your code")).toBeInTheDocument();
      expect(screen.getByText("New password is required")).toBeInTheDocument();
      expect(screen.getByText("Please confirm your password")).toBeInTheDocument();
    });
  });

  it("validate for short password", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <ResetPassword />
    //   </Provider>
    // );

    fireEvent.change(screen.getByLabelText("New password"), { target: { value: "eg" } });

    fireEvent.click(screen.getByRole("button", { name: "Reset password" }));

    await waitFor(() => {
      expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
    });
  });

  it("validate password don't match", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <ResetPassword />
    //   </Provider>
    // );

    fireEvent.change(screen.getByLabelText("New password"), { target: { value: "adminadmin" } });
    fireEvent.change(screen.getByLabelText("Confirm password"), {
      target: { value: "adminadmin2" },
    });

    fireEvent.click(screen.getByRole("button", { name: "Reset password" }));

    await waitFor(() => {
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
    });
  });

  it("Validate for submit", async () => {
    // const { container } = render(
    //   <Provider store={mockStore}>
    //     <ResetPassword />
    //   </Provider>
    // );

    fireEvent.change(screen.getByLabelText("Verification code"), { target: { value: "111111" } });
    fireEvent.change(screen.getByLabelText("New password"), { target: { value: "Admin@123" } });
    fireEvent.change(screen.getByLabelText("Confirm password"), { target: { value: "Admin@123" } });

    const submitButton = screen.getByRole("button", { name: "Reset password" });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(submitButton).toHaveTextContent(/auth.resetting/i);
    });
  });
});

// npm test -- features/auth/components/ResetPassword/ResetPassword.test.tsx

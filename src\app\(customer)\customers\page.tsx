"use client";

import { useMemo, useState } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/customer/components/CustomerList/column";
import { useCustomerList } from "@/features/customer/hooks/customer";
import { Customer } from "@/features/customer/hooks/type";
import { EditCustomerDialog } from "@/features/orders/components/dialog/edit_customer";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";

export default function CustomersPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | undefined>(undefined);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { customers, isLoading, isFetching, refetch, total, useDeleteCustomerMutation } =
    useCustomerList(options);

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setOpenEditDialog(true);
  };

  const isTableLoading = isLoading || isFetching;
  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "customers",
      searchPlaceHolder: t("pages.customers.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "customer_group.id",
          type: EFilterType.SELECT_BOX,
          title: t("pages.customers.filters.group"),
          remote: true,
          pathUrlLoad: "actor/customer_groups",
          defaultValue: getInitialParams["customer_group.id"],
        },
        {
          id: "source.channel_name",
          type: EFilterType.SELECT_BOX,
          title: t("pages.products.filters.source"),
          defaultValue: getInitialParams["source.channel_name"],
          isChannelFetch: true,
          remote: true,
        },
        {
          id: "created_at",
          type: EFilterType.DATE,
          title: t("pages.customers.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: EFilterType.DATE,
          title: t("pages.customers.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("common.add"),
        icon: PlusIcon,
        onClick: () => setOpenCreateDialog(true),
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  const handleDialogSuccess = () => {
    refetch();
  };

  return (
    <TableCard>
      <TableHeader
        title={t("nav.customerList")}
        filterType="customers"
        data={customers || []}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(t, useDeleteCustomerMutation, false, handleEditCustomer)}
        data={customers || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          const newList = listIndexId.map(
            (row: any) => (customers?.[row] as unknown as Customer).id
          );

          // Delete customers one by one
          for (const id of newList) {
            await useDeleteCustomerMutation.mutateAsync(id);
          }

          // Call the handler after all deletions
          handleRestRows();
        }}
      />

      {/* Create customer dialog */}
      <EditCustomerDialog
        open={openCreateDialog}
        onOpenChange={setOpenCreateDialog}
        onSuccess={handleDialogSuccess}
        mode="create"
      />

      {/* Edit customer dialog */}
      <EditCustomerDialog
        customer={selectedCustomer}
        open={openEditDialog}
        onOpenChange={setOpenEditDialog}
        onSuccess={handleDialogSuccess}
        mode="edit"
      />
    </TableCard>
  );
}

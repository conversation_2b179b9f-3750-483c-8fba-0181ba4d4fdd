export type PricingTier = "free" | "starter" | "pro" | "agency";
interface PricingPlan {
  id: PricingTier;
  name: string;
  monthlyPrice: number;
  description: string;
  features: string[];
  popular?: boolean;
}

export const PRICING_PLANS: PricingPlan[] = [
  {
    id: "free",
    name: "Free",
    monthlyPrice: 0,
    description: "Choose to connect multiple stores",
    features: [
      "Up to 50 variants, overall statistics.",
      "Real-time inventory syncing.",
      "Ideal for startups (1,000 items).",
    ],
  },
  {
    id: "starter",
    name: "Starter",
    monthlyPrice: 59,
    description: "Choose to connect multiple stores",
    features: [
      "Up to 50 variants, overall statistics.",
      "Real-time inventory syncing.",
      "Ideal for startups (1,000 items).",
    ],
  },
  {
    id: "pro",
    name: "Pro",
    monthlyPrice: 99,
    description: "Choose to connect multiple stores",
    features: [
      "Up to 50 variants, overall statistics.",
      "Real-time inventory syncing.",
      "Ideal for startups (1,000 items).",
    ],
    popular: true,
  },
  {
    id: "agency",
    name: "Agency",
    monthlyPrice: 199,
    description: "Choose to connect multiple stores",
    features: [
      "Up to 50 variants, overall statistics.",
      "Real-time inventory syncing.",
      "Ideal for startups (1,000 items).",
    ],
  },
];

// Optional: If you want to export the pricing tiers as well
export const PRICING_TIERS: PricingTier[] = PRICING_PLANS.map((plan) => plan.id);

"use client";

import { useEffect, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UploadImage } from "@/lib/apis/product";

interface AddBrandDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: {
    name: string;
    image: { id: string; name: string; url: string } | null;
  }) => Promise<any>;
  initialName?: string;
}

export function AddBrandDialog({
  open,
  onOpenChange,
  onSubmit,
  initialName = "",
}: AddBrandDialogProps) {
  const [name, setName] = useState("");
  const [image, setImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (open && initialName) {
      setName(initialName);
    }
  }, [open, initialName]);

  const handleImageChange = async (base64: string | null) => {
    if (!base64) {
      setImage(null);
      return;
    }

    try {
      setIsUploading(true);
      const fileName = `brand_${Date.now()}.png`;
      const uploadedImage = await UploadImage({
        name: fileName,
        image: base64,
        prefix: "media",
      });
      setImage(uploadedImage.image);
    } catch (error) {
      console.error("Failed to upload image:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const result = await onSubmit({
        name,
        image: image ? { id: "1", name: "image", url: image } : null,
      });

      setName("");
      setImage(null);
      onOpenChange(false);

      return result;
    } catch (error) {
      console.error("Failed to create brand:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add new brand</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label>Brand name</Label>
            <Input
              placeholder="Enter brand name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label>Image</Label>
            <ImageUpload value={image} onChange={handleImageChange} className="h-[200px]" />
          </div>
          <Button className="w-full" onClick={handleSubmit} disabled={!name.trim() || isUploading}>
            Add
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

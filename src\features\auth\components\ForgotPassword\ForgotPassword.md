# Component Name

## Design Reference
See the `/designs` folder for:
- component-light.png - Light theme design
- component-dark.png - Dark theme design
- component-mobile.png - Mobile responsive design
- component-states.png - Component states and variants

## Requirements
- List of requirements derived from design
- Interaction behaviors
- Responsive design considerations

## Props
| Prop Name | Type | Description | Required |
|-----------|------|-------------|----------|
|           |      |             |          |

## Test Cases
- [ ] Basic rendering
- [ ] Props validation
- [ ] Interactive behavior
- [ ] Responsive design
- [ ] Edge cases

## Implementation Notes
- Any special considerations
- Dependencies
- Performance optimizations 
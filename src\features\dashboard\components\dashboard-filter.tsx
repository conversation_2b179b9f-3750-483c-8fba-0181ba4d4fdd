import { useState } from "react";
import { RefreshCw, Search } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import useDebounce from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

interface FilterProps {
  period: string;
  onPeriodChange: (value: string) => void;
  location: string;
  onLocationChange: (value: string) => void;
  onRefresh: () => void;
  isLoading: boolean;
}

export function DashboardFilter({
  period,
  onPeriodChange,
  location,
  onLocationChange,
  onRefresh,
  isLoading,
}: FilterProps) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedQuery = useDebounce(searchQuery, 300); // Use custom hook

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const periods = [
    { value: "daily", label: "pages.overview.filters.daily" },
    { value: "weekly", label: "pages.overview.filters.weekly" },
    { value: "monthly", label: "pages.overview.filters.monthly" },
    { value: "yearly", label: "pages.overview.filters.yearly" },
  ];

  const locations = [
    { value: "all", label: "All" },
    { value: "hanoi", label: "Ha Noi" },
    { value: "hochiminh", label: "Ho Chi Minh" },
    { value: "danang", label: "Da Nang" },
    { value: "nhatrang", label: "Nha Trang" },
    { value: "phuquoc", label: "Phu Quoc" },
  ];

  const filteredLocations = locations.filter((loc) =>
    loc.label.toLowerCase().includes(debouncedQuery.toLowerCase())
  );

  return (
    <Card className="flex flex-1 gap-4 p-4">
      <div className="flex flex-wrap items-center gap-4">
        <Select value={period} onValueChange={onPeriodChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("pages.overview.filters.period")} />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>{t("pages.overview.filters.period")}</SelectLabel>
              {periods.map((p) => (
                <SelectItem key={p.value} value={p.value}>
                  {t(p.label)}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>

        <Select value={location} onValueChange={onLocationChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t("pages.overview.filters.selectLocation")} />
          </SelectTrigger>
          <SelectContent>
            <div className="flex items-center gap-2 px-3 pb-2">
              <Search className="size-4 text-muted-foreground" />
              <Input
                placeholder="Search Branch"
                value={searchQuery}
                onChange={handleSearchChange}
                className="h-8 border-none bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>

            <div
              className={cn(
                "max-h-[200px] overflow-y-auto",
                filteredLocations.length === 0 && "hidden"
              )}>
              {filteredLocations.map((loc) => (
                <SelectItem key={loc.value} value={loc.value}>
                  {loc.label}
                </SelectItem>
              ))}
            </div>

            {filteredLocations.length === 0 && (
              <div className="px-3 py-2 text-sm text-muted-foreground">No results found</div>
            )}
          </SelectContent>
        </Select>
      </div>

      <Button
        variant="outline"
        size="icon"
        loading={isLoading}
        disabled={isLoading}
        onClick={onRefresh}
        className="ml-auto shrink-0">
        <RefreshCw className="size-4 text-primary" />
      </Button>
    </Card>
  );
}

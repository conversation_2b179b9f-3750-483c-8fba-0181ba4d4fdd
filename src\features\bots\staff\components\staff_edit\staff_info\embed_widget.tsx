"use client";

import { useState } from "react";
import { Code } from "lucide-react";

import { Button } from "@/components/ui/button";

import { EmbedDialog } from "./embeded-dialog";

interface EmbedWidgetProps {
  staffId: string;
  onThemeColorChange?: (color: string) => void;
  staffImage?: string;
}

export function EmbedWidget({ staffId, onThemeColorChange, staffImage }: EmbedWidgetProps) {
  const [themeColor, setThemeColor] = useState("#FF9500");
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleThemeColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value;
    setThemeColor(newColor);
    if (onThemeColorChange) {
      onThemeColorChange(newColor);
    }
  };

  return (
    <>
      <div className="rounded-md border border-border p-4">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">Embed Virtual Staff Widget</span>
            <div className="flex items-center gap-2">
              <span className="text-sm">Themes color</span>
              <div className="relative">
                <div
                  className="size-8 cursor-pointer rounded-sm border border-border shadow-sm"
                  style={{ backgroundColor: themeColor }}
                  onClick={() => document.getElementById("theme-color-picker")?.click()}
                />
                <input
                  id="theme-color-picker"
                  type="color"
                  value={themeColor}
                  onChange={handleThemeColorChange}
                  className="pointer-events-none absolute opacity-0"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button type="button" variant="secondary" size="sm" onClick={() => setDialogOpen(true)}>
              <Code className="mr-1 size-4" />
              Embed code
            </Button>
          </div>
        </div>
      </div>

      <EmbedDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        staffId={staffId}
        themeColor={themeColor}
        staffImage={staffImage}
      />
    </>
  );
}

"use client";

import React, { useEffect, useRef } from "react";
import { motion } from "framer-motion";

import ChatboxSkeleton from "@/features/bots/staff/components/interact/skeleton/chatbox";
import { Message } from "@/features/xbots/components/message";
import { MESSAGE } from "@/features/xbots/hooks/types";

import { CustomImage } from "@/components/ui/image";
import LoadingThreeDotsJumping from "@/components/ui/loading-dot-jump";

interface MessageListProps {
  messages: MESSAGE[];
  isStreaming?: boolean;
  isLoading?: boolean;
  streamingMessage?: MESSAGE | null;
  isSending?: boolean;
  isFetchingNextPage?: boolean;
  className?: string;
  conversationId?: string | null;
  staffAvatar?: string;
  receiverRoles?: string[];
  senderRoles?: string[];
  isStream?: boolean;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  isStreaming,
  isLoading,
  streamingMessage,
  isSending,
  className,
  staffAvatar,
  receiverRoles,
  isStream = true,
  senderRoles,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, streamingMessage]);

  if (isLoading) {
    return <ChatboxSkeleton />;
  }
  if (!isLoading && messages.length === 0 && !streamingMessage) {
    return (
      <div className="flex size-full p-4 items-end justify-center">
        <p className="text-center text-2xl font-medium leading-8 text-muted-foreground">
          What can I help with?
        </p>
      </div>
    );
  }

  // Sort messages by timestamp
  const sortedMessages = [...messages].sort((a, b) => {
    const timeA = new Date(a.created_at || "").getTime();
    const timeB = new Date(b.created_at || "").getTime();
    return timeA - timeB;
  });
  return (
    <div className={"flex w-full flex-col gap-4 overflow-y-auto p-4 " + (className || "")}>
      {sortedMessages.map((message) => {
        const isSender = senderRoles ? senderRoles.includes(message.role) : message.role === "USER";
        return (
          <Message
            key={message.id}
            message={message}
            staffAvatar={staffAvatar}
            isSender={isSender}
            isStream={isStream}
          />
        );
      })}
      {streamingMessage && (
        <Message
          key={streamingMessage.id}
          message={streamingMessage}
          isSending={isSending}
          isStream={isStream}
          staffAvatar={staffAvatar}
          isSender={
            senderRoles
              ? senderRoles.includes(streamingMessage.role)
              : streamingMessage.role === "USER"
          }
        />
      )}
      {isStream && isStreaming && !streamingMessage?.content && (
        <div className="flex items-center gap-2">
          <div className="relative size-8 shrink-0 overflow-hidden rounded-full border border-border">
            <CustomImage
              src={staffAvatar || "/avatars/default.png"}
              alt="Staff Avatar"
              fill
              className="object-cover"
            />
          </div>
          <motion.div
            initial={{ opacity: 0, y: 0, x: -40 }}
            animate={{ opacity: 1, y: 0, x: 0 }}
            transition={{ duration: 0.3 }}
            className="flex w-full items-center ">
            <div className=" bg-bg-primary rounded-2xl p-3 h-[44px] flex items-center text-muted-foreground ">
              <LoadingThreeDotsJumping />
            </div>
          </motion.div>
        </div>
      )}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;

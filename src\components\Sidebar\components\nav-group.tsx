"use client";

import type { UrlObject } from "url";
import Link from "next/link";
import { ChevronRight, type LucideIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

type NavUrl = string | UrlObject;

function toNavUrl(url: NavUrl): UrlObject {
  if (typeof url === "string") {
    return { pathname: url };
  }
  return url;
}

export function NavGroup({
  items,
  groupTitle,
}: {
  items: {
    title: string;
    url?: NavUrl;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: NavUrl;
      isActive?: boolean;
    }[];
  }[];
  groupTitle?: string;
}) {
  const { t } = useTranslation();
  const { state, isTablet, openTablet, setOpenTablet } = useSidebar();
  const isCollapsed = state === "collapsed";
  const defaultUrl = { pathname: "/" };

  return (
    <SidebarGroup>
      {groupTitle && <SidebarGroupLabel>{t(groupTitle)}</SidebarGroupLabel>}
      <SidebarMenu>
        {items.map((item) => {
          const Icon = item.icon;
          const hasActiveChild = item.items?.some((subItem) => subItem.isActive);

          if (!item.items?.length) {
            return (
              <SidebarMenuItem
                key={item.title}
                onClick={() => {
                  setOpenTablet(false);
                }}>
                <Link
                  href={item.url ? toNavUrl(item.url) : defaultUrl}
                  passHref
                  legacyBehavior={true}>
                  <SidebarMenuButton isActive={item.isActive} tooltip={t(item.title)}>
                    {Icon && <Icon className="size-4" />}
                    <span>{t(item.title)}</span>
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
            );
          }

          if (isCollapsed && !(isTablet && openTablet)) {
            return (
              <SidebarMenuItem key={item.title}>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuButton
                      isActive={hasActiveChild}
                      className="group data-[state=open]:bg-accent data-[state=open]:text-accent-foreground">
                      {Icon && <Icon className="size-4" />}
                    </SidebarMenuButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" side="right" sideOffset={16} className="w-56">
                    <p className="px-2 py-1.5 text-sm font-medium text-foreground">
                      {t(item.title)}
                    </p>
                    <DropdownMenuSeparator />
                    {item.items.map((subItem) => (
                      <DropdownMenuItem
                        key={subItem.title}
                        asChild
                        className={cn(
                          "px-2 py-1.5",
                          subItem.isActive ? "bg-accent text-accent-foreground font-medium" : ""
                        )}>
                        <Link href={toNavUrl(subItem.url)}>{t(subItem.title)}</Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </SidebarMenuItem>
            );
          }

          return (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={hasActiveChild}
              className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton isActive={hasActiveChild}>
                    {Icon && <Icon className="size-4" />}
                    <span>{t(item.title)}</span>
                    <ChevronRight className="ml-auto size-4 transition-transform group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent asChild>
                  <SidebarMenuSub>
                    {item.items.map((subItem) => (
                      <SidebarMenuSubItem
                        key={subItem.title}
                        onClick={() => {
                          setOpenTablet(false);
                        }}>
                        <Link href={toNavUrl(subItem.url)} passHref legacyBehavior={true}>
                          <SidebarMenuSubButton isActive={subItem.isActive}>
                            {t(subItem.title)}
                          </SidebarMenuSubButton>
                        </Link>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}

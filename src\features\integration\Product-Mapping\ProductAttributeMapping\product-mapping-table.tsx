import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { MappingField } from "./mocks/mapping-attribute";
import { ProductMappingField } from "./product-mapping-field";

interface MappingFieldsTableProps {
  fields: MappingField[];
  onFieldChange?: (index: number, field: MappingField) => void;
  onFieldToggle?: (index: number, enabled: boolean) => void;
  onAdvancedMapping?: (index: number, field: MappingField) => void;
  attributeData?: any;
  attributeDetail?: any;
}

export function MappingFieldsTable({
  fields,
  onFieldChange,
  onFieldToggle,
  onAdvancedMapping,
  attributeData,
  attributeDetail,
}: MappingFieldsTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Shopify Field</TableHead>
          <TableHead className="w-[30px]"></TableHead>
          <TableHead>TikTok Field</TableHead>
          <TableHead className="w-[100px] text-center">Required</TableHead>
          <TableHead className="w-[80px] text-center">Mapping</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {fields.map((field, index) => (
          <ProductMappingField
            key={index}
            field={field}
            onChange={(updatedField) => onFieldChange?.(index, updatedField)}
            onToggle={(enabled) => onFieldToggle?.(index, enabled)}
            onAdvancedMapping={(selectedField) => onAdvancedMapping?.(index, selectedField)}
            useTableRow
            attributeData={attributeData}
            attributeDetail={attributeDetail}
          />
        ))}
      </TableBody>
    </Table>
  );
}

export type { MappingField };

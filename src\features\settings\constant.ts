import { SettingBox } from "@/app/(settings)/settings/page";
import { Palette } from "lucide-react";

import { authProtectedPaths } from "@/constants/paths";

export const StoreSetups = [
  // {
  //   title: "Store information",
  //   description: "Manage contact information and store addresses",
  //   icon: Store,
  //   to: "shop-info",
  // },
  // {
  //   title: "Price group",
  //   description: "Create and manage the pricing policies of the store",
  //   icon: DollarSign,
  //   to: "price-groups",
  // },
  // {
  //   title: "Unit",
  //   description: "Create and manage the unit of variants",
  //   icon: Box,
  //   to: "units",
  // },
  // {
  //   title: "Print template",
  //   description: "Set up & customize print templates by branch.",
  //   icon: Printer,
  //   to: "prints",
  // },
  // {
  //   title: "Employees and Permissions",
  //   description: "Manage & delegate employee accounts",
  //   icon: Users,
  //   to: "accounts",
  // },
  {
    title: "Theme color",
    description: "Manage theme color for your webapp",
    icon: Palette,
    to: authProtectedPaths.SETTINGS_THEME,
  },
];

export const POSSetups: Array<SettingBox> = [];

export const Logs: Array<SettingBox> = [
  // {
  //   title: "Activity log",
  //   description: "Manage operations and activity logs of the store",
  //   icon: History,
  //   to: "activities",
  // },
];

---
description: 
globs: 
alwaysApply: true
---
# Project Structure Overview

This is a Next.js TypeScript application with the following key directories and files:

## Core Directories
- `src/app`: Next.js app router and page components
- `src/components`: Reusable UI components
- `src/features`: Feature-specific components and logic
- `src/hooks`: Custom React hooks
- `src/utils`: Utility functions and helpers
- `src/lib`: Core libraries and integrations
- `src/types`: TypeScript type definitions
- `src/i18n`: Internationalization resources
- `src/config`: Application configuration
- `src/constants`: Application constants
- `src/assets`: Static assets and resources

## Configuration Files
- [tsconfig.json](mdc:tsconfig.json): TypeScript configuration
- [next.config.js](mdc:next.config.js): Next.js configuration
- [tailwind.config.ts](mdc:tailwind.config.ts): Tailwind CSS configuration
- [package.json](mdc:package.json): Project dependencies and scripts
- [.eslintrc.json](mdc:.eslintrc.json): ESLint configuration
- [prettier.config.js](mdc:prettier.config.js): Code formatting rules

## Testing
- [jest.config.js](mdc:jest.config.js): Jest test configuration
- [jest.setup.js](mdc:jest.setup.js): Jest setup file
- `E2Etests/`: End-to-end test files
- `__mocks__/`: Mock files for testing

import { ArrowRight } from "lucide-react";

import { ProductVariant, VariantMappingData } from "../hooks/types";
import { VariantColumn } from "./components/variant-column";

interface VariantProps {
  sourceVariants: ProductVariant[] | undefined;
  destinationVariants: ProductVariant[] | undefined;
  mappedVariants: VariantMappingData | undefined;
}

export const Variant = ({ sourceVariants, destinationVariants, mappedVariants }: VariantProps) => {
  console.log("Variant component - received data:", {
    destinationVariants,
    mappedVariants,
  });

  // Handle undefined props with fallbacks
  const sourcevars = sourceVariants || [];
  const destvars = destinationVariants || [];
  const mappings = mappedVariants || {};

  // In case there are no variants but we need to render something
  if (sourcevars.length === 0) {
    return (
      <div className="flex items-center justify-center py-4 text-muted-foreground">
        No variants available
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-[1fr,auto,1fr]">
      {/* Left Column - Source Variants */}
      <VariantColumn variants={sourcevars} className="pl-4" />

      {/* Middle Column - Arrows */}
      <div className="hidden md:flex md:flex-col md:justify-center">
        {sourcevars.map((_, i) => (
          <div key={i} className="flex h-[72px] items-center justify-center">
            <ArrowRight className="size-4" />
          </div>
        ))}
      </div>

      {/* Right Column - Destination Variants */}
      <VariantColumn variants={destvars} className="pr-4" />
    </div>
  );
};

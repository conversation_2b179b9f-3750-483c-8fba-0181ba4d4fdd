---
description: 
globs: 
alwaysApply: false
---
# Page Setup Guidelines

## Overview
This document outlines the standards and best practices for implementing new pages in the application, ensuring consistent integration with the Sidebar and breadcrumb navigation system.

## Page Structure

### Basic Template
```typescript
"use client";

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export default function YourPage() {
  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-6">
          <CardTitle className="text-xl font-bold">Your Page Title</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Your page content */}
        </CardContent>
      </Card>
    </div>
  );
}
```

## Sidebar Integration

### Automatic Integration
- The Sidebar is automatically provided by the `Providers` component
- No additional setup is needed for basic Sidebar functionality
- Active page highlighting is handled automatically based on the current route

### Sidebar Menu Items
To add a new item to the Sidebar menu:
1. Update `src/components/Sidebar/data.ts`
2. Follow the existing structure:
```typescript
{
  title: "nav.yourPage", // Translation key
  icon: YourIcon, // Import from lucide-react
  items: [
    {
      title: "nav.subPage",
      url: authProtectedPaths.YOUR_PAGE_PATH
    }
  ]
}
```

## Breadcrumb Navigation

### Setup Process
1. Add an entry in `src/components/Layout/CustomBreadCrumb/breadcrumb-data.ts`:
```typescript
yourPage: {
  title: "nav.yourPage", // Translation key
  endpoint: authProtectedPaths.YOUR_PAGE_PATH, // Path constant
  parent: "parentPage" // Reference to parent page if any
}
```

2. Ensure the path is added to `authProtectedPaths` in `src/constants/paths.ts` if the page requires authentication

### Breadcrumb Structure
- Root pages should have `parent: null`
- Child pages should reference their parent using the parent's key
- Use proper translation keys for all titles
- Maintain a logical hierarchy in the breadcrumb structure

## Best Practices

### Page Organization
- Place pages in appropriate directories under `src/app/`
- Use route groups (folders with parentheses) for logical grouping
- Follow the existing naming conventions

### Component Usage
- Always use the `Card` component for consistent styling
- Maintain consistent spacing with `container mx-auto py-6`
- Use proper heading hierarchy with `CardTitle`

### Internationalization
- All text should use translation keys
- Follow the existing i18n structure
- Add new translation keys to the appropriate language files

### Responsive Design
- The Sidebar automatically handles responsive behavior
- Test pages on different screen sizes
- Ensure content is properly contained within the Card component

### Accessibility
- The Sidebar and breadcrumb components include built-in accessibility features
- Maintain proper heading hierarchy
- Use semantic HTML elements
- Ensure proper ARIA labels are present

## Example Implementation

### Creating a Settings Page
1. Create the page file:
```typescript
// src/app/settings/page.tsx
"use client";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function SettingsPage() {
  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-6">
          <CardTitle className="text-xl font-bold">Settings</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Settings content */}
        </CardContent>
      </Card>
    </div>
  );
}
```

2. Add the breadcrumb entry:
```typescript
// In src/components/Layout/CustomBreadCrumb/breadcrumb-data.ts
settings: {
  title: "nav.settings",
  endpoint: authProtectedPaths.SETTINGS,
  parent: null
}
```

3. Add the Sidebar menu item:
```typescript
// In src/components/Sidebar/data.ts
{
  title: "nav.settings",
  icon: Settings,
  items: [
    {
      title: "nav.settings",
      url: authProtectedPaths.SETTINGS
    }
  ]
}
```

## Common Pitfalls to Avoid

1. **Missing "use client" Directive**
   - Always include at the top of page components
   - Required for client-side interactivity

2. **Inconsistent Styling**
   - Always use the provided Card component
   - Maintain consistent spacing and layout

3. **Missing Breadcrumb Entries**
   - Every page should have a corresponding breadcrumb entry
   - Ensure proper parent-child relationships

4. **Improper Translation Keys**
   - Use existing translation key patterns
   - Add new keys to all language files

5. **Ignoring Responsive Design**
   - Test on different screen sizes
   - Ensure content is properly contained

## Maintenance

### Updating Existing Pages
- Follow the same guidelines when updating existing pages
- Maintain consistency with the established patterns
- Update breadcrumb data when changing page hierarchy

### Documentation
- Keep this document updated with any changes to the page setup process
- Document any new patterns or best practices
- Include examples of common implementations

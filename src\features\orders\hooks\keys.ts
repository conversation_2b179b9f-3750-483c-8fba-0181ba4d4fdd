export interface IGetOrdersParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}

export const orderKeys = {
  all: () => ["order"] as const,
  lists: () => [...orderKeys.all(), "list"] as const,
  list: (params: IGetOrdersParams) => [...orderKeys.lists(), params] as const,
  details: () => [...orderKeys.all(), "detail"] as const,
  detail: (id: string) => [...orderKeys.details(), id] as const,
  update: (id: string) => [...orderKeys.all(), "update", id] as const,
  status: () => [...orderKeys.all(), "status"] as const,
};

"use client";

import { forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { capitalize } from "lodash";
import { ImageIcon, Loader2, PackageOpen, Pencil, Plus, Search, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { OrderItem, OrderVariant } from "@/features/orders/hooks/types";
import { QUERY_KEYS } from "@/features/products/hooks/keys";
import { useVariants } from "@/features/products/hooks/variant";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CustomImage } from "@/components/ui/image";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON><PERSON><PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ORDER_STATUS, OrderStatus } from "@/constants/order_status";
import useDebounce from "@/hooks/use-debounce";
import { priceGroupApi } from "@/lib/apis/product";

import { AdjustPriceDialog } from "./dialog/adjust_price";

export interface AddOrderRef {
  resetOrder: () => void;
  getTotal: () => number;
  getOrderItemCount: () => number;
  setOrderItems: (items: OrderItem[]) => void;
  convertToApiPayload: () => any[];
}

export interface AddOrderProps {
  selectedLocationId?: string;
  onTotalChange?: (total: number) => void;
  selectedPriceGroupId?: string;
  onOrderItemsChange?: (orderItems: OrderItem[]) => void;
  disabled?: boolean;
  readOnly?: boolean;
  initialOrderItems?: OrderItem[];
  showAddServiceButton?: boolean;
  orderNumber?: string;
  orderStatus?: string;
}

export const AddOrder = forwardRef<AddOrderRef, AddOrderProps>(
  (
    {
      selectedLocationId,
      onTotalChange,
      selectedPriceGroupId,
      onOrderItemsChange,
      disabled,
      readOnly = false,
      initialOrderItems = [],
      showAddServiceButton = true,
      orderNumber,
      orderStatus,
    },
    ref
  ) => {
    const router = useRouter();
    const [selectedPriceGroup, setSelectedPriceGroup] = useState<string>("");
    const [orderItems, setOrderItems] = useState<OrderItem[]>(initialOrderItems);
    const [searchQuery, setSearchQuery] = useState("");
    const [isSearchOpen, setIsSearchOpen] = useState(false);
    const debouncedSearch = useDebounce(searchQuery, 300);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const loaderRef = useRef<HTMLDivElement>(null);
    const { t } = useTranslation();
    const [selectedItemForPriceAdjust, setSelectedItemForPriceAdjust] = useState<string | null>(
      null
    );
    const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
    const [editingServiceId, setEditingServiceId] = useState<string | null>(null);
    const [itemToDelete, setItemToDelete] = useState<string | null>(null);
    const [emptyServiceWarning, setEmptyServiceWarning] = useState<string | null>(null);
    const MAX_SERVICE_NAME_LENGTH = 250;

    // Use selectedPriceGroupId if provided
    useEffect(() => {
      if (selectedPriceGroupId) {
        setSelectedPriceGroup(selectedPriceGroupId);
      }
    }, [selectedPriceGroupId]);

    // Initialize order items from props if provided
    useEffect(() => {
      if (initialOrderItems.length > 0) {
        setOrderItems(initialOrderItems);
      }
    }, [initialOrderItems]);

    // Notify parent of order items changes
    useEffect(() => {
      if (onOrderItemsChange) {
        onOrderItemsChange(orderItems);
      }
    }, [orderItems, onOrderItemsChange]);

    // Update order items when location changes
    useEffect(() => {
      if (selectedLocationId && orderItems.length > 0 && !readOnly) {
        setOrderItems((prevItems) =>
          prevItems.map((item) => {
            const inventory = getInventoryForLocation(item.variant);
            const available = inventory?.available || 0;
            // If current quantity exceeds new available amount, adjust it
            const newQuantity = item.quantity > available ? available : item.quantity;
            return {
              ...item,
              quantity: newQuantity,
              total: newQuantity * item.price,
            };
          })
        );
      }
    }, [selectedLocationId, readOnly]);

    // Expose reset function and getTotal via ref
    useImperativeHandle(ref, () => ({
      resetOrder: () => {
        setOrderItems([]);
        setSearchQuery("");
        setIsSearchOpen(false);
      },
      getTotal: calculateTotal,
      getOrderItemCount: () => {
        const total = orderItems.reduce((sum, item) => {
          const quantity =
            typeof item.quantity === "string" ? parseInt(item.quantity, 10) : item.quantity || 0;
          return sum + quantity;
        }, 0);
        return total;
      },
      setOrderItems: (items: OrderItem[]) => {
        setOrderItems(items);
      },
      convertToApiPayload,
    }));

    const { variants, isLoading, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage } =
      useVariants({
        query: debouncedSearch,
        enabled: true,
      });

    // Fetch price groups
    const { data: priceGroupsData } = useQuery({
      queryKey: QUERY_KEYS.PRICE_GROUPS,
      queryFn: async () => {
        const response = await priceGroupApi.list();
        return response;
      },
    });

    const priceGroups = priceGroupsData?.items || [];

    const getPriceForVariant = (variant: OrderVariant, priceGroupId: string) => {
      if (!variant.prices || variant.prices.length === 0) {
        return variant.price || 0;
      }

      const priceGroup = variant.prices.find((p) => p.price_group?.id === priceGroupId);
      if (!priceGroup) {
        return variant.price || 0;
      }

      return priceGroup.price;
    };

    // Set default price group when data is loaded
    useEffect(() => {
      if (priceGroups.length > 0 && !selectedPriceGroup) {
        const defaultGroup = selectedPriceGroupId || priceGroups[0].id;
        setSelectedPriceGroup(defaultGroup);

        // Update prices for all items when setting default price group
        setOrderItems((prevItems) =>
          prevItems.map((item) => {
            const newPrice = getPriceForVariant(item.variant, defaultGroup);
            return {
              ...item,
              price: newPrice,
              total: item.quantity * newPrice,
            };
          })
        );
      }
    }, [priceGroups, selectedPriceGroup, selectedPriceGroupId]);

    // Update prices when price group changes
    useEffect(() => {
      if (!selectedPriceGroup) return;
      setOrderItems((prevItems) =>
        prevItems.map((item) => {
          const newPrice = getPriceForVariant(item.variant, selectedPriceGroup);
          return {
            ...item,
            price: newPrice,
            total: item.quantity * newPrice,
          };
        })
      );
    }, [selectedPriceGroup]);

    // Handle price group selection change
    const handlePriceGroupChange = (newPriceGroupId: string) => {
      setSelectedPriceGroup(newPriceGroupId);
    };

    // Handle infinite scroll with IntersectionObserver
    useEffect(() => {
      // Only set up observer when dropdown is open
      if (!isSearchOpen || !hasNextPage) return;

      const observer = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (entry.isIntersecting && !isFetchingNextPage) {
            fetchNextPage();
          }
        },
        { threshold: 0.1 } // Trigger when 10% of the element is visible
      );

      if (loaderRef.current) {
        observer.observe(loaderRef.current);
      }

      return () => {
        if (loaderRef.current) {
          observer.unobserve(loaderRef.current);
        }
        observer.disconnect();
      };
    }, [isSearchOpen, hasNextPage, isFetchingNextPage, fetchNextPage]);

    // Handle initial loading
    useEffect(() => {
      if (isSearchOpen && variants.length < 10 && hasNextPage && !isFetchingNextPage) {
        // Load initial data if there's not enough to fill the viewport
        setTimeout(() => fetchNextPage(), 200);
      }
    }, [isSearchOpen, variants.length, hasNextPage, isFetchingNextPage, fetchNextPage]);

    // Reset search when closing dropdown
    useEffect(() => {
      if (!isSearchOpen) {
        setSearchQuery("");
      }
    }, [isSearchOpen]);

    const getInventoryForLocation = (variant: OrderVariant) => {
      return variant.inventories?.find((inv) => inv.location_id === selectedLocationId);
    };

    // Add effect to update total when orderItems change
    useEffect(() => {
      const total = calculateTotal();
      onTotalChange?.(total);
    }, [orderItems, onTotalChange]);

    const addProductToOrder = (variant: OrderVariant) => {
      const inventory = getInventoryForLocation(variant);
      const available = inventory?.available || 0;
      if (available === 0) return;

      const existingItem = orderItems.find((item) => item.variant_id === variant.id);
      const price = existingItem
        ? existingItem.price
        : getPriceForVariant(variant, selectedPriceGroup);

      if (existingItem) {
        const currentQuantity = existingItem.quantity;

        // Check if adding one more would exceed available stock
        if (currentQuantity + 1 > available) return;

        setOrderItems(
          orderItems.map((item) =>
            item.variant_id === variant.id
              ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * price }
              : item
          )
        );
      } else {
        const newItem: OrderItem = {
          variant_id: variant.id,
          name: variant.name,
          sku: variant.sku,
          image: variant.images?.[0]?.url || "",
          price,
          quantity: 1,
          total: price,
          variant,
          custom: false,
        };
        setOrderItems([...orderItems, newItem]);
      }

      setSearchQuery("");
      setIsSearchOpen(false);
    };

    const updateQuantity = (id: string, value: string | number) => {
      const numericValue = typeof value === "string" ? (value === "" ? 0 : parseInt(value)) : value;

      const item = orderItems.find((item) => item.variant_id === id);
      if (!item) return;

      const isService = Boolean(item.variant.custom);

      // For services, only prevent negative values
      if (isService) {
        if (numericValue < 0) return;
        setOrderItems(
          orderItems.map((item) =>
            item.variant_id === id
              ? {
                  ...item,
                  quantity: numericValue,
                  total: numericValue * (item.sale_price || item.price),
                }
              : item
          )
        );
        return;
      }

      // For products, check inventory
      const inventory = getInventoryForLocation(item.variant);
      const available = inventory?.available || 0;

      // Allow zero during typing, but not more than available and not negative
      if (numericValue > available || numericValue < 0) return;

      const updatedItems = orderItems.map((item) =>
        item.variant_id === id
          ? {
              ...item,
              quantity: numericValue,
              total: numericValue * (item.sale_price || item.price),
            }
          : item
      );
      setOrderItems(updatedItems);

      // Notify parent of changes
      onOrderItemsChange?.(updatedItems);
    };

    const updateNote = (id: string, note: string) => {
      setOrderItems(orderItems.map((item) => (item.variant_id === id ? { ...item, note } : item)));
    };

    const handleNoteBlur = () => {
      setEditingNoteId(null);
    };

    const handleNoteKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, id: string) => {
      if (e.key === "Enter") {
        e.preventDefault();
        setEditingNoteId(null);
      }
    };

    const handleTextAreaKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>, id: string) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        setEditingNoteId(null);
      }
    };

    const removeItem = (id: string) => {
      setItemToDelete(id);
    };

    const handleConfirmedDelete = () => {
      if (itemToDelete) {
        setOrderItems(orderItems.filter((item) => item.variant_id !== itemToDelete));
        setItemToDelete(null);
      }
    };

    const calculateTotal = () => {
      return orderItems.reduce((sum, item) => {
        const price = item.sale_price || item.price || 0;
        const total = sum + item.quantity * price;
        return total;
      }, 0);
    };

    const handlePriceChange = (
      variantId: string,
      prices: { originalPrice: number; discountedPrice: number }
    ) => {
      setOrderItems(
        orderItems.map((item) =>
          item.variant_id === variantId
            ? {
                ...item,
                price: prices.originalPrice,
                sale_price: prices.discountedPrice,
                total: item.quantity * prices.discountedPrice,
                variant: {
                  ...item.variant,
                  price: prices.originalPrice,
                },
              }
            : item
        )
      );
    };

    const addServiceToOrder = () => {
      // Generate a UUID for the service
      const serviceId = crypto.randomUUID();

      // Create the order item for the service with minimal required fields
      const newService: OrderItem = {
        variant_id: serviceId,
        name: "",
        sku: serviceId,
        image: "",
        price: 0,
        quantity: 1,
        total: 0,
        custom: true,
        variant: {
          id: serviceId,
          name: "",
          sku: serviceId,
          custom: true,
          price: 0,
        } as OrderVariant,
      };

      setOrderItems([...orderItems, newService]);
      setEditingServiceId(serviceId);
    };

    const updateServiceName = (id: string, name: string) => {
      if (!name.trim()) {
        setEmptyServiceWarning(id);
      } else {
        setEmptyServiceWarning(null);
      }

      // Limit name to MAX_SERVICE_NAME_LENGTH characters
      const limitedName = name.slice(0, MAX_SERVICE_NAME_LENGTH);

      setOrderItems(
        orderItems.map((item) =>
          item.variant_id === id
            ? {
                ...item,
                name: limitedName,
                total: item.quantity, // Ensure total is maintained
                variant: {
                  ...item.variant,
                  name: limitedName,
                },
              }
            : item
        )
      );
    };

    // This function converts the internal OrderItems to the API payload format
    const convertToApiPayload = () => {
      return orderItems.map((item) => {
        const isService = Boolean(item.variant.custom);

        if (isService) {
          return {
            unit_price: item.price,
            sale_price: item.sale_price || item.price,
            sku: item.sku,
            name: item.name,
            image_url: null,
            quantity: item.quantity,
            custom: true,
            location: {
              id: selectedLocationId || "",
              name: "Mặc định",
            },
          };
        } else {
          return {
            unit_price: item.price,
            sale_price: item.sale_price || item.price,
            sku: item.sku,
            name: item.name,
            variant_name: item.variant.name,
            image_url: item.image,
            variant_id: item.variant.id,
            product_id: (item.variant as any).product_id || "",
            quantity: item.quantity,
            location: {
              id: selectedLocationId || "",
              name: "Mặc định",
            },
            note: item.note,
          };
        }
      });
    };

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          {readOnly ? (
            <div className="flex w-full items-center justify-between pb-4">
              <div className="flex items-center gap-2 text-sm">
                <h3 className="font-medium text-foreground">{t("nav.order")}</h3>
                {orderNumber && <span className="text-muted-foreground">#{orderNumber}</span>}
              </div>
              {orderStatus && (
                <Badge variant={ORDER_STATUS[orderStatus as OrderStatus].variant}>
                  {capitalize(ORDER_STATUS[orderStatus as OrderStatus].label)}
                </Badge>
              )}
            </div>
          ) : (
            <>
              <h2 className="pb-4 text-sm font-medium">{t("nav.order")}</h2>
              <div className="w-[200px]">
                <Combobox
                  value={selectedPriceGroup}
                  onValueChange={handlePriceGroupChange}
                  items={priceGroups.map((group) => ({
                    id: group.id,
                    name: group.name,
                    displayValue: group.name,
                  }))}
                  placeholder={t("pages.orders.loading")}
                  searchPlaceholder={t("pages.orders.searchPriceGroup")}
                  emptyText={t("pages.orders.noPriceGroupsFound")}
                />
              </div>
            </>
          )}
        </div>

        {/* Product Search - Only show in edit mode */}
        {!readOnly && (
          <div className="relative">
            <div className="relative">
              <Input
                type="text"
                ref={searchInputRef}
                placeholder={t("pages.orders.placeholder")}
                leftIcon={<Search className="size-4" />}
                rightIcon={
                  isLoading || isFetching ? (
                    <Loader2 className="animate-spin" />
                  ) : searchQuery ? (
                    <button
                      type="button"
                      className="hover:text-foreground"
                      onClick={() => setSearchQuery("")}>
                      ✕
                    </button>
                  ) : null
                }
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onClick={() => {
                  setIsSearchOpen(true);
                }}
                disabled={disabled}
              />
            </div>

            {isSearchOpen && (
              <div className="relative z-50">
                <div className="fixed inset-0" onClick={() => setIsSearchOpen(false)} />
                <div className="absolute inset-x-0 mt-1 overflow-hidden rounded-md border bg-popover">
                  {/* Fixed Add new product button */}
                  <div
                    className="cursor-pointer border-b px-4 py-2 hover:bg-accent"
                    onClick={() => {
                      setIsSearchOpen(false);
                      router.push("/products/new");
                    }}>
                    <div className="flex items-center gap-2">
                      <Plus className="size-4 text-primary" />
                      <span className="text-sm text-primary hover:underline">
                        {t("pages.orders.addProduct")}
                      </span>
                    </div>
                  </div>

                  {/* Scrollable product list */}
                  <ScrollArea className="h-[250px]">
                    {isLoading && !isFetchingNextPage && (
                      <div className="flex items-center justify-center py-4 text-sm text-muted-foreground">
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        {t("pages.orders.loading")}
                      </div>
                    )}

                    {/* All variants */}
                    {variants.map((variant) => {
                      const price = getPriceForVariant(variant, selectedPriceGroup);
                      const inventory = getInventoryForLocation(variant);
                      const available = inventory?.available || 0;
                      const onHand = inventory?.on_hand || 0;
                      const isAvailable = available > 0;

                      return (
                        <div
                          key={variant.id}
                          className={`flex items-center justify-between px-4 py-2 ${
                            isAvailable
                              ? "cursor-pointer hover:bg-accent"
                              : "cursor-not-allowed opacity-50"
                          }`}
                          onClick={() => isAvailable && addProductToOrder(variant)}>
                          <div className="flex items-center justify-center gap-4">
                            <div className="relative size-10 items-center rounded-md">
                              {variant.images && variant.images.length > 0 ? (
                                <CustomImage
                                  src={variant.images[0].url}
                                  alt={variant.name}
                                  className="size-full rounded-md object-cover"
                                  fill
                                />
                              ) : (
                                <ImageIcon className="size-full stroke-[1] text-muted-foreground" />
                              )}
                            </div>
                            <div className="min-w-[440px] max-w-[440px]">
                              <p className="truncate text-sm font-medium">{variant.name}</p>
                              <p className="text-sm text-muted-foreground">SKU: {variant.sku}</p>
                            </div>
                          </div>
                          <div className="min-w-[180px] text-right">
                            <p className="text-sm font-medium">{price.toLocaleString()} đ</p>
                            <p
                              className={`text-sm ${isAvailable ? "text-primary" : "text-destructive"}`}>
                              {t("pages.orders.available")}: {available} |{" "}
                              {t("pages.orders.onHand")}: {onHand}
                            </p>
                          </div>
                        </div>
                      );
                    })}

                    {isFetchingNextPage && (
                      <div className="flex items-center justify-center py-4 text-sm text-muted-foreground">
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        {t("pages.orders.loadingMore")}
                      </div>
                    )}

                    {/* Invisible loader element for intersection observer */}
                    {hasNextPage && <div ref={loaderRef} className="h-4" />}

                    {!isLoading && !isFetching && variants.length === 0 && (
                      <div className="flex items-center justify-center py-4 text-sm text-muted-foreground">
                        {t("pages.orders.noProductsFound")}
                      </div>
                    )}
                    <ScrollBar orientation="vertical" />
                    <ScrollBar orientation="horizontal" />
                  </ScrollArea>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Order Items Table */}
        <div className="rounded-lg border">
          <div className="max-h-[400px] overflow-auto">
            <Table>
              <TableHeader className="sticky top-0 z-10 bg-card">
                <TableRow>
                  <TableHead className="w-full">{t("pages.products.title")}</TableHead>
                  <TableHead className="w-full min-w-20 text-center">
                    {t("pages.orders.quantity")}
                  </TableHead>
                  <TableHead className="w-[15%] text-right">{t("pages.orders.price")}</TableHead>
                  <TableHead className="w-[15%] text-right">{t("pages.orders.total")}</TableHead>
                  {!readOnly && <TableHead className="w-[5%]"></TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody className="max-h-[400px] overflow-auto">
                {orderItems.length > 0 ? (
                  orderItems.map((item) => {
                    const isService = Boolean(item.variant.custom);
                    const inventory = !isService ? getInventoryForLocation(item.variant) : null;
                    const available = inventory?.available || 0;
                    const isAtMaxQuantity = !isService && item.quantity >= available;
                    const isEditingService = editingServiceId === item.variant_id;

                    return (
                      <TableRow key={item.variant_id}>
                        <TableCell>
                          <div className={`flex min-w-0 items-start ${!isService ? "gap-4" : ""}`}>
                            {!isService && (
                              <div className="relative my-auto size-10 shrink-0 rounded-md">
                                {item.image ? (
                                  <CustomImage
                                    src={item.image}
                                    alt={item.name}
                                    className="size-full rounded-md object-cover"
                                    fill
                                  />
                                ) : (
                                  <ImageIcon className="size-full stroke-[1] text-muted-foreground" />
                                )}
                              </div>
                            )}
                            <div className="flex min-w-0 flex-1 flex-col">
                              {isService && isEditingService ? (
                                <div className="flex flex-col gap-1">
                                  <Input
                                    value={item.name}
                                    placeholder="Enter service name"
                                    onChange={(e) =>
                                      updateServiceName(item.variant_id, e.target.value)
                                    }
                                    onBlur={() => {
                                      setEditingServiceId(null);
                                      if (!item.name.trim()) {
                                        setEmptyServiceWarning(item.variant_id);
                                      }
                                    }}
                                    onKeyDown={(e) => {
                                      if (e.key === "Enter") {
                                        e.preventDefault();
                                        e.currentTarget.blur();
                                      }
                                    }}
                                    className="h-7"
                                    maxLength={MAX_SERVICE_NAME_LENGTH}
                                    autoFocus
                                  />
                                  <div className="text-right text-xs text-muted-foreground">
                                    {item.name.length}/{MAX_SERVICE_NAME_LENGTH}
                                  </div>
                                </div>
                              ) : (
                                <div className="flex min-w-0 items-center gap-2">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <span className="truncate pb-1 font-medium">
                                          {item.name}
                                        </span>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>{item.name}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                  {isService && !readOnly && (
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="icon"
                                      className="size-6 shrink-0 p-0"
                                      onClick={() => setEditingServiceId(item.variant_id)}>
                                      <Pencil className="size-3 text-primary" />
                                    </Button>
                                  )}
                                </div>
                              )}
                              {emptyServiceWarning === item.variant_id && (
                                <p className="mt-1 text-xs text-destructive">
                                  Service name cannot be empty
                                </p>
                              )}
                              <div className="flex flex-col gap-2 text-sm text-muted-foreground">
                                {!isService && (
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <span className="truncate">SKU: {item.sku}</span>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>SKU: {item.sku}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                )}
                                {!isService && !readOnly && (
                                  <span>
                                    {t("pages.orders.available")}:{" "}
                                    {getInventoryForLocation(item.variant)?.available || 0}
                                  </span>
                                )}
                                {!isService && (
                                  <span>Unit: {item.variant.unit?.name || "Default"}</span>
                                )}
                                {!isService && (
                                  <div className="flex items-center gap-2">
                                    <span>{t("pages.orders.note")}:</span>
                                    {editingNoteId === item.variant_id && !readOnly ? (
                                      <div>
                                        <Textarea
                                          placeholder=""
                                          value={item.note || ""}
                                          onChange={(e) =>
                                            updateNote(item.variant_id, e.target.value)
                                          }
                                          onBlur={handleNoteBlur}
                                          onKeyDown={(e) =>
                                            handleTextAreaKeyDown(e, item.variant_id)
                                          }
                                          className="h-7 border border-muted-foreground/50 bg-transparent"
                                          disabled={disabled}
                                          autoFocus
                                        />
                                      </div>
                                    ) : (
                                      <div className="flex items-center gap-1">
                                        <TooltipProvider>
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <span className="max-w-[70px] truncate text-sm">
                                                {item.note || "—"}
                                              </span>
                                            </TooltipTrigger>
                                            {item.note && (
                                              <TooltipContent>
                                                <p>{item.note}</p>
                                              </TooltipContent>
                                            )}
                                          </Tooltip>
                                        </TooltipProvider>
                                        {!readOnly && (
                                          <Button
                                            type="button"
                                            variant="ghost"
                                            size="icon"
                                            className="size-5 p-0 text-primary"
                                            onClick={() => setEditingNoteId(item.variant_id)}
                                            disabled={disabled}>
                                            <Pencil className="size-3" />
                                          </Button>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          {readOnly ? (
                            <span>{item.quantity}</span>
                          ) : (
                            <div className="flex items-center justify-center gap-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                className="size-8 min-w-8"
                                disabled={disabled}
                                onClick={() => updateQuantity(item.variant_id, item.quantity - 1)}>
                                -
                              </Button>
                              <Input
                                value={item.quantity}
                                onChange={(e) => updateQuantity(item.variant_id, e.target.value)}
                                onBlur={(e) => {
                                  const value = e.target.value;
                                  if (value === "" || parseInt(value) < 1) {
                                    updateQuantity(item.variant_id, 1);
                                  }
                                }}
                                onKeyDown={(e) => {
                                  if (e.key === "Enter") {
                                    e.preventDefault();
                                    e.currentTarget.blur();
                                  }
                                }}
                                className="size-8 w-24 min-w-16 text-center [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                                max={available}
                                disabled={disabled}
                                title=""
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                className="size-8 min-w-8"
                                disabled={disabled || isAtMaxQuantity}
                                onClick={() => updateQuantity(item.variant_id, item.quantity + 1)}>
                                +
                              </Button>
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          {readOnly ? (
                            <div className="flex flex-col items-end">
                              {item.unit_price &&
                              item.sale_price &&
                              item.unit_price !== item.sale_price ? (
                                <>
                                  <span>{item.sale_price.toLocaleString()} đ</span>
                                  <span className="text-xs text-muted-foreground line-through">
                                    {item.unit_price.toLocaleString()} đ
                                  </span>
                                </>
                              ) : (
                                <span>
                                  {(item.unit_price || item.sale_price || 0).toLocaleString()} đ
                                </span>
                              )}
                            </div>
                          ) : isService ? (
                            <div className="flex flex-col items-end">
                              <AdjustPriceDialog
                                open={selectedItemForPriceAdjust === item.variant_id}
                                onOpenChange={(open) =>
                                  !open && setSelectedItemForPriceAdjust(null)
                                }
                                currentPrice={item.price}
                                currentDiscount={item.discount}
                                onPriceChange={(prices) => {
                                  handlePriceChange(item.variant_id, {
                                    originalPrice: prices.originalPrice,
                                    discountedPrice: prices.discountedPrice,
                                  });
                                  setSelectedItemForPriceAdjust(null);
                                }}
                                trigger={
                                  <Button
                                    type="button"
                                    className="bg-transparent p-0 text-primary hover:bg-transparent hover:underline"
                                    disabled={disabled}
                                    onClick={() => setSelectedItemForPriceAdjust(item.variant_id)}>
                                    {(item.sale_price || item.price).toLocaleString()} đ
                                  </Button>
                                }
                              />
                              {item.price > (item.sale_price || item.price) && (
                                <span className="text-xs text-muted-foreground line-through">
                                  {item.price.toLocaleString()} đ
                                </span>
                              )}
                            </div>
                          ) : (
                            <span>{(item.sale_price || item.price).toLocaleString()} đ</span>
                          )}
                        </TableCell>
                        <TableCell className="items-end truncate text-right font-medium">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span>
                                  {(
                                    (item.sale_price || item.price) * item.quantity
                                  ).toLocaleString()}{" "}
                                  đ
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  {(
                                    (item.sale_price || item.price) * item.quantity
                                  ).toLocaleString()}{" "}
                                  đ
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        {!readOnly && (
                          <TableCell className="text-center">
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="size-8 text-muted-foreground hover:bg-destructive/10"
                              onClick={() => removeItem(item.variant_id)}
                              disabled={disabled}>
                              <Trash2 className="size-4 text-destructive" />
                            </Button>
                          </TableCell>
                        )}
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={readOnly ? 4 : 5} className="h-40 py-6 text-center">
                      <div className="flex flex-col items-center justify-center gap-2">
                        <div className="flex size-20 items-center justify-center">
                          <PackageOpen className="size-72 stroke-[1] text-muted-foreground/70" />
                        </div>
                        <p className="text-muted-foreground">
                          {t("pages.orders.noProductsInOrder")}
                        </p>
                        {!readOnly && (
                          <Button
                            type="button"
                            onClick={() => {
                              setIsSearchOpen(true);
                              searchInputRef.current?.focus();
                            }}>
                            <Plus className="size-4" />
                            {t("pages.orders.addProduct")}
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Add Service Button - Only show in edit mode */}
        {!readOnly && showAddServiceButton && (
          <Button
            type="button"
            variant="link"
            className="gap-2 text-primary"
            disabled={disabled}
            onClick={addServiceToOrder}>
            <Plus className="size-4" />
            {t("pages.orders.addService")}
          </Button>
        )}

        {/* Add Delete Confirmation Dialog */}
        <Dialog open={!!itemToDelete} onOpenChange={(open) => !open && setItemToDelete(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("pages.orders.confirm")}</DialogTitle>
              <DialogDescription>{t("pages.orders.cancelWarning")}</DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setItemToDelete(null)}>
                {t("pages.orders.cancel")}
              </Button>
              <Button type="button" variant="destructive" onClick={handleConfirmedDelete}>
                {t("pages.orders.confirmDelete")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
);

AddOrder.displayName = "AddOrder";

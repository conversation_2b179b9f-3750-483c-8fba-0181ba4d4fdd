"use client";

import { use<PERSON><PERSON>back, useEffect, useLayoutEffect, useRef, useState } from "react";
import { Check } from "lucide-react";

import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { SaveFilterItem as SaveFilterItemType } from "@/lib/apis/save-filter";

import LucideIcon from "./lucide-icon";

interface SaveFilterItemProps {
  filter: Partial<SaveFilterItemType> & {
    id: string;
    name: string;
    type?: string;
    filters?: Record<string, unknown>;
    icon?: string;
  };
  editingIndex: string | null;
  editValue: string;
  setEditValue: (value: string) => void;
  handleAcceptFilter: (filters: SaveFilterItemType[]) => void;
  handleEditFilter: (id: string) => void;
  handleCancelEdit: () => void;
}

const SaveFilterItem = ({
  filter,
  editingIndex,
  editValue,
  setEditValue,
  handleAcceptFilter,
  handleEditFilter,
  handleCancelEdit,
}: SaveFilterItemProps) => {
  const isDefaultFilter = filter.type === "default";
  const textRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isTextTruncated, setIsTextTruncated] = useState(false);
  const [inputWidth, setInputWidth] = useState(150);

  // Check if text is truncated
  const checkIfTruncated = () => {
    if (textRef.current) {
      const element = textRef.current;
      setIsTextTruncated(element.scrollWidth > element.clientWidth);
    }
  };

  // Calculate input width based on content
  const calculateInputWidth = useCallback(() => {
    // Use filter name as initial value when entering edit mode
    const textToMeasure = editValue || filter.name;

    if (textToMeasure) {
      // Create a temporary span element to measure the text width
      const span = document.createElement("span");
      span.style.visibility = "hidden";
      span.style.position = "absolute";
      span.style.whiteSpace = "pre";
      span.style.font = window.getComputedStyle(inputRef.current || document.body).font;
      span.textContent = textToMeasure;
      document.body.appendChild(span);

      // Get width and add some padding
      const textWidth = span.getBoundingClientRect().width;
      document.body.removeChild(span);

      // Set minimum and maximum widths
      const newWidth = Math.max(100, Math.min(300, textWidth + 40));
      setInputWidth(newWidth);
    } else {
      // Default width when empty
      setInputWidth(150);
    }
  }, [editValue, filter.name]);

  useEffect(() => {
    checkIfTruncated();

    // Set up resize observer to check for truncation on resize
    const resizeObserver = new ResizeObserver(() => {
      checkIfTruncated();
    });

    if (textRef.current) {
      resizeObserver.observe(textRef.current);
    }

    // Clean up observer on unmount
    return () => {
      resizeObserver.disconnect();
    };
  }, [filter.name]);

  // Calculate input width whenever edit value changes
  useEffect(() => {
    calculateInputWidth();
  }, [calculateInputWidth, editValue]);

  // Initial calculation when entering edit mode
  useLayoutEffect(() => {
    if (editingIndex === filter.id) {
      calculateInputWidth();
    }
  }, [calculateInputWidth, editingIndex, filter.id]);

  const handleInputBlur = (e: React.FocusEvent) => {
    // Check if the related target is the accept button
    const isAcceptButton = (e.relatedTarget as HTMLElement)?.dataset?.role === "accept-button";
    if (!isAcceptButton) {
      handleCancelEdit();
    }
  };

  return (
    <div className="flex items-center justify-between">
      {editingIndex === filter.id ? (
        <div className="flex items-center gap-2">
          <Input
            ref={inputRef}
            value={editValue}
            placeholder={filter.name}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleAcceptFilter([{ ...filter, name: editValue } as SaveFilterItemType]);
              }
              if (e.key === "Escape") {
                handleCancelEdit();
              }
            }}
            onBlur={handleInputBlur}
            className="h-6"
            style={{ width: `${inputWidth}px` }}
            autoFocus
          />
          <div className="flex items-center gap-1">
            <button
              data-role="accept-button"
              onClick={() =>
                handleAcceptFilter([{ ...filter, name: editValue } as SaveFilterItemType])
              }
              className="flex items-center justify-center">
              <Check className="size-4" />
            </button>
          </div>
        </div>
      ) : (
        <div
          className="flex items-center justify-between gap-2"
          onDoubleClick={() => !isDefaultFilter && handleEditFilter(filter.id)}>
          {filter.icon && <LucideIcon iconName={filter.icon} />}
          <Tooltip delayDuration={300}>
            <TooltipTrigger asChild>
              <div ref={textRef} className="max-w-[150px] truncate text-nowrap">
                {filter.name}
              </div>
            </TooltipTrigger>
            {isTextTruncated && (
              <TooltipContent side="bottom" align="center" className="max-w-[250px] break-words">
                {filter.name}
              </TooltipContent>
            )}
          </Tooltip>
        </div>
      )}
    </div>
  );
};

export default SaveFilterItem;

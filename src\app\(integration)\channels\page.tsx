"use client";

import { Suspense, useMemo } from "react";
import { useRouter } from "next/navigation";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/integration/Connections/column";
import { useConnection } from "@/features/integration/hooks/connection";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { ChannelStatusOptions } from "@/utils/constants/common-options";

export default function ChannelPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ChannelPageContent />
    </Suspense>
  );
}
function ChannelPageContent() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const router = useRouter();
  const { connections, total, isLoading, isFetching, refetch } = useConnection({
    limit: Number(getInitialParams.limit),
    ...getInitialParams,
  });

  const isTableLoading = isLoading || isFetching;

  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "connections",
      searchPlaceHolder: t("pages.channel.filters.search.placeholder"),
      numberOfFilters: 2,
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "status",
          type: EFilterType.SELECT_BOX,
          title: t("pages.channel.filters.status"),
          dataOption: ChannelStatusOptions,
          defaultValue: getInitialParams["status"],
        },
        {
          id: "created_at",
          type: EFilterType.DATE,
          title: t("pages.products.filters.createdAt"),
          defaultValue: "",
        },
        {
          id: "updated_at",
          type: EFilterType.DATE,
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("pages.channel.actions.install"),
        icon: PlusIcon,
        onClick: () => router.push("/channels/supported-channels"),
      },
    ],
    onRefresh: () => refetch(),
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.channel.title")}
        filterProps={filterConfig as FilterTableProps}
        filterType="connections"
        data={connections || []}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(t)}
        data={connections || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
      />
    </TableCard>
  );
}

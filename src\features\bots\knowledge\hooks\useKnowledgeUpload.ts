import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { useZodForm } from "@/components/ui";
import { knowledgeApi } from "@/lib/apis/knowledge";
import { createI18nResolver } from "@/lib/utils";

import { CreateKnowledgeByTextPayload } from "../types";
import { addKnowledgeTextSchema, addKnowledgeURLSchema } from "../utils/validators/add-knowledge";
import { knowledgeKeys } from "./keys";

export type KnowledgeTab = "FILE" | "URL" | "TEXT" | "LIST";

export function useKnowledgeUpload() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [fileList, setFileList] = useState<File[]>([]);
  const [urls, setUrls] = useState<string[]>([]);
  const [listText, setListText] = useState<CreateKnowledgeByTextPayload[]>([]);
  const textForm = useZodForm({
    schema: addKnowledgeTextSchema,
    resolver: createI18nResolver(addKnowledgeTextSchema, t),
    defaultValues: { knowledge_name: "", content: "" },
    mode: "onTouched",
  });
  const urlForm = useZodForm({
    schema: addKnowledgeURLSchema,
    resolver: createI18nResolver(addKnowledgeURLSchema, t),
    defaultValues: { url: "" },
    mode: "onTouched",
  });
  const [isUploading, setIsUploading] = useState(false);

  const uploadKnowledge = async (tab: KnowledgeTab) => {
    try {
      setIsUploading(true);
      if (tab === "FILE") {
        if (!fileList.length)
          throw new Error(t("pages.knowledge.upload.pleaseSelectAtLeastOneFile"));
        const presignedUrls = await knowledgeApi.getUploadUrls({
          files: fileList.map((f) => ({ file_name: f.name, type: f.type, size: f.size })),
        });
        const batchSize = 5;
        const uploadedFiles: any[] = [];
        for (let i = 0; i < fileList.length; i += batchSize) {
          const batchFiles = fileList.slice(i, i + batchSize);
          const batchUrls = presignedUrls.slice(i, i + batchSize);
          const batchResults = await Promise.all(
            batchFiles.map(async (file, idx) => {
              const urlInfo = batchUrls[idx];
              await knowledgeApi.putUploadFileWithFileUrl(urlInfo.url, file, () => {});
              return {
                s3_key: urlInfo.s3_key,
                file_name: file.name,
                size: file.size,
                type: file.type,
              };
            })
          );
          uploadedFiles.push(...batchResults);
        }
        await knowledgeApi.createByFile({ files: uploadedFiles });
        toast.success(t("pages.knowledge.upload.uploadSuccess"));
        setFileList([]);
      } else if (tab === "URL") {
        if (!urls.length) throw new Error(t("pages.knowledge.upload.pleaseEnterAtLeastOneURL"));
        await knowledgeApi.createByUrl(urls);
        toast.success(t("pages.knowledge.upload.uploadSuccess"));
        setUrls([]);
      } else if (tab === "TEXT") {
        if (!listText.length)
          throw new Error(t("pages.knowledge.upload.pleaseEnterAtLeastOneTextFile"));
        await knowledgeApi.createByText(listText);
        toast.success(t("pages.knowledge.upload.uploadSuccess"));
        setListText([]);
      } else {
        toast.error(t("pages.knowledge.upload.invalidTab"));
      }

      // Refetch knowledge list after successful upload
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: knowledgeKeys.list({}) });
      }, 2000);
    } catch (error) {
      toast.error(String(error));
    } finally {
      setIsUploading(false);
    }
  };

  return {
    fileList,
    setFileList,
    urls,
    setUrls,
    textForm,
    urlForm,
    listText,
    setListText,
    isUploading,
    uploadKnowledge,
  };
}

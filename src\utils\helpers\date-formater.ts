import { DateRange } from "react-day-picker";

import i18n from "@/i18n";

export const dateFormatter = (date: string): string => {
  const parsedDate = new Date(date);

  if (isNaN(parsedDate.getTime())) {
    return "Invalid Date";
  }

  return parsedDate
    .toLocaleString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    })
    .replace(",", "")
    .replace(/\s(am|pm)$/, (match) => match.toUpperCase());
};

export const timeAgo = (date: string): string => {
  const t = i18n.t.bind(i18n);
  const now = new Date();
  const past = new Date(date);
  const msPerMinute = 60 * 1000;
  const msPerHour = msPerMinute * 60;
  const msPerDay = msPerHour * 24;
  const msPerMonth = msPerDay * 30;
  const msPerYear = msPerDay * 365;

  const elapsed = now.getTime() - past.getTime();

  if (elapsed < msPerMinute) {
    const seconds = Math.round(elapsed / 1000);
    return t("common.time.timeAgo.seconds", { count: seconds });
  } else if (elapsed < msPerHour) {
    const minutes = Math.round(elapsed / msPerMinute);
    return t("common.time.timeAgo.minutes", { count: minutes });
  } else if (elapsed < msPerDay) {
    const hours = Math.round(elapsed / msPerHour);
    return t("common.time.timeAgo.hours", { count: hours });
  } else if (elapsed < msPerMonth) {
    const days = Math.round(elapsed / msPerDay);
    return t("common.time.timeAgo.days", { count: days });
  } else if (elapsed < msPerYear) {
    const months = Math.round(elapsed / msPerMonth);
    return t("common.time.timeAgo.months", { count: months });
  } else {
    const years = Math.round(elapsed / msPerYear);
    return t("common.time.timeAgo.years", { count: years });
  }
};

/**
 * Formats a date string intelligently based on how recent it is:
 * - If less than an hour ago: "X minutes ago" format
 * - If same day but more than an hour ago: Hours and minutes only
 * - If different day: Date only (day, month, year)
 */
export const formatSyncDate = (dateString: string | Date): string => {
  const date = typeof dateString === "string" ? new Date(dateString) : dateString;

  if (isNaN(date.getTime())) {
    return "Invalid Date";
  }

  const now = new Date();
  const msPerHour = 60 * 60 * 1000;
  const elapsed = now.getTime() - date.getTime();

  // If less than an hour ago, show "X minutes ago" format
  if (elapsed < msPerHour) {
    return timeAgo(date.toISOString());
  }

  // Check if same day
  const isSameDay =
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear();

  if (isSameDay) {
    // Same day - show hours and minutes
    return new Intl.DateTimeFormat("default", {
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  } else {
    // Different day - show date only
    return new Intl.DateTimeFormat("default", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(date);
  }
};

export const formatDateToISO = (date: string | Date) => {
  const currentDate = new Date(date);
  const year = currentDate.getFullYear();
  const month = ("0" + (currentDate.getMonth() + 1)).slice(-2);
  const day = ("0" + currentDate.getDate()).slice(-2);
  return `${year}-${month}-${day}`;
};

export const setStartOfDay = (date: Date) => {
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
};

export const setEndOfDay = (date: Date) => {
  const newDate = new Date(date);
  newDate.setHours(23, 59, 59, 999);
  return newDate;
};

export const convertDateStringToDateRange = (date: string) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  let dateRange: DateRange | undefined;
  switch (date) {
    case "today": {
      dateRange = {
        from: setStartOfDay(today),
        to: setEndOfDay(today),
      };
      break;
    }
    case "yesterday": {
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      dateRange = {
        from: setStartOfDay(yesterday),
        to: setEndOfDay(yesterday),
      };
      break;
    }
    case "this_week": {
      const day = today.getDay();
      const diff = day === 0 ? 6 : day - 1; // Adjust to Monday
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - diff);
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // End on Sunday
      dateRange = {
        from: setStartOfDay(startOfWeek),
        to: setEndOfDay(endOfWeek),
      };
      break;
    }
    case "last_week": {
      const day = today.getDay();
      const diff = day === 0 ? 6 : day - 1;
      const startOfLastWeek = new Date(today);
      startOfLastWeek.setDate(today.getDate() - diff - 7);
      const endOfLastWeek = new Date(startOfLastWeek);
      endOfLastWeek.setDate(startOfLastWeek.getDate() + 6); // End on Sunday
      dateRange = {
        from: setStartOfDay(startOfLastWeek),
        to: setEndOfDay(endOfLastWeek),
      };
      break;
    }
    case "this_month": {
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      dateRange = {
        from: setStartOfDay(startOfMonth),
        to: setEndOfDay(endOfMonth),
      };
      break;
    }
    case "last_month": {
      const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      dateRange = {
        from: setStartOfDay(startOfLastMonth),
        to: setEndOfDay(endOfLastMonth),
      };
      break;
    }
    default:
      dateRange = undefined;
  }
  return dateRange;
};

export const convertDateRangeToString = (fromDate: string, toDate: string) => {
  if (!fromDate || !toDate) return "all";

  const from = new Date(fromDate);
  const to = new Date(toDate);

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  const isSameDay = (date1: Date, date2: Date) => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  };

  // Check for today
  if (isSameDay(from, today) && isSameDay(to, today)) {
    return "today";
  }

  // Check for yesterday
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  if (isSameDay(from, yesterday) && isSameDay(to, yesterday)) {
    return "yesterday";
  }

  // Check for this week
  const startOfWeek = new Date(today);
  const day = today.getDay();
  const diff = day === 0 ? 6 : day - 1; // Adjust to Monday
  startOfWeek.setDate(today.getDate() - diff);
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6); // End on Sunday
  if (isSameDay(from, startOfWeek) && isSameDay(to, endOfWeek)) {
    return "this_week";
  }

  // Check for last week
  const startOfLastWeek = new Date(startOfWeek);
  startOfLastWeek.setDate(startOfWeek.getDate() - 7);
  const endOfLastWeek = new Date(startOfLastWeek);
  endOfLastWeek.setDate(startOfLastWeek.getDate() + 6);
  if (isSameDay(from, startOfLastWeek) && isSameDay(to, endOfLastWeek)) {
    return "last_week";
  }

  // Check for this month
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
  if (isSameDay(from, startOfMonth) && isSameDay(to, endOfMonth)) {
    return "this_month";
  }

  // Check for last month
  const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
  if (isSameDay(from, startOfLastMonth) && isSameDay(to, endOfLastMonth)) {
    return "last_month";
  }

  return "customize";
};

export const determineDateOption = (fromDate: string, toDate: string): string => {
  if (!fromDate || !toDate) return "all";

  const from = new Date(fromDate);
  const to = new Date(toDate);

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  // Helper function to check if two dates are the same day
  const isSameDay = (date1: Date, date2: Date) => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  };

  // Check for today
  if (isSameDay(from, today) && isSameDay(to, today)) {
    return "today";
  }

  // Check for yesterday
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  if (isSameDay(from, yesterday) && isSameDay(to, yesterday)) {
    return "yesterday";
  }

  // Check for this week
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Monday
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday
  if (isSameDay(from, startOfWeek) && isSameDay(to, endOfWeek)) {
    return "this_week";
  }

  // Check for last week
  const startOfLastWeek = new Date(startOfWeek);
  startOfLastWeek.setDate(startOfWeek.getDate() - 7);
  const endOfLastWeek = new Date(startOfLastWeek);
  endOfLastWeek.setDate(startOfLastWeek.getDate() + 6);
  if (isSameDay(from, startOfLastWeek) && isSameDay(to, endOfLastWeek)) {
    return "last_week";
  }

  // Check for this month
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
  if (isSameDay(from, startOfMonth) && isSameDay(to, endOfMonth)) {
    return "this_month";
  }

  // Check for last month
  const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
  if (isSameDay(from, startOfLastMonth) && isSameDay(to, endOfLastMonth)) {
    return "last_month";
  }

  // If no matches, it's a custom range
  return "customize";
};

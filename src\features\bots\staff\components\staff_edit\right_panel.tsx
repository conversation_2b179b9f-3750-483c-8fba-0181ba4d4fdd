"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import { useTranslation } from "react-i18next";

import { Card } from "@/components/ui";

import { useStaffDetail } from "../../hooks/staff";
import ChatBox from "../interact/chatbox";
import { RadarChartData } from "../radar_chart";

interface RightPanelProps {
  staff: any;
  radarData: RadarChartData[];
  chatboxLoading?: boolean;
}

export default function RightPanel({ staff, radarData, chatboxLoading }: RightPanelProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { t } = useTranslation();
  const params = useParams();
  const mockRadarData: RadarChartData[] = [
    { subject: "Consistency", value: 80 },
    { subject: "Coverage", value: 60 },
    { subject: "Accuracy", value: 70 },
    { subject: "Friendliness", value: 50 },
    { subject: "Response Time", value: 75 },
    { subject: "Relevance", value: 64 },
  ];
  const { data: mockStaff, isLoading } = useStaffDetail(params.id as string);
  return (
    <div className="sticky top-0 mx-auto flex size-full flex-col gap-4  overflow-hidden ">
      <Card className="flex w-full flex-auto flex-col overflow-hidden ">
        <ChatBox staff={mockStaff!} isLoading={isLoading} isDefaultHeader />
      </Card>
    </div>
  );
}

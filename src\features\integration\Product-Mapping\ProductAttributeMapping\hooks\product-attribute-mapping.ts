import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { mappingProductAttributeAPI } from "@/lib/apis/connection";

import { AttributeDetails, MappingAttribute, MappingPayload } from "./types";

export const useProductAttributeMapping = (connectionId?: string, syncRecordId?: string) => {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ["productAttributeMapping", connectionId],
    queryFn: async () => {
      if (!connectionId) {
        throw new Error("Connection ID is required");
      }
      const response = await mappingProductAttributeAPI.list({
        connection_id: connectionId,
      });
      return response;
    },
    enabled: !!connectionId,
  });

  const mutation = useMutation({
    mutationFn: async () => {
      if (!connectionId || !syncRecordId) {
        throw new Error("Connection ID and Sync Record ID are required");
      }
      const response = await mappingProductAttributeAPI.list({
        connection_id: connectionId,
        sync_record_id: syncRecordId,
      });
      return response;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(["productAttributeMapping", connectionId], data);
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to fetch product attribute mapping");
    },
  });

  return {
    ...query,
    mutate: mutation.mutate,
    isLoading: query.isLoading || mutation.isPending,
    error: query.error || mutation.error,
  };
};

// Group attributes by type for UI tabs
export const groupAttributesByType = (attributes: MappingAttribute[] = []) => {
  const productAttributes: MappingAttribute[] = [];
  const categoryAttributes: MappingAttribute[] = [];
  const pricingAttributes: MappingAttribute[] = [];

  attributes.forEach((attribute) => {
    // Simple categorization logic based on attribute names
    if (attribute.type === "product") {
      productAttributes.push(attribute);
    } else if (attribute.type === "category") {
      categoryAttributes.push(attribute);
    } else if (attribute.type === "pricing") {
      pricingAttributes.push(attribute);
    } else {
      // Default to product attributes if type is not specified
      productAttributes.push(attribute);
    }
  });

  return {
    productAttributes,
    categoryAttributes,
    pricingAttributes,
  };
};

// Helper to format attributes for the UI
export const formatAttributesForUI = (attributes: MappingAttribute[], sourceData?: any) => {
  return attributes.map((attribute) => {
    const sourceAttribute = attribute.source_attributes || {};
    const destinationAttribute = attribute.destination_attributes || {};

    const sourceKeys = Object.keys(sourceAttribute);
    const destinationKeys = Object.keys(destinationAttribute);

    return {
      id: attribute.connection_id,
      name: `${sourceKeys[0] || ""} to ${destinationKeys[0] || ""}`,
      channel: attribute.channel,
      fields: [
        ...Object.entries(sourceAttribute).map(([key, details]) => ({
          id: key,
          name: (details as AttributeDetails).label || key,
          description: (details as AttributeDetails).description || "",
          required: (details as AttributeDetails).is_required || false,
          source: "source",
          destination: "",
          sourceType: (details as AttributeDetails).type || "string",
          enabled: true,
          mapped: false,
          incompatible: false,
        })),
      ],
    };
  });
};

// Helper function to check if an attribute is mapped
export const isMappingComplete = (attributes: MappingAttribute[] = []) => {
  let totalRequired = 0;
  let totalMapped = 0;
  let errors = 0;

  attributes.forEach((attribute) => {
    const destinationAttrs = attribute.destination_attributes || {};

    Object.values(destinationAttrs).forEach((details) => {
      const attrDetails = details as AttributeDetails;
      if (attrDetails.is_required) {
        totalRequired++;
        // Check if this required field is mapped
        // Simplified logic - in real implementation, check actual mapping status
        if (false) {
          // Replace with actual mapping check
          totalMapped++;
        } else {
          errors++;
        }
      }
    });
  });

  return {
    totalRequired,
    totalMapped,
    errors,
    isComplete: totalRequired === totalMapped,
  };
};

export const useProductAttributeMappingDetail = (connectionId?: string) => {
  return useQuery({
    queryKey: ["productAttributeMappingDetail", connectionId],
    queryFn: async () => {
      if (!connectionId) {
        throw new Error("Connection ID is required");
      }
      const response = await mappingProductAttributeAPI.detail(connectionId);
      return response;
    },
    enabled: !!connectionId,
  });
};

export const useDeleteProductAttributeMapping = () => {
  return useMutation({
    mutationFn: async (connectionId: string) => {
      if (!connectionId) {
        throw new Error("Connection ID is required");
      }
      const response = await mappingProductAttributeAPI.delete(connectionId);
      return response;
    },
    onSuccess: () => {
      toast.success("Back to default successfully");
    },
    onError: (message: string) => {
      toast.error(message);
    },
  });
};

export const useUpdateProductAttributeMapping = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ connectionId, data }: { connectionId: string; data: MappingPayload }) => {
      if (!connectionId) {
        throw new Error("Connection ID is required");
      }
      const response = await mappingProductAttributeAPI.update(connectionId, data);
      return response;
    },
    onSuccess: () => {
      toast.success("Mappings updated successfully");
      // Return a promise that resolves after the timeout and refetch
      return new Promise((resolve) => {
        setTimeout(async () => {
          await queryClient.invalidateQueries({ queryKey: ["productAttributeMappingDetail"] });
          resolve(true);
        }, 5000);
      });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update mappings");
    },
  });
};

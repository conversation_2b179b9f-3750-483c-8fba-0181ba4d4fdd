import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PaginationProps {
  total: number;
  pageSize: number;
  currentPage: number;
  onPageSizeChange: (value: number) => void;
  onPageChange: (page: number) => void;
}
export function useStableTotal(total?: number) {
  const ref = useRef(0);

  useEffect(() => {
    if (typeof total === "number" && total > 0) {
      ref.current = total;
    }
  }, [total]);

  return ref.current;
}

export function CustomPagination({
  total,
  pageSize,
  currentPage,
  onPageSizeChange,
  onPageChange,
}: PaginationProps) {
  const { t } = useTranslation();
  const [initialPage, setInititalPage] = useState(currentPage); // Store initial page to prevent redundant call
  const newTotal = useStableTotal(total);
  const totalPages = Math.ceil(newTotal / pageSize) || 1;

  useEffect(() => {
    setInititalPage(currentPage);
  }, [currentPage, newTotal]);
  const getPageNumbers = useMemo(() => {
    const pages: (number | "ellipsis")[] = [];
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    pages.push(1);

    if (currentPage > 3) {
      pages.push("ellipsis");
    }

    for (
      let i = Math.max(2, currentPage - 1);
      i <= Math.min(totalPages - 1, currentPage + 1);
      i++
    ) {
      pages.push(i);
    }

    if (currentPage < totalPages - 2) {
      pages.push("ellipsis");
    }

    // Always show last page if there are pages
    if (totalPages > 1) {
      pages.push(totalPages);
    }

    return pages;
  }, [currentPage, totalPages]);

  const handlePageChange = (page: number) => {
    if (page === initialPage) return; // Skip if it's the initial page
    onPageChange(page);
  };

  return (
    <div className="flex h-fit flex-none flex-wrap items-center justify-between px-2">
      <div className="flex items-center gap-4">
        <div className="hidden items-center gap-2 py-1 md:flex">
          <span className="text-nowrap text-sm text-muted-foreground ">Rows per page</span>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(parseInt(value))}>
            <SelectTrigger className="w-[70px] bg-card">
              <SelectValue>{pageSize}</SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {newTotal > 0 && (
          <span className=" hidden items-center gap-2 text-sm text-muted-foreground md:flex">
            {t("table.pagination.description", {
              start: (currentPage - 1) * pageSize + 1,
              end: Math.min(currentPage * pageSize, newTotal),
              total: newTotal,
            })}
          </span>
        )}
      </div>

      <Pagination className="w-fit md:!mr-0 ">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (currentPage > 1) handlePageChange(currentPage - 1);
              }}
              aria-disabled={currentPage === 1}
              className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>

          {getPageNumbers.map((pageNum, idx) => (
            <PaginationItem key={idx}>
              {pageNum === "ellipsis" ? (
                <PaginationEllipsis />
              ) : (
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(pageNum);
                  }}
                  isActive={currentPage === pageNum}>
                  {pageNum}
                </PaginationLink>
              )}
            </PaginationItem>
          ))}

          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (currentPage < totalPages) handlePageChange(currentPage + 1);
              }}
              aria-disabled={currentPage === totalPages}
              className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}

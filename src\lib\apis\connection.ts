import {
  Transformation,
  TransformationPayload,
} from "@/features/integration/Product-Mapping/AdvancedMapping/hooks/types";
import {
  DestinationProductData,
  MappingProduct,
} from "@/features/integration/Product-Mapping/hooks/types";
import {
  MappingAttribute,
  MappingPayload,
} from "@/features/integration/Product-Mapping/ProductAttributeMapping/hooks/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseAxiosDetail, ResponseList } from "./types/common";
import { MappingProductMapPayload } from "./types/mapping-product";

export enum ConnectionStatus {
  INACTIVE = "INACTIVE",
  ACTIVE = "ACTIVE",
  PENDING = "PENDING",
}
export interface Connection {
  company_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  id: string;
  status: ConnectionStatus;
  url: string | null;
  image: string;
}

interface UpdateStatusResponse {
  message: string;
}

export const connectionApis = {
  list: async (params?: Record<string, unknown>) => {
    return privateApi.get<ResponseList<Connection>>(
      ENDPOINTS.INTEGRATION_ENDPOINTS.CONNECTION_LIST,
      { params }
    );
  },
  updateStatus: async (connection_id: string) => {
    return privateApi.post<UpdateStatusResponse>(
      ENDPOINTS.INTEGRATION_ENDPOINTS.CONNECTION_UPDATE_STATUS(connection_id)
    );
  },
};

export const mappingProduct = {
  list: async (params?: Record<string, unknown>) => {
    return privateApi.get<ResponseList<MappingProduct>>(ENDPOINTS.MAPPING_PRODUCT_ENDPOINTS.LIST, {
      params,
    });
  },
  mapProduct: async (data: MappingProductMapPayload) => {
    const url = `${ENDPOINTS.MAPPING_PRODUCT_ENDPOINTS.MAP}`;
    return privateApi.put(url, data);
  },
  unmapProduct: async (data: { connection_id: string; sync_record_id: string }) => {
    const url = `${ENDPOINTS.MAPPING_PRODUCT_ENDPOINTS.UNMAP}`;
    return privateApi.put(url, data);
  },
  mappingDetail: async (sync_record_id: string) => {
    const url = `${ENDPOINTS.MAPPING_PRODUCT_ENDPOINTS.DETAIL}`;
    return privateApi.post(url, { sync_record_id });
  },
};

export const transformationAPI = {
  list: async (params?: Record<string, unknown>) => {
    return privateApi.get<ResponseAxiosDetail<Transformation[]>>(
      ENDPOINTS.TRANSFORMATION_ENDPOINTS.LIST,
      { params }
    );
  },
  handleTransformation: async (data: TransformationPayload) => {
    const url = ENDPOINTS.TRANSFORMATION_ENDPOINTS.HANDLE_TRANSFORMATION;
    return privateApi.put<ResponseAxiosDetail<TransformationPayload>>(url, data);
  },
};

export const mappingProductAttributeAPI = {
  list: async (params?: Record<string, unknown>) => {
    const connectionId = params?.connection_id as string;
    return privateApi.post<MappingAttribute>(
      ENDPOINTS.PRODUCT_ATTRIBUTE_ENDPOINTS.LIST(connectionId),
      { sync_record_id: params?.sync_record_id }
    );
  },
  detail: async (connectionId: string) => {
    return privateApi.get<MappingAttribute>(
      ENDPOINTS.PRODUCT_ATTRIBUTE_ENDPOINTS.DETAIL(connectionId)
    );
  },
  update: async (connectionId: string, data: MappingPayload) => {
    return privateApi.post(ENDPOINTS.PRODUCT_ATTRIBUTE_ENDPOINTS.UPDATE(connectionId), data);
  },
  delete: async (connectionId: string) => {
    return privateApi.delete(ENDPOINTS.PRODUCT_ATTRIBUTE_ENDPOINTS.DELETE(connectionId));
  },
};

export const productDestinationDataAPI = {
  list: async (params?: Record<string, unknown>) => {
    const connectionId = params?.connection_id as string;
    return privateApi.get<ResponseList<DestinationProductData>>(
      ENDPOINTS.PRODUCT_DESTINATION_DATA.LIST(connectionId)
    );
  },
  detail: async (connectionId: string, productId: string) => {
    return privateApi.get<DestinationProductData>(
      ENDPOINTS.PRODUCT_DESTINATION_DATA.DETAIL(connectionId, productId)
    );
  },
};

export const syncProductAPI = {
  sync: async (connectionId: string, data: { sync_record_ids?: string[] }) => {
    return privateApi.post(ENDPOINTS.SYNC_PRODUCT_ENDPOINTS.SYNC(connectionId), data);
  },
};

import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";

import { useBrands } from "../../hooks/brand";
import { Brand } from "../../hooks/types";

export const columns = (
  useDeleteBrandMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any,
  onEdit?: (brand: Brand) => void
): CustomColumn<Brand>[] => [
  {
    id: "brand",
    accessorKey: "brand",
    header: t("pages.products.headers.brand"),
    sorter: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Brand> }) => {
      const brand = row.original;
      return (
        <div className="flex items-center gap-4  truncate">
          <ImageColumn
            src={typeof brand?.image === "string" ? brand.image : brand?.image?.url || ""}
            alt={brand?.name}
            width={0}
            iszoomable={true}
            height={0}
            sizes="100vw"
            className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
          />
          <div className="flex flex-auto flex-col justify-between gap-1 truncate ">
            <TextColumn text={brand?.name} className=" font-medium" />
          </div>
        </div>
      );
    },
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    sorter: true,
    sortKey: "updated_at",
    header: t("pages.products.headers.updatedAt"),
    cell: ({ row }: { row: Row<Brand> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "createAt",
    accessorKey: "createAt",
    sorter: true,
    sortKey: "created_at",
    header: t("pages.products.headers.createdAt"),
    cell: ({ row }: { row: Row<Brand> }) => <DateColumn date={row?.original?.created_at} />,
  },
  {
    id: "actions",
    header: t("pages.products.headers.actions"),
    cell: ({ row }: { row: Row<Brand> }) => (
      <ActionCell
        useDeleteBrandMutation={useDeleteBrandMutation}
        row={row}
        isDeleting={isDeleting}
        onEdit={onEdit}
      />
    ),
  },
];

const ActionCell = ({
  useDeleteBrandMutation,
  row,
  isDeleting,
  onEdit,
}: {
  useDeleteBrandMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Brand>;
  onEdit?: (brand: Brand) => void;
}) => {
  const router = useRouter();
  const brand = row.original;
  const { refetch } = useBrands({ enabled: false });
  const [isPending, startTransition] = useTransition();

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(brand);
    }
  }, [onEdit, brand]);

  const handleDelete = useCallback(() => {
    return useDeleteBrandMutation.mutateAsync(brand.id).then(() => {
      refetch();
    });
  }, [useDeleteBrandMutation, brand.id, refetch]);

  return (
    <ActionGroup
      actions={[
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

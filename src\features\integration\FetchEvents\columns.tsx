import { useCallback } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Row } from "@tanstack/react-table";
import { capitalize } from "lodash";

import ChannelLogo, { DateColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Badge } from "@/components/ui/badge";
import { FetchEvent } from "@/lib/apis/fetch-event";
import toNavUrl from "@/utils/helpers/nav-url-formater";
import { convertSnakeToCamel } from "@/utils/helpers/text-formater";

export const columns = (t: any): CustomColumn<FetchEvent>[] => [
  {
    id: "channel",
    accessorKey: "channel",
    header: t("pages.fetchEvents.headers.channel"),
    sorter: true,
    isMainColumn: true,
    sortKey: "channel",
    cell: ({ row }: { row: Row<FetchEvent> }) => (
      <ChannelLogo
        channelKey={row?.original?.channel || ""}
        href={`/fetch-events/${row?.original?.id}`}
      />
    ),
  },
  {
    id: "actionType",
    accessorKey: "action_type",
    header: t("pages.fetchEvents.headers.actionType"),
    cell: ({ row }: { row: Row<FetchEvent> }) => (
      <span className="text-sm text-blue-500">
        <Link href={toNavUrl(`/fetch-events/${row?.original?.id}`)}>
          {convertSnakeToCamel(row?.original?.action_type)}
        </Link>
      </span>
    ),
  },
  {
    id: "actionGroup",
    accessorKey: "action_group",
    header: t("pages.fetchEvents.headers.actionGroup"),
    sorter: true,
    sortKey: "action_group",
    cell: ({ row }: { row: Row<FetchEvent> }) => (
      <span className="text-sm">{convertSnakeToCamel(row?.original?.action_group)}</span>
    ),
  },
  {
    id: "eventSource",
    accessorKey: "event_source",
    header: t("pages.fetchEvents.headers.eventSource"),
    sorter: true,
    sortKey: "event_source",
    cell: ({ row }: { row: Row<FetchEvent> }) => (
      <span className="text-sm">{convertSnakeToCamel(row?.original?.event_source)}</span>
    ),
  },
  {
    id: "eventTime",
    accessorKey: "event_time",
    header: t("pages.fetchEvents.headers.eventTime"),
    sorter: true,
    sortKey: "event_time",
    cell: ({ row }: { row: Row<FetchEvent> }) => <DateColumn date={row?.original?.event_time} />,
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.fetchEvents.headers.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<FetchEvent> }) => (
      <FetchEventStatus status={row?.original?.status} />
    ),
  },
  {
    id: "actions",
    header: t("pages.fetchEvents.headers.actions"),
    cell: ({ row }: { row: Row<FetchEvent> }) => <ActionCell t={t} row={row} />,
  },
];

const FetchEventStatus = ({ status }: { status: string }) => {
  const getStatusVariant = (status: string) => {
    switch (status.toUpperCase()) {
      case "PENDING":
        return "sematic_warning";
      case "PROCESSING":
        return "sematic_info";
      case "FAILED":
        return "sematic_error";
      case "COMPLETED":
        return "sematic_success";
      default:
        return "secondary";
    }
  };

  return (
    <Badge className="border-none" variant={getStatusVariant(status)}>
      {capitalize(status)}
    </Badge>
  );
};

const ActionCell = ({ row, t }: { row: Row<FetchEvent>; t: any }) => {
  const router = useRouter();
  const fetchEvent = row.original;

  const handleView = useCallback(() => {
    router.push(`/sync-records?fetch_event_id=${fetchEvent.id}`);
  }, [router, fetchEvent.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          title: t("pages.fetchEvents.viewSyncRecordsList", "View Sync records list"),
          onClick: handleView,
        },
      ]}
    />
  );
};

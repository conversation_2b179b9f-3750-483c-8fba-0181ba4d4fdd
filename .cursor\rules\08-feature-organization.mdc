---
description: 
globs: 
alwaysApply: false
---
# Feature Organization Standards

## Directory Structure
```typescript
src/features/[feature-name]/
├── components/          // Feature-specific components
│   ├── index.ts        // Export all components
│   └── [component]/    // Component folder
│       ├── index.tsx   // Component implementation
│       ├── styles.ts   // Styled components (if needed)
│       └── types.ts    // Component types
├── hooks/              // Feature-specific hooks
│   ├── index.ts        // Export all hooks
│   ├── use-[name].ts   // Individual hooks
│   └── types.ts        // Hook types
├── api/                // API integration
│   ├── index.ts        // Export all API functions
│   └── types.ts        // API types
└── utils/              // Feature-specific utilities
    └── index.ts        // Utility functions
```

## Component Organization
1. Feature-Specific Components:
   ```typescript
   // src/features/departments/components/department-table/index.tsx
   import { type DepartmentTableProps } from "./types";
   import { useDepartmentTable } from "../../hooks/use-department-table";

   export function DepartmentTable(props: DepartmentTableProps) {
     const { data, handlers } = useDepartmentTable(props);
     return (
       // Component JSX
     );
   }
   ```

2. Component Types:
   ```typescript
   // src/features/departments/components/department-table/types.ts
   export interface DepartmentTableProps {
     type: "xs" | "md" | "staff";
   }
   ```

## Feature-Specific Hooks
```typescript
// src/features/departments/hooks/use-departments.ts
import { departmentApi } from "../api";
import type { Department } from "../api/types";

export function useDepartments() {
  // Hook implementation
  return {
    // Hook return values
  };
}
```

## Page Implementation
```typescript
// src/app/(bots)/department/page.tsx
import { 
  DepartmentTable, 
  CreateDepartmentDialog 
} from "@/features/departments/components";

export default function DepartmentPage() {
  // Page implementation using feature components
}
```

## Best Practices
1. Component Placement:
   - Place in `src/features/[feature]/components` if:
     - Component is only used within the feature
     - Component has feature-specific logic/state
     - Component is tightly coupled with feature data
   - Place in `src/components` if:
     - Component is shared across features
     - Component is purely presentational
     - Component has no feature-specific dependencies

2. Hook Organization:
   - Feature-specific hooks in `features/[feature]/hooks`
   - Shared hooks in `src/hooks`
   - Data fetching hooks with feature's API

3. Type Management:
   - Keep types close to their usage
   - Export shared types from feature's `types.ts`
   - Use barrel exports for clean imports

4. API Integration:
   - Feature-specific API calls in `features/[feature]/api`
   - Shared API utilities in `src/lib/api`
   - Type definitions with API functions

## Example Implementation
```typescript
// src/features/departments/index.ts
export * from "./components";
export * from "./hooks";
export * from "./api/types";

// src/features/departments/components/index.ts
export * from "./department-table";
export * from "./create-department-dialog";

// src/features/departments/hooks/index.ts
export * from "./use-departments";
export * from "./use-department-mutations";
```

## Testing Organization
```typescript
src/features/[feature]/
├── __tests__/
│   ├── components/
│   │   └── [component].test.tsx
│   └── hooks/
│       └── [hook].test.ts
└── __mocks__/
    └── [mock-data].ts
```

## State Management
1. Local State:
   - Use hooks for component-specific state
   - Keep state close to where it's used

2. Feature State:
   - Use React Query for server state
   - Context for feature-wide state
   - Zustand/Jotai for complex state

## Documentation
1. Component Documentation:
   ```typescript
   /**
    * DepartmentTable displays a list of departments with filtering and sorting
    * @param type - The type of departments to display (xs, md, staff)
    * @example
    * <DepartmentTable type="xs" />
    */
   export function DepartmentTable({ type }: DepartmentTableProps) {
     // Implementation
   }
   ```

2. Hook Documentation:
   ```typescript
   /**
    * Hook for managing department data and operations
    * @param options - Configuration options for the hook
    * @returns Department data and mutation functions
    */
   export function useDepartments(options: UseDepartmentsOptions) {
     // Implementation
   }
   ```

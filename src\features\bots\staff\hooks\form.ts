import * as z from "zod";

import { STAFF_AVATARS } from "../components/create-staff/staff-avatar";
import {
  COMMUNICATION_TONE_OPTIONS,
  LANGUAGE_OPTIONS,
  RESPONSE_LENGTH_OPTIONS,
} from "../components/staff_edit/constants/interaction_style";
import { VirtualStaffModel } from "./type";

const imageSchema = z.object({
  name: z.string(),
  image: z.string(),
});

export const staffFormSchema = z.object({
  staffInfo: z.object({
    name: z.string().min(1, "Name is required"),
    role: z.string().min(1, "Role is required"),
    department: z.string().min(1, "Department is required"),
    image: imageSchema.optional(),
    requireNamePhone: z.boolean(),
  }),
  personality: z.object({
    tone: z.string(),
    language: z.string(),
    response_length: z.string(),
    personal_trait: z.object({
      formality: z.number().min(0).max(100),
      detailed: z.number().min(0).max(100),
      creativity: z.number().min(0).max(100),
    }),
    ethical_constraints: z.boolean(),
  }),
  knowledge_base: z.object({
    knowledge_list: z.array(z.any()),
    domain_expertise_ids: z.array(z.string()),
  }),
  tools: z.object({
    products: z.boolean(),
    orders: z.boolean(),
    inventory: z.boolean(),
  }),
  conversation: z.object({
    tasks: z.array(
      z.object({
        id: z.number(),
        text: z.string(),
        created: z.string(),
      })
    ),
  }),
});

export type StaffFormValues = z.infer<typeof staffFormSchema>;

export const getDefaultFormValues = (): StaffFormValues => ({
  staffInfo: {
    name: "",
    role: "",
    department: "",
    image: {
      name: "default_avatar.png",
      image: STAFF_AVATARS[0].image.src,
    },
    requireNamePhone: false,
  },
  personality: {
    tone: "professional",
    language: "en",
    response_length: RESPONSE_LENGTH_OPTIONS[0].id,
    personal_trait: {
      formality: 30,
      detailed: 20,
      creativity: 50,
    },
    ethical_constraints: true,
  },
  knowledge_base: {
    knowledge_list: [],
    domain_expertise_ids: [],
  },
  tools: {
    products: true,
    orders: true,
    inventory: true,
  },
  conversation: {
    tasks: [],
  },
});

export const mapStaffToFormValues = (staff: VirtualStaffModel): StaffFormValues => {
  return {
    staffInfo: {
      name: staff.name || "",
      role: staff.role || "",
      department: staff.department_id || "",
      image: staff.image
        ? {
            name: staff.image.name || "current_avatar.png",
            image: staff.image.url || STAFF_AVATARS[0].image.src,
          }
        : {
            name: "default_avatar.png",
            image: STAFF_AVATARS[0].image.src,
          },
      requireNamePhone: false,
    },
    personality: {
      tone: staff.configuration?.personality?.tone ?? COMMUNICATION_TONE_OPTIONS[0].id,
      language: staff.configuration?.personality?.language ?? LANGUAGE_OPTIONS[0].id,
      response_length: RESPONSE_LENGTH_OPTIONS[0].id,
      personal_trait: {
        formality:
          staff?.configuration?.personality?.personal_trait?.formality === undefined
            ? 0
            : staff?.configuration?.personality?.personal_trait?.formality,
        detailed:
          staff?.configuration?.personality?.personal_trait?.detailed === undefined
            ? 0
            : staff?.configuration?.personality?.personal_trait?.detailed,
        creativity:
          staff?.configuration?.personality?.personal_trait?.creativity === undefined
            ? 0
            : staff?.configuration?.personality?.personal_trait?.creativity,
      },
      ethical_constraints: staff.configuration?.personality?.ethical_constraints ?? true,
    },
    knowledge_base: {
      knowledge_list: staff.configuration?.knowledge_base?.knowledge_list || [],
      domain_expertise_ids: staff.configuration?.knowledge_base?.domain_expertise_ids || [],
    },
    tools: {
      products: staff.configuration?.tools?.config?.products || true,
      orders: staff.configuration?.tools?.config?.orders || true,
      inventory: staff.configuration?.tools?.config?.inventory || true,
    },
    conversation: {
      tasks: [],
    },
  };
};

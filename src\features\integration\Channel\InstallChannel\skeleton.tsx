import { Button } from "@/components/ui/button";
import { Card, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function InstallChannelSkeleton() {
  return (
    <div className="flex max-h-full min-h-full flex-col justify-between overflow-hidden">
      <Card className="m-4 mt-0 h-fit overflow-auto p-4">
        <div className="flex flex-col gap-4">
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>

          <div className="flex items-center gap-4">
            <Skeleton className="size-16 rounded-lg" />
            <div className="flex flex-col justify-between gap-2 p-4">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-64" />
            </div>
          </div>

          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        </div>
      </Card>

      <Card className="flex w-full flex-none justify-end gap-2 rounded-none border-x-0 p-6 pt-4">
        <Button variant="outline" disabled>
          Cancel
        </Button>
        <Button loading>Install</Button>
      </Card>
    </div>
  );
}

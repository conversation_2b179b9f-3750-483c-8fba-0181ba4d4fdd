{"nav": {"overview": "<PERSON><PERSON><PERSON> quan", "patientManagement": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON>nh nhân", "doctorManagement": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> b<PERSON>", "medicalSupplies": "<PERSON><PERSON><PERSON> tư y tế", "invoicesPayments": "Hóa đơn & Thanh toán", "report": "Báo cáo", "administration": "<PERSON><PERSON><PERSON><PERSON> trị", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productList": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "newProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "editProduct": "Chỉnh s<PERSON>a sản phẩm", "variantsList": "<PERSON><PERSON> s<PERSON>ch biến thể", "brandList": "<PERSON><PERSON> s<PERSON>ch th<PERSON><PERSON><PERSON> hi<PERSON>u", "categoryList": "<PERSON><PERSON> s<PERSON>ch danh mục", "order": "<PERSON><PERSON><PERSON> hàng", "orderList": "<PERSON><PERSON> s<PERSON>ch đơn hàng", "orderDetail": "<PERSON> tiết đơn hàng", "orderEdit": "Chỉnh sửa đơn hàng", "orderProcess": "<PERSON><PERSON> lý đơn hàng", "returnOrderList": "<PERSON><PERSON> s<PERSON>ch đơn hàng trả lại", "packageList": "<PERSON><PERSON> s<PERSON>ch gói hàng", "integration": "<PERSON><PERSON><PERSON>", "fetchEvent": "<PERSON><PERSON><PERSON> s<PERSON> kiện", "syncRecords": "<PERSON>ồng bộ dữ liệu", "channel": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "logistics": "<PERSON><PERSON><PERSON> ch<PERSON>", "shippingProviderList": "<PERSON><PERSON> s<PERSON>ch nhà vận chuy<PERSON>n", "purchaseOrder": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hàng", "purchaseOrderList": "<PERSON><PERSON> s<PERSON>ch đơn nh<PERSON><PERSON> hàng", "supplierList": "<PERSON><PERSON> s<PERSON>ch nhà cung cấp", "customers": "<PERSON><PERSON><PERSON><PERSON>", "customerDashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "customerList": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>ch hàng", "customerDetail": "<PERSON> tiết kh<PERSON>ch hàng", "customerGroupList": "<PERSON><PERSON> s<PERSON>ch nhóm khách hàng", "loyaltyProgram": "<PERSON><PERSON><PERSON><PERSON> trình thành viên", "rewardProgram": "<PERSON><PERSON><PERSON><PERSON> trình tích điểm", "finance": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "transaction": "<PERSON><PERSON><PERSON>", "inventory": "<PERSON><PERSON>", "locationList": "<PERSON><PERSON> s<PERSON>ch đị<PERSON> điểm", "inventoryList": "<PERSON><PERSON> s<PERSON>ch tồn kho", "stockAdjustmentList": "<PERSON><PERSON> s<PERSON>ch điều chỉnh kho", "stockRelocateList": "<PERSON><PERSON> s<PERSON>ch chuy<PERSON>n kho", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "discountList": "<PERSON><PERSON> s<PERSON>ch gi<PERSON>m giá", "voucherList": "<PERSON><PERSON> s<PERSON>ch voucher", "import": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "importList": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON><PERSON> li<PERSON>u", "recordList": "<PERSON><PERSON> s<PERSON> bản ghi", "website": "Website", "blogCategory": "<PERSON><PERSON> m<PERSON><PERSON> b<PERSON>i viết", "blogList": "<PERSON><PERSON> s<PERSON>ch b<PERSON>i vi<PERSON>t", "notification": "<PERSON><PERSON><PERSON><PERSON> báo", "notificationList": "<PERSON><PERSON> s<PERSON>ch thông báo", "loyaltyApp": "Ứng dụng thành viên", "pos": "<PERSON><PERSON>", "detailFetchEvent": "<PERSON> tiết sự kiện", "supportedChannels": "<PERSON><PERSON><PERSON> hỗ trợ", "installChannel": "Cài đặt kênh mới", "terminalList": "<PERSON><PERSON> s<PERSON>ch thi<PERSON>t bị", "shiftList": "<PERSON><PERSON> s<PERSON>ch ca làm việc", "posFnB": "Bán hàng F&B", "settings": "Cài đặt", "dashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "productReport": "<PERSON><PERSON><PERSON> c<PERSON>o sản ph<PERSON>m", "productDetail": "<PERSON> tiết sản phẩm", "orderManual": "<PERSON><PERSON><PERSON><PERSON> đơn hàng", "productMapping": "<PERSON><PERSON>ng bộ sản phẩm", "productMappingDetail": "<PERSON> ti<PERSON>t Mapping", "productMappingAttribute": "Mapping th<PERSON><PERSON><PERSON> t<PERSON> sản phẩm", "staff": "Nhân viên", "staffList": "<PERSON><PERSON> s<PERSON>ch nhân viên", "department": "Phòng ban", "conversation": "<PERSON><PERSON><PERSON> tho<PERSON>i", "interact": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "knowledge": "<PERSON><PERSON><PERSON> th<PERSON>", "task": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "editStaff": "Chỉnh sửa nhân viên"}, "product": {"image": "<PERSON><PERSON><PERSON> sản phẩm", "title": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "description": "<PERSON><PERSON>", "price": "Giá", "sku": "Mã SKU", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "category": "<PERSON><PERSON>", "inventory": "<PERSON><PERSON><PERSON> kho", "notMapped": "<PERSON><PERSON><PERSON><PERSON> map đến đích"}, "productMapping": {"lastSynced": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "errorLoading": "Lỗi tải chi tiết đồng bộ sản phẩm", "manualRetry": "<PERSON><PERSON><PERSON> lại thủ công", "cancelledMessage": "<PERSON><PERSON> hủy đồng bộ sản phẩm", "mappingStatus": "<PERSON>r<PERSON><PERSON> thái đồng bộ", "variant": "<PERSON><PERSON><PERSON><PERSON> thể"}, "groups": {"operations": "<PERSON><PERSON><PERSON> h<PERSON>nh", "virtual_staff": "<PERSON><PERSON><PERSON> viên <PERSON>o"}, "branch": {"title": "<PERSON> n<PERSON>h", "all": "<PERSON><PERSON><PERSON> cả chi nh<PERSON>h", "addBranch": "<PERSON><PERSON><PERSON><PERSON> chi nh<PERSON>h mới", "branch": "<PERSON> n<PERSON>h", "shortcuts": {"alt": "Alt", "plus": "+"}, "daily": "<PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON> t<PERSON>", "monthly": "<PERSON><PERSON><PERSON>g", "yearly": "<PERSON><PERSON><PERSON>", "annually": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>"}, "profile": {"profile": "<PERSON><PERSON> sơ", "settings": "Cài đặt", "darkMode": "<PERSON><PERSON> độ tối", "on": "<PERSON><PERSON><PERSON>", "off": "Tắt", "language": "<PERSON><PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON><PERSON>", "vietnamese": "Tiếng <PERSON>", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "founder": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>g lập"}, "auth": {"username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "usernamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON> ký", "forgotPasswordDescription": "Nhập email và hướng dẫn sẽ được gửi đến bạn!", "forgotPasswordTitle": "<PERSON>u<PERSON><PERSON>?", "forgotPasswordSubtitle": "Nhập email của bạn để nhận hướng dẫn đặt lại mật khẩu", "resetPassword": "Đặt lại mật khẩu", "resetPasswordTitle": "Đặt lại mật khẩu", "resetPasswordDescription": "<PERSON><PERSON><PERSON><PERSON> mã xác thực và mật khẩu mới", "resetPasswordSubtitle": "<PERSON><PERSON><PERSON><PERSON> mã xác thực và mật khẩu mới", "resetPasswordButton": "Đặt lại mật khẩu", "resetPasswordSuccess": "Đặt lại mật khẩu thành công", "resetPasswordSuccessDescription": "<PERSON><PERSON>y giờ bạn có thể đăng nhập với mật khẩu mới của bạn", "resetPasswordError": "<PERSON><PERSON><PERSON><PERSON> thể đặt lại mật khẩu", "resetPasswordLoading": "<PERSON>ang đặt lại...", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "backToLogin": "Quay lại đăng nh<PERSON>p", "backToForgotPassword": "Quay lại quên mật khẩu", "loginTitle": "<PERSON><PERSON><PERSON>", "loginSubtitle": "<PERSON>hập tên đăng nhập hoặc email để đăng nhập vào tài khoản của bạn", "email": "Email", "emailPlaceholder": "m@example", "emailPlaceholderSignUp": "Nhập email", "verifyEmail": "<PERSON><PERSON><PERSON> thực email", "verifyEmailButton": "<PERSON><PERSON><PERSON> thực email", "verifyEmailSuccess": "<PERSON><PERSON> đã đư<PERSON><PERSON> xác thực thành công", "verifyEmailError": "<PERSON><PERSON><PERSON><PERSON> thể xác thực email", "verifyEmailLoading": "<PERSON><PERSON> xác thực...", "verifyEmailCode": "<PERSON><PERSON>ậ<PERSON> mã đã đư<PERSON><PERSON> g<PERSON><PERSON> đến email củ<PERSON> bạn", "verifyEmailCodePlaceholder": "<PERSON>h<PERSON><PERSON> mã", "verifyEmailCodeButton": "<PERSON><PERSON><PERSON> thực mã", "verifyEmailCodeSuccess": "<PERSON>ã đã đư<PERSON><PERSON> xác thực thành công", "verifyEmailCodeError": "<PERSON><PERSON><PERSON><PERSON> thể xác thực mã", "verifyEmailCodeLoading": "<PERSON><PERSON> xác thực mã...", "newPassword": "<PERSON><PERSON><PERSON> mới", "newPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u mới", "verificationCode": "<PERSON><PERSON> xác thực", "verificationCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã xác thực", "verificationCodeButton": "<PERSON><PERSON><PERSON> th<PERSON>c", "verificationCodeSuccess": "<PERSON><PERSON><PERSON> thực thành công", "verificationCodeError": "<PERSON><PERSON><PERSON> thực thất bại", "verificationCodeDescription": "<PERSON><PERSON>g tôi đã gửi mã đến {{username}}. <PERSON><PERSON><PERSON><PERSON> nó bên dưới.", "sendInstructions": "<PERSON><PERSON><PERSON> h<PERSON>ng dẫn", "sending": "<PERSON><PERSON> g<PERSON>...", "resetting": "<PERSON>ang đặt lại...", "password": "<PERSON><PERSON><PERSON>", "passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "rememberMe": "<PERSON><PERSON> nhớ đăng nhập", "loginButton": "<PERSON><PERSON><PERSON>", "loginWithGoogle": "Đ<PERSON>ng nhập với Google", "loginWithGithub": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "noAccount": "Chưa có tài k<PERSON>n?", "signUp": "<PERSON><PERSON><PERSON> ký", "signUpTitle": "<PERSON><PERSON><PERSON> ký", "signUpSubtitle": "<PERSON><PERSON><PERSON> ký để đăng nhập vào trang quản trị", "signUpButton": "<PERSON><PERSON><PERSON> ký", "signUpSuccess": "Đăng ký thành công! Vui lòng xác thực email của bạn", "signUpError": "<PERSON><PERSON><PERSON> ký thất bại", "signUpLoading": "<PERSON><PERSON> đăng ký...", "alreadyHaveAccount": "Đã có tài k<PERSON>n?", "sendNewCode": "<PERSON><PERSON><PERSON> l<PERSON>i", "resendCodeSuccess": "Đ<PERSON> gửi lại mã xác thực", "resendCodeError": "<PERSON><PERSON><PERSON><PERSON> thể gửi lại mã xác thực", "usernameOrEmail": "T<PERSON><PERSON> đ<PERSON>ng nhập hoặc email", "usernameOrEmailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập hoặc email", "forgot": "<PERSON>uên?", "or": "Hoặc", "loginSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "loginError": "<PERSON><PERSON><PERSON> nh<PERSON>p thất bại", "loginLoading": "<PERSON><PERSON> đăng nhập...", "usernameRequired": "<PERSON><PERSON> lòng nhập tên đăng nhập", "emailRequired": "<PERSON><PERSON> lòng nhập email", "passwordRequired": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "confirmPasswordRequired": "<PERSON><PERSON> lòng xác nhận mật kh<PERSON>u", "invalidPassword": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "forgotPasswordSuccess": "Hướng dẫn đặt lại mật khẩu đã đượ<PERSON> gửi đến email của bạn", "forgotPasswordError": "<PERSON><PERSON><PERSON><PERSON> thể gửi hướng dẫn đặt lại mật khẩu", "newPasswordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u mới là bắt buộc", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "passwordMustBeAtLeast8Characters": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "codeRequired": "<PERSON><PERSON> lòng x<PERSON>c nhận mã", "resendCodeCountdown": "G<PERSON><PERSON> lại trong {{seconds}}g"}, "common": {"start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "back": "Quay lại", "words": "từ", "confirm": "<PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON>", "totalSize": "Tổng dung lượng", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại", "setAsDefault": "Đặt lại mặc định", "saveFilters": "<PERSON><PERSON><PERSON> b<PERSON> lọc", "sort": "<PERSON><PERSON><PERSON>p", "view": "Xem", "add": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "saving": "<PERSON><PERSON> l<PERSON>...", "close": "Đ<PERSON><PERSON>", "clear": "Xóa", "loading": "<PERSON><PERSON> tả<PERSON>...", "loadingMore": "<PERSON><PERSON> tải thêm...", "deleting": "Đang xóa...", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "error": "Đ<PERSON> xảy ra lỗi", "success": "<PERSON><PERSON><PERSON><PERSON> công", "uploadImage": "<PERSON><PERSON><PERSON> và thả ảnh hoặc", "upload": "<PERSON><PERSON><PERSON>", "uploading": "<PERSON><PERSON> tả<PERSON>...", "fileSizeError": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tệp phải nhỏ hơn 5MB", "uploadError": "<PERSON><PERSON><PERSON><PERSON> thể tải <PERSON>nh lên", "imageUploadError": "<PERSON><PERSON><PERSON><PERSON> thể tải ảnh nhân viên lên", "areYouSure": "Bạn có chắc chắn không?", "leaveDesc": "<PERSON><PERSON><PERSON> thay đổi chưa đ<PERSON><PERSON><PERSON> lưu sẽ bị xóa.", "deleteProductConfirmation": "<PERSON>ành động này không thể hoàn tác. Sản phẩm này sẽ bị xóa vĩnh viễn.", "deleteListProductConfirmation": "<PERSON><PERSON><PERSON> động này không thể hoàn tác. {{count}} sản phẩm  sẽ bị xóa vĩnh viễn.", "install": "Cài đặt", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "deleteSuccess": "<PERSON><PERSON> x<PERSON>a sản phẩm thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa sản phẩm", "deleteSuccessDescription": "<PERSON><PERSON><PERSON> phẩm đã được xóa thành công", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "unsavedChanges": "<PERSON><PERSON> đ<PERSON>i chưa đ<PERSON><PERSON><PERSON> l<PERSON>u", "leaveWithoutSavingDescription": "Bạn có thay đổi chưa đ<PERSON><PERSON><PERSON> l<PERSON>. Bạn có chắc chắn muốn thoát không?", "leaveWithoutSaving": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> l<PERSON>u", "leave": "<PERSON><PERSON><PERSON><PERSON>", "stay": "Ở lại", "knowledgeUpdated": "<PERSON><PERSON><PERSON> thức đã đư<PERSON><PERSON> cập nhật thành công", "areYouSureDescription": "Bạn có chắc chắn muốn xóa kiến thức này không?", "staffUpdated": "<PERSON><PERSON><PERSON> viên đã đư<PERSON><PERSON> cập nhật thành công", "updateStaffError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật nhân viên", "areYouSureConfirm": "<PERSON><PERSON><PERSON>", "areYouSureCancel": "Hủy bỏ", "updateAttribute": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> th<PERSON><PERSON>h", "time": {"month": "<PERSON><PERSON><PERSON><PERSON>", "timeAgo": {"seconds": "{{count}} g<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON>", "seconds_plural": "{{count}} g<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON>", "minutes": "{{count}} ph<PERSON><PERSON> tr<PERSON><PERSON>c", "minutes_plural": "{{count}} ph<PERSON><PERSON> tr<PERSON><PERSON>c", "hours": "{{count}} g<PERSON><PERSON> trước", "hours_plural": "{{count}} g<PERSON><PERSON> trước", "days": "{{count}} ng<PERSON><PERSON> tr<PERSON>", "days_plural": "{{count}} ng<PERSON><PERSON> tr<PERSON>", "months": "{{count}} th<PERSON>g tr<PERSON>", "months_plural": "{{count}} th<PERSON>g tr<PERSON>", "years": "{{count}} năm tr<PERSON>c", "years_plural": "{{count}} năm tr<PERSON>c"}}, "empty": {"title": "<PERSON><PERSON><PERSON>ng có gì ở đây!", "description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả phù hợp."}, "create": "Tạo", "noFileSelected": "<PERSON><PERSON><PERSON> ch<PERSON> file", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "fileTooLarge": "File vượt quá dung lượng tối đa {{max}}MB"}, "pages": {"overview": {"title": "<PERSON><PERSON><PERSON> quan", "filters": {"period": "<PERSON><PERSON><PERSON><PERSON> gian", "daily": "<PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON> t<PERSON>", "monthly": "<PERSON><PERSON><PERSON>g", "yearly": "<PERSON><PERSON><PERSON>", "selectLocation": "<PERSON><PERSON><PERSON> cơ sở", "refresh": "<PERSON><PERSON><PERSON>"}, "stats": {"totalFacilities": "Tổng số cơ sở", "totalPatients": "<PERSON><PERSON>ng số bệnh nhân", "averageOccupancy": "Tỷ lệ lấp đầy", "totalRevenue": "<PERSON><PERSON>ng doanh thu", "viewMore": "<PERSON><PERSON>", "fromLastMonth": "so với tháng tr<PERSON>c"}, "patientStats": {"title": "<PERSON><PERSON><PERSON><PERSON> kê b<PERSON>nh nhân", "outpatient": "Ngoại trú", "inpatient": "Nội trú"}, "topTwenty": {"title": "Top 20", "icdDiagnoses": "<PERSON>ẩn đo<PERSON> ICD", "prescribedMedications": "<PERSON><PERSON><PERSON><PERSON> kê đơn"}, "costs": {"averageTreatmentCosts": "<PERSON> phí điều trị trung bình", "insurancePayments": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON>o <PERSON>", "insurance": "<PERSON><PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON> v<PERSON>", "specialCare": "<PERSON><PERSON><PERSON> s<PERSON>c đặc biệt"}, "treatmentOutcomes": {"title": "<PERSON><PERSON><PERSON> quả điều trị", "recovered": "<PERSON><PERSON><PERSON>", "improved": "<PERSON><PERSON><PERSON>", "unchanged": "<PERSON><PERSON><PERSON><PERSON> thay đổi", "deteriorated": "<PERSON><PERSON><PERSON> đi", "deceased": "<PERSON><PERSON> vong", "left": "<PERSON><PERSON><PERSON> đi"}}, "customers": {"title": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>ch hàng", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm theo tên"}, "group": "Nhóm"}, "name": "<PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "address": "Địa chỉ", "group": "Nhóm", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}, "orders": {"filters": {"shift": "<PERSON>a làm việc", "status": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "paymentStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}, "orderHistory": "<PERSON><PERSON><PERSON> sử đơn hàng", "amount": "Số lượng", "redeemPoints": "<PERSON><PERSON><PERSON><PERSON> thưởng", "loyalPoints": "<PERSON><PERSON><PERSON><PERSON> thưởng", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "title": "<PERSON><PERSON> s<PERSON>ch đơn hàng", "searchPriceGroup": "<PERSON><PERSON>m kiếm nhóm giá", "noPriceGroupsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhóm giá", "searchBranch": "<PERSON><PERSON><PERSON> kiếm chi nh<PERSON>h", "noBranchFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chi nh<PERSON>h", "emptyServiceName": "<PERSON><PERSON> lòng điền đ<PERSON>y đủ tên dịch vụ", "updateOrder": "<PERSON><PERSON><PERSON>", "selectGender": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> t<PERSON>h", "tax": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON> vận chuy<PERSON>n", "addCustomer": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "save": "<PERSON><PERSON><PERSON>", "defaultShippingAddress": "Địa chỉ giao hàng mặc định", "defaultBillingAddress": "Đ<PERSON><PERSON> chỉ thanh toán mặc định", "noAddressesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy địa chỉ", "edit": "Chỉnh sửa", "removeRecipientInfo": "<PERSON><PERSON><PERSON> thông tin ng<PERSON>ời nhận", "addRecipientInfo": "<PERSON><PERSON><PERSON><PERSON> thông tin người nhận", "enterName": "<PERSON><PERSON><PERSON><PERSON> tên", "enterAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterCompanyName": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "selectWard": "<PERSON><PERSON><PERSON> xã", "searchWard": "<PERSON><PERSON><PERSON> kiếm xã", "searchDistrict": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>", "selectDistrict": "<PERSON><PERSON><PERSON>", "selectProvince": "<PERSON><PERSON><PERSON> tỉnh", "searchProvince": "<PERSON><PERSON><PERSON> kiếm tỉnh", "province": "Tỉnh", "district": "<PERSON><PERSON><PERSON><PERSON>", "ward": "Xã", "addAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "address": "Địa chỉ", "noProvincesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tỉnh", "noDistrictsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy huy<PERSON>n", "noWardsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy xã", "shippingDefault": "Địa chỉ giao hàng mặc định", "billingDefault": "Đ<PERSON><PERSON> chỉ thanh toán mặc định", "editCustomer": "Chỉnh sửa kh<PERSON>ch hàng", "Name": "<PERSON><PERSON><PERSON>", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "enterEmail": "Nhập email", "birthday": "<PERSON><PERSON><PERSON>", "pickADate": "<PERSON><PERSON><PERSON>", "customerGroup": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng", "selectCustomerGroup": "<PERSON><PERSON><PERSON> nh<PERSON>m kh<PERSON>ch hàng", "companyName": "<PERSON><PERSON>n công ty", "addresses": "Địa chỉ", "submit": "<PERSON><PERSON><PERSON>", "accumulatedPoints": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch <PERSON>", "groupName": "Nhóm", "placeholder": "<PERSON><PERSON><PERSON> kiếm đơn hàng...", "quantity": "Số lượng", "price": "Giá", "total": "Tổng", "noProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "addService": "<PERSON><PERSON><PERSON><PERSON> (F9)", "loadingMore": "<PERSON><PERSON> tải thêm...", "addProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "available": "<PERSON><PERSON> sẵn", "onHand": "Trong kho", "note": "<PERSON><PERSON><PERSON>", "maximumAvailableQuantity": "<PERSON><PERSON> lượng tối đa có sẵn", "branch": "<PERSON> n<PERSON>h", "loadingCustomerDetails": "<PERSON><PERSON> tải chi tiết kh<PERSON>ch hàng...", "customer": "<PERSON><PERSON><PERSON><PERSON>", "shippingAddress": "Đ<PERSON>a chỉ giao hàng", "billingAddress": "<PERSON><PERSON><PERSON> chỉ thanh toán", "noCustomersFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khách hàng", "loading": "<PERSON><PERSON> tả<PERSON>...", "searchCustomer": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>m kh<PERSON>ch hàng", "payment": "<PERSON><PERSON> toán", "addPromotion": "<PERSON><PERSON><PERSON><PERSON> mãi", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "subtotal": "<PERSON><PERSON><PERSON> tiền", "discount": "G<PERSON>ảm giá", "voucher": "Mã giảm giá", "fees": "<PERSON><PERSON> vụ", "promotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "notePlaceholder": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "tags": "Thẻ", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thẻ", "noProductsInOrder": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm trong đơn hàng.", "cancel": "<PERSON><PERSON><PERSON>", "addOrder": "<PERSON><PERSON><PERSON><PERSON> đơn hàng", "success": "<PERSON><PERSON>n hàng đã đư<PERSON><PERSON> tạo thành công!", "error": "Đ<PERSON><PERSON> hàng không thể đư<PERSON>c xử lý", "adjustPrice": "Điều chỉnh giá", "adjustPriceSuccess": "Giá đã được điều chỉnh thành công!", "adjustPriceError": "<PERSON>h<PERSON>ng thể điều chỉnh giá", "adjustPriceDescription": "Điều chỉnh giá của sản phẩm đã chọn", "adjustPricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> mới", "adjustPriceButton": "Điều chỉnh giá", "adjustPriceCancel": "<PERSON><PERSON><PERSON>", "setNewPrice": "Đặt giá mới", "value": "<PERSON><PERSON><PERSON> trị", "percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "addProductToOrderWarning": "<PERSON><PERSON> lòng thêm sản phẩm vào đơn hàng", "selectCustomer": "<PERSON><PERSON> lòng chọn kh<PERSON>ch hàng", "addVoucher": "Thêm mã giảm giá", "voucherCode": "Mã giảm giá", "voucherCodePlaceholder": "<PERSON>hậ<PERSON> mã giảm giá", "voucherCodeButton": "Thêm mã giảm giá", "voucherCodeSuccess": "<PERSON>ã giảm giá đã đư<PERSON><PERSON> thêm thành công", "voucherCodeError": "<PERSON><PERSON><PERSON><PERSON> thể thêm mã giảm giá", "confirm": "Bạn có chắc chắn không?", "confirmCancel": "<PERSON><PERSON><PERSON>", "confirmDelete": "Xóa", "cancelWarning": "Hành động này không thể hoàn tác. Hủy thay đổi?", "cancelDelete": "Hành động này không thể hoàn tác. <PERSON><PERSON><PERSON> mục này?"}, "variants": {"title": "<PERSON><PERSON><PERSON><PERSON> thể", "filters": {"search": {"placeholder": "<PERSON><PERSON>m kiếm theo tên, mã, mã vạch"}}}, "products": {"title": "<PERSON><PERSON><PERSON>", "filters": {"search": {"placeholder": "<PERSON><PERSON>m kiếm theo tên, mã, mã vạch", "placeholderBrand": "<PERSON><PERSON><PERSON> kiếm thương hiệu..."}, "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "source": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "otherFilters": {"title": "Bộ lọ<PERSON> kh<PERSON>c", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "description": "Hai bộ lọc đầu tiên sẽ được ưu tiên hiển thị trên trang chính. Tùy chỉnh chúng dựa trên nhu cầu của bạn."}, "dateOptions": {"allTime": "<PERSON><PERSON><PERSON> cả thời gian", "today": "<PERSON><PERSON><PERSON> nay", "yesterday": "<PERSON><PERSON><PERSON> qua", "lastWeek": "<PERSON><PERSON><PERSON> tr<PERSON>", "thisWeek": "<PERSON><PERSON><PERSON>", "lastMonth": "<PERSON><PERSON><PERSON><PERSON>", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "customize": "<PERSON><PERSON><PERSON> chỉnh"}, "deletionFailed": "<PERSON><PERSON><PERSON> biến thể thất bại", "deletedSuccessfully": "<PERSON><PERSON><PERSON> biến thể thành công"}, "headers": {"productInfo": "Thông tin sản phẩm", "category": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "createdAt": "Tạo lúc", "available": "Số lượng", "variant": "<PERSON><PERSON><PERSON><PERSON> thể"}, "actions": {"addProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "addManual": {"title": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "onThisPage": "<PERSON><PERSON><PERSON>", "sections": {"basicInfo": "Thông Tin Cơ Bản", "options": "<PERSON><PERSON><PERSON>", "units": "Đơn <PERSON>", "prices": "<PERSON><PERSON><PERSON>", "measurements": "<PERSON><PERSON><PERSON>"}}, "addQuick": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m n<PERSON>h", "addBulk": "<PERSON><PERSON><PERSON><PERSON> sản phẩm hàng lo<PERSON>t", "refresh": "<PERSON><PERSON><PERSON>", "saveFilters": "<PERSON><PERSON><PERSON> b<PERSON> lọc", "reset": "Đặt lại", "filter": "<PERSON><PERSON><PERSON>"}, "deletionFailed": "<PERSON><PERSON><PERSON> biến thể thất bại", "deletedSuccessfully": "<PERSON><PERSON><PERSON> biến thể thành công", "descriptionDeleteOption": "<PERSON><PERSON><PERSON><PERSON> thể hoàn tác hành động này. <PERSON><PERSON> liệu liên quan đến Đơn vị gói, <PERSON><PERSON><PERSON> sản phẩm và K<PERSON>ch thước sẽ bị xóa vĩnh viễn.", "descriptionDeleteValueOption": "<PERSON><PERSON><PERSON><PERSON> thể hoàn tác hành động này. <PERSON><PERSON> liệu liên quan đến Giá sản phẩm và Kích thước sẽ bị xóa vĩnh viễn.", "name": "<PERSON><PERSON><PERSON>", "sku": "Mã SKU", "barcode": "Mã vạch", "option1": "Tùy chọn 1", "option2": "T<PERSON>y <PERSON> 2", "option3": "Tùy chọ<PERSON> 3", "unit": "Đơn vị", "weight": "Cân nặng", "height": "<PERSON><PERSON><PERSON> cao", "width": "<PERSON><PERSON><PERSON> r<PERSON>", "length": "<PERSON><PERSON><PERSON> dài", "variantDetails": "<PERSON> tiết biến thể", "variants": "<PERSON><PERSON><PERSON><PERSON> thể", "source": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "viewLess": "<PERSON><PERSON>", "viewMore": "<PERSON><PERSON>", "noPricesAvailable": "Không có giá", "prices": "Giá", "inventory": {"noMatchResult": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "title": "<PERSON><PERSON>", "branch": "<PERSON> n<PERSON>h", "history": "<PERSON><PERSON><PERSON> s<PERSON>", "allBranches": "<PERSON><PERSON><PERSON> cả chi nh<PERSON>h", "inventory": "<PERSON><PERSON>", "packing": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "minValue": "<PERSON><PERSON><PERSON> trị tối thiểu", "maxValue": "<PERSON><PERSON><PERSON> trị tối đa", "staff": "Nhân viên", "transactionType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "change": "<PERSON><PERSON> đ<PERSON>i", "quantity": "Số lượng", "reference": "<PERSON><PERSON>", "available": "<PERSON><PERSON> sẵn", "incoming": "Đang vào", "onHand": "Trong kho"}, "addManual": {"title": "<PERSON><PERSON><PERSON><PERSON>", "onThisPage": "<PERSON><PERSON><PERSON>", "publish": "<PERSON><PERSON><PERSON>", "sections": {"addVariant": "<PERSON><PERSON><PERSON> biến thể khi có nhiều lựa chọn, chẳng hạn như kích cỡ hoặc màu sắc.", "variant": "<PERSON><PERSON><PERSON><PERSON> thể", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "apply": "<PERSON><PERSON>", "variantPlaceholder": "<PERSON><PERSON><PERSON> biến thể", "usedByAllVariants": " <PERSON><PERSON><PERSON><PERSON> sử dụng bởi tất cả các biến thể", "usedByThis": " <PERSON><PERSON><PERSON><PERSON> sử dụng bởi biến thể này", "unitPlaceholder": "<PERSON><PERSON>n đơn vị", "unitSearchPlaceholder": "<PERSON><PERSON><PERSON> ki<PERSON>m đơn vị", "variantSearchPlaceholder": "<PERSON><PERSON><PERSON> kiến biến thể", "unitEmptyText": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đơn vị", "addType": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON> đơn vị", "basicInfo": "Thông Tin Cơ Bản", "options": "<PERSON><PERSON><PERSON>", "option": "t<PERSON><PERSON>n", "units": "Đơn <PERSON>", "prices": "<PERSON><PERSON><PERSON>", "measurements": "<PERSON><PERSON><PERSON>", "selectImages": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "stay": "Ở lại", "leave": "<PERSON><PERSON><PERSON> đi", "values": "<PERSON><PERSON><PERSON> trị", "valuesPlaceholder": "<PERSON><PERSON><PERSON> tr<PERSON> 1, <PERSON><PERSON><PERSON>r<PERSON> 2, <PERSON><PERSON><PERSON> trị 3", "optionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tùy chọn", "addOption": "<PERSON><PERSON><PERSON><PERSON> tù<PERSON> ch<PERSON>n", "optionName": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON>n", "addValue": "<PERSON>h<PERSON><PERSON> giá trị", "remove": "Xóa", "valuesPlaceholderInput": "<PERSON><PERSON><PERSON><PERSON> giá trị", "duplicateValue": "<PERSON><PERSON>á trị này đã tồn tại", "createVariant": "<PERSON><PERSON><PERSON> biến thể có nhiều lựa chọn, chẳng hạn như kích cỡ hoặc màu sắc."}, "basicInfo": {"brandPlaceholder": "<PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>u", "brandSearchPlaceholder": "<PERSON><PERSON><PERSON> ki<PERSON>m thư<PERSON> hi<PERSON>u", "brandEmptyText": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư<PERSON>ng hiệu", "categoryPlaceholder": "<PERSON><PERSON><PERSON> danh mục", "categorySearchPlaceholder": "<PERSON><PERSON><PERSON> ki<PERSON>m danh mục", "categoryEmptyText": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh mục", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thẻ", "images": "<PERSON><PERSON><PERSON> Ảnh", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "shortDescription": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "sku": "Mã SKU", "tags": "Thẻ", "price": "Giá", "uploadImage": "<PERSON><PERSON><PERSON>n", "optimize": "<PERSON><PERSON><PERSON>", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "imageRequired": "<PERSON><PERSON> n<PERSON>t có một <PERSON>nh", "nameRequired": "<PERSON><PERSON><PERSON> sản phẩm là bắt buộc", "nameWarning": "<PERSON><PERSON><PERSON> sản phẩm là bắt buộc để tối ưu hóa", "descriptionWarning": "<PERSON><PERSON> tả là bắt buộc để tối ưu hóa", "skuRequired": "Mã SKU là bắt buộc", "priceRequired": "<PERSON><PERSON><PERSON> là b<PERSON> bu<PERSON>c"}, "options": {"addOption": "<PERSON><PERSON><PERSON><PERSON> tù<PERSON> ch<PERSON>n", "optionName": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON>n", "values": "<PERSON><PERSON><PERSON> trị", "addValue": "<PERSON>h<PERSON><PERSON> giá trị", "remove": "Xóa"}, "units": {"title": "Đơn <PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON> đơn vị", "unitName": "<PERSON><PERSON><PERSON> đơn vị", "ratio": "Tỷ lệ", "remove": "Xóa"}, "prices": {"title": "<PERSON><PERSON><PERSON>", "addGroup": "Thêm nhóm giá mới", "groupName": "<PERSON><PERSON><PERSON>", "price": "Giá", "apply": "<PERSON><PERSON>", "applyAll": "<PERSON><PERSON> dụng tất cả"}, "measurements": {"weight": "Cân nặng", "height": "<PERSON><PERSON><PERSON> cao", "width": "<PERSON><PERSON><PERSON> r<PERSON>", "length": "<PERSON><PERSON><PERSON> dài", "apply": "<PERSON><PERSON>", "applyAll": "<PERSON><PERSON> dụng tất cả"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "stay": "Ở lại", "leave": "<PERSON><PERSON><PERSON> đi"}, "dialogs": {"leaveTitle": "Bạn có chắc muốn rời đi?", "leaveDesc": "<PERSON><PERSON><PERSON> thay đổi chưa lưu sẽ bị mất."}, "validation": {"hasErrors": "Lỗi xác thực", "checkFields": "<PERSON><PERSON> lòng kiểm tra tất cả các trườ<PERSON> bắt buộc và thử lại"}, "success": "<PERSON><PERSON> tạo sản phẩm", "successUpdate": "<PERSON><PERSON> cập nh<PERSON>t sản phẩm", "successDescription": "<PERSON><PERSON><PERSON> phẩm đã đư<PERSON>c tạo thành công", "successDescriptionUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "error": "Lỗi", "errorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo sản phẩm. <PERSON><PERSON> lòng thử lại.", "errorDescriptionUpdate": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật sản phẩm. <PERSON><PERSON> lòng thử lại"}}, "choosePlan": {"forIndividuals": "Cá nhân", "forCompanies": "<PERSON><PERSON>ng ty", "monthly": "<PERSON><PERSON><PERSON><PERSON>", "annually": "Năm", "chooseAPlan": "<PERSON><PERSON><PERSON>", "planDescription": {"firstSection": "<PERSON><PERSON><PERSON> gói phù hợp nhất với nhu cầu kinh doanh của bạn.", "middleSection": "<PERSON><PERSON><PERSON> có thể nâng cấp hoặc hạ cấp sau này.", "secondSection": "T<PERSON><PERSON> cả các gói đều bao gồm các t<PERSON>h năng c<PERSON> bản."}, "planData": {"Up to 50 variants": "<PERSON><PERSON><PERSON> đến 50 biến thể, thống kê tổng quan", "Real-time inventory syncing": "<PERSON><PERSON>ng bộ tồn kho thời gian thực", "Ideal for startups (1,000 items)": "<PERSON><PERSON> tưởng cho các doanh nghiệp mới thành lập (1.000 sản phẩm)", "Analytics dashboard": "<PERSON><PERSON>ng điều khiển phân tích", "User-friendly interface": "<PERSON><PERSON><PERSON>n thân thiện với người dùng", "Support for multiple currencies and languages": "Hỗ trợ nhiều loại tiền tệ và ngôn ngữ", "Real time inventory": "<PERSON><PERSON><PERSON><PERSON> lý tồn kho thời gian thực"}, "planNames": {"Free": "<PERSON><PERSON><PERSON> phí", "Starter": "<PERSON><PERSON> bản", "Pro": "<PERSON><PERSON><PERSON><PERSON>", "Agency": "<PERSON><PERSON><PERSON>"}, "mostPopular": "<PERSON><PERSON>", "numberIntegrations": "<PERSON><PERSON> l<PERSON><PERSON> tích hợp", "explainNoIntegrations": "<PERSON><PERSON><PERSON><PERSON> có tích hợp nào", "getStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u"}, "synchronization": {"platforms": {"source": "<PERSON><PERSON><PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>"}, "title": {"success": "<PERSON><PERSON><PERSON> {{source}} với {{destination}}", "error": "<PERSON><PERSON><PERSON> bộ hóa cấu hình"}, "description": "<PERSON>ọn một lĩnh vực phù hợp với sở thích và đối tượng mục tiêu của bạn.", "error": {"missingConnection": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết n<PERSON>i", "connectionError": "Lỗi kết nối", "sourceNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nguồn", "destinationNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đích"}, "success": {"completeTitle": "<PERSON><PERSON>t n<PERSON>i hoàn tất!", "gotoDashboard": "<PERSON><PERSON> đến bảng điều khiển"}, "syncSetting": {"title": "Cài đặt đồng bộ", "product": {"title": "<PERSON><PERSON><PERSON> p<PERSON>m", "description": "<PERSON><PERSON><PERSON> bộ sản phẩm từ {{source}} sang {{destination}}"}, "inventory": {"title": "<PERSON><PERSON><PERSON> tồn kho", "description": "<PERSON><PERSON><PERSON> mức tồn kho đ<PERSON><PERSON><PERSON> đồng bộ"}, "order": {"title": "<PERSON><PERSON><PERSON> hàng", "description": "<PERSON><PERSON><PERSON><PERSON> đơn hàng {{destination}} vào {{source}}"}, "buttonTitle": "<PERSON><PERSON><PERSON> n<PERSON>i với {{destination}}"}}, "syncRecords": {"title": "<PERSON><PERSON> s<PERSON>ch đồng bộ", "filters": {"search": {"placeholder": "T<PERSON><PERSON> kiếm đơn hàng trả lại..."}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "recordType": "<PERSON><PERSON><PERSON> bản <PERSON>hi", "channel": "<PERSON><PERSON><PERSON>", "connectionId": "ID Kế<PERSON> n<PERSON>", "fetchEventId": "ID Sự kiện lấy dữ liệu"}, "columns": {"channel": "<PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON><PERSON> bản <PERSON>hi", "fetchEventId": "ID Sự kiện lấy dữ liệu", "connectionId": "ID Kế<PERSON> n<PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "fetchedAt": "<PERSON><PERSON><PERSON> dữ liệu lúc", "finishedAt": "<PERSON><PERSON><PERSON> tất lúc", "publishedAt": "<PERSON><PERSON><PERSON> b<PERSON>", "transformedAt": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>i lúc"}}, "fetchEvents": {"title": "<PERSON><PERSON> kiện <PERSON>tch", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm sự kiện Fetch..."}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actionType": "<PERSON><PERSON><PERSON> hành động", "actionGroup": "<PERSON><PERSON><PERSON><PERSON> hành động", "eventTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "eventSource": "<PERSON><PERSON><PERSON><PERSON> sự kiện", "fetchEventId": "ID sự kiện <PERSON>tch"}, "columns": {"channel": "<PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON><PERSON> bản <PERSON>hi", "fetchEventId": "ID sự kiện <PERSON>tch", "connectionId": "<PERSON> kết n<PERSON>i", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "fetchedAt": "<PERSON><PERSON><PERSON> dữ liệu lúc", "finishedAt": "<PERSON><PERSON><PERSON> thành l<PERSON>c", "publishedAt": "<PERSON><PERSON><PERSON> b<PERSON>", "transformedAt": "<PERSON>y<PERSON><PERSON> đ<PERSON>i lúc"}, "headers": {"channel": "<PERSON><PERSON><PERSON>", "actionType": "<PERSON><PERSON><PERSON> hành động", "actionGroup": "<PERSON><PERSON><PERSON><PERSON> hành động", "eventSource": "<PERSON><PERSON><PERSON><PERSON>", "eventTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "fetchEventDetail": {"title": "<PERSON> tiết sự kiện Fetch của", "actionGroup": "<PERSON><PERSON><PERSON><PERSON> hành động", "connectionId": "ID Kế<PERSON> n<PERSON>", "actionType": "<PERSON><PERSON><PERSON> hành động", "eventSource": "<PERSON><PERSON><PERSON><PERSON> sự kiện", "retryCount": "<PERSON><PERSON> lần thử lại", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "continuationToken": "<PERSON><PERSON> tiếp tục", "objectId": "<PERSON> đối t<PERSON>", "eventTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "createdAt": "Tạo lúc", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "eventNumber": "{{number}}. <PERSON><PERSON><PERSON><PERSON> có ID"}, "channel": {"title": "<PERSON><PERSON> s<PERSON>ch kết n<PERSON>i", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm kết n<PERSON>i..."}, "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "headers": {"channel": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "url": "URL", "createdAt": "<PERSON><PERSON><PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "actions": {"install": "Cài đặt kênh mới", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a"}}, "supportedChannels": {"title": "<PERSON><PERSON> s<PERSON> k<PERSON>nh", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm kênh..."}}}, "installChannel": {"title": "Cài đặt kênh"}, "settings": {"themeSetting": "Cài đặt chủ đề", "saveSuccess": "<PERSON><PERSON><PERSON> sắc đã đ<PERSON><PERSON><PERSON> lưu", "saveError": "<PERSON><PERSON><PERSON><PERSON> thể lưu màu sắc", "colorSetting": "Cài đặt màu sắc", "lightMode": "<PERSON><PERSON> độ sáng", "darkMode": "<PERSON><PERSON> độ tối", "logoSetting": "Cài đặt logo", "logo": {"lightModeLogo": "Logo chế độ sáng", "darkModeLogo": "Logo chế độ tối", "lightModeIcon": "<PERSON>con chế độ sáng", "darkModeIcon": "<PERSON>con chế độ tối", "favicon": "Favicon", "lightModeLogoDescription": "<PERSON><PERSON><PERSON> lên logo cho chế độ sáng (k<PERSON><PERSON> thư<PERSON><PERSON> khu<PERSON>n nghị: 180x40px)", "darkModeLogoDescription": "<PERSON><PERSON>i lên logo cho chế độ tối (k<PERSON><PERSON> thư<PERSON><PERSON> k<PERSON>n nghị: 180x40px)", "lightModeIconDescription": "Tải lên icon cho chế độ sáng (k<PERSON><PERSON> thư<PERSON><PERSON> k<PERSON>n nghị: 40x40px)", "darkModeIconDescription": "Tải lên icon cho chế độ tối (k<PERSON><PERSON> thư<PERSON><PERSON> k<PERSON>n nghị: 40x40px)", "faviconDescription": "T<PERSON>i lên favicon cho website (k<PERSON><PERSON> th<PERSON><PERSON><PERSON> k<PERSON>n nghị: 32x32px)", "saveSuccess": "Logo đã đ<PERSON><PERSON><PERSON> l<PERSON>u", "saveError": "<PERSON><PERSON><PERSON>ng thể lưu logo", "noChangesToSave": "<PERSON><PERSON><PERSON><PERSON> có thay đổi để lưu", "resetSuccess": "Logo đã được đặt lại", "resetError": "Không thể đặt lại logo"}, "color": {"saveSuccess": "<PERSON><PERSON><PERSON> chủ đề đã đư<PERSON><PERSON> lưu", "saveError": "<PERSON><PERSON><PERSON><PERSON> thể lưu màu chủ đề", "resetSuccess": "<PERSON><PERSON>u chủ đề đã được đặt lại", "resetError": "<PERSON><PERSON><PERSON>ng thể đặt lại màu chủ đề"}}, "productMappingList": {"syncSuccess": "<PERSON><PERSON><PERSON><PERSON> yêu cầu đồng bộ sản phẩm thành công", "syncFail": "<PERSON><PERSON><PERSON><PERSON> yêu cầu đồng bộ sản phẩm thất bại", "noConnection": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết n<PERSON>i", "title": "Đồng bộ <PERSON> phẩm", "description": "Đồng bộ sản phẩm của bạn từ Shopify đến Tiktok Shop", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON>"}, "status": {"all": "<PERSON><PERSON><PERSON> cả sản phẩm", "synced": "<PERSON><PERSON> đồng bộ", "mapped": "Đã map", "unMapped": "Chưa map", "errors": "Lỗi"}}, "alert": {"title": "<PERSON><PERSON><PERSON> nhận đồng bộ sản phẩm?", "description": "Bạn đang muốn đồng bộ tất cả sản phẩm giữa hai nền tảng. Qu<PERSON> trình này có thể mất một khoảng thời gian để hoàn tất.", "note": "<PERSON><PERSON><PERSON> ý:", "noteDescription": "<PERSON><PERSON><PERSON> bạn chỉ muốn đồng bộ một số sản phẩm, vui lòng chọn chúng từ bảng trước khi tiếp tục.", "confirm": "<PERSON><PERSON><PERSON> bộ", "cancel": "<PERSON><PERSON><PERSON>", "areYouSure": "Bạn có chắc chắn muốn tiếp tục?", "unmapSuccess": "Gỡ đồng bộ sản phẩm thành công", "unmapFail": "Gỡ đồng bộ sản phẩm thất bại"}, "groupButton": {"settingButton": "Cài đặt", "syncButton": "<PERSON><PERSON>ng bộ sản phẩm"}, "status": {"synced": "<PERSON><PERSON> đồng bộ", "mapped": "Đã map", "unmapped": "Chưa map", "error": "Lỗi"}, "actions": {"unmap": "Bỏ map", "map": "Map", "fix": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "headers": {"product": "<PERSON><PERSON><PERSON> phẩm {{product}}", "price": "Giá", "last_synced": "<PERSON><PERSON><PERSON> cu<PERSON>i đồng bộ", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON>"}, "nomap": "Chưa map đến {{destination}}"}, "productMapping": {"advancedMapping": {"title": "Mapping nâng cao", "description": "<PERSON><PERSON><PERSON> hình các quy tắc mapping nâng cao cho sản phẩm của bạn.", "sourceField": "<PERSON><PERSON><PERSON><PERSON><PERSON> ngu<PERSON>n", "transformationType": "<PERSON><PERSON><PERSON> chuyển đổi", "addTransformation": "<PERSON><PERSON><PERSON><PERSON> chuyển đổi", "removeTransformation": "<PERSON><PERSON><PERSON> chuyển đổi", "ruleConfiguration": "<PERSON><PERSON><PERSON> hình quy tắc", "outputPreview": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>u ra", "finalOutput": "<PERSON><PERSON><PERSON> ra cuối cùng", "applyTransformations": "<PERSON><PERSON> dụng chuyển đổi", "transformationChain": "Chuỗi chuyển đổi", "sampleData": "<PERSON><PERSON> liệu mẫu", "preview": "<PERSON><PERSON>", "output": "<PERSON><PERSON><PERSON> ra", "singleValue": "<PERSON><PERSON><PERSON> trị đơn", "transformationForm": "<PERSON><PERSON><PERSON><PERSON> đổi", "exampleUsage": "Ví dụ sử dụng", "selectFieldsPlaceholder": "<PERSON><PERSON><PERSON> một trường", "searchFieldsPlaceholder": "T<PERSON><PERSON> kiếm trường...", "source": "<PERSON><PERSON><PERSON><PERSON>", "searchTransformationTypes": "T<PERSON>m kiếm loại chuyển đổi...", "selectTransformationTypes": "<PERSON><PERSON><PERSON> loại chuyển đổi..."}, "lastSynced": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "errorLoading": "Lỗi tải chi tiết đồng bộ sản phẩm", "manualRetry": "<PERSON><PERSON><PERSON> lại thủ công", "cancelledMessage": "<PERSON><PERSON> hủy đồng bộ sản phẩm", "mappingStatus": "<PERSON>r<PERSON><PERSON> thái đồng bộ"}, "staff": {"title": "<PERSON><PERSON> s<PERSON>ch nhân viên", "filters": {"department": "Phòng ban", "role": "<PERSON>ai trò", "search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm nhân viên..."}}, "actionButton": {"create": "Tạo nhân viên"}, "columns": {"staff": "Nhân viên", "role": "<PERSON>ai trò", "skills": "<PERSON><PERSON> n<PERSON>ng", "task": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "actions": {"view": "Xem", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa"}, "maxCharactersReached": "<PERSON><PERSON> đạt số ký tự tối đa", "online": "<PERSON><PERSON><PERSON><PERSON>", "noStaff": "<PERSON><PERSON><PERSON><PERSON> có nhân viên nào.", "loading": "<PERSON><PERSON> tả<PERSON>...", "interact": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "createStaff": "Tạo nhân viên", "staffName": "<PERSON><PERSON><PERSON> nhân viên", "enterStaffName": "<PERSON><PERSON><PERSON><PERSON> tên nhân viên", "staffNameRequired": "<PERSON><PERSON> lòng nhập tên nhân viên", "maxCharacters": "Tối đa 250 ký tự", "selectDepartment": "<PERSON><PERSON><PERSON> phòng ban", "searchDepartments": "<PERSON><PERSON>m kiếm phòng ban...", "selectRole": "<PERSON><PERSON><PERSON> vai trò", "searchRoles": "T<PERSON>m kiếm vai trò...", "creating": "<PERSON><PERSON> t<PERSON>o", "update": "<PERSON><PERSON><PERSON>", "role": "<PERSON>ai trò", "knowledgeWarning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON>ui lòng xem xét thêm chi tiết để cải thiện hiệu suất.", "score": "<PERSON><PERSON><PERSON><PERSON>", "avatar": {"title": "Ảnh đại diện", "xbotAvatar": "Ảnh đại diện XBot", "image": "<PERSON><PERSON><PERSON>", "selectedAvatar": "Ảnh đại diện đã chọn", "avatar": "Ảnh đại diện"}, "knowledge": {"tab": "<PERSON><PERSON><PERSON> th<PERSON>", "baby": "Sơ khai", "warning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON><PERSON> lòng bổ sung thêm thông tin để cải thiện hiệu suất."}, "interactionStyle": {"tab": "<PERSON><PERSON> cách tư<PERSON> tác", "description": "<PERSON><PERSON><PERSON> hình cách nhân viên tương tác với người dùng", "communicationTone": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u giao ti<PERSON>", "languagePreferences": "<PERSON><PERSON><PERSON> ch<PERSON>n ngôn ngữ", "responseLength": "<PERSON><PERSON> dài phản hồi", "personalityTraits": "Đặc điểm t<PERSON>h c<PERSON>ch", "formal": "<PERSON><PERSON> trọng", "casual": "<PERSON><PERSON><PERSON> m<PERSON>t", "detailed": "<PERSON> ti<PERSON>", "concise": "<PERSON><PERSON><PERSON>", "creative": "<PERSON><PERSON><PERSON>", "analytical": "<PERSON><PERSON> tích", "ethicalConstraints": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> đ<PERSON><PERSON> đức", "contentFiltering": "<PERSON><PERSON><PERSON> l<PERSON> nội dung và kiểm tra an toàn"}, "skills": {"tab": "<PERSON><PERSON><PERSON> hình truy cập dữ liệu", "description": "<PERSON><PERSON><PERSON> hình các nguồn dữ liệu mà nhân viên có thể truy cập", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "orders": "<PERSON><PERSON><PERSON> hàng", "inventory": "<PERSON><PERSON>"}, "staffInfo": {"tab": "Thông tin nhân viên", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin cơ bản của nhân viên"}, "task": {"tab": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý công việc và nhiệm vụ của nhân viên", "noTasks": "<PERSON><PERSON><PERSON><PERSON> có công việc nào"}, "editStaff": {"title": "<PERSON><PERSON><PERSON> nhân viên", "staffName": "<PERSON><PERSON><PERSON> nhân viên", "rolePurpose": "<PERSON><PERSON> trò/<PERSON><PERSON>c tiêu", "department": "Phòng ban", "domainExpertise": "<PERSON><PERSON><PERSON><PERSON> môn", "customExpertisePlaceholder": "<PERSON><PERSON><PERSON><PERSON> chuyên môn tùy chỉnh và nhấn Enter", "noRoleFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vai trò", "noDepartmentFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phòng ban", "selectRole": "<PERSON><PERSON><PERSON> vai trò", "selectDepartment": "<PERSON><PERSON><PERSON> phòng ban", "searchRoles": "T<PERSON>m kiếm vai trò...", "searchDepartments": "<PERSON><PERSON>m kiếm phòng ban...", "namePhoneRequirement": "<PERSON><PERSON><PERSON>, số điện thoại yêu cầu", "roles": {"contentWriter": "<PERSON><PERSON><PERSON> văn nội dung", "seoSpecialist": "Chuyên gia SEO", "socialMediaManager": "<PERSON><PERSON><PERSON><PERSON> lý trang mạng xã hội"}}, "embed": {"instructions": {"title": "Hướng dẫn nhúng", "step1Title": "Bước 1: Thê<PERSON> mã script", "step1Description": "Sao chép thẻ script và dán vào HTML của trang web của bạn, tốt nhất là trong phần <head> hoặc ngay trước thẻ đóng </body>.", "step2Title": "Bước 2: Thêm container widget", "step2Description": "<PERSON>o ch<PERSON>p phần tử div và dán vào nơi bạn muốn widget nhân viên ảo xuất hiện trên trang của bạn. Widget sẽ tự động khởi tạo ở vị trí này.", "step3Title": "Bước 3: <PERSON><PERSON><PERSON> chỉnh widget (<PERSON><PERSON><PERSON>)", "step3Description": "Bạn có thể tùy chỉnh giao diện của widget bằng cách thêm CSS vào trang web của bạn. Container widget có ID là xbot-container.", "step4Title": "Bước 4: <PERSON><PERSON><PERSON> tra tích hợp", "step4Description": "<PERSON><PERSON> <PERSON>hi thêm mã, làm mới trang của bạn và xác minh rằng widget nhân viên ảo xuất hiện chính xác. Widget nên hiển thị thông tin của nhân viên và cho phép người truy cập tương tác với họ.", "troubleshootingTitle": "<PERSON>ử lý sự cố", "troubleshooting1": "<PERSON><PERSON><PERSON> b<PERSON>o URL script có thể truy cập đ<PERSON><PERSON><PERSON> từ trang web của bạn.", "troubleshooting2": "<PERSON><PERSON><PERSON> tra bảng điều khiển của trình duyệt để xem thông báo lỗi.", "troubleshooting3": "<PERSON><PERSON><PERSON> minh rằng ID và tên nhân viên là chính xác.", "troubleshooting4": "<PERSON><PERSON><PERSON> bảo trang web của bạn cho phép tải các script bên ngoài."}, "script": {"title": "<PERSON><PERSON> n<PERSON>g", "copy": "Sao chép", "copied": "Đã sao chép!", "containerInstructions": "1. Thêm container n<PERSON><PERSON> vào n<PERSON>i bạn muốn widget xuất hiện", "scriptInstructions": "2. <PERSON><PERSON><PERSON><PERSON> đoạn mã này vào HTML của bạn"}}}, "department": {"title": "Phòng ban", "createDepartment": "<PERSON><PERSON><PERSON> phòng ban", "createStaff": "Tạo nhân viên", "departmentName": "<PERSON>ên phòng ban", "enterDepartmentName": "<PERSON><PERSON><PERSON><PERSON> tên phòng ban", "description": "<PERSON><PERSON>", "enterDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả...", "departmentNameRequired": "<PERSON><PERSON> lòng nhập tên phòng ban", "viewStaff": "<PERSON><PERSON> viên", "staffCount": "{{count}} nh<PERSON> viên", "interact": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "upload": "<PERSON><PERSON><PERSON>", "deleteKnowledge": "<PERSON><PERSON><PERSON>c", "deleteKnowledgeDescription": "Bạn có chắc chắn muốn xóa kiến thức này? Hành động này không thể hoàn tác.", "deleteKnowledgeSuccess": "<PERSON><PERSON><PERSON> thức đã được xóa thành công", "deleteKnowledgeError": "<PERSON><PERSON><PERSON><PERSON> thể xóa kiến thức", "deleteKnowledgeConfirm": "Xóa", "deleteKnowledgeCancel": "<PERSON><PERSON><PERSON>", "knowledgeWarning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON>ui lòng xem xét thêm chi tiết để cải thiện hiệu suất.", "knowledge": {"tab": "<PERSON><PERSON><PERSON> th<PERSON>", "baby": "Sơ khai", "warning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON><PERSON> lòng bổ sung thêm thông tin để cải thiện hiệu suất.", "status": {"error": "Lỗi", "pending": "<PERSON><PERSON> lý", "success": "<PERSON><PERSON><PERSON><PERSON> công"}}}, "knowledge": {"title": "<PERSON><PERSON><PERSON> th<PERSON>", "headers": {"file": "File", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "size": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i"}, "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> k<PERSON> file..."}, "fileType": "Loại file", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "status": {"pending": "Chờ xử lý", "processing": "<PERSON><PERSON> lý", "ready": "Sẵn sàng", "error": "Lỗi"}, "actions": {"upload": "<PERSON><PERSON><PERSON> lên file mới"}, "upload": {"dragAndDrop": "Kéo thả file vào đây hoặc", "uploadButton": "<PERSON><PERSON><PERSON>", "supportedFiles": "File PDF, DOCX, TXT, hoặc CSV", "totalSize": "Tổng dung lượng", "noFileSelected": "<PERSON><PERSON><PERSON> ch<PERSON> file", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "fileTooLarge": "File vượt quá dung lượng tối đa {{max}}MB", "file": "<PERSON><PERSON><PERSON> l<PERSON>", "url": "<PERSON><PERSON><PERSON><PERSON> trang web", "text": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "title": "<PERSON><PERSON><PERSON> lên ki<PERSON>n thức", "invalidUrl": "URL không hợp lệ", "urlAlreadyAdded": "URL đã tồn tại", "noUrlsToUpload": "<PERSON><PERSON> lòng nhập ít nhất một URL", "uploadError": "Lỗi khi tải lên", "uploaded": "<PERSON><PERSON><PERSON> thức đã tải lên", "knowledgeNameRequired": "<PERSON><PERSON><PERSON> ki<PERSON><PERSON> thức là bắt buộc", "knowledgeNameTooLong": "<PERSON>ên kiến thức ph<PERSON>i có ít hơn 250 ký tự", "textRequired": "<PERSON><PERSON><PERSON> bản là b<PERSON>t buộc", "textTooLong": "<PERSON><PERSON><PERSON> bản ph<PERSON>i có ít hơn 20000 ký tự", "search": "<PERSON><PERSON><PERSON> kiếm kiến thức", "textTitle": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> thức", "fileTitle": "<PERSON><PERSON><PERSON> thức từ tệp", "urlTitle": "<PERSON><PERSON><PERSON> thức từ URL", "allTitle": "<PERSON><PERSON><PERSON> cả kiến thức", "deleteTitle": "<PERSON><PERSON><PERSON> k<PERSON> thức", "deleteDescription": "Bạn có chắc chắn muốn xóa kiến thức này? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON> xóa kiến thức thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa kiến thức", "deleteConfirm": "Xóa", "deleteCancel": "<PERSON><PERSON><PERSON>", "leaveTitle": "Rời đi mà không lưu?", "leaveDescription": "<PERSON><PERSON><PERSON> thay đổi chưa lưu sẽ bị mất.", "leaveConfirm": "<PERSON><PERSON><PERSON> đi", "leaveCancel": "Ở lại", "textInput": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp", "textInputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> văn bản kiến thức của bạn ở đây...", "textInputTitle": "<PERSON><PERSON><PERSON> thức v<PERSON><PERSON> bản", "textTitlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiêu đề kiến thức", "pleaseSelectAtLeastOneFile": "<PERSON>ui lòng chọn ít nhất một file", "pleaseEnterAtLeastOneURL": "<PERSON><PERSON> lòng nhập ít nhất một URL", "pleaseEnterAtLeastOneTextFile": "<PERSON><PERSON> lòng nhập ít nhất một tệp văn bản", "pleaseEnterAllFields": "<PERSON><PERSON> lòng nhập đầy đủ tiêu đề và nội dung", "newest": "<PERSON><PERSON><PERSON>", "oldest": "<PERSON><PERSON> n<PERSON>", "noKnowledge": "<PERSON><PERSON><PERSON><PERSON> có kiến thức nào", "urlImport": "<PERSON><PERSON><PERSON><PERSON> từ URL", "urlImportDescription": "<PERSON><PERSON><PERSON><PERSON> kiến thức từ trang web", "deleteKnowledge": "<PERSON><PERSON><PERSON>c", "deleteKnowledgeDescription": "Bạn có chắc chắn muốn xóa kiến thức này? Hành động này không thể hoàn tác.", "deleteKnowledgeSuccess": "<PERSON><PERSON><PERSON> thức đã được xóa thành công", "deleteKnowledgeError": "<PERSON><PERSON><PERSON><PERSON> thể xóa kiến thức", "deleteKnowledgeConfirm": "Xóa", "deleteKnowledgeCancel": "<PERSON><PERSON><PERSON>", "fileImport": "<PERSON><PERSON><PERSON><PERSON> từ file", "fileImportDescription": "<PERSON><PERSON><PERSON><PERSON> kiến thức từ file", "fileImportSuccess": "<PERSON><PERSON><PERSON> thức đã đư<PERSON>c tải lên thành công", "fileImportError": "<PERSON><PERSON><PERSON><PERSON> thể tải lên kiến thức", "fileImportConfirm": "<PERSON><PERSON><PERSON>", "fileImportCancel": "<PERSON><PERSON><PERSON>", "textImport": "<PERSON><PERSON><PERSON><PERSON> từ văn bản", "textImportDescription": "<PERSON><PERSON><PERSON><PERSON> kiến thức từ văn bản", "textImportSuccess": "<PERSON><PERSON><PERSON> thức đã đư<PERSON>c tải lên thành công", "textImportError": "<PERSON><PERSON><PERSON><PERSON> thể tải lên kiến thức", "textImportConfirm": "<PERSON><PERSON><PERSON>", "textImportCancel": "<PERSON><PERSON><PERSON>", "urlImportSuccess": "<PERSON><PERSON><PERSON> thức đã đư<PERSON>c tải lên thành công", "urlImportError": "<PERSON><PERSON><PERSON><PERSON> thể tải lên kiến thức", "urlImportConfirm": "<PERSON><PERSON><PERSON>", "urlImportCancel": "<PERSON><PERSON><PERSON>"}}, "customer": {"details": {"customerDetails": "Thông tin khách hàng", "name": "<PERSON><PERSON><PERSON>", "birthday": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "email": "Email", "shippingAddress": "Đ<PERSON>a chỉ giao hàng", "billingAddress": "<PERSON><PERSON><PERSON> chỉ thanh toán", "groupName": "<PERSON><PERSON><PERSON>", "totalLoyalPoints": "<PERSON><PERSON><PERSON> điểm tích l<PERSON>y", "totalRedeemPoints": "Tổng điểm đổi thưởng", "tags": "Thẻ", "noTags": "---"}, "purchase": {"purchaseInfo": "Thông tin mua hàng", "totalSpent": "T<PERSON>ng chi tiêu", "totalProductsPurchased": "<PERSON>ổng sản phẩm đã mua", "purchasedOrder": "Đơn hàng đã mua", "totalProductsReturned": "<PERSON><PERSON>ng sản phẩm đã trả", "lastOrderAt": "Đơn hàng cuối cùng vào"}, "sales": {"suggestionInfo": "<PERSON><PERSON><PERSON><PERSON> tin đề xuất bán hàng", "defaultPriceGroup": "Nhóm giá mặc định", "defaultPaymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán mặc định", "discountPercent": "Phần tr<PERSON>m giảm giá"}, "order": {"orderHistory": "<PERSON><PERSON><PERSON> sử đơn hàng"}}}, "table": {"pagination": {"rowsPerPage": "<PERSON><PERSON> hàng trên trang", "description": "{{start}} đến {{end}} hàng trong tổng số {{total}}", "next": "<PERSON><PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": {"title": "<PERSON><PERSON> ch<PERSON>n", "delete": "Xóa {{count}} đ<PERSON> chọn"}, "export": {"title": "<PERSON><PERSON><PERSON> dữ liệu", "description": "<PERSON><PERSON><PERSON> dữ liệu", "confirm": "<PERSON><PERSON><PERSON> dữ liệu", "cancel": "<PERSON><PERSON><PERSON>"}, "filter": {"clearFilter": "Xóa bộ lọc", "loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "savedFilters": {"settings": {"settings": "Cài đặt", "title": "Cài đặt tab", "description": "6 tab đầu tiên sẽ được ưu tiên hiển thị trên trang chính. Tùy chỉnh chúng dựa trên nhu cầu của bạn."}}}, "validation": {"required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "invalidEmail": "<PERSON><PERSON> lòng nhập email hợp lệ", "minLength": "<PERSON><PERSON><PERSON> có <PERSON>t nhất {{count}} ký tự", "maxLength": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {{count}} ký tự", "passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "invalidUsername": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập ít nhất có 3 ký tự", "emailRequired": "<PERSON><PERSON> lòng nhập email", "usernameRequired": "<PERSON><PERSON> lòng nhập tên đăng nhập", "passwordRequired": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "confirmPasswordRequired": "<PERSON><PERSON> lòng xác nhận mật kh<PERSON>u", "invalidPassword": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "verificationCodeRequired": "<PERSON><PERSON> lòng nhập mã xác thực", "verificationCodeLength": "<PERSON>ã xác thực ph<PERSON>i có 6 ký tự", "sessionRequired": "Session l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "usernameSpecialCharacters": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập chỉ có thể chứa chữ cái, số và dấu gạch dưới", "skuFormat": "Mã SKU chỉ có thể chứa chữ cái, số và dấu gạch dưới", "skuRequired": "Mã SKU là bắt buộc", "nameRequired": "<PERSON><PERSON><PERSON> sản phẩm là bắt buộc", "nameTooLong": "<PERSON><PERSON><PERSON> sản phẩm phải có ít hơn 250 ký tự", "priceRequired": "<PERSON><PERSON><PERSON> là b<PERSON> bu<PERSON>c", "imageRequired": "<PERSON><PERSON> nh<PERSON>t một ảnh là bắt buộc", "imageFormat": "<PERSON><PERSON><PERSON> dạng <PERSON>nh không hợp lệ", "priceMustBePositive": "<PERSON><PERSON> lòng nh<PERSON>p gi<PERSON> hợp lệ", "invalidPrice": "<PERSON><PERSON><PERSON> k<PERSON>ng h<PERSON>p lệ", "wrongUsernameOrPassword": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập hoặc mật khẩu không chính xác", "phoneNumberAlreadyExists": "<PERSON><PERSON> điện thoại đã tồn tại"}, "footer": {"crafted": "<PERSON><PERSON><PERSON><PERSON> tạo với", "by": "bởi", "team": "đội ngũ OneXAPIs", "heart": "tr<PERSON>i tim"}, "install": {"installing": "<PERSON><PERSON> cài đặt...", "pleaseWait": "<PERSON>ui lòng đợi trong gi<PERSON>y lát", "error": {"backToHome": "Trở về trang chủ", "notFound": "<PERSON><PERSON> vẻ như bạn đã đi vào vùng cực lạc không xác định", "installationFailed": "Cài đặt thất bại", "missingSourceChannel": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kênh nguồn", "authorizeDestination": "<PERSON><PERSON> lòng xác thực kênh đích"}}, "error": {"backToHome": "Trở về trang chủ", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang", "notFoundDescription": "<PERSON>rang bạn đang tìm kiếm không tồn tại."}}
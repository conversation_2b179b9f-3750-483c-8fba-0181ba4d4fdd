"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Loader2, Plus, X } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface ComboboxProps {
  value: string;
  onValueChange: (value: string) => void;
  items: {
    id: string;
    name: string;
    disabled?: boolean;
    displayValue?: string;
    number?: number;
  }[];
  placeholder: string;
  searchPlaceholder?: string;
  emptyText?: string;
  onCreateNew?: (value: string) => void;
  onLoadMore?: () => void;
  onClear?: () => void;
  isLoading?: boolean;
  hasNextPage?: boolean;
  isLoadingMore?: boolean;
  isShowChevronsUpDown?: boolean;
  leftIcon?: React.ComponentType<{ className?: string }>;
  leftIconNumber?: number;
  middleIcon?: React.ComponentType<{ className?: string }>;
  rightIcon?: React.ComponentType<{ className?: string }>;
  rightIconNumber?: number;
  onRightIconClick?: () => void;
  variantButton?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "icon";
  popoverWidth?: string;
  popoverAlign?: "center" | "start" | "end";
  buttonSize?: "default" | "sm" | "lg" | "icon";
  footerItem?: {
    id: string;
    name: string;
    icon?: React.ComponentType<{ className?: string }>;
    onClick: () => void;
    number?: number;
  };
}

export function Combobox({
  value,
  onValueChange,
  items,
  placeholder,
  searchPlaceholder,
  emptyText,
  onCreateNew,
  onLoadMore,
  onClear,
  isLoading,
  hasNextPage,
  isLoadingMore,
  isShowChevronsUpDown = true,
  leftIcon,
  leftIconNumber,
  middleIcon,
  rightIcon,
  rightIconNumber,
  onRightIconClick,
  variantButton = "outline",
  popoverWidth,
  popoverAlign = "start",
  buttonSize = "default",
  footerItem,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");
  const { t } = useTranslation();

  const selectedItem = items.find((item) => item.id === value);

  // Move filtering logic into useMemo to optimize performance
  const filteredItems = React.useMemo(() => {
    const query = searchQuery.toLowerCase().trim();
    return items.filter(
      (item) =>
        item.name.toLowerCase().includes(query) ||
        (item.displayValue && item.displayValue.toLowerCase().includes(query))
    );
  }, [items, searchQuery]);

  const handleSelect = (itemId: string) => {
    onValueChange(itemId);
    setOpen(false);
    setSearchQuery("");
  };

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  // Reset search when popover closes
  React.useEffect(() => {
    if (!open) {
      setSearchQuery("");
    }
  }, [open]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const bottom =
      e.currentTarget.scrollHeight - e.currentTarget.scrollTop === e.currentTarget.clientHeight;
    if (bottom && hasNextPage && !isLoadingMore && onLoadMore) {
      onLoadMore();
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={variantButton}
          role="combobox"
          aria-expanded={open}
          className={cn("w-full", {
            "h-8 px-3 text-xs": buttonSize === "sm",
            "h-9 px-4": buttonSize === "default",
            "h-10 px-8": buttonSize === "lg",
            "h-7 w-7 p-0": buttonSize === "icon",
          })}>
          {middleIcon ? (
            <div className="flex w-full items-center justify-center">
              {React.createElement(middleIcon, {
                className: "size-4 shrink-0",
              })}
            </div>
          ) : (
            <div className="flex w-full items-center justify-between">
              <div className="flex min-w-0 flex-1 items-center gap-2">
                {leftIcon && (
                  <div className="relative">
                    {React.createElement(leftIcon, {
                      className: "size-4 shrink-0",
                    })}
                    {leftIconNumber !== undefined && leftIconNumber > 0 && (
                      <div className="absolute -right-1 -top-1 flex size-3 items-center justify-center rounded-sm bg-primary text-[10px] font-medium text-primary-foreground">
                        {leftIconNumber}
                      </div>
                    )}
                  </div>
                )}
                {selectedItem ? (
                  <span className="truncate text-sm font-normal">
                    {selectedItem.displayValue || selectedItem.name}
                  </span>
                ) : (
                  <span className="truncate text-sm font-normal">{placeholder}</span>
                )}
              </div>

              <div className="ml-2 flex shrink-0 items-center gap-1">
                {selectedItem && rightIcon && (
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      onRightIconClick?.();
                    }}
                    className="cursor-pointer">
                    <div className="relative">
                      {React.createElement(rightIcon, {
                        className:
                          "size-5 shrink-0 opacity-70 hover:opacity-100 border border-border rounded-md p-0.5",
                      })}
                      {rightIconNumber !== undefined && rightIconNumber > 0 && (
                        <div className="absolute -right-1 -top-1 flex size-3 items-center justify-center rounded-sm bg-primary text-[10px] font-medium text-primary-foreground">
                          {rightIconNumber}
                        </div>
                      )}
                    </div>
                  </div>
                )}
                {selectedItem && onClear && (
                  <X
                    className="size-4 shrink-0 cursor-pointer opacity-50 hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      onClear();
                      setSearchQuery("");
                    }}
                  />
                )}

                {isShowChevronsUpDown && (
                  <ChevronsUpDown className="ml-1 size-4 shrink-0 opacity-50" />
                )}
              </div>
            </div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={popoverWidth ? "p-0" : "w-[--radix-popover-trigger-width] p-0"}
        style={popoverWidth ? { width: popoverWidth } : undefined}
        align={popoverAlign}
        sideOffset={1}>
        <Command shouldFilter={false} className="overflow-hidden">
          <CommandInput
            placeholder={searchPlaceholder || `Search ${placeholder.toLowerCase()}...`}
            value={searchQuery}
            onValueChange={handleSearch}
          />
          <CommandList onScroll={handleScroll}>
            <CommandGroup>
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <div
                    key={item.id}
                    onClick={() => !item.disabled && handleSelect(item.id)}
                    className={cn(
                      "cursor-pointer hover:bg-accent rounded-lg",
                      item.disabled && "cursor-not-allowed opacity-50"
                    )}>
                    <CommandItem
                      className={`text-foreground ${value === item.id ? "bg-accent text-accent-foreground" : ""}`}
                      disabled={item.disabled}>
                      <div className="flex w-full items-center">
                        {item.number !== undefined && (
                          <span className="mr-2 flex size-5 items-center justify-center rounded-full bg-muted text-xs">
                            {item.number}
                          </span>
                        )}
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4 text-foreground",
                            value === item.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <span className="flex-1 truncate text-foreground">
                          {item.displayValue || item.name}
                        </span>
                      </div>
                    </CommandItem>
                  </div>
                ))
              ) : (
                <CommandEmpty>
                  {onCreateNew && searchQuery && (
                    <Button
                      variant="ghost"
                      className="w-full justify-start"
                      onClick={() => {
                        onCreateNew(searchQuery);
                        setOpen(false);
                        setSearchQuery("");
                      }}>
                      <Plus className="size-4" />
                      {searchQuery}
                    </Button>
                  )}
                  <div className="py-2">{emptyText || "No results found."}</div>
                </CommandEmpty>
              )}
            </CommandGroup>
            {isLoading && (
              <Button variant="ghost" className="w-full" disabled>
                <Loader2 className="mr-2 size-4 animate-spin" />
                {t("common.loading")}
              </Button>
            )}
            {isLoadingMore && (
              <Button variant="ghost" className="w-full" disabled>
                <Loader2 className="mr-2 size-4 animate-spin" />
                {t("common.loadingMore")}
              </Button>
            )}
          </CommandList>

          {/* Footer Item - Always visible */}
          {footerItem && (
            <div className="border-t">
              <CommandGroup>
                <div
                  onClick={() => {
                    footerItem.onClick();
                    setOpen(false);
                  }}
                  className="cursor-pointer rounded-lg hover:bg-accent">
                  <CommandItem className="flex items-center text-sm text-muted-foreground">
                    <div className="flex w-full items-center justify-between">
                      {footerItem.icon && (
                        <div className="relative">
                          {React.createElement(footerItem.icon, { className: "size-4" })}
                        </div>
                      )}
                      <span className="flex-1 truncate text-center">{footerItem.name}</span>
                      {footerItem.number !== undefined && footerItem.number > 0 && (
                        <Badge variant="primary" className="ml-2 px-2">
                          <span className="px-1">{footerItem.number}</span>
                        </Badge>
                      )}
                    </div>
                  </CommandItem>
                </div>
              </CommandGroup>
            </div>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
}

import { z } from "zod";

export const basicInformationSchema = z.object({
  name: z.string().min(1, "Product name is required").max(250, "Name cannot exceed 250 characters"),
  description: z.string().max(1000, "Description cannot exceed 1000 characters").optional(),
  shortDescription: z
    .string()
    .max(400, "Short description cannot exceed 400 characters")
    .optional(),
  sku: z
    .string()
    .min(1, "SKU is required")
    .regex(
      /^[a-zA-Z0-9_-]+$/,
      "The SKU can contain only letters, numbers, dashes, and underscores"
    ),
  brand: z
    .object({
      id: z.string(),
      name: z.string(),
    })
    .nullable(),
  category: z
    .object({
      id: z.string(),
      name: z.string(),
    })
    .nullable(),
  tags: z.array(z.string()).nullable(),
  images: z
    .array(
      z.object({
        name: z.string(),
        image: z.string(),
      })
    )
    .min(1, "At least one image is required"),
});

export type BasicInformationSchema = z.infer<typeof basicInformationSchema>;

// Helper function to validate basic information
export const validateBasicInformation = (values: unknown) => {
  try {
    basicInformationSchema.parse(values);
    return { success: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      };
    }
    return {
      success: false,
      errors: [{ field: "unknown", message: "Validation failed" }],
    };
  }
};

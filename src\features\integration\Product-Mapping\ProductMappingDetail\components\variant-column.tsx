import { Label } from "@/components/ui/label";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { ProductVariant } from "../../hooks/types";

interface VariantColumnProps {
  variants: ProductVariant[];
  className?: string;
}

export const VariantColumn = ({ variants, className = "" }: VariantColumnProps) => {
  if (variants.length === 0) {
    return (
      <div className="flex flex-1 items-center justify-center py-4 text-sm text-muted-foreground">
        No variants available
      </div>
    );
  }

  return (
    <div className={`py-4 ${className}`}>
      <div className="space-y-4">
        {variants.map((variant) => (
          <div key={variant.id} className="flex items-center justify-between border-b pb-4 pl-4">
            <div className="flex items-center gap-3">
              <div className="size-10 flex-none items-center justify-center rounded-md bg-gray-200">
                {/* Placeholder for variant image */}
              </div>
              <div className="min-w-0 flex-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Label className="block max-w-[100px] truncate font-medium md:max-w-[200px] lg:max-w-[300px]">
                        {variant.name || variant.title || variant.sku || "--"}
                      </Label>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{variant.name || variant.title || variant.sku || "--"}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <p className="text-sm text-muted-foreground">Price: ${variant.price || "--"}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

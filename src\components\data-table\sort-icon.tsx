import { MoveDown, MoveUp } from "lucide-react";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";

interface SortIconProps {
  directionSort?: SortDirection;
}

export default function SortIcon({ directionSort }: SortIconProps) {
  if (!directionSort) return null;
  return (
    <div className="flex h-auto flex-col">
      {directionSort === SortDirection.ASC && <MoveUp size={16} />}
      {directionSort === SortDirection.DESC && <MoveDown size={16} />}
    </div>
  );
}

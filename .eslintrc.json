{
  "$schema": "https://json.schemastore.org/eslintrc",
  "root": true,
  "extends": [
    "next/core-web-vitals",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
    "plugin:tailwindcss/recommended"
  ],
  "plugins": ["@typescript-eslint/eslint-plugin"],
  "rules": {
    "prettier/prettier": "warn",
    "react/no-unescaped-entities": "off",
    "@next/next/no-page-custom-font": "off",
    // "@typescript-eslint/no-unused-vars": [
    //   "warn",
    //   { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }
    // ],
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": "off"
  },
  "overrides": [
    {
      "files": ["*.ts", "*.tsx"],
      "parser": "@typescript-eslint/parser"
    }
  ],
  "ignorePatterns": [
    "dist/*",
    ".cache",
    "public",
    "node_modules",
    "*.esm.js",
    ".next",
    ".jest",
    "__mocks__"
  ]
}

"use client";

import { useState } from "react";

import { DashboardFilter } from "@/features/dashboard/components/dashboard-filter";
import { PatientStatistics } from "@/features/dashboard/components/patient-statistics";
import { PatientStatisticsSkeleton } from "@/features/dashboard/components/skeletons/patient-statistics-skeleton";
import { StatsOverviewSkeleton } from "@/features/dashboard/components/skeletons/stats-overview-skeleton";
import { TopTwentySkeleton } from "@/features/dashboard/components/skeletons/top-twenty-skeleton";
import { TreatmentCostsSkeleton } from "@/features/dashboard/components/skeletons/treatment-costs-skeleton";
import { TreatmentOutcomesSkeleton } from "@/features/dashboard/components/skeletons/treatment-outcomes-skeleton";
import { StatsOverview } from "@/features/dashboard/components/stats/stats-overview";
import { TopTwenty } from "@/features/dashboard/components/top-twenty";
import { TreatmentCosts } from "@/features/dashboard/components/treatment-costs";
import { TreatmentOutcomes } from "@/features/dashboard/components/treatment-outcomes";

import { ScrollArea } from "@/components/ui/scroll-area";

interface StatsData {
  totalFacilities: {
    value: number;
    change: number;
  };
  totalPatients: {
    value: number;
    change: number;
  };
  averageOccupancy: {
    value: number;
    change: number;
  };
  totalRevenue: {
    value: number;
    change: number;
  };
}

// Mock data
const statsData: StatsData = {
  totalFacilities: {
    value: 150,
    change: 0.0,
  },
  totalPatients: {
    value: 25000,
    change: 0.0,
  },
  averageOccupancy: {
    value: 75,
    change: 0.0,
  },
  totalRevenue: {
    value: 15000000000,
    change: 0.0,
  },
};

const patientData = {
  outpatient: [
    { name: "Jan", value: 1500 },
    { name: "Feb", value: 2000 },
    { name: "Mar", value: 1800 },
    { name: "Apr", value: 2800 },
    { name: "May", value: 2600 },
    { name: "Jun", value: 3800 },
    { name: "Jul", value: 3400 },
    { name: "Aug", value: 2200 },
    { name: "Sep", value: 3200 },
    { name: "Oct", value: 2800 },
    { name: "Nov", value: 3600 },
    { name: "Dec", value: 3400 },
  ],
  inpatient: [
    { name: "Feb", admitted: 4300, discharged: 3200, transferred: 1400 },
    { name: "Jun", admitted: 6200, discharged: 3300, transferred: 2600 },
    { name: "Oct", admitted: 1400, discharged: 1400, transferred: 4300 },
  ],
};

const treatmentCostsData = {
  costs: [
    { name: "Feb", insurance: 4300, service: 3200, specialCare: 1400 },
    { name: "Jun", insurance: 6000, service: 3200, specialCare: 2600 },
    { name: "Oct", insurance: 1400, service: 1400, specialCare: 4300 },
  ],
  insurance: [
    { name: "Mar", value: 2800 },
    { name: "Jun", value: 3800 },
    { name: "Sep", value: 1800 },
    { name: "Dec", value: 3000 },
  ],
};

// Treatment Outcomes Data
interface OutcomeData {
  name: string;
  value: number;
  color: string;
}

const treatmentOutcomesData: OutcomeData[] = [
  { name: "Recovered", value: 25, color: "hsl(214, 84%, 56%)" },
  { name: "Improved", value: 25, color: "hsl(142, 71%, 45%)" },
  { name: "Unchanged", value: 13, color: "hsl(48, 96%, 53%)" },
  { name: "Deteriorated", value: 13, color: "hsl(25, 95%, 53%)" },
  { name: "Deceased", value: 13, color: "hsl(262, 83%, 58%)" },
  { name: "Left", value: 12, color: "hsl(84, 81%, 44%)" },
];

const topTwentyData = {
  diagnoses: [
    { code: "J00", name: "Common cold", value: 600 },
    { code: "I10", name: "Hypertension", value: 300 },
    { code: "E11", name: "Type 2 diabetes", value: 150 },
    { code: "K21", name: "GERD", value: 120 },
    { code: "M54", name: "Back pain", value: 100 },
  ],
  medications: [
    { code: "P01", name: "Paracetamol", value: 550 },
    { code: "A01", name: "Amoxicillin", value: 320 },
    { code: "O01", name: "Omeprazole", value: 180 },
    { code: "I01", name: "Ibuprofen", value: 150 },
    { code: "M01", name: "Metformin", value: 120 },
  ],
};

export default function OverviewPage() {
  const [loading, setLoading] = useState(false);
  const [period, setPeriod] = useState("daily");
  const [location, setLocation] = useState("all");

  const handleRefresh = () => {
    setLoading(true);
    // Refresh data logic here
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="flex flex-1 flex-col space-y-6 overflow-auto p-6">
      <DashboardFilter
        period={period}
        onPeriodChange={setPeriod}
        location={location}
        onLocationChange={setLocation}
        onRefresh={handleRefresh}
        isLoading={loading}
      />

      {loading ? <StatsOverviewSkeleton /> : <StatsOverview data={statsData} />}

      <ScrollArea>
        {loading ? <PatientStatisticsSkeleton /> : <PatientStatistics data={patientData} />}
      </ScrollArea>

      <ScrollArea>
        {loading ? <TopTwentySkeleton /> : <TopTwenty data={topTwentyData} />}
      </ScrollArea>

      <ScrollArea>
        {loading ? <TreatmentCostsSkeleton /> : <TreatmentCosts data={treatmentCostsData} />}
      </ScrollArea>
      <ScrollArea>
        {loading ? (
          <TreatmentOutcomesSkeleton />
        ) : (
          <TreatmentOutcomes data={treatmentOutcomesData} />
        )}
      </ScrollArea>
    </div>
  );
}

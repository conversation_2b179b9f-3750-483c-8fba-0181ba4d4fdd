import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Product } from "@/features/products/hooks/types";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";

import { TransformationForm } from "./components/transformation-form";
import { TransformationPreview } from "./components/transformation-preview";
import { useHandleTransformation } from "./hooks/transformation";
import { TransformationPayload } from "./hooks/types";
import { useAdvancedMapping } from "./hooks/use-advanced-mapping";

interface AdvancedMappingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product: Product | Record<string, any>;
  syncRecordId: string;
  connectionId?: string;
  sourceData?: Record<string, any>;
  sourceFields: string[];
  selectedField?: {
    sourceField?: string;
    destinationField: string;
    transformations?: Array<{
      type: string;
      config: Record<string, any>;
    }>;
  };
  onSave?: (mappingData: {
    sourceField: string;
    value: string;
    hasAdvancedMapping: boolean;
    transformations: Array<{
      type: string;
      config: Record<string, any>;
    }>;
    transformationCount: number;
  }) => void;
}

export function AdvancedMappingDialog({
  open,
  onOpenChange,
  product,
  syncRecordId = "",
  connectionId = "",
  sourceData = {},
  sourceFields = [],
  selectedField,
  onSave,
}: AdvancedMappingDialogProps) {
  const {
    sourceField,
    setSourceField,
    transformations,
    activeTab,
    setActiveTab,
    handleAddTransformation,
    handleTransformationTypeChange: baseHandleTransformationTypeChange,
    handleRemoveTransformation: baseHandleRemoveTransformation,
    handleConfigChange: baseHandleConfigChange,
  } = useAdvancedMapping({
    initialSourceField: selectedField?.sourceField || "",
    initialTransformations: selectedField?.transformations || [],
  });

  const [transformedValue, setTransformedValue] = useState("");
  const [isTransforming, setIsTransforming] = useState(false);
  const [transformationPayload, setTransformationPayload] = useState<TransformationPayload | null>(
    null
  );
  const [shouldFetchTransform, setShouldFetchTransform] = useState(false);
  const [configChangeNeeded, setConfigChangeNeeded] = useState(false);
  const [sourceFieldChangeNeeded, setSourceFieldChangeNeeded] = useState(false);

  const { t } = useTranslation();

  // For immediate execution (selects/dropdowns)
  const immediateSetShouldFetchTransform = useCallback(() => {
    setShouldFetchTransform(true);
  }, []);

  // Reset state when dialog closes
  useEffect(() => {
    if (!open) {
      setTransformedValue("");
      setIsTransforming(false);
      setTransformationPayload(null);
      setShouldFetchTransform(false);
    }
  }, [open]);

  // Initialize with selected field data when dialog opens
  useEffect(() => {
    if (open && selectedField) {
      setSourceField(selectedField.sourceField || "");
    }
  }, [open, selectedField, setSourceField]);

  const handleSave = () => {
    if (onSave) {
      // Filter out transformations with type and format them
      const validTransformations = transformations
        .filter((t): t is typeof t & { type: string } => Boolean(t.type))
        .map((t) => ({
          type: t.type,
          config: t.config || {},
        }));

      // Count valid transformations with type
      const transformationCount = validTransformations.length;

      onSave({
        sourceField,
        value: transformedValue,
        hasAdvancedMapping: true,
        transformations: validTransformations,
        transformationCount,
      });
    }
    onOpenChange(false);
  };

  // Get source value based on the selected field
  // From the API response screenshot, standard_source_data has direct properties
  const sourceValue =
    sourceData?.[sourceField] || product?.[sourceField as keyof typeof product] || "";

  // Enhanced handlers that trigger transformation API calls
  const handleTransformationTypeChange = useCallback(
    (id: string, type: string) => {
      baseHandleTransformationTypeChange(id, type);
      if (type) {
        // For select inputs, immediate execution is better
        immediateSetShouldFetchTransform();
      }
    },
    [baseHandleTransformationTypeChange, immediateSetShouldFetchTransform]
  );

  const handleRemoveTransformation = useCallback(
    (id: string) => {
      baseHandleRemoveTransformation(id);
      immediateSetShouldFetchTransform();
    },
    [baseHandleRemoveTransformation, immediateSetShouldFetchTransform]
  );

  const handleConfigChange = useCallback(
    (id: string, config: any) => {
      baseHandleConfigChange(id, config);
      // For text inputs, use debounce to prevent excessive API calls
      setConfigChangeNeeded(true);
    },
    [baseHandleConfigChange]
  );

  const handleSourceFieldChange = useCallback(
    (value: string) => {
      setSourceField(value);
      if (value && transformations.some((t) => t.type)) {
        // Use debounce for text field
        setSourceFieldChangeNeeded(true);
      }
    },
    [setSourceField, transformations]
  );

  // Create the transformation payload when needed (not on every change)
  useEffect(() => {
    // Only build payload when requested
    if (!shouldFetchTransform) return;

    // Skip if we don't have the required fields
    if (!sourceField || !syncRecordId || !connectionId || transformations.length === 0) {
      setTransformationPayload(null);
      setShouldFetchTransform(false);
      return;
    }

    // Filter out transformations without a type
    const validTransformations = transformations.filter((t) => t.type);

    if (validTransformations.length === 0) {
      setTransformationPayload(null);
      setShouldFetchTransform(false);
      return;
    }

    // Format transformations for the API
    const apiTransformations = validTransformations.map((t) => ({
      type: t.type || "",
      config: t.config || {},
    }));

    setTransformationPayload({
      sync_record_id: syncRecordId,
      connection_id: connectionId,
      source_field: sourceField,
      transformations: apiTransformations,
    });

    // Reset the flag after building the payload
    setShouldFetchTransform(false);
  }, [sourceField, transformations, syncRecordId, connectionId, shouldFetchTransform]);

  // Use the transformation hook when the payload is available
  const {
    executeTransformation,
    data: transformationResponse,
    isLoading,
  } = useHandleTransformation();

  // Update transformed value when the API response changes
  useEffect(() => {
    if (transformationResponse?.transformedValue) {
      setTransformedValue(transformationResponse.transformedValue);
    } else {
      setTransformedValue("");
    }
  }, [transformationResponse]);

  // Execute transformation when the payload changes
  useEffect(() => {
    if (transformationPayload && connectionId && shouldFetchTransform) {
      setIsTransforming(true);
      executeTransformation(transformationPayload)
        .then(() => {
          console.log("Transformation executed successfully");
        })
        .catch((error) => {
          console.error("Transformation failed:", error);
        })
        .finally(() => {
          setIsTransforming(false);
          setShouldFetchTransform(false);
        });
    }
  }, [transformationPayload, connectionId, shouldFetchTransform, executeTransformation]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex h-[80vh] max-h-[800px] max-w-4xl flex-col gap-0 bg-background p-0">
        <DialogHeader className="bg-card p-6">
          <DialogTitle>{t("pages.productMapping.advancedMapping.title")}</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto ">
          <div className="grid grid-cols-1 gap-4 px-6 pb-8 pt-2 md:grid-cols-2">
            <TransformationForm
              sourceField={sourceField}
              onSourceFieldChange={handleSourceFieldChange}
              transformations={transformations}
              onTransformationTypeChange={handleTransformationTypeChange}
              onConfigChange={handleConfigChange}
              onRemoveTransformation={handleRemoveTransformation}
              onAddTransformation={handleAddTransformation}
              sourceData={sourceData}
              sourceFields={sourceFields}
            />

            {isTransforming || isLoading ? (
              <div className="flex h-fit flex-col gap-4">
                <Skeleton className="h-[400px] w-full" />
              </div>
            ) : (
              <TransformationPreview
                activeTab={activeTab}
                onTabChange={setActiveTab}
                sourceValue={sourceValue.toString()}
                transformedValue={transformedValue}
                sourceField={sourceField}
                transformations={transformations}
                syncRecordId={syncRecordId}
                connectionId={connectionId}
                sourceData={sourceData}
              />
            )}
          </div>
        </div>

        <DialogFooter className="shrink-0 gap-y-2 border-t bg-card px-4 py-3">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("common.cancel")}
          </Button>
          <Button onClick={handleSave} disabled={isTransforming || isLoading}>
            {t("common.save")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/**
 * ProductOptions Component
 *
 * IMPORTANT IMAGE HANDLING NOTES:
 * 1. Images are only associated with option1 values (the first option)
 * 2. Each option1 value should have a unique set of images (no duplicates)
 * 3. When editing a product, images are extracted from variants based on option1 values
 * 4. Variants with the same option1 value should share the same images
 * 5. Images are referenced by option1 value and not duplicated per variant
 *
 * This approach prevents duplicate image uploads and ensures efficient image management.
 */

"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToParentElement } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Check, GripVertical, ImagePlus, Minus, Plus, Trash2, X } from "lucide-react";
import { useTranslation } from "react-i18next";

import type {
  CreateImage,
  ProductOption,
  ProductOptionValue,
} from "@/features/products/hooks/types";

import { Button } from "@/components/ui/button";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

/**
 * ProductOptions Component
 *
 * IMPORTANT: Images are only associated with the first option (option 0) values.
 * This is an intentional design decision to ensure that variants are created correctly.
 *
 * When a user selects images for a value in the first option, those images will be
 * assigned to all variants that use that value. Images should never be associated
 * with values from option 2 or option 3.
 *
 * This applies to both add and edit modes. In edit mode, images are extracted only
 * from option1 values in the variants.
 */

interface ProductOptionsProps {
  options: ProductOption[];
  onChange: (
    options: ProductOption[],
    selectedImages: { value: string; images: CreateImage[] }[]
  ) => void;
  availableImages?: CreateImage[];
  showValidation?: boolean;
  onValidationChange?: (hasErrors: boolean) => void;
  onSectionFocus?: () => void;
  initialValueImages?: { value: string; images: CreateImage[] }[];
}

// Helper to check if a specific value is duplicated in the array
const isDuplicate = (value: string, values: ProductOptionValue[], currentIndex: number) => {
  return (
    value && values.findIndex((val, idx) => idx !== currentIndex && val.value === value) !== -1
  );
};

// Add SortableValue component
// const SortableValue = ({
//   id,
//   value,
//   children,
// }: {
//   id: string;
//   value: string;
//   children: React.ReactNode;
// }) => {
//   const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
//     id,
//     disabled: !value.trim(), // Disable drag for empty values
//   });

//   const style = {
//     transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
//     transition,
//     opacity: isDragging ? 0.5 : 1,
//   };

//   return (
//     <div ref={setNodeRef} style={style}>
//       <div className="rounded-lg border bg-card p-3">
//         <div className="flex items-center gap-4">
//           <div
//             className={cn("cursor-grab", !value.trim() && "opacity-50 cursor-not-allowed")}
//             {...attributes}
//             {...listeners}>
//             <GripVertical className="m-4 text-muted-foreground" size={16} />
//           </div>
//           {children}
//         </div>
//       </div>
//     </div>
//   );
// };
const SortableValue = ({
  id,
  value,
  children,
  isLast,
}: {
  id: string;
  value: string;
  children: React.ReactNode;
  isLast: boolean;
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });

  const style = {
    transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // disable drag handlers if it's the last + empty
  const isDragDisabled = isLast && !value.trim();

  return (
    <div ref={setNodeRef} style={style}>
      <div className="rounded-lg border bg-card p-3">
        <div className="flex items-center gap-4">
          <div
            className={cn("cursor-grab", isDragDisabled && "opacity-50 cursor-not-allowed")}
            {...(!isDragDisabled ? { ...attributes, ...listeners } : {})} // disable drag handlers
          >
            <GripVertical className="m-4 text-muted-foreground" size={16} />
          </div>
          {children}
        </div>
      </div>
    </div>
  );
};

export const ProductOptions = ({
  options,
  onChange,
  availableImages = [],
  showValidation = false,
  onValidationChange,
  onSectionFocus,
  initialValueImages = [],
}: ProductOptionsProps) => {
  const { t } = useTranslation();
  const [optionsList, setOptionsList] = useState<ProductOption[]>(options);
  const [newOption, setNewOption] = useState<ProductOption>({ name: "", values: [] });
  const [valueInput, setValueInput] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedOptionIndex, setSelectedOptionIndex] = useState<number | null>(null);
  const [selectedValueIndex, setSelectedValueIndex] = useState<number | null>(null);
  const [selectedImageIndexes, setSelectedImageIndexes] = useState<string[]>([]);
  const [selectedImageOrder, setSelectedImageOrder] = useState<string[]>([]);
  const [touchedFields, setTouchedFields] = useState<{
    [key: string]: boolean;
  }>({});
  const [imageDialogOpen, setImageDialogOpen] = useState(false);
  const [valueImages, setValueImages] =
    useState<{ value: string; images: CreateImage[] }[]>(initialValueImages);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [deleteOptionIndex, setDeleteOptionIndex] = useState<number | null>(null);
  const [deleteValueIndex, setDeleteValueIndex] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteOptionAlert, setShowDeleteOptionAlert] = useState(false);
  const [optionToDelete, setOptionToDelete] = useState<number | null>(null);

  // Update valueImages when initialValueImages change
  useEffect(() => {
    if (initialValueImages && initialValueImages.length > 0) {
      setValueImages(initialValueImages);
    }
  }, [initialValueImages]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  useEffect(() => {
    if (showValidation) {
      const newTouchedFields: { [key: string]: boolean } = {};
      optionsList.forEach((option, optionIndex) => {
        option.values.forEach((value, valueIndex) => {
          newTouchedFields[`${optionIndex}-${valueIndex}`] = true;
        });
      });
      setTouchedFields(newTouchedFields);
    }
  }, [showValidation, optionsList]);

  useEffect(() => {
    if (showValidation) {
      let hasDuplicates = false;
      optionsList.forEach((option) => {
        const values = option.values.filter((v) => v.value.trim());
        const duplicates = values.filter(
          (value) => values.indexOf(value) !== values.lastIndexOf(value)
        );
        if (duplicates.length > 0) {
          hasDuplicates = true;
        }
      });
      onValidationChange?.(hasDuplicates);
    }
  }, [showValidation, optionsList, onValidationChange]);

  const handleOptionsChange = (newOptions: ProductOption[]) => {
    setOptionsList(newOptions);
    onChange(newOptions, valueImages);
  };

  const handleAddOption = () => {
    if (newOption.name && valueInput) {
      let values = valueInput
        .split(",")
        .map((v) => v.trim())
        .filter((v) => v);

      // Filter out duplicates before adding to options
      values = Array.from(new Set(values));

      // Convert string values to ProductOptionValue objects
      const optionValues: ProductOptionValue[] = values.map((value) => ({
        id: crypto.randomUUID(),
        value,
      }));

      // Add empty value at the end
      optionValues.push({ id: crypto.randomUUID(), value: "" });

      const updatedOptions = [
        ...optionsList,
        {
          name: newOption.name,
          values: optionValues,
        },
      ];

      setOptionsList(updatedOptions);
      onChange(updatedOptions, valueImages);
      setNewOption({ name: "", values: [] });
      setValueInput("");
      setIsDialogOpen(false);
    }
  };

  const handleRemoveOption = (index: number) => {
    // Remove only the specified option
    const updatedOptions = optionsList.filter((_, i) => i !== index);

    // If removing the first option (index 0), clear all images
    if (index === 0) {
      setValueImages([]);
      onChange(updatedOptions, []);
    } else {
      // For other options, keep the images unchanged
      onChange(updatedOptions, valueImages);
    }

    setOptionsList(updatedOptions);
  };

  const handleRemoveValue = (optionIndex: number, valueIndex: number) => {
    const updatedOptions = [...optionsList];
    const valueToRemove = updatedOptions[optionIndex].values[valueIndex];
    const currentOption = updatedOptions[optionIndex];

    // Remove value from options
    currentOption.values.splice(valueIndex, 1);

    // Only process image removal for option 0
    if (optionIndex === 0) {
      // Remove images for this value
      const newValueImages = valueImages.filter((item) => item.value !== valueToRemove.value);

      // If this was the last non-empty value in the option, remove the entire option
      const remainingValues = currentOption.values.filter((v) => v.value.trim());
      if (remainingValues.length === 0) {
        // Remove the entire option
        updatedOptions.splice(optionIndex, 1);

        // Remove all images associated with this option's values
        const optionValues = currentOption.values;
        const filteredValueImages = valueImages.filter(
          (item) => !optionValues.some((v) => v.value === item.value)
        );

        setOptionsList(updatedOptions);
        setValueImages(filteredValueImages);
        onChange(updatedOptions, filteredValueImages);
        return;
      }

      setOptionsList(updatedOptions);
      setValueImages(newValueImages);
      onChange(updatedOptions, newValueImages);
    } else {
      // For options other than the first, don't modify images
      const remainingValues = currentOption.values.filter((v) => v.value.trim());
      if (remainingValues.length === 0) {
        updatedOptions.splice(optionIndex, 1);
      }

      setOptionsList(updatedOptions);
      onChange(updatedOptions, valueImages);
    }
  };

  // const handleValueChange = (optionIndex: number, valueIndex: number, newValue: string) => {
  //   const updatedOptions = [...optionsList];
  //   const currentOption = updatedOptions[optionIndex];

  //   if (!newValue && valueIndex !== currentOption.values.length - 1) {
  //     // If value is empty and it's not the last placeholder value, remove it
  //     currentOption.values.splice(valueIndex, 1);

  //     // Only modify images if this is option 0
  //     if (optionIndex === 0) {
  //       // Also remove any associated images
  //       const valueToRemove = currentOption.values[valueIndex];
  //       const newValueImages = valueImages.filter((item) => item.value !== valueToRemove.value);

  //       setOptionsList(updatedOptions);
  //       setValueImages(newValueImages);

  //       // Filter duplicates before onChange
  //       const filteredOptions = updatedOptions.map((opt) => ({
  //         ...opt,
  //         values: Array.from(new Set(opt.values.filter((v) => v.value))),
  //       }));
  //       onChange(filteredOptions, newValueImages);
  //     } else {
  //       setOptionsList(updatedOptions);
  //       const filteredOptions = updatedOptions.map((opt) => ({
  //         ...opt,
  //         values: Array.from(new Set(opt.values.filter((v) => v.value))),
  //       }));
  //       onChange(filteredOptions, valueImages);
  //     }
  //   } else if (newValue && valueIndex === currentOption.values.length - 1) {
  //     // If this is the last (empty) value and user types something,
  //     // update the value and add a new empty value
  //     currentOption.values[valueIndex] = { id: crypto.randomUUID(), value: newValue };
  //     currentOption.values.push({ id: crypto.randomUUID(), value: "" });
  //     setOptionsList(updatedOptions);
  //     const filteredOptions = updatedOptions.map((opt) => ({
  //       ...opt,
  //       values: Array.from(new Set(opt.values.filter((v) => v.value))),
  //     }));
  //     onChange(filteredOptions, valueImages);
  //   } else {
  //     // Normal value update
  //     currentOption.values[valueIndex] = { ...currentOption.values[valueIndex], value: newValue };
  //     setOptionsList(updatedOptions);
  //     const filteredOptions = updatedOptions.map((opt) => ({
  //       ...opt,
  //       values: Array.from(new Set(opt.values.filter((v) => v.value))),
  //     }));
  //     onChange(filteredOptions, valueImages);
  //   }
  // };

  // Add this new function to handle keyboard navigation
  const dedupeValues = (values: { id: string; value: string }[]) => {
    const seen = new Set<string>();
    return values.filter((v) => {
      if (!v.value || seen.has(v.value)) return false;
      seen.add(v.value);
      return true;
    });
  };

  const handleValueChange = (optionIndex: number, valueIndex: number, newValue: string) => {
    const updatedOptions = [...optionsList];
    const currentOption = updatedOptions[optionIndex];

    // Clone the values array to avoid mutation issues
    const valuesCopy = [...currentOption.values];
    const isLastValue = valueIndex === valuesCopy.length - 1;

    if (!newValue && !isLastValue) {
      // Remove the item if it's not the last one and is empty
      const removed = valuesCopy.splice(valueIndex, 1);

      if (optionIndex === 0) {
        const newValueImages = valueImages.filter((item) => item.value !== removed[0]?.value);
        updatedOptions[optionIndex].values = valuesCopy;
        setOptionsList(updatedOptions);
        setValueImages(newValueImages);

        const filteredOptions = updatedOptions.map((opt) => ({
          ...opt,
          values: dedupeValues(opt.values),
        }));
        onChange(filteredOptions, newValueImages);
      } else {
        updatedOptions[optionIndex].values = valuesCopy;
        setOptionsList(updatedOptions);

        const filteredOptions = updatedOptions.map((opt) => ({
          ...opt,
          values: dedupeValues(opt.values),
        }));
        onChange(filteredOptions, valueImages);
      }
    } else if (newValue && isLastValue) {
      // ✅ Check for duplicates before adding a new blank item
      const isDuplicate = valuesCopy.some(
        (v, idx) =>
          idx !== valueIndex && v.value.trim().toLowerCase() === newValue.trim().toLowerCase()
      );

      if (isDuplicate) {
        // Just update the value and don't add a new one
        valuesCopy[valueIndex] = { ...valuesCopy[valueIndex], value: newValue };
      } else {
        // Update current last item and add a new blank one
        valuesCopy[valueIndex] = { ...valuesCopy[valueIndex], value: newValue };
        valuesCopy.push({ id: crypto.randomUUID(), value: "" });
      }

      updatedOptions[optionIndex].values = valuesCopy;
      setOptionsList(updatedOptions);

      const filteredOptions = updatedOptions.map((opt) => ({
        ...opt,
        values: dedupeValues(opt.values),
      }));
      onChange(filteredOptions, valueImages);
    } else {
      // Just update the value normally
      valuesCopy[valueIndex] = { ...valuesCopy[valueIndex], value: newValue };
      updatedOptions[optionIndex].values = valuesCopy;

      setOptionsList(updatedOptions);

      const filteredOptions = updatedOptions.map((opt) => ({
        ...opt,
        values: dedupeValues(opt.values),
      }));
      onChange(filteredOptions, valueImages);
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    optionIndex: number,
    valueIndex: number
  ) => {
    if ((e.key === "Enter" || e.key === "Tab") && !e.shiftKey) {
      e.preventDefault();

      const currentOption = optionsList[optionIndex];
      const nextValueIndex = valueIndex + 1;

      // If there's a next input in current option, focus it
      if (nextValueIndex < currentOption.values.length) {
        const nextInput = document.querySelector(
          `input[data-option-index="${optionIndex}"][data-value-index="${nextValueIndex}"]`
        ) as HTMLInputElement;
        nextInput?.focus();
        return;
      }

      // If we're at the last value in current option, move to next option's first value
      const nextOptionIndex = optionIndex + 1;
      if (nextOptionIndex < optionsList.length) {
        const nextInput = document.querySelector(
          `input[data-option-index="${nextOptionIndex}"][data-value-index="0"]`
        ) as HTMLInputElement;
        nextInput?.focus();
      }
    }
  };

  const handleImageDialogOpen = (optionIndex: number, valueIndex: number) => {
    // Only allow opening image dialog for the first option (option 0)
    if (optionIndex !== 0) {
      console.warn(
        `Attempted to open image dialog for option ${optionIndex}, but images are only allowed for option 0`
      );
      return;
    }

    console.log(`Opening image dialog for option ${optionIndex}, value ${valueIndex}`);

    setSelectedOptionIndex(optionIndex);
    setSelectedValueIndex(valueIndex);

    // Pre-select existing images for this value
    const value = optionsList[optionIndex].values[valueIndex].value.toLowerCase();
    const existingImages = valueImages.find((vi) => vi.value === value)?.images || [];

    // Clear previous selections first
    setSelectedImageIndexes([]);
    setSelectedImageOrder([]);

    // We need to create proper indexes for the existing images
    const selectedKeys: string[] = [];

    // First handle existing images from availableImages
    existingImages.forEach((img) => {
      // Try to find the image in availableImages to get its original index
      const availableIndex = availableImages.findIndex((availImg) => availImg.image === img.image);

      if (availableIndex >= 0) {
        // This is an image from availableImages
        selectedKeys.push(`${img.image}|||${availableIndex}`);
      } else {
        // This is an uploaded image, assign index starting from availableImages.length
        const uploadedIndex = availableImages.length + selectedKeys.length;
        selectedKeys.push(`${img.image}|||${uploadedIndex}`);
      }
    });

    // Set both arrays with the same values to maintain order
    setSelectedImageIndexes(selectedKeys);
    setSelectedImageOrder(selectedKeys);

    setImageDialogOpen(true);
  };

  const handleImageSelect = (imageUrl: string, index: number) => {
    // Use a separator that won't appear in URLs
    const imageKey = `${imageUrl}|||${index}`;

    // First check if we're toggling off an exact key
    if (selectedImageIndexes.includes(imageKey)) {
      // Toggle off this exact image
      setSelectedImageIndexes((prev) => prev.filter((key) => key !== imageKey));
      setSelectedImageOrder((prev) => prev.filter((key) => key !== imageKey));
      return;
    }

    // Check if any key with this image URL exists (a duplicate)
    const existingKeyWithSameUrl = selectedImageIndexes.find((key) => {
      const [url] = key.split("|||");
      return url === imageUrl;
    });

    if (existingKeyWithSameUrl) {
      // We have a duplicate - remove the old one
      setSelectedImageIndexes((prev) => {
        const filtered = prev.filter((key) => {
          const [url] = key.split("|||");
          return url !== imageUrl;
        });
        return [...filtered, imageKey];
      });

      setSelectedImageOrder((prev) => {
        // Find position of the existing key with same URL
        const position = prev.findIndex((key) => {
          const [url] = key.split("|||");
          return url === imageUrl;
        });

        if (position >= 0) {
          // Replace at same position to maintain order
          const newOrder = [...prev];
          newOrder[position] = imageKey;
          return newOrder;
        } else {
          // Should not happen, but if it does, add to end
          return [...prev, imageKey];
        }
      });
    } else {
      // Brand new image URL, add it to both arrays
      setSelectedImageIndexes((prev) => [...prev, imageKey]);
      setSelectedImageOrder((prev) => [...prev, imageKey]);
    }
  };

  const handleConfirmImageSelection = () => {
    if (selectedOptionIndex !== null && selectedValueIndex !== null) {
      const value = optionsList[selectedOptionIndex].values[selectedValueIndex].value.toLowerCase();

      // Create a Map to track unique images by URL
      const uniqueImageMap = new Map<string, CreateImage>();

      // Process selections in the order they were selected
      for (const imageKey of selectedImageOrder) {
        if (selectedImageIndexes.includes(imageKey)) {
          const [imageUrl, indexStr] = imageKey.split("|||");
          const index = parseInt(indexStr, 10);

          // Skip if we already have this image URL
          if (uniqueImageMap.has(imageUrl)) continue;

          // Handle library images
          if (index < availableImages.length) {
            const originalImage = availableImages[index];
            if (originalImage && originalImage.image === imageUrl) {
              uniqueImageMap.set(imageUrl, {
                name: originalImage.name,
                image: originalImage.image,
              });
            }
          } else {
            // Handle uploaded images
            const nameMatch = imageUrl.match(/;name=(.*?)(;|$)/);
            const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : "image.jpg";
            uniqueImageMap.set(imageUrl, {
              name: filename,
              image: imageUrl,
            });
          }
        }
      }

      // Convert the Map values back to an array
      const selectedImageObjects = Array.from(uniqueImageMap.values());

      console.log(`Selected ${selectedImageObjects.length} unique images for ${value}`);

      // Update valueImages
      const newValueImages = [...valueImages];
      const existingIndex = newValueImages.findIndex((vi) => vi.value === value);

      if (existingIndex >= 0) {
        newValueImages[existingIndex] = {
          value,
          images: selectedImageObjects,
        };
      } else {
        newValueImages.push({
          value,
          images: selectedImageObjects,
        });
      }

      setValueImages(newValueImages);

      // Notify parent with both updated options and valueImages
      onChange(optionsList, newValueImages);

      // Reset dialog state
      setImageDialogOpen(false);
      setSelectedImageIndexes([]);
      setSelectedImageOrder([]);
    }
  };

  // const handleDragEnd = (event: DragEndEvent, optionIndex: number) => {
  //   const { active, over } = event;
  //   if (!over || active.id === over.id) return;

  //   const updatedOptions = [...optionsList];
  //   const currentValues = updatedOptions[optionIndex].values;

  //   const oldIndex = currentValues.findIndex((item) => item.id === active.id);
  //   const newIndex = currentValues.findIndex((item) => item.id === over.id);

  //   if (oldIndex === -1 || newIndex === -1) return;

  //   updatedOptions[optionIndex].values = arrayMove(currentValues, oldIndex, newIndex);

  //   setOptionsList(updatedOptions);
  //   handleOptionsChange(updatedOptions); // or onChange(updatedOptions, valueImages) depending on your setup
  // };
  const handleDragEnd = (event: DragEndEvent, optionIndex: number) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const updatedOptions = [...optionsList];
    const currentValues = updatedOptions[optionIndex].values;

    const oldIndex = currentValues.findIndex((item) => item.id === active.id);
    const newIndex = currentValues.findIndex((item) => item.id === over.id);

    if (oldIndex === -1 || newIndex === -1) return;

    const activeItem = currentValues[oldIndex];
    const overItem = currentValues[newIndex];

    const isOverLastEmpty = newIndex === currentValues.length - 1 && !overItem.value.trim();
    const isActiveEmpty = !activeItem.value.trim();

    // ✅ Block any drop if either is empty AND involves the last item
    if (isOverLastEmpty || isActiveEmpty) return;

    updatedOptions[optionIndex].values = arrayMove(currentValues, oldIndex, newIndex);

    setOptionsList(updatedOptions);
    handleOptionsChange(updatedOptions);
  };

  return (
    <div
      className="space-y-4"
      onClick={() => onSectionFocus?.()}
      onFocus={() => onSectionFocus?.()}>
      <div className="text-sm text-muted-foreground">
        {t("pages.products.addManual.sections.createVariant")}
        <span className="ml-2 text-xs text-muted-foreground">
          {optionsList.length}/3 {t("pages.products.addManual.sections.option")}
        </span>
      </div>

      {optionsList.map((option, optionIndex) => (
        <div key={optionIndex} className="space-y-4 rounded-lg bg-background p-4">
          <div className="flex items-center justify-between gap-2">
            <Input
              placeholder={t("pages.products.addManual.sections.optionsPlaceholder")}
              value={option.name}
              onChange={(e) => {
                const updatedOptions = [...optionsList];
                updatedOptions[optionIndex].name = e.target.value;
                setOptionsList(updatedOptions);
                handleOptionsChange(updatedOptions);
              }}
              className="bg-bg-secondary"
            />
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                setOptionToDelete(optionIndex);
                setShowDeleteOptionAlert(true);
              }}
              className="text-destructive hover:bg-destructive/10 hover:text-destructive">
              <Trash2 size={16} />
            </Button>
          </div>

          <div className="space-y-3">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              modifiers={[restrictToParentElement]}
              onDragEnd={(event) => handleDragEnd(event, optionIndex)}>
              <SortableContext
                // items={option.values.map((_) => _.id.toString())}
                items={option.values
                  .filter((v, i) => !(i === option.values.length - 1 && !v.value.trim()))
                  .map((v) => v.id.toString())}
                strategy={verticalListSortingStrategy}>
                {option.values.map((valueObj, valueIndex) => (
                  <SortableValue
                    key={valueObj.id}
                    id={valueObj.id}
                    value={valueObj.value}
                    isLast={valueIndex === option.values.length - 1}>
                    <div className="w-[200px]">
                      <Input
                        placeholder={t("pages.products.addManual.sections.valuesPlaceholderInput")}
                        value={valueObj.value}
                        onChange={(e) => handleValueChange(optionIndex, valueIndex, e.target.value)}
                        onKeyDown={(e) => handleKeyDown(e, optionIndex, valueIndex)}
                        onBlur={() => {
                          setTouchedFields((prev) => ({
                            ...prev,
                            [`${optionIndex}-${valueIndex}`]: true,
                          }));
                        }}
                        data-option-index={optionIndex}
                        data-value-index={valueIndex}
                        className={cn(
                          "w-full",
                          (touchedFields[`${optionIndex}-${valueIndex}`] || showValidation) &&
                            isDuplicate(valueObj.value, option.values, valueIndex) &&
                            "border-destructive focus-visible:ring-destructive"
                        )}
                      />
                      {(touchedFields[`${optionIndex}-${valueIndex}`] || showValidation) &&
                        isDuplicate(valueObj.value, option.values, valueIndex) && (
                          <p className="mt-1 text-xs text-destructive">
                            {t("pages.products.addManual.sections.duplicateValue")}
                          </p>
                        )}
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-wrap gap-1">
                        {/* Only show upload button for the first option (option 0) */}
                        {optionIndex === 0 && (
                          <button
                            onClick={() => handleImageDialogOpen(optionIndex, valueIndex)}
                            disabled={!valueObj.value}
                            className={cn(
                              "flex aspect-square w-[72px] shrink-0 items-center justify-center rounded-md border-2 border-dashed transition-colors",
                              valueObj.value
                                ? "bg-input hover:bg-accent/50 cursor-pointer"
                                : "bg-muted cursor-not-allowed opacity-50"
                            )}>
                            <ImagePlus className="size-4 text-muted-foreground" />
                          </button>
                        )}
                        {/* Display selected images for this value - only for first option (option 0) */}
                        {valueObj.value &&
                          optionIndex === 0 &&
                          (() => {
                            // Find the images for this value
                            const imageData = valueImages.find(
                              (vi) => vi.value === valueObj.value.toLowerCase()
                            );

                            // If no images, nothing to display
                            if (!imageData || !imageData.images.length) return null;

                            // Get unique images by their URLs
                            const uniqueImagesMap = new Map();
                            imageData.images.forEach((img, idx) => {
                              // Only add if this image URL isn't already in our map
                              if (!uniqueImagesMap.has(img.image)) {
                                uniqueImagesMap.set(img.image, { ...img, originalIndex: idx });
                              }
                            });

                            // Convert back to array for rendering
                            const uniqueImages = Array.from(uniqueImagesMap.values());

                            return uniqueImages.map((imgData) => {
                              const { originalIndex, ...img } = imgData;
                              return (
                                <div
                                  key={`${img.name}-${img.image}`}
                                  className="group relative aspect-square w-[72px] shrink-0">
                                  <Image
                                    src={img.image}
                                    alt={`${valueObj.value}-${originalIndex}`}
                                    fill
                                    className="rounded-lg border-2 border-dashed object-cover"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setPreviewImage(img.image);
                                      setPreviewDialogOpen(true);
                                    }}
                                  />
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const newValueImages = valueImages.map((vi) => {
                                        if (vi.value === valueObj.value.toLowerCase()) {
                                          // Remove all instances of this image
                                          return {
                                            ...vi,
                                            images: vi.images.filter((i) => i.image !== img.image),
                                          };
                                        }
                                        return vi;
                                      });
                                      setValueImages(newValueImages);

                                      // Notify parent with both updated options and valueImages
                                      onChange(optionsList, newValueImages);
                                    }}
                                    className="absolute -right-1.5 -top-1.5 flex size-4 items-center justify-center rounded-full bg-red-500 opacity-50 shadow-md group-hover:opacity-100">
                                    <X className="size-2.5 text-white" />
                                  </button>
                                </div>
                              );
                            });
                          })()}
                      </div>
                    </div>

                    {(valueIndex !== option.values.length - 1 || valueObj.value) && (
                      <div className="ml-auto border-l border-gray-300 pl-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteOptionIndex(optionIndex);
                            setDeleteValueIndex(valueIndex);
                            setShowDeleteOptionAlert(true);
                          }}
                          className="text-destructive hover:bg-destructive/10 hover:text-destructive">
                          <Minus size={16} />
                        </Button>
                      </div>
                    )}
                  </SortableValue>
                ))}
              </SortableContext>
            </DndContext>
          </div>
        </div>
      ))}

      {optionsList.length < 3 && (
        <Popover open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="bg-bg-primary">
              <Plus className="mr-2 size-4" />
              {t("pages.products.addManual.sections.options")} ({optionsList.length}/3)
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px]" align="start">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("pages.products.addManual.sections.options")}
                </label>
                <Input
                  className="border border-muted-foreground/50"
                  placeholder={t("pages.products.addManual.sections.optionsPlaceholder")}
                  value={newOption.name}
                  onChange={(e) =>
                    setNewOption((prev) => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("pages.products.addManual.sections.values")}
                </label>
                <Input
                  className="border border-muted-foreground/50"
                  placeholder={t("pages.products.addManual.sections.valuesPlaceholder")}
                  value={valueInput}
                  onChange={(e) => setValueInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey && newOption.name && valueInput) {
                      e.preventDefault();
                      handleAddOption();
                    }
                  }}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDialogOpen(false);
                    setNewOption({ name: "", values: [] });
                    setValueInput("");
                  }}>
                  {t("pages.products.addManual.sections.cancel")}
                </Button>
                <Button
                  onClick={handleAddOption}
                  disabled={!newOption.name.trim() || !valueInput.trim()}>
                  {t("pages.products.addManual.sections.add")}
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      )}

      {/* Image Selection Dialog */}
      <Dialog open={imageDialogOpen} onOpenChange={setImageDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{t("pages.products.addManual.sections.selectImages")}</DialogTitle>
          </DialogHeader>

          {/* Upload Section */}
          <div className="mb-4 overflow-y-auto border-b pb-4">
            <ImageUpload
              value={JSON.stringify(
                // When generating the value for the ImageUpload component,
                // use a Map to deduplicate images by URL first to prevent showing duplicates
                Array.from(
                  new Map(
                    selectedImageIndexes
                      .filter((key) => {
                        const [_, indexStr] = key.split("|||");
                        const index = parseInt(indexStr, 10);
                        // Only show uploaded images (indexes >= availableImages.length) in the uploader
                        return index >= availableImages.length;
                      })
                      .map((key) => {
                        const [imageUrl] = key.split("|||");
                        // Use the URL as the key for deduplication
                        return [imageUrl, imageUrl];
                      })
                  ).values()
                )
              )}
              onChange={(base64) => {
                if (base64) {
                  try {
                    // Parse new images from the upload component
                    const newUploadedImages = JSON.parse(base64) as string[];

                    // Keep existing selected images from availableImages
                    const existingAvailableKeys = selectedImageIndexes.filter((key) => {
                      const [_, indexStr] = key.split("|||");
                      const index = parseInt(indexStr, 10);
                      return index < availableImages.length;
                    });

                    // Create a map of existing URLs to avoid duplicates
                    const existingUrls = new Map<string, string>();

                    // Add existing library selections to the map
                    existingAvailableKeys.forEach((key) => {
                      const [url] = key.split("|||");
                      existingUrls.set(url, key);
                    });

                    // Calculate starting index for new uploads
                    let startIndex = availableImages.length;

                    // Process each new uploaded image
                    const newUploadedKeys: string[] = [];

                    for (const img of newUploadedImages) {
                      // Skip if we already have this exact URL
                      if (existingUrls.has(img)) {
                        continue;
                      }

                      // Create new key for this image
                      const newKey = `${img}|||${startIndex}`;
                      newUploadedKeys.push(newKey);
                      existingUrls.set(img, newKey);
                      startIndex++;
                    }

                    // Combine all keys
                    const allKeys = [...existingAvailableKeys, ...newUploadedKeys];

                    // Update state with deduplicated images
                    setSelectedImageIndexes(allKeys);

                    // For order, add new keys at the end of the order array
                    setSelectedImageOrder((prev) => {
                      // Filter out any keys that might have been replaced
                      const filteredPrev = prev.filter((key) => {
                        // Only keep keys that are still in allKeys
                        return allKeys.includes(key);
                      });

                      // Add any new keys that aren't already in the order
                      const newKeysNotInOrder = newUploadedKeys.filter(
                        (key) => !filteredPrev.includes(key)
                      );

                      return [...filteredPrev, ...newKeysNotInOrder];
                    });
                  } catch (error) {
                    console.error("Error processing uploaded images:", error);
                  }
                } else {
                  // When images are removed from upload area, only keep selected library images
                  const libraryKeys = selectedImageIndexes.filter((key) => {
                    const [_, indexStr] = key.split("|||");
                    const index = parseInt(indexStr, 10);
                    return index < availableImages.length;
                  });
                  setSelectedImageIndexes(libraryKeys);
                  setSelectedImageOrder((prev) => prev.filter((key) => libraryKeys.includes(key)));
                }
              }}
              className="w-full"
              multiple
              maxFiles={5}
            />
          </div>

          {/* Image Grid */}
          <div className="grid grid-cols-4 gap-4 overflow-auto">
            {/* Display unique images by deduplicating with a Map */}
            {(() => {
              // Create a Map to deduplicate images by URL
              const uniqueImagesMap = new Map();

              // Process each available image
              availableImages.forEach((image, index) => {
                if (!uniqueImagesMap.has(image.image)) {
                  uniqueImagesMap.set(image.image, { image, index });
                }
              });

              // Convert Map values to array for rendering
              return Array.from(uniqueImagesMap.values()).map(({ image, index }) => {
                // Generate a unique key for each image based on URL and index
                const imageKey = `${image.image}|||${index}`;

                // Check if this image URL is selected with any index
                const isSelected = selectedImageIndexes.some((key) => {
                  const [url] = key.split("|||");
                  return url === image.image;
                });

                return (
                  <button
                    key={`${image.image}-${index}`}
                    onClick={() => handleImageSelect(image.image, index)}
                    className="relative aspect-square rounded-md border-2 border-dashed">
                    <Image
                      src={image.image}
                      alt={image.name}
                      fill
                      className="rounded-lg object-cover"
                    />
                    <div className="absolute right-2 top-2">
                      {isSelected && (
                        <div className="rounded-full bg-primary p-1 text-white">
                          <Check className="size-4" />
                        </div>
                      )}
                    </div>
                  </button>
                );
              });
            })()}
          </div>

          <div className="mt-4 flex justify-end gap-2">
            <Button variant="outline" onClick={() => setImageDialogOpen(false)}>
              {t("pages.products.addManual.sections.cancel")}
            </Button>
            <Button onClick={handleConfirmImageSelection}>
              {t("pages.products.addManual.sections.submit")} (
              {(() => {
                // Count unique images by URL
                const uniqueUrls = new Set();
                selectedImageIndexes.forEach((key) => {
                  const [url] = key.split("|||");
                  uniqueUrls.add(url);
                });
                return uniqueUrls.size;
              })()}{" "}
              selected)
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Preview Dialog */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent className="max-w-4xl p-0">
          {previewImage && (
            <div className="relative flex size-full items-center justify-center bg-black/50">
              <Image
                src={previewImage}
                alt="Preview"
                width={800}
                height={800}
                className="max-h-[80vh] max-w-[80vw] object-contain"
                unoptimized
              />
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Option Confirmation Alert */}
      <ConfirmDialog
        open={showDeleteOptionAlert}
        onOpenChange={() => {}}
        variant="destructive"
        title={t("common.areYouSure")}
        description={t("pages.products.descriptionDeleteOption")}
        cancelText={t("common.cancel")}
        confirmText={t("common.delete")}
        onCancel={() => !isDeleting && setShowDeleteOptionAlert(false)}
        onConfirm={() => {
          if (optionToDelete !== null) {
            setIsDeleting(true);
            handleRemoveOption(optionToDelete);
            setIsDeleting(false);
            setShowDeleteOptionAlert(false);
            setOptionToDelete(null);
          }
          if (deleteOptionIndex !== null && deleteValueIndex !== null) {
            setIsDeleting(true);
            handleRemoveValue(deleteOptionIndex, deleteValueIndex);
            setIsDeleting(false);
            setShowDeleteOptionAlert(false);
            setDeleteOptionIndex(null);
            setDeleteValueIndex(null);
          }
        }}
      />
    </div>
  );
};

export const validateOptions = (options: ProductOption[]) => {
  const errors: { field: string; message: string }[] = [];

  options.forEach((option, optionIndex) => {
    // Check for empty values
    if (!option.name) {
      errors.push({
        field: `option-${optionIndex}-name`,
        message: "Option name is required",
      });
    }

    // Check for empty values array
    if (!option.values || option.values.length === 0) {
      errors.push({
        field: `option-${optionIndex}-values`,
        message: "At least one value is required",
      });
    }

    // Check each value for duplicates
    option.values.forEach((valueObj, valueIndex) => {
      if (valueObj.value && isDuplicate(valueObj.value, option.values, valueIndex)) {
        errors.push({
          field: `option-${optionIndex}-value-${valueIndex}`,
          message: "Duplicate value",
        });
      }
    });
  });

  return errors;
};

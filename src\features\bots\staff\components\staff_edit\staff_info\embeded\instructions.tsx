import { Info } from "lucide-react";
import { useTranslation } from "react-i18next";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export function EmbedInstructions() {
  const { t } = useTranslation();

  return (
    <div className="mb-4">
      <Accordion type="single" collapsible defaultValue="item-1">
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="px-4 py-2 hover:no-underline">
            <div className="flex items-center gap-2">
              <Info className="size-5 text-primary" />
              <span className="font-medium">{t("pages.staff.embed.instructions.title")}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-2">
            <div className="space-y-4 text-sm">
              <div className="space-y-2">
                <h3 className="font-medium">{t("pages.staff.embed.instructions.step1Title")}</h3>
                <p>{t("pages.staff.embed.instructions.step1Description")}</p>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">{t("pages.staff.embed.instructions.step2Title")}</h3>
                <p>{t("pages.staff.embed.instructions.step2Description")}</p>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">{t("pages.staff.embed.instructions.step3Title")}</h3>
                <p>{t("pages.staff.embed.instructions.step3Description")}</p>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">{t("pages.staff.embed.instructions.step4Title")}</h3>
                <p>{t("pages.staff.embed.instructions.step4Description")}</p>
              </div>

              <div className="space-y-2 border-t pt-2">
                <h3 className="font-medium">
                  {t("pages.staff.embed.instructions.troubleshootingTitle")}
                </h3>
                <ul className="list-disc space-y-1 pl-5">
                  <li>{t("pages.staff.embed.instructions.troubleshooting1")}</li>
                  <li>{t("pages.staff.embed.instructions.troubleshooting2")}</li>
                  <li>{t("pages.staff.embed.instructions.troubleshooting3")}</li>
                  <li>{t("pages.staff.embed.instructions.troubleshooting4")}</li>
                </ul>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}

import React, { useState } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

interface InformationProps {
  onSubmit: (name: string, phoneNumber: string) => void;
}

const Information: React.FC<InformationProps> = ({ onSubmit }) => {
  const [name, setName] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");

  const handleSubmit = () => {
    if (name && phoneNumber) {
      onSubmit(name, phoneNumber);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Please enter your name and phone number to continue.</CardTitle>
        <CardDescription>
          This information helps us personalize your experience and support you better.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="flex flex-col items-end gap-4 md:flex-row">
          <div className="flex-1">
            <Input
              id="name"
              label="Name"
              type="text"
              placeholder="Enter your name"
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </div>

          <div className="flex-1">
            <Input
              id="phoneNumber"
              label="Phone number"
              type="tel"
              placeholder="Enter your phone number"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
            />
          </div>

          <Button
            onClick={handleSubmit}
            disabled={!name || !phoneNumber}
            className="mt-4 bg-orange-500 hover:bg-orange-600 md:mt-0">
            OK
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default Information;

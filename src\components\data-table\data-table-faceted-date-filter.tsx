"use client";

import { useEffect, useRef } from "react";
import { CirclePlus } from "lucide-react";
import { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";

import { dateOptions } from "../custom-table/header/date-options";
import { Button, Popover, PopoverContent, PopoverTrigger } from "../ui";
import { Badge } from "../ui/badge";
import { Calendar } from "../ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Separator } from "../ui/separator";

interface DataTableFacetedDateFilterProps {
  title: string;
  dateSelectedOption?: string | null;
  onDateOptionSelect?: (value: string) => void;
  handleApplyFilters: () => void;
  dateRange?: DateRange | undefined;
  onDateRangeChange?: (range: DateRange | undefined) => void;
  type?: string;
}

export default function DataTableFacetedDateFilter({
  title,
  dateSelectedOption,
  onDateOptionSelect,
  handleApplyFilters,
  dateRange,
  onDateRangeChange,
  type = "main",
}: DataTableFacetedDateFilterProps) {
  const { t } = useTranslation();
  const handleAppyFilterRef = useRef(handleApplyFilters);
  handleAppyFilterRef.current = handleApplyFilters;
  const handleValueChange = (newValue: string) => {
    onDateOptionSelect?.(newValue);
  };
  useEffect(() => {
    if (type === "other") {
      handleAppyFilterRef.current();
    }
  }, [dateSelectedOption, dateRange, type]);
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="default"
          className="border-dashed px-3"
          leftIcon={<CirclePlus size={16} />}>
          {title}
          {dateSelectedOption && dateSelectedOption !== "all" && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <span className="font-normal">
                <Badge variant="secondary" className="rounded-sm px-1 font-normal ">
                  {(() => {
                    const option = dateOptions.find((opt) => opt.value === dateSelectedOption);
                    return option ? t(option.label) : "";
                  })()}
                </Badge>
              </span>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-fit p-2">
        <div className="space-y-2">
          <Select value={dateSelectedOption || "all"} onValueChange={handleValueChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {dateOptions
                .filter((opt) => opt.value !== "customize")
                .map((option) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className="flex items-center justify-between">
                    <span>{t(option.label)}</span>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>

          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange?.from}
            selected={dateRange}
            onSelect={onDateRangeChange}
            numberOfMonths={2}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
}

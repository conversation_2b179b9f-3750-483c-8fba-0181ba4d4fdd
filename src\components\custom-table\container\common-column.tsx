import React, { useCallback, useEffect, useRef, useState } from "react";
import Image, { ImageProps } from "next/image";
import Link from "next/link";

import { useChannels } from "@/features/integration/hooks/use-channel";

import logo from "@/assets/images/logotextv2.png";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui";
import { Channel } from "@/lib/apis/types/channel";
import { cn } from "@/lib/utils";
import { convertByte } from "@/lib/utils/convert_byte";
import { dateFormatter, timeAgo } from "@/utils/helpers/date-formater";
import toNavUrl from "@/utils/helpers/nav-url-formater";

// Helper function to merge refs
function useMergedRef<T>(...refs: React.Ref<T>[]) {
  return useCallback(
    (element: T) => {
      refs.forEach((ref) => {
        if (typeof ref === "function") {
          ref(element);
        } else if (ref && "current" in ref) {
          // Casting here avoids the TypeScript error
          // We know this is safe because we check if 'current' exists
          (ref as React.MutableRefObject<T>).current = element;
        }
      });
    },
    [refs]
  );
}

const isInvalidDate = (date?: string) => {
  if (!date) return true;
  const timestamp = new Date(date).getTime();
  const invalidDate = new Date("1970-01-01T08:00:00.000Z").getTime();
  return timestamp === 0 || timestamp === invalidDate;
};

interface ZoomImageProps extends Omit<ImageProps, "iszoomable"> {
  tooltipText?: string;
  rounded?: boolean;
  iszoomable?: boolean;
}

const ZoomImage = React.forwardRef<HTMLDivElement, ZoomImageProps>(
  ({ src, alt, tooltipText, rounded, className, ...props }, ref) => {
    const [srcState, setSrcState] = useState(src);

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            ref={ref}
            className={cn(
              "group relative flex cursor-pointer items-center justify-center overflow-hidden flex-none",
              rounded && "rounded",
              className
            )}>
            <div className="w-full">
              <Image
                onError={(e) => {
                  if (e.currentTarget.src !== logo.src) {
                    setSrcState(logo.src);
                  }
                }}
                width={0}
                height={0}
                sizes="100vw"
                alt={alt || "Image"}
                src={srcState || logo}
                className={cn("size-full flex-none object-contain", className)}
                {...props}
              />
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent
          side={tooltipText ? "bottom" : "right"}
          className={cn(!tooltipText && "border-0 bg-transparent p-0")}>
          {tooltipText ? (
            <span>{tooltipText}</span>
          ) : (
            <div className="overflow-hidden rounded-lg border border-border bg-background shadow-lg">
              <Image
                onError={(e) => {
                  if (e.currentTarget.src !== logo.src) {
                    setSrcState(logo.src);
                  }
                }}
                src={srcState || logo}
                alt={alt || "Image"}
                width={800}
                height={800}
                className="aspect-square max-h-[25vh] min-h-[400px] w-auto min-w-[400px] object-contain"
              />
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    );
  }
);
ZoomImage.displayName = "ZoomImage";

export const DateColumn = React.forwardRef<HTMLDivElement, { date?: string }>(({ date }, ref) => {
  if (isInvalidDate(date))
    return (
      <span ref={ref} className="text-sm">
        ---
      </span>
    );

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span ref={ref} className="text-nowrap text-sm">
          {timeAgo(date!)}
        </span>
      </TooltipTrigger>
      <TooltipContent>
        <span>{dateFormatter(date!)}</span>
      </TooltipContent>
    </Tooltip>
  );
});
DateColumn.displayName = "DateColumn";

export const ByteColumn = React.forwardRef<HTMLDivElement, { bytes?: number }>(({ bytes }, ref) => {
  if (typeof bytes !== "number" || isNaN(bytes))
    return (
      <span ref={ref} className="text-sm">
        ---
      </span>
    );

  const data = convertByte(bytes);
  if (data.endsWith("TB") || data.endsWith("GB") || data.endsWith("MB") || data.endsWith("KB")) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <span ref={ref} className="text-nowrap text-sm">
            {data}
          </span>
        </TooltipTrigger>
        <TooltipContent>
          <span>{bytes}B</span>
        </TooltipContent>
      </Tooltip>
    );
  } else {
    return (
      <span ref={ref} className="text-nowrap text-sm">
        {data}
      </span>
    );
  }
});
ByteColumn.displayName = "ByteColumn";

export const ImageColumn = React.forwardRef<HTMLDivElement, ZoomImageProps>(
  ({ alt, src, iszoomable = false, className, ...props }, ref) => {
    const [srcState, setSrcState] = useState(src);
    if (iszoomable) {
      return <ZoomImage ref={ref} alt={alt} src={srcState} className={className} {...props} />;
    }
    return (
      <div className="flex-none" ref={ref}>
        <Image
          onError={(e) => {
            if (e.currentTarget.src !== logo.src) {
              setSrcState(logo.src);
            }
          }}
          alt={alt || "Image"}
          src={srcState || logo}
          className={cn("flex-none", className)}
          width={0}
          height={0}
          sizes="100vw"
          {...props}
        />
      </div>
    );
  }
);
ImageColumn.displayName = "ImageColumn";

interface ImageColumnProps {
  src: string;
  alt?: string;
  href?: string;
  tooltipText?: string;
  width?: number;
  height?: number;
  className?: string;
  rounded?: boolean;
  iszoomable?: boolean;
}

export const ImageColumnWithLink = React.forwardRef<HTMLDivElement, ImageColumnProps>(
  (
    {
      src,
      alt = "Image",
      href,
      tooltipText,
      iszoomable = true,
      width = 32,
      height = 32,
      className = "",
      rounded = true,
    },
    ref
  ) => {
    const imageElement = iszoomable ? (
      <ZoomImage
        ref={ref}
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        rounded={rounded}
        tooltipText={tooltipText}
      />
    ) : (
      <div ref={ref}>
        <Image
          src={src || logo}
          alt={alt || "Image"}
          width={width}
          height={height}
          className={className}
        />
      </div>
    );

    if (href) {
      return (
        <Link href={toNavUrl(href)} className="flex items-center">
          {imageElement}
        </Link>
      );
    }

    return imageElement;
  }
);
ImageColumnWithLink.displayName = "ImageColumnWithLink";

const ChannelLogo = React.forwardRef<HTMLDivElement, { channelKey: string; href: string }>(
  ({ channelKey, href }, ref) => {
    const { data: saleChannels } = useChannels({ enabled: true });
    const saleChannel = saleChannels?.find((channel: Channel) => channel?.key === channelKey);
    return (
      <ImageColumnWithLink
        ref={ref}
        src={saleChannel?.logo || ""}
        alt={channelKey}
        iszoomable={false}
        href={href}
        className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
        rounded
      />
    );
  }
);
ChannelLogo.displayName = "ChannelLogo";

export const TextColumn = React.forwardRef<HTMLDivElement, { text?: string; className?: string }>(
  ({ text, className }, ref) => {
    const [isTruncated, setIsTruncated] = useState(false);
    const internalRef = useRef<HTMLDivElement>(null);

    // Create a merged ref that can be used for both our internal state and the forwarded ref
    const mergedRef = useMergedRef(internalRef, ref);

    // Check if text is truncated
    const checkIfTruncated = useCallback(() => {
      if (internalRef.current) {
        const element = internalRef.current;
        setIsTruncated(element.scrollWidth > element.clientWidth);
      }
    }, []);

    useEffect(() => {
      checkIfTruncated();

      // Set up resize observer to check for truncation on resize
      const resizeObserver = new ResizeObserver(() => {
        checkIfTruncated();
      });

      const element = internalRef.current;
      if (element) {
        resizeObserver.observe(element);
      }

      // Clean up observer on unmount
      return () => {
        resizeObserver.disconnect();
      };
    }, [checkIfTruncated, text]);

    if (!text) {
      return (
        <div ref={ref} className={cn("text-sm w-full", className)}>
          ---
        </div>
      );
    }

    return (
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div ref={mergedRef} className={cn("text-sm truncate w-full", className)}>
            {text}
          </div>
        </TooltipTrigger>
        {isTruncated && (
          <TooltipContent side="bottom" align="center" className="max-w-[300px] break-words">
            {text}
          </TooltipContent>
        )}
      </Tooltip>
    );
  }
);
TextColumn.displayName = "TextColumn";

export default ChannelLogo;

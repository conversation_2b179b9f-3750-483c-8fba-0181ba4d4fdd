import { useCallback, useEffect, useMemo, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { saveFilterApi, SaveFilterItem } from "@/lib/apis/save-filter";

import { SortDirection } from "./use-data-table";

interface FilterResponse {
  items: SaveFilterItem[];
}

interface UseSavedFiltersProps {
  filterType: string;
  onFilterChange?: (filters: Record<string, unknown>) => void;
}

export const useSavedFilters = ({ filterType, onFilterChange }: UseSavedFiltersProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const filterTabId = searchParams?.get("filter_tab") || null;

  const queryClient = useQueryClient();
  const [editingIndex, setEditingIndex] = useState<string | null>(null);
  const [editValue, setEditValue] = useState("");

  const initialTab = searchParams?.get("filter_tab") || "all";
  const [activeTab, setActiveTab] = useState(initialTab);

  const { data: filterResponse, isPending: isLoading } = useQuery({
    queryKey: ["saveFilter", filterType] as const,
    queryFn: async () => {
      const response = await saveFilterApi.get({
        id: filterType,
        params: { ["sort_updated_at"]: SortDirection.DESC },
      });
      return response;
    },
  });

  const updateFilter = useMutation({
    mutationFn: async (filters: SaveFilterItem[]) => {
      return saveFilterApi.update(filters, filterType);
    },
    onSuccess: (_, newFilters) => {
      queryClient.setQueryData<FilterResponse>(["saveFilter", filterType], (oldData) => {
        if (!oldData) return oldData;
        if (newFilters.length === oldData.items.length)
          return {
            ...oldData,
            items: newFilters,
          };
        return {
          ...oldData,
          items: oldData.items.map((item) => {
            const updatedItem = newFilters.find((newItem) => newItem.id === item.id);
            return updatedItem || item;
          }),
        };
      });

      setEditingIndex(null);
      setEditValue("");
      toast.success("Filter updated");
    },
    onError: () => {
      toast.error("Failed to update filter");
    },
  });
  const deleteFilterMutation = useMutation({
    mutationFn: async (id: string) => {
      return saveFilterApi.delete({ id: `${filterType}/${id}` });
    },
    onSuccess: (_, id) => {
      queryClient.setQueryData<FilterResponse>(["saveFilter", filterType], (oldData) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          items: oldData.items.filter((item) => item.id !== id),
        };
      });

      if (activeTab === id) {
        setActiveTab("all");
        const currentParams = new URLSearchParams(searchParams?.toString());
        currentParams.delete("filter_tab");
        router.push(currentParams.toString() ? `?${currentParams.toString()}` : "?");
      }
      toast.success("Filter deleted successfully");
    },
    onError: () => {
      toast.error("Failed to delete filter");
    },
  });

  const data = useMemo(() => {
    // const defaultFilters = defaultSaveFilter.map((filter) => ({
    //   ...filter,
    //   type: "default",
    // }));

    // const savedFilters =
    //   filterResponse?.items?.map((item) => ({
    //     id: item.id,
    //     name: item.name,
    //     type: item.type,
    //     icon: item.icon,
    //     filters: item.filters,
    //   })) || [];

    return [
      { id: "all", name: "All", type: "default", filters: {}, icon: "Star" },
      ...(filterResponse?.items || []),
    ];
  }, [filterResponse]);

  const handleTabChange = useCallback(
    (value: string) => {
      if (editingIndex === null) {
        setActiveTab(value);
        const filter = data?.find((f) => f.id === value);
        if (filter) {
          if (value === "all") {
            router.push("?");
            if (onFilterChange) {
              onFilterChange({ page: 0, limit: 20 });
            }
          } else {
            const allParams = {
              ...filter.filters,
              page: (filter.filters as any).page || 0,
              limit: (filter.filters as any).limit || 20,
              filter_tab: value,
            };
            const url = new URLSearchParams();
            Object.entries(allParams).forEach(([key, val]) => {
              url.set(key, String(val));
            });
            router.push(`?${url.toString()}`);
            if (onFilterChange) {
              onFilterChange(allParams);
            }
          }
        }
      }
    },
    [data, editingIndex, onFilterChange, router]
  );

  const handleEditFilter = useCallback(
    (id: string) => {
      setEditingIndex(id);
      const filter = data?.find((f) => f.id === id);
      if (filter) {
        setEditValue(filter.name);
      }
    },
    [data]
  );

  const handleAcceptFilter = useCallback(
    async (filters: SaveFilterItem[]) => {
      return updateFilter.mutateAsync(filters);
    },
    [updateFilter]
  );

  const handleConfirmDelete = useCallback(
    async (id: string) => {
      if (id) {
        return deleteFilterMutation.mutateAsync(id);
      }
    },
    [deleteFilterMutation]
  );

  const handleCancelEdit = useCallback(() => {
    setEditingIndex(null);
    setEditValue("");
  }, []);

  const updateFilters = useCallback(
    async (newFilters: SaveFilterItem[]) => {
      return updateFilter.mutateAsync(newFilters);
    },
    [updateFilter]
  );

  const updateIcon = useCallback(
    (id: string, icon: string) => {
      queryClient.setQueryData<FilterResponse>(["saveFilter", filterType], (oldData) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          items: oldData.items.map((filter) => (filter.id === id ? { ...filter, icon } : filter)),
        };
      });
    },
    [queryClient, filterType]
  );
  useEffect(() => {
    if (filterTabId) {
      setActiveTab(filterTabId);
    } else {
      setActiveTab("all");
    }
  }, [filterTabId]);
  return {
    data,
    isLoading,
    isUpdating: updateFilter.isPending,
    isDeleting: deleteFilterMutation.isPending,
    activeTab,
    editingIndex,
    editValue,

    setEditValue,
    handleTabChange,
    handleEditFilter,
    handleAcceptFilter,
    handleConfirmDelete,
    handleCancelEdit,
    updateFilters,
    updateIcon,
  };
};

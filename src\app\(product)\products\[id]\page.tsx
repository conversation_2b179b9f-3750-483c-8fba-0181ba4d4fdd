"use client";

import { useEffect, useRef, useState } from "react";
import { notFound, useP<PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";

import { Inventory } from "@/features/products/components/ProductDetails/inventory";
import { PriceList } from "@/features/products/components/ProductDetails/price-list";
import { ProductCard } from "@/features/products/components/ProductDetails/product-card";
import { ProductDetailSkeleton } from "@/features/products/components/ProductDetails/product-detail-skeleton";
import { VariantDetails } from "@/features/products/components/ProductDetails/variant-details";
import { VariantList } from "@/features/products/components/ProductDetails/variant-list";
import { productKeys } from "@/features/products/hooks/keys";
import { useProduct } from "@/features/products/hooks/product";

import { Button } from "@/components/ui/button";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { productApi } from "@/lib/apis/product";

export default function DetailProductPage() {
  const { t } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const { id } = useParams();
  const { data: product, isLoading } = useProduct(id as string);
  const [selectedVariant, setSelectedVariant] = useState<string | undefined>(undefined);
  const rightColumnRef = useRef<HTMLDivElement>(null);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const initialSelectionMade = useRef(false);

  // Get the variant ID from URL query parameters
  useEffect(() => {
    // Only set from URL if we haven't made the initial selection yet
    if (!initialSelectionMade.current && product?.variants) {
      initialSelectionMade.current = true;
      const variantId = searchParams.get("variant");

      if (variantId && product.variants.some((v) => v.id === variantId)) {
        setSelectedVariant(variantId);
      } else if (product.variants.length > 0) {
        setSelectedVariant(product.variants[0].id);
      }
    }
  }, [product, searchParams]);

  if (isLoading) {
    return <ProductDetailSkeleton />;
  }

  if (!product) {
    notFound();
  }

  const currentVariant = product.variants?.find((v) => v.id === selectedVariant);

  const handleEdit = () => {
    router.push(`/products/${id}/edit`);
  };

  const handleDelete = () => {
    setShowDeleteAlert(true);
  };

  const handleConfirmDelete = async () => {
    try {
      setIsDeleting(true);
      await productApi.delete(id as string);

      // Invalidate both the list and detail queries
      await queryClient.invalidateQueries({
        queryKey: productKeys.lists(),
      });

      // Navigate back with refresh flag
      router.push("/products?refresh=true");
    } catch (error) {
      console.error("Failed to delete product:", error);
    } finally {
      setIsDeleting(false);
      setShowDeleteAlert(false);
    }
  };

  return (
    <div className="mx-auto rounded-lg px-6">
      {/* Product Info Section */}
      <div className="mb-4 rounded-lg bg-card">
        <ProductCard
          product={{
            id: product.id,
            name: product.name,
            images: product.images || [],
            source: product.source?.channel_name || "System",
            category: product.category?.name || "",
            brand: product.brand?.name || "",
            tags: product.tags || null,
            description: product.description,
            shortDescription: product.shortDescription,
            createdAt: product.created_at,
          }}
        />
      </div>

      {/* Variants and Details Section */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Left Column - Variants List */}
        <div className="hide-scrollbar top-6 h-fit max-h-[calc(100vh-200px)] overflow-y-auto rounded-lg bg-card lg:sticky">
          <div className="h-full">
            <div className="p-4">
              <VariantList
                variants={product.variants}
                selectedVariant={selectedVariant}
                onSelectVariant={setSelectedVariant}
              />
            </div>
          </div>
        </div>

        {/* Right Column - Stacked Cards */}
        <div className="lg:col-span-2" ref={rightColumnRef}>
          {currentVariant ? (
            <div className="space-y-6">
              {/* Variant Details Card */}
              <div className="rounded-lg bg-card p-4">
                <VariantDetails variant={currentVariant} />
              </div>

              {/* Price Card */}
              <div className="rounded-lg bg-card p-4">
                <PriceList prices={currentVariant.prices} />
              </div>

              {/* Inventory Card */}
              <div className="rounded-lg bg-card p-4">
                <Inventory sku={currentVariant.sku} />
              </div>
            </div>
          ) : (
            <div className="rounded-lg bg-card p-8 text-center text-gray-500">
              No variants available for this product
            </div>
          )}
        </div>
      </div>

      {/* Footer Actions */}
      <div className="fixed inset-x-0 bottom-0 border-t bg-card shadow-2xl">
        <div className="mx-auto px-6 py-4">
          <div className="flex justify-end gap-2">
            <Button
              onClick={handleDelete}
              disabled={isDeleting}
              className="inline-flex items-center rounded-lg border bg-primary-foreground px-6 py-2 text-sm font-medium text-destructive hover:bg-destructive/10 disabled:cursor-not-allowed disabled:opacity-50">
              {t("common.delete")}
            </Button>
            <Button
              onClick={handleEdit}
              disabled={isDeleting}
              className="inline-flex items-center rounded-lg bg-primary px-6 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50">
              {t("common.edit")}
            </Button>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Alert */}
      <ConfirmDialog
        open={showDeleteAlert}
        onOpenChange={() => {}}
        variant="destructive"
        title={t("common.areYouSure")}
        description={t("common.deleteProductConfirmation")}
        cancelText={t("common.cancel")}
        confirmText={t("common.delete")}
        onCancel={() => !isDeleting && setShowDeleteAlert(false)}
        onConfirm={handleConfirmDelete}
      />

      {/* Add padding to prevent content from being hidden behind fixed footer */}
      <div className="h-20" />
    </div>
  );
}

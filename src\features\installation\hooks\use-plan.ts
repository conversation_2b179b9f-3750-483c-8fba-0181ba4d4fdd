import { useEffect, useRef, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";

import { planApi } from "@/lib/apis/plan";
import { COOKIES } from "@/utils/constants/cookies";
import { getCookie, setCookie } from "@/utils/cookie";

import { installationKeys } from "./key";
import { Plan, PlanFeature } from "./type";

export type PlanType = "monthly" | "annually";
export type PricingTier = "free" | "starter" | "pro" | "agency";

interface Integrations {
  [key: string]: number;
}

export function usePlan(connectionId: string | null | undefined) {
  const { t } = useTranslation();
  const query = useQuery({
    queryKey: installationKeys.listPlans(),
    queryFn: () => planApi.getPlanList({ connection_id: connectionId }),
    enabled: !!connectionId,
  });

  // TODO: Remove this after the API is ready
  const hard_coded_plans: Plan[] = [
    {
      updated_at: "2025-03-05T10:42:46.848742+00:00",
      service_id: "5c4bf15b-29fc-492c-a338-b15f3ce4b5be",
      code: "free",
      sale_price: "0",
      created_at: "2025-03-05T10:42:46.848742+00:00",
      price: "0",
      description: "Free",
      id: "1ac4851a-f25a-42c8-88db-bd",
      name: t("pages.choosePlan.planNames.Free"),
      features: [
        {
          enable: false as boolean,
          description: t("pages.choosePlan.planData.Up to 50 variants") as string,
        },
        {
          enable: false as boolean,
          description: t("pages.choosePlan.planData.Real-time inventory syncing") as string,
        },
        {
          enable: false as boolean,
          description: t("pages.choosePlan.planData.Ideal for startups (1,000 items)") as string,
        },
        {
          enable: false as boolean,
          description: t("pages.choosePlan.planData.Analytics dashboard") as string,
        },
        {
          enable: false as boolean,
          description: t("pages.choosePlan.planData.User-friendly interface") as string,
        },
        {
          enable: false as boolean,
          description: t(
            "pages.choosePlan.planData.Support for multiple currencies and languages"
          ) as string,
        },
        {
          enable: false as boolean,
          description: t(
            "pages.choosePlan.planData.Support for multiple currencies and languages"
          ) as string,
        },
      ] as PlanFeature[],
    } as Plan,
    {
      updated_at: "2025-03-05T10:42:46.848742+00:00",
      service_id: "5c4bf15b-29fc-492c-a338-b15f3ce4b5be",
      code: "starter",
      sale_price: "54",
      created_at: "2025-03-05T10:42:46.848742+00:00",
      price: "59",
      description: "Free",
      id: "1ac4851a-f25a-42c8-88db-bdh",
      name: t("pages.choosePlan.planNames.Starter"),
      features: [
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        {
          enable: false,
          description: t("pages.choosePlan.planData.Support for multiple currencies and languages"),
        },
      ] as PlanFeature[],
    } as Plan,
    {
      updated_at: "2025-03-05T10:42:46.848742+00:00",
      service_id: "5c4bf15b-29fc-492c-a338-b15f3ce4b5be",
      code: "pro",
      sale_price: "90",
      created_at: "2025-03-05T10:42:46.848742+00:00",
      price: "99",
      description: "Free",
      id: "1ac4851a-f25a-42c8-88db-hjr",
      name: t("pages.choosePlan.planNames.Pro"),
      is_popular: true,
      features: [
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        {
          enable: false,
          description: t("pages.choosePlan.planData.Support for multiple currencies and languages"),
        },
      ] as PlanFeature[],
    } as Plan,
    {
      updated_at: "2025-03-05T10:42:46.848742+00:00",
      service_id: "5c4bf15b-29fc-492c-a338-b15f3ce4b5be",
      code: "agency",
      sale_price: "150",
      created_at: "2025-03-05T10:42:46.848742+00:00",
      price: "199",
      description: "Free",
      id: "1ac4851a-f25a-42c8-88db-hewr",
      name: t("pages.choosePlan.planNames.Agency"),
      features: [
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        { enable: false, description: t("pages.choosePlan.planData.Real time inventory") },
        {
          enable: false,
          description: t("pages.choosePlan.planData.Support for multiple currencies and languages"),
        },
      ] as PlanFeature[],
    } as Plan,
  ];

  // TODO: Enable this after the API is ready
  // const plans = query?.data?.data as Plan[] | undefined;

  // TODO: Remove this after the API is ready
  const plans = hard_coded_plans;

  const [planType, setPlanType] = useState<PlanType>("annually");
  const [integrations, setIntegrations] = useState<Integrations>({});
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [subscribingPlanId, setSubscribingPlanId] = useState<string | null>(null);
  const plansRef = useRef<Plan[] | undefined>();

  useEffect(() => {
    if (!Array.isArray(plans) || plans.length === 0) return;

    // Only update integrations if the plans have changed
    const planIdsString = plans
      .map((plan) => plan.id)
      .sort()
      .join(",");
    const prevPlanIdsString = plansRef.current
      ? plansRef.current
          .map((plan) => plan.id)
          .sort()
          .join(",")
      : "";

    if (planIdsString !== prevPlanIdsString) {
      const planIds = plans.map((plan: Plan) => plan.id);
      const newIntegrations = planIds.reduce(
        (acc: Integrations, id: string) => ({ ...acc, [id]: 1 }),
        {}
      );

      setIntegrations(newIntegrations);
      plansRef.current = plans;
    }
  }, [plans]);

  const handleIncrement = (plan: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setIntegrations((prev) => ({
      ...prev,
      [plan]: prev[plan] + 1,
    }));
  };

  const handleDecrement = (plan: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (integrations[plan] > 0) {
      setIntegrations((prev) => ({
        ...prev,
        [plan]: prev[plan] - 1,
      }));
    }
  };

  const getUndiscountPrice = (price: number) => {
    if (planType === "annually") {
      return (price * 12).toFixed(0);
    }
    return price.toFixed(0);
  };

  const getPrice = (price: number, salePrice: number) => {
    if (planType === "annually") {
      // Apply 10% discount for annual plans
      return salePrice.toFixed(0);
    }
    return price.toFixed(0);
  };

  const handleSubscribePlan = async () => {
    if (!selectedPlan || !connectionId) return;

    try {
      setSubscribingPlanId(selectedPlan);
      // Tìm thông tin của plan được chọn
      const selectedPlanData = plans?.find((plan: Plan) => plan.id === selectedPlan);

      if (!selectedPlanData) {
        throw new Error("Selected plan not found");
      }

      const response = await planApi.subscribePlan({
        plan_id: selectedPlan,
        connection_id: connectionId,
        duration: planType === "monthly" ? "MONTHLY" : "YEARLY",
        ...selectedPlanData,
      });

      if (response.data?.confirmation_url) {
        // Save response data to cookie
        const cookieData = {
          shopify_charge_id: response.data.shopify_charge_id,
          subscription_id: response.data.subscription_id,
          plan: selectedPlanData,
        };
        const installationData = getCookie(COOKIES.INSTALLATION_DATA);
        setCookie(COOKIES.INSTALLATION_DATA, {
          ...installationData,
          ...cookieData,
          status: COOKIES.INSTALLATION_STATUS.SUBSCRIBED,
        });
        // Redirect to Shopify confirmation page
        window.location.href = response.data.confirmation_url;
      }
    } catch (error) {
      // Handle error
      console.error("Failed to subscribe plan:", error);
    } finally {
      setSubscribingPlanId(null);
    }
  };

  return {
    plans,
    planType,
    setPlanType,
    integrations,
    selectedPlan,
    handleIncrement,
    handleDecrement,
    getUndiscountPrice,
    getPrice,
    setSelectedPlan,
    handleSubscribePlan,
    isLoading: query.isLoading,
    subscribingPlanId,
  };
}

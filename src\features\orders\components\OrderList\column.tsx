import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { Row } from "@tanstack/react-table";
import { capitalize } from "lodash";

import ChannelLogo, {
  DateColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Badge } from "@/components/ui/badge";
import { ORDER_STATUS } from "@/constants/order_status";

import { Order } from "../../hooks/types";

export const columns = (t: any): CustomColumn<Order>[] => [
  {
    id: "order",
    accessorKey: "order",
    header: t("nav.order"),
    sorter: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Order> }) => {
      const order = row.original;
      return (
        <div
          className="flex cursor-pointer items-center gap-4 text-primary hover:underline"
          onClick={() => {
            window.location.href = `/orders/${order.id}`;
          }}>
          <TextColumn text={`${order?.number}`} />
        </div>
      );
    },
  },
  {
    id: "source",
    accessorKey: "source",
    header: t("pages.products.source"),
    sorter: true,
    sortKey: "source.id",
    cell: ({ row }: { row: Row<Order> }) => (
      <ChannelLogo channelKey={row?.original?.source?.channel_name || ""} href={""} />
    ),
  },
  {
    id: "customer",
    accessorKey: "customer",
    header: t("pages.orders.customer"),
    sorter: true,
    sortKey: "customer",
    cell: ({ row }: { row: Row<Order> }) => (
      <TextColumn text={`${row?.original?.customer?.first_name}`} className="text-sm" />
    ),
  },
  {
    id: "staff",
    accessorKey: "staff",
    header: t("pages.products.inventory.staff"),
    sorter: true,
    sortKey: "staff",
    cell: ({ row }: { row: Row<Order> }) => <TextColumn text={row?.original?.staff?.name || ""} />,
  },
  {
    id: "created_at",
    accessorKey: "created_at",
    header: t("pages.orders.filters.createdAt"),
    sorter: true,
    hidden: true,
    sortKey: "created_at",
    cell: ({ row }: { row: Row<Order> }) => <DateColumn date={row?.original?.created_at} />,
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.orders.filters.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Order> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.orders.filters.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<Order> }) => {
      const status = row?.original?.status;
      const statusConfig = ORDER_STATUS[status as keyof typeof ORDER_STATUS] || {
        variant: "default",
      };

      return (
        <span className="text-sm">
          <Badge variant={statusConfig.variant}>{capitalize(status)}</Badge>
        </span>
      );
    },
  },
  {
    id: "payment_status",
    accessorKey: "payment_status",
    header: t("pages.orders.filters.paymentStatus"),
    sorter: true,
    sortKey: "payment_status",
    cell: ({ row }: { row: Row<Order> }) => {
      const paymentStatus = row?.original?.payment_status;

      // Determine variant based on payment status
      const variant =
        paymentStatus === "UNPAID" ? "destructive" : paymentStatus === "PAID" ? "blue" : "default";

      return (
        <span className="text-sm">
          <Badge variant={variant}>{capitalize(paymentStatus)}</Badge>
        </span>
      );
    },
  },
  {
    id: "actions",
    header: t("common.actions"),
    cell: ({ row }: { row: Row<Order> }) => <ActionCell row={row} />,
  },
];

const ActionCell = ({ row }: { row: Row<Order> }) => {
  const router = useRouter();
  const order = row.original;

  const handleView = useCallback(() => {
    router.push(`/orders/${order.id}`);
  }, [router, order.id]);

  const handleEdit = useCallback(() => {
    router.push(`/orders/${order.id}/edit`);
  }, [router, order.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
      ]}
    />
  );
};

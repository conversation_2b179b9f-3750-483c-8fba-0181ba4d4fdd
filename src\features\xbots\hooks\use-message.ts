import { useCallback, useState } from "react";

import { conversationApi } from "@/lib/apis/conversation";

import { MESSAGE } from "./types";

interface UseMessageOptions {
  staffId: string;
  role: string;
  listMessenger?: MESSAGE[];
  conversationId?: string | null;
  customer_name?: string;
  customer_phone_number?: string;
  isEmbedded?: boolean;
  isStream?: boolean;
}

export function useMessage({
  staffId,
  role,
  listMessenger = [],
  conversationId,
  customer_name,
  customer_phone_number,
  isEmbedded = false,
  isStream = true,
}: UseMessageOptions) {
  const [messages, setMessages] = useState<MESSAGE[]>(listMessenger || []);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState<MESSAGE | null>(null);
  const [isSending, setIsSending] = useState(false);

  // useEffect(() => {
  //   if (listMessenger) {
  //     setMessages(listMessenger);
  //   }
  // }, [listMessenger]);

  const addMessages = (newMessages: MESSAGE[]) => {
    setMessages((prev) => [...prev, ...newMessages]);
  };

  const clearMessages = () => {
    setMessages([]);
  };

  const setWelcomeMessages = (botName: string, userName: string) => {
    const welcomeMessages: MESSAGE[] = [
      {
        id: "welcome-1",
        content: `Hi there! I'm ${botName}. How can I help you today?`,
        role: "VIRTUAL_STAFF",
        updated_at: new Date(),
        created_at: new Date().toISOString(),
      },
    ];

    setMessages(welcomeMessages);
  };

  // Function to create a message without streaming
  const createMessage = async (content: string) => {
    try {
      if (!conversationId) {
        throw new Error("No conversation ID available");
      }

      // Create a non-streaming message using the createMessage API
      const response = await conversationApi.createMessage(conversationId, {
        content: content,
        role: "VIRTUAL_STAFF", // Fixed role as VIRTUAL_STAFF
      });

      return response;
    } catch (error) {
      console.error("Error creating message:", error);
      throw error;
    }
  };

  const sendMessage = useCallback(
    async (message?: string) => {
      console.log("message: ", message, input);
      const messageText = typeof message === "string" ? message : input;
      if (typeof messageText !== "string" || !messageText.trim()) return;
      const content = messageText.trim();

      // Add user message
      const userMessage: MESSAGE = {
        id: Date.now().toString(),
        content,
        role: isEmbedded ? "EXTERNAL_USER" : "USER",
        updated_at: new Date(),
        created_at: new Date().toISOString(),
      };

      setMessages((prev) => [...prev, userMessage]);
      setInput("");
      setIsLoading(true);
      setIsSending(true);

      const userInfo = localStorage.getItem("auth_user");
      const name = userInfo ? JSON.parse(userInfo)?.Username : "";
      setIsStreaming(true);

      try {
        // Create initial streaming message
        const initialStreamingMessage: MESSAGE = {
          id: (Date.now() + 1).toString(),
          content: "",
          role: "VIRTUAL_STAFF",
          updated_at: new Date(),
          created_at: new Date().toISOString(),
        };

        // Decide which method to use based on embedding status
        if (!isStream && conversationId) {
          // For non-embedded use with conversation ID, use createMessage
          setStreamingMessage(initialStreamingMessage);

          const response = await createMessage(content);

          // Update messages with the response
          // setMessages((prev) => [
          //   ...prev,
          //   {
          //     ...initialStreamingMessage,
          //     content: response.content || "I received your message.",
          //     id: response.id || (Date.now() + 1).toString(),
          //     conversation_id: conversationId,
          //   },
          // ]);

          setStreamingMessage(null);
          setIsStreaming(false);
          setIsSending(false);
        } else {
          // For embedded use or when there's no conversation ID, use streaming
          setStreamingMessage(initialStreamingMessage);

          // Start streaming response
          let accumulatedContent = "";
          await conversationApi.streamChatMessenger(
            staffId,
            {
              name: customer_name || name,
              message: message || content,
              phone_number: customer_phone_number,
              ...(conversationId && { connection_id: conversationId }),
            },
            (chunk) => {
              console.log(chunk);
              if (chunk.done) {
                // When done, add the final message to the messages list
                setMessages((prev) => {
                  // Remove the streaming message if it exists
                  const filteredMessages = prev.filter(
                    (msg) => msg.id !== initialStreamingMessage.id
                  );
                  return [
                    ...filteredMessages,
                    {
                      ...initialStreamingMessage,
                      content: accumulatedContent,
                      conversation_id: chunk.conversation_id,
                    },
                  ];
                });
                setStreamingMessage(null);
                setIsStreaming(false);
                setIsSending(false);
              } else {
                accumulatedContent += chunk.content;
                // Update streaming message with accumulated content
                setStreamingMessage((prev) => {
                  if (!prev) return null;
                  return {
                    ...prev,
                    content: accumulatedContent,
                    conversation_id: chunk.conversation_id,
                  };
                });
              }
            }
          );
        }
      } catch (error) {
        console.error("Error sending message:", error);
        setIsStreaming(false);
        setIsSending(false);

        const errorMessage: MESSAGE = {
          id: Date.now().toString(),
          content: "Sorry, there was an error processing your request. Please try again.",
          role: "VIRTUAL_STAFF",
          updated_at: new Date(),
          created_at: new Date().toISOString(),
        };

        setMessages((prev) => [...prev, errorMessage]);
      } finally {
        setIsLoading(false);
      }
    },
    [input, staffId, conversationId, customer_name, customer_phone_number, isEmbedded]
  );

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInput(event.target.value);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (input.trim()) {
        sendMessage();
      }
    }
  };

  return {
    messages,
    input,
    isLoading,
    isStreaming,
    streamingMessage,
    isSending,
    setInput,
    sendMessage,
    handleInputChange,
    handleKeyDown,
    addMessages,
    clearMessages,
    setWelcomeMessages,
  };
}

import { Card } from "@/components/ui";
import { CustomImage } from "@/components/ui/image";

import { CONVERSATION } from "./conversation-list";

interface ConversationItemProps {
  conversation: CONVERSATION;
  isActive: boolean;
  onClick: () => void;
  formatDate: (dateString: string) => string;
}

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  isActive,
  onClick,
  formatDate,
}) => {
  return (
    <Card
      onClick={onClick}
      className={`flex cursor-pointer items-center justify-between border-none p-4 ${
        isActive ? " bg-card " : "bg-background"
      }`}>
      <div className="flex min-w-0 flex-1 flex-col gap-1">
        <div className="flex items-center justify-between">
          <span className="truncate font-semibold">{conversation.name}</span>
          <span className="ml-2 text-xs text-muted-foreground">
            {formatDate(conversation.updated_at)}
          </span>
        </div>
        <div className="truncate text-sm text-muted-foreground">{conversation.id}</div>
      </div>
      <div className="relative ml-3 size-7 rounded-full border border-dashed border-border">
        <CustomImage
          src={""}
          alt={conversation.name}
          className="rounded-full object-cover"
          fill
          width={32}
          height={32}
        />
      </div>
    </Card>
  );
};

export default ConversationItem;

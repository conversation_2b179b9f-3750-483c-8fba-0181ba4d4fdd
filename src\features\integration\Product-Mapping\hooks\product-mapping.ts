import { useCallback, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useInfiniteQuery, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { mappingProduct } from "@/lib/apis/connection";
import { Channel } from "@/lib/apis/types/channel";
import { MappingProduct, MappingProductMapPayload } from "@/lib/apis/types/mapping-product";

import { IGetProductMappingParams, productMappingKeys } from "./keys";

export const useChannelSelection = (channels: Channel[]) => {
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    if (!searchParams.get("channel") && channels.length > 0) {
      const params = new URLSearchParams(searchParams.toString());
      params.set("channel", channels[0].key);
      router.push(`?${params.toString()}`);
    }
  }, [channels, router, searchParams]);

  const handleChannelSelect = (channel: Channel) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("channel", channel.key);
    router.push(`?${params.toString()}`);
  };

  const selectedChannel =
    channels.find((channel) => channel.key === searchParams.get("channel")) || channels[0];

  return {
    handleChannelSelect,
    selectedChannel,
  };
};

interface UseProductMappingsOptions extends Partial<IGetProductMappingParams> {
  enabled?: boolean;
}

export const useProductMappings = (options: UseProductMappingsOptions = {}) => {
  const { limit = 20, enabled = true, connection_id, ...restOptions } = options;

  const query = useInfiniteQuery({
    queryKey: productMappingKeys.list({ limit, connection_id, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      mappingProduct.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        connection_id,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled: enabled && !!connection_id, // Only fetch when enabled AND connection_id exists
  });

  const mappingProducts = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    mappingProducts,
    total,
  };
};

export const useProductMapping = (id: string, connectionId?: string) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUnmapping, setIsUnmapping] = useState(false);
  const [isMapping, setIsMapping] = useState(false);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const searchParams = useSearchParams();

  const handleMapOpen = useCallback(() => {
    setIsDialogOpen(true);
  }, []);

  const handleMapClose = useCallback(() => {
    setIsDialogOpen(false);
  }, []);

  const handleMap = useCallback(
    async (data: {
      destinationProductId: string;
      mappedVariants: Record<string, string>;
      isExtraDestinationMapping: boolean;
    }) => {
      try {
        setIsMapping(true);
        const payload: MappingProductMapPayload = {
          connection_id: connectionId || searchParams.get("connection_id") || "",
          sync_record_id: id,
          destination_data_id: data.destinationProductId,
          is_extra_destination_mapping: data.isExtraDestinationMapping,
          extra_mapping_data: data.mappedVariants,
          record_type: "product",
        };

        await mappingProduct.mapProduct(payload);
        toast.success(t("Product mapped successfully"));

        // Delay query invalidation and state updates
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: productMappingKeys.all() });
          setIsMapping(false);
          setIsDialogOpen(false);
        }, 5000);
      } catch (message: any) {
        toast.error(message);
        console.error(message);
        setIsMapping(false);
      }
    },
    [id, connectionId, queryClient, t, searchParams]
  );

  const handleUnmap = useCallback(async () => {
    try {
      setIsUnmapping(true);
      const connId = connectionId || searchParams.get("connection_id") || "";

      await mappingProduct.unmapProduct({
        connection_id: connId,
        sync_record_id: id,
      });

      toast.success(t("pages.productMappingList.alert.unmapSuccess"));

      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: productMappingKeys.lists() });
        setIsUnmapping(false);
      }, 5000);
    } catch (error) {
      toast.error(t("pages.productMappingList.alert.unmapFail"));
      console.error(error);
      setIsUnmapping(false);
    }
  }, [id, connectionId, queryClient, t, searchParams]);

  return {
    isDialogOpen,
    isMapping,
    isUnmapping,
    handleMapOpen,
    handleMapClose,
    handleMap,
    handleUnmap,
  };
};

export const useProductActions = (product: MappingProduct) => {
  const router = useRouter();

  const handleView = useCallback(() => {
    router.push(`/product-mapping/${product.id}`);
  }, [router, product.id]);

  return {
    handleView,
  };
};

export const useProductDetail = (id: string) => {
  const router = useRouter();

  // Get the raw ID from the potentially encoded URL parameter
  // If id comes from URL, it might already be encoded
  const rawId = decodeURIComponent(id);
  // Use encoded ID only for URL navigation
  const encodedId = encodeURIComponent(rawId);

  const { data, isLoading, isError, error, refetch } = useQuery<any>({
    queryKey: productMappingKeys.detail(rawId),
    queryFn: async () => {
      try {
        // Use the raw ID for the API call
        const response = await mappingProduct.mappingDetail(rawId);

        // Return the full response (may be wrapped in success/data or may be an array)
        return response;
      } catch (error) {
        console.error("Error fetching product detail:", error);
        throw error;
      }
    },
    // Don't auto fetch when component mounts in the list view
    enabled: false,
    // Don't cache the result between component unmounts
    staleTime: 0,
    // Always refetch when revisiting
    refetchOnMount: "always",
  });

  const handleViewDetail = useCallback(() => {
    // Use encoded ID for URL navigation
    router.push(`/product-mapping/${encodedId}`);

    // Don't prefetch here - we'll fetch when the component mounts on the detail page
  }, [router, encodedId]);

  // Process the data to handle different response formats
  const actualData = (() => {
    if (!data) return null;

    // If it's an array, take the first item
    if (Array.isArray(data) && data.length > 0) {
      return data[0];
    }

    // If it has a success wrapper with data array
    if (data?.success && Array.isArray(data.data) && data.data.length > 0) {
      return data.data[0];
    }

    // Otherwise return as is
    return data;
  })();

  // Extract the necessary data from the processed response
  const mappedVariants = actualData?.other_mappings?.mapping_data || {};
  const sourceVariants = actualData?.standard_source_data?.variants || [];
  const destinationVariants = actualData?.standard_destination_data?.variants || [];

  return {
    productDetail: data, // Return full data for components to handle
    sourceVariants,
    destinationVariants,
    mappedVariants,
    isLoading,
    isError,
    error,
    refetch,
    handleViewDetail,
  };
};

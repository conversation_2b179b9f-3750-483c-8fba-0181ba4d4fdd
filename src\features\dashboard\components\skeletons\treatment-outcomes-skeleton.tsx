import { <PERSON>, CardContent, <PERSON>Header } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function TreatmentOutcomesSkeleton() {
  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <Skeleton className="h-5 w-[180px]" />
      </CardHeader>
      <CardContent>
        <div className="flex justify-center">
          <Skeleton className="size-[200px] rounded-full sm:size-[300px]" />
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4 sm:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="flex items-center space-x-2">
              <Skeleton className="size-3 shrink-0" />
              <Skeleton className="h-4 w-full sm:w-[100px]" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

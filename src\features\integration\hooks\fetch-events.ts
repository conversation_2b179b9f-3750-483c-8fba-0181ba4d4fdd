import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { fetchEventApi } from "@/lib/apis/fetch-event";

import { fetchEventKeys, ICommonParams } from "./keys";

interface UseFetchEventsOptions extends Partial<ICommonParams> {
  enabled?: boolean;
}

export function useFetchEvents(options: UseFetchEventsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;

  const query = useInfiniteQuery({
    queryKey: fetchEventKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      fetchEventApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const fetchEvents = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    fetchEvents,
    total,
  };
}

export function useFetchEventDetail(id: string) {
  return useQuery({
    queryKey: fetchEventKeys.detail(id),
    queryFn: () => fetchEventApi.detail(id),
    enabled: !!id,
  });
}

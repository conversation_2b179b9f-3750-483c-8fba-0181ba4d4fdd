import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";

import { Variant } from "../../hooks/types";

export const columns = (
  useDeleteVariantMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Variant>[] => [
  {
    id: "product",
    accessorKey: "product",
    header: t("pages.products.headers.variant"),
    sorter: true,
    isMainColumn: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Variant> }) => {
      const variant = row.original;
      return (
        <div className="flex items-center gap-4  truncate">
          <ImageColumn
            src={variant?.images?.[0]?.url || ""}
            alt={variant?.name}
            width={0}
            iszoomable={true}
            height={0}
            sizes="100vw"
            className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
          />
          <div className="flex flex-auto flex-col justify-between gap-1 truncate ">
            <TextColumn text={variant?.name} className=" font-medium" />
            <TextColumn text={`SKU: ${variant?.sku}`} className="text-xs text-muted-foreground" />
          </div>
        </div>
      );
    },
  },
  {
    id: "category",
    accessorKey: "category",
    sorter: true,
    sortKey: "category.id",
    header: t("pages.products.headers.category"),
    cell: ({ row }: { row: Row<Variant> }) => <TextColumn text={row?.original?.category?.name} />,
  },
  {
    id: "brand",
    accessorKey: "brand",
    sorter: true,
    sortKey: "brand.name",
    header: t("pages.products.headers.brand"),

    cell: ({ row }: { row: Row<Variant> }) => <TextColumn text={row?.original?.brand?.name} />,
  },
  {
    id: "inventories",
    accessorKey: "inventories",
    header: t("pages.products.headers.available"),
    cell: ({ row }: { row: Row<Variant> }) => (
      <TextColumn text={row?.original?.inventories?.[0]?.available?.toString() || "0"} />
    ),
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    sorter: true,
    sortKey: "updated_at",
    header: t("pages.products.headers.updatedAt"),
    cell: ({ row }: { row: Row<Variant> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.products.headers.createdAt"),
    cell: ({ row }: { row: Row<Variant> }) => (
      <ActionCell
        useDeleteVariantMutation={useDeleteVariantMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];

const ActionCell = ({
  useDeleteVariantMutation,
  row,
  isDeleting,
}: {
  useDeleteVariantMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Variant>;
}) => {
  const router = useRouter();
  const variant = row.original;
  // const { refetch } = useProducts();
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(`/products/${variant.product_id}?variant=${variant.id}`);
    });
  }, [router, variant.product_id, variant.id]);

  const handleEdit = useCallback(() => {
    // Store the current path in sessionStorage to return here after cancel
    sessionStorage.setItem("returnToVariantList", "true");
    router.push(`/products/${variant.product_id}/edit`);
  }, [router, variant.product_id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
      ]}
    />
  );
};

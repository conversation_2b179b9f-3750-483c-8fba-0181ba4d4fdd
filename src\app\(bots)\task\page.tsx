"use client";

import { useMemo, useState } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/bots/task/components/column";
import { useTasks } from "@/features/bots/task/hooks/task";
import { Task } from "@/features/bots/task/hooks/type";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";

export default function CustomersPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | undefined>(undefined);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { tasks, isLoading, isFetching, refetch, total } = useTasks(options);

  const isTableLoading = isLoading || isFetching;
  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "customers",
      searchPlaceHolder: t("pages.customers.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "created_at",
          type: EFilterType.DATE,
          title: t("pages.customers.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: EFilterType.DATE,
          title: t("pages.customers.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as unknown as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("common.add"),
        icon: PlusIcon,
        onClick: () => setOpenCreateDialog(true),
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  const handleDialogSuccess = () => {
    refetch();
  };

  return (
    <TableCard>
      <TableHeader
        title={t("nav.customerList")}
        filterType="customers"
        data={tasks || []}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(t)}
        data={tasks || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          const newList = listIndexId.map((row: any) => (tasks?.[row] as unknown as Task).id);

          // Delete customers one by one
          //   for (const id of newList) {
          //     await useDeleteTaskMutation.mutateAsync(id);
          //   }

          // Call the handler after all deletions
          handleRestRows();
        }}
      />
    </TableCard>
  );
}

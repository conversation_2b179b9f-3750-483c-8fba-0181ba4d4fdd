import { clsx, type ClassValue } from "clsx";
import { TFunction } from "i18next";
import { twMerge } from "tailwind-merge";
import { ZodSchema } from "zod";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const createI18nResolver = (schema: ZodSchema, t: TFunction) => {
  return async (values: any) => {
    try {
      await schema.parseAsync(values);
      return { values, errors: {} };
    } catch (error: any) {
      const errors = error.errors?.map((err: any) => ({
        ...err,
        message: t(err.message),
      }));
      return {
        values: {},
        errors: errors?.reduce(
          (acc: any, curr: any) => ({
            ...acc,
            [curr.path[0]]: { message: curr.message, type: curr.type },
          }),
          {}
        ),
      };
    }
  };
};

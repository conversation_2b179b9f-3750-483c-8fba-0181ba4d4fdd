import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { HexColorInput, HexColorPicker } from "react-colorful";

import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

import { COLOR_FORMATS } from "./constants";

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  disabled?: boolean;
}

type ColorFormat = "hex" | "rgb" | "hsb";

interface RGB {
  r: number;
  g: number;
  b: number;
  a: number;
}

interface HSB {
  h: number;
  s: number;
  b: number;
  a: number;
}

function hexToRgba(hex: string): RGB {
  // Handle 8-digit hex (with alpha)
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
        a: result[4] ? Math.round((parseInt(result[4], 16) / 255) * 100) : 100,
      }
    : { r: 0, g: 0, b: 0, a: 100 };
}

function rgbaToHex(r: number, g: number, b: number, a: number): string {
  const alpha = Math.round((a / 100) * 255);
  return (
    "#" +
    [r, g, b, alpha]
      .map((x) => {
        const hex = x.toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      })
      .join("")
  );
}

function rgbaToHsba(r: number, g: number, b: number, a: number): HSB {
  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const d = max - min;
  let h = 0;
  const s = max === 0 ? 0 : d / max;
  const v = max;

  if (max !== min) {
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    b: Math.round(v * 100),
    a,
  };
}

function hsbaToRgba(h: number, s: number, b: number, a: number): RGB {
  s /= 100;
  b /= 100;
  h /= 360;

  let r, g, bl;
  if (s === 0) {
    r = g = bl = b;
  } else {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    const q = b < 0.5 ? b * (1 + s) : b + s - b * s;
    const p = 2 * b - q;

    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    bl = hue2rgb(p, q, h - 1 / 3);
  }

  return {
    r: Math.round(r * 255),
    g: Math.round(g * 255),
    b: Math.round(bl * 255),
    a,
  };
}

export function ColorPickerInput({ value, onChange, disabled }: ColorPickerProps) {
  const [format, setFormat] = useState<ColorFormat>("hex");
  const [isOpen, setIsOpen] = useState(false);
  const rgba = hexToRgba(value);
  const hsba = rgbaToHsba(rgba.r, rgba.g, rgba.b, rgba.a);

  const getFormattedValue = () => {
    switch (format) {
      case "hex":
        return value;
      case "rgb":
        return `RGBA(${rgba.r},${rgba.g},${rgba.b}${rgba.a !== 100 ? "," + rgba.a + "%" : ""})`;
      case "hsb":
        return `HSBA(${hsba.h},${hsba.s}%,${hsba.b}%${hsba.a !== 100 ? "," + hsba.a + "%" : ""})`;
      default:
        return value;
    }
  };

  const handleRgbChange = (component: keyof RGB, val: string) => {
    const num = parseInt(val, 10);
    if (isNaN(num)) return;
    if (component === "a") {
      if (num < 0 || num > 100) return;
      const newRgba = { ...rgba, a: num };
      onChange(rgbaToHex(newRgba.r, newRgba.g, newRgba.b, newRgba.a));
    } else {
      if (num < 0 || num > 255) return;
      const newRgba = { ...rgba, [component]: num };
      onChange(rgbaToHex(newRgba.r, newRgba.g, newRgba.b, newRgba.a));
    }
  };

  const handleHsbChange = (component: keyof HSB, val: string) => {
    const num = parseInt(val, 10);
    if (isNaN(num)) return;
    if (component === "h" && (num < 0 || num > 360)) return;
    if (component === "a" && (num < 0 || num > 100)) return;
    if ((component === "s" || component === "b") && (num < 0 || num > 100)) return;

    const newHsba = { ...hsba, [component]: num };
    const newRgba = hsbaToRgba(newHsba.h, newHsba.s, newHsba.b, newHsba.a);
    onChange(rgbaToHex(newRgba.r, newRgba.g, newRgba.b, newRgba.a));
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger disabled={disabled}>
        <div
          className={cn(
            "flex h-8 w-full items-center gap-2 rounded-md border bg-background px-2",
            disabled ? "cursor-not-allowed opacity-50" : "cursor-pointer hover:border-primary",
            isOpen && "border-primary ring-1 ring-primary"
          )}>
          <div className="size-4 flex-none rounded-sm border" style={{ backgroundColor: value }} />
          <span className="w-full truncate text-sm">{getFormattedValue()}</span>
          <ChevronDown className="size-4" />
        </div>
      </PopoverTrigger>

      <PopoverContent className=" w-fit p-3" align="start">
        <div className="flex flex-col">
          <div className="mb-3 w-full">
            <HexColorPicker className="!w-full" color={value} onChange={onChange} />
          </div>

          <div className="flex w-full items-start gap-1">
            <div className="flex items-center gap-2">
              <Select value={format} onValueChange={(v) => setFormat(v as ColorFormat)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {COLOR_FORMATS.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      {format.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {format === "hex" && (
              <HexColorInput
                color={value}
                onChange={onChange}
                prefixed
                className="h-8 w-fit rounded-md border bg-transparent px-2 text-sm outline-none focus:border-primary focus:ring-1 focus:ring-primary"
              />
            )}

            {format === "rgb" && (
              <div className="grid grid-cols-3   gap-2">
                <div>
                  <Input
                    type="number"
                    min={0}
                    max={255}
                    value={rgba.r}
                    onChange={(e) => handleRgbChange("r", e.target.value)}
                    className="h-8 w-20"
                  />
                </div>
                <div>
                  <Input
                    type="number"
                    min={0}
                    max={255}
                    value={rgba.g}
                    onChange={(e) => handleRgbChange("g", e.target.value)}
                    className="h-8 w-20"
                  />
                </div>
                <div>
                  <Input
                    type="number"
                    min={0}
                    max={255}
                    value={rgba.b}
                    onChange={(e) => handleRgbChange("b", e.target.value)}
                    className="h-8 w-20"
                  />
                </div>
              </div>
            )}

            {format === "hsb" && (
              <div className="flex items-center gap-2">
                <div>
                  <Input
                    type="number"
                    min={0}
                    max={360}
                    value={hsba.h}
                    onChange={(e) => handleHsbChange("h", e.target.value)}
                    className="h-8 w-20"
                  />
                </div>
                <div>
                  <Input
                    type="number"
                    min={0}
                    max={100}
                    value={hsba.s}
                    onChange={(e) => handleHsbChange("s", e.target.value)}
                    className="h-8 w-20"
                  />
                </div>
                <div>
                  <Input
                    type="number"
                    min={0}
                    max={100}
                    value={hsba.b}
                    onChange={(e) => handleHsbChange("b", e.target.value)}
                    className="h-8 w-20"
                  />
                </div>
              </div>
            )}

            <Input
              type="number"
              min={0}
              max={100}
              value={rgba.a}
              className="h-8 w-20 "
              onChange={(e) => handleRgbChange("a", e.target.value)}
              containerClassName="!space-y-0"
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

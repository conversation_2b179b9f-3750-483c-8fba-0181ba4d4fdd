import { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TagInput } from "emblor";
import { RefreshCw } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useBrands, useCategories } from "@/features/products/hooks/product";
import type {
  BasicInformationValues,
  CreateImage as ProductImage,
} from "@/features/products/hooks/types";
import { basicInformationSchema } from "@/features/products/utils/validators/basic-information";

import OptimizeBtn from "@/assets/images/OptimizeBtn.png";
import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "@/components/ui/use-toast";
import { mediaApi } from "@/lib/apis/media";
import { optimizeProduct, productApi } from "@/lib/apis/product";

import { QUERY_KEYS } from "../hooks/keys";
import { AddBrandDialog } from "./dialogs/add-brand-dialog";
import { AddCategoryDialog } from "./dialogs/add-category-dialog";
import { AIOptimize } from "./dialogs/ai-optimize";

interface BasicInformationProps {
  onChange: (values: BasicInformationValues) => void;
  onImagesChange: (images: ProductImage[]) => void;
  onValidationChange?: (errors: { field: string; message: string }[]) => void;
  showValidation?: boolean;
  onSectionFocus?: () => void;
  initialValues?: Partial<BasicInformationValues>;
  isEditing?: boolean;
}

interface OptimizedData {
  title: string;
  description: string;
  improvements: { title: string; description: string };
  seo_impact: string;
  readability_score: string;
}

export function BasicInformation({
  onChange,
  onImagesChange,
  onValidationChange,
  showValidation = false,
  onSectionFocus,
  initialValues,
  isEditing = false,
}: BasicInformationProps) {
  const { t } = useTranslation();
  const [tags, setTags] = useState<{ id: string; text: string }[]>(() =>
    (initialValues?.tags ? initialValues.tags.split(",") : []).map((tag) => ({
      id: crypto.randomUUID(),
      text: tag,
    }))
  );
  const [activeTagIndex, setActiveTagIndex] = useState<number | null>(null);
  const [addBrandOpen, setAddBrandOpen] = useState(false);
  const [addCategoryOpen, setAddCategoryOpen] = useState(false);
  const [newItemName, setNewItemName] = useState("");
  const [, setShowNameWarning] = useState(false);
  const [, setShowDescriptionWarning] = useState(false);
  const [optimizeOpen, setOptimizeOpen] = useState(false);
  const [hasOptimized, setHasOptimized] = useState(false);
  const [optimizeLoading, setOptimizeLoading] = useState(false);
  const [optimizedData, setOptimizedData] = useState<OptimizedData | null>(null);
  const queryClient = useQueryClient();

  const form = useZodForm({
    schema: basicInformationSchema,
    defaultValues: {
      name: initialValues?.name || "",
      description: initialValues?.description || "",
      shortDescription: initialValues?.shortDescription || "",
      sku: initialValues?.sku || "",
      brand: typeof initialValues?.brand === "object" ? initialValues.brand : null,
      category: typeof initialValues?.category === "object" ? initialValues.category : null,
      tags: initialValues?.tags ? initialValues.tags.split(",") : [],
      images: initialValues?.images || [],
    },
    mode: "onChange",
  });

  const {
    data: brandsData,
    fetchNextPage: fetchNextBrands,
    hasNextPage: hasNextBrands,
    isFetchingNextPage: isFetchingNextBrands,
  } = useBrands();

  const {
    data: categoriesData,
    fetchNextPage: fetchNextCategories,
    hasNextPage: hasNextCategories,
    isFetchingNextPage: isFetchingNextCategories,
  } = useCategories();

  // Create brand mutation
  const createBrandMutation = useMutation({
    mutationFn: async (data: { name: string; image: string | null }) => {
      return await productApi.createBrand({
        name: data.name,
        image: data.image ? { name: "image", image: data.image, prefix: "media" } : null,
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.BRANDS });
      form.setValue("brand", { id: data.id, name: data.name });
      setAddBrandOpen(false);
    },
  });

  // Create category mutation
  const createCategoryMutation = useMutation({
    mutationFn: async (data: {
      name: string;
      parent_category_id: string | null;
      image: string | null;
    }) => {
      return await productApi.createCategory({
        name: data.name,
        image: data.image ? { name: "image", image: data.image, prefix: "media" } : null,
        parent_category_id: data.parent_category_id,
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CATEGORIES });
      form.setValue("category", { id: data.id, name: data.name });
      setAddCategoryOpen(false);
    },
  });

  // Watch form errors and notify parent component
  useEffect(() => {
    if (onValidationChange) {
      // Get current form values
      const currentValues = {
        name: form.getValues("name"),
        sku: form.getValues("sku"),
        images: form.getValues("images"),
        description: form.getValues("description"),
        shortDescription: form.getValues("shortDescription"),
        brand: form.getValues("brand"),
        category: form.getValues("category"),
        tags: form.getValues("tags"),
      };

      // Validate using schema
      const result = basicInformationSchema.safeParse(currentValues);

      // If validation is successful, clear errors
      if (result.success) {
        onValidationChange([]);
      } else {
        // Map Zod errors to our format
        const errors = result.error.errors.map((zodError) => ({
          field: zodError.path[0].toString(),
          message: zodError.message,
        }));
        onValidationChange(errors);
      }
    }
  }, [form, onValidationChange, form.formState.errors]);

  // Trigger validation when showValidation changes
  useEffect(() => {
    if (showValidation) {
      form.trigger().catch(console.error);
    }
  }, [showValidation, form]);

  // Watch form values and notify parent component of changes
  useEffect(() => {
    const subscription = form.watch((value) => {
      const formattedValue = {
        ...value,
        tags: Array.isArray(value.tags) ? value.tags.join(",") : value.tags,
      };
      onChange(formattedValue as BasicInformationValues);
    });
    return () => subscription.unsubscribe();
  }, [form, onChange]);

  const handleOptimizeClick = async () => {
    const name = form.getValues("name");
    const description = form.getValues("description");

    if (!name) {
      setShowNameWarning(true);
      return;
    }
    if (!description) {
      setShowDescriptionWarning(true);
      return;
    }

    if (!hasOptimized) {
      setOptimizeLoading(true);
      try {
        const response = await optimizeProduct({
          title: name,
          description: description,
          target_marketplace: "amazon" as "amazon" | "ebay" | "general",
          max_title_length: 200,
        });

        setOptimizedData({
          title: response?.result?.optimized?.title || "",
          description: response?.result?.optimized?.description || "",
          improvements: {
            title: response?.result?.analysis?.improvements?.title || "",
            description: response?.result?.analysis?.improvements?.description || "",
          },
          seo_impact: response?.result?.analysis?.seo_impact || "",
          readability_score: response?.result?.analysis?.readability_score || "",
        });
        setHasOptimized(true);
        setOptimizeOpen(true);
      } catch {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to optimize content. Please try again.",
        });
      } finally {
        setOptimizeLoading(false);
      }
    } else {
      setOptimizeOpen(true);
    }
  };

  const handleOptimizedContent = (title: string, description: string) => {
    form.setValue("name", title);
    form.setValue("description", description);
  };

  const onOptimized = useCallback(() => {
    setHasOptimized(true);
  }, []);

  const handleTagsChange = (
    value:
      | { id: string; text: string }[]
      | ((prev: { id: string; text: string }[]) => { id: string; text: string }[])
  ) => {
    const newTags = typeof value === "function" ? value(tags) : value;
    if (newTags.length <= 10) {
      setTags(newTags);
      const tagsString = newTags.map((tag) => tag.text).join(",");
      form.setValue("tags", tagsString);
    } else {
      toast({
        variant: "destructive",
        title: "Tag limit reached",
        description: "You can only add up to 10 tags",
      });
    }
  };

  const handleImageUpload = async (base64: string | null) => {
    if (!base64) {
      form.setValue("images", []);
      onImagesChange([]);
      return;
    }

    try {
      const images = JSON.parse(base64) as string[];

      if (isEditing) {
        // Get current images
        const currentImages = form.getValues("images") || [];

        // Process new images
        const processedImages = await Promise.all(
          images.map(async (image) => {
            // Check if this is an existing image (has a URL format) or a new base64 image
            if (image.startsWith("http://") || image.startsWith("https://")) {
              // This is an existing image, find its original data
              const existingImage = currentImages.find((img: ProductImage) => img.image === image);
              if (existingImage) {
                return existingImage;
              }
            }

            // This is a new image that needs to be uploaded
            const nameMatch = image.match(/;name=(.*?)(;|$)/);
            const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : "image.jpg";

            try {
              // Upload to API
              const response = await mediaApi.uploadImage({
                prefix: "media",
                name: filename,
                image: image,
              });

              // Return in CreateImage format
              return {
                name: filename,
                image: response.url,
              };
            } catch (uploadError) {
              console.error("Error uploading individual image:", uploadError);
              throw uploadError;
            }
          })
        );

        const newImages = processedImages;

        // Update form and notify parent
        form.setValue("images", newImages);
        onImagesChange(newImages);
      } else {
        // In add mode, use local images
        const processedImages = images.map((image) => {
          const nameMatch = image.match(/;name=(.*?)(;|$)/);
          const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : "image.jpg";

          return {
            name: filename,
            image: image,
          };
        });

        // We always use the images directly from the callback, not appending to existing
        // This ensures remove operations work correctly as well
        const newImages = processedImages;

        // Update form and notify parent
        form.setValue("images", newImages);
        onImagesChange(newImages);
      }
    } catch (error) {
      console.error("Error in handleImageUpload:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to process images. Please try again.",
      });
    }
  };

  return (
    <Form {...form}>
      <form className="space-y-4" onChange={() => onSectionFocus?.()}>
        <FormField
          control={form.control}
          name="images"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {t("pages.products.addManual.basicInfo.images")}{" "}
                <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <ImageUpload
                  value={
                    field.value.length > 0
                      ? JSON.stringify(field.value.map((img: { image: string }) => img.image))
                      : null
                  }
                  onChange={handleImageUpload}
                  multiple
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="name"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>
                {t("pages.products.addManual.basicInfo.name")}{" "}
                <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  error={fieldState.error?.message}
                  placeholder={t("pages.products.addManual.basicInfo.name")}
                  onChange={(e) => {
                    const value = e.target.value.slice(0, 250);
                    field.onChange(value);
                    if (value) setShowNameWarning(false);
                  }}
                />
              </FormControl>
              <p className="mt-1 text-xs text-muted-foreground">{field.value.length}/250</p>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("pages.products.addManual.basicInfo.description")}</FormLabel>
              <div className="relative">
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder={t("pages.products.addManual.basicInfo.description")}
                    onChange={(e) => {
                      const value = e.target.value.slice(0, 1000);
                      field.onChange(value);
                      if (value) setShowDescriptionWarning(false);
                    }}
                    className="min-h-[120px]"
                  />
                </FormControl>
                <div className="flex items-center justify-between">
                  <p className="mt-1 text-xs text-muted-foreground">{field.value.length}/1000</p>
                  <div className="absolute bottom-2 right-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="outline"
                            className="mb-6 ml-6 size-[32px] p-0 opacity-70 transition-opacity hover:opacity-100"
                            onClick={handleOptimizeClick}
                            disabled={optimizeLoading}>
                            {optimizeLoading ? (
                              <RefreshCw className="size-4 animate-spin" />
                            ) : (
                              <Image src={OptimizeBtn} alt="Optimize" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent className="bg-primary text-white">
                          <p className="text-sm">
                            Please provide both the product name and description before opening the
                            modal.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="shortDescription"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("pages.products.addManual.basicInfo.shortDescription")}</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder={t("pages.products.addManual.basicInfo.shortDescription")}
                  onChange={(e) => {
                    const value = e.target.value.slice(0, 400);
                    field.onChange(value);
                  }}
                />
              </FormControl>
              <p className="mt-1 text-xs text-muted-foreground">{field.value.length}/400</p>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="brand"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("pages.products.addManual.basicInfo.brand")}</FormLabel>
              <FormControl>
                <Combobox
                  value={field.value?.id || ""}
                  onValueChange={(value) => {
                    const selectedBrand = brandsData?.pages
                      .flatMap((page) => page.items)
                      .find((item) => item.id === value);

                    if (selectedBrand) {
                      field.onChange({ id: selectedBrand.id, name: selectedBrand.name });
                    } else {
                      field.onChange(null);
                    }
                  }}
                  items={
                    brandsData?.pages.flatMap((page) =>
                      page.items.map((brand) => ({
                        id: brand.id,
                        name: brand.name,
                        displayValue: brand.name,
                      }))
                    ) || []
                  }
                  placeholder={t("pages.products.addManual.basicInfo.brandPlaceholder")}
                  searchPlaceholder={t("pages.products.addManual.basicInfo.brandSearchPlaceholder")}
                  emptyText={t("pages.products.addManual.basicInfo.brandEmptyText")}
                  onCreateNew={(name) => {
                    setNewItemName(name);
                    setAddBrandOpen(true);
                  }}
                  onLoadMore={fetchNextBrands}
                  hasNextPage={hasNextBrands}
                  isLoadingMore={isFetchingNextBrands}
                  onClear={() => field.onChange(null)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("pages.products.addManual.basicInfo.category")}</FormLabel>
              <FormControl>
                <Combobox
                  value={field.value?.id || ""}
                  onValueChange={(value) => {
                    const selectedCategory = categoriesData?.pages
                      .flatMap((page) => page.items)
                      .find((item) => item.id === value);

                    if (selectedCategory) {
                      field.onChange({ id: selectedCategory.id, name: selectedCategory.name });
                    } else {
                      field.onChange(null);
                    }
                  }}
                  items={
                    categoriesData?.pages.flatMap((page) =>
                      page.items.map((category) => ({
                        id: category.id,
                        name: category.name,
                        displayValue: category.name,
                      }))
                    ) || []
                  }
                  placeholder={t("pages.products.addManual.basicInfo.categoryPlaceholder")}
                  searchPlaceholder={t(
                    "pages.products.addManual.basicInfo.categorySearchPlaceholder"
                  )}
                  emptyText={t("pages.products.addManual.basicInfo.categoryEmptyText")}
                  onCreateNew={(name) => {
                    setNewItemName(name);
                    setAddCategoryOpen(true);
                  }}
                  onLoadMore={fetchNextCategories}
                  hasNextPage={hasNextCategories}
                  isLoadingMore={isFetchingNextCategories}
                  onClear={() => field.onChange(null)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="sku"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>
                {t("pages.products.addManual.basicInfo.sku")}{" "}
                <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  error={fieldState.error?.message}
                  placeholder={t("pages.products.addManual.basicInfo.sku")}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="tags"
          render={({ field }) => (
            <FormItem className="[&>label]:text-foreground">
              <FormLabel>{t("pages.products.addManual.basicInfo.tags")}</FormLabel>
              <FormControl>
                <div>
                  <TagInput
                    {...field}
                    placeholder={t("pages.products.addManual.basicInfo.tagsPlaceholder")}
                    tags={tags}
                    setTags={handleTagsChange}
                    activeTagIndex={activeTagIndex}
                    setActiveTagIndex={setActiveTagIndex}
                    styleClasses={{
                      tag: {
                        body: "bg-muted text-bg-background py-0.5",
                      },
                      input: "bg-transparent p-0 ps-0 shadow-none",
                      inlineTagsContainer:
                        "bg-transparent rounded-md px-3 py-2 text-sm gap-1 border border-border",
                    }}
                    truncate={20}
                    maxLength={30}
                    maxTags={10}
                    shape={"rounded"}
                  />
                  <p className="mt-1 text-xs text-muted-foreground">{tags.length}/10</p>
                </div>
              </FormControl>
            </FormItem>
          )}
        />

        <AddBrandDialog
          open={addBrandOpen}
          onOpenChange={setAddBrandOpen}
          onSubmit={async (data) => {
            await createBrandMutation.mutateAsync({
              name: data.name,
              image: data.image?.url || null,
            });
          }}
          initialName={newItemName}
        />

        <AddCategoryDialog
          open={addCategoryOpen}
          onOpenChange={setAddCategoryOpen}
          onSubmit={async (data) => {
            await createCategoryMutation.mutateAsync({
              name: data.name,
              parent_category_id: data.parent_category_id,
              image: data.image?.url || null,
            });
          }}
          categories={categoriesData?.pages[0]?.items || []}
          initialName={newItemName}
        />

        <AIOptimize
          open={optimizeOpen}
          onOpenChange={setOptimizeOpen}
          productName={form.getValues("name")}
          description={form.getValues("description")}
          onReplace={handleOptimizedContent}
          hasOptimized={hasOptimized}
          onOptimized={onOptimized}
          initialData={optimizedData}
        />
      </form>
    </Form>
  );
}

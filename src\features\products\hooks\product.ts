import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import type { CreateProduct, Product } from "@/features/products/hooks/types";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { priceGroupApi, productApi } from "@/lib/apis/product";
import { ResponseList } from "@/lib/apis/types/common";

import { IGetProductsParams, productKeys, QUERY_KEYS } from "./keys";

interface UseProductsOptions extends Partial<IGetProductsParams> {
  enabled?: boolean;
}

export function useProducts(options: UseProductsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();
  const query = useInfiniteQuery({
    queryKey: productKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      productApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const products = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteProductMutation = useMutation({
    mutationFn: async (id: string) => {
      return productApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Product>>>(
        productKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Product deleted successfully");
    },
    onError: () => {
      toast.error("Product deletion failed");
    },
  });

  const useDeleteListProductMutation = useMutation({
    mutationFn: async (objectData: any) => {
      // Khi TypeScript hiểu rõ kiểu của `variables`, sẽ không báo lỗi nữa
      return productApi.deleteListProducts(objectData.listId); // đảm bảo API trả về đúng kiểu `Product`
    },
    onSuccess: (_, objectDeletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Product>>>(
        productKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => !objectDeletedId.listId.includes(item.id)),
            total: page.total - objectDeletedId.listId.length,
          }));
          objectDeletedId?.handleRestRows();
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Product deleted successfully");
    },
    onError: () => {
      toast.error("Product deletion failed");
    },
  });

  return {
    ...query,
    products,
    total,
    useDeleteProductMutation,
    useDeleteListProductMutation,
  };
}

interface UseAddProductOptions {
  onSuccess?: (data: Product) => void;
  onError?: (error: Error) => void;
}

export function useAddProduct(options: UseAddProductOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation<Product, Error, CreateProduct>({
    mutationFn: async (data: CreateProduct) => {
      const response = await productApi.create(data);
      return response;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      onSuccess?.(data);
    },
    onError,
  });
}

interface UpdateProductPayload {
  id: string;
  data: Partial<Product>;
}

interface UseUpdateProductOptions {
  onSuccess?: (data: Product) => void;
  onError?: (error: Error) => void;
}

export function useUpdateProduct(options: UseUpdateProductOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: UpdateProductPayload) => {
      const response = await productApi.update(id, data);
      return response;
    },
    onSuccess: (updatedProduct) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Product>>>(
        productKeys.lists(),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.map((item) =>
              item.id === updatedProduct.id ? updatedProduct : item
            ),
          }));
          return { ...oldData, pages: newPages };
        }
      );
      queryClient.setQueryData(productKeys.detail(updatedProduct.id), updatedProduct);
      onSuccess?.(updatedProduct);
    },
    onError,
  });
}

export const useBrands = () => {
  return useInfiniteQuery({
    queryKey: productKeys.brands(),
    queryFn: async ({ pageParam = 0 }) => {
      const response = await productApi.getBrands({
        limit: 20,
        page: Number(pageParam),
      });
      return response;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      if (lastPage.items.length < 20) return undefined;
      return Number(lastPage.page) + 1;
    },
  });
};

export const useCategories = () => {
  return useInfiniteQuery({
    queryKey: productKeys.categories(),
    queryFn: async ({ pageParam = 0 }) => {
      const response = await productApi.getCategories({
        limit: 20,
        page: Number(pageParam),
      });
      return response;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      if (lastPage.items.length < 20) return undefined;
      return Number(lastPage.page) + 1;
    },
  });
};

export function useProduct(id: string) {
  return useQuery<Product>({
    queryKey: productKeys.detail(id),
    queryFn: async () => {
      const response = await productApi.getById(id);
      return response;
    },
    enabled: !!id,
  });
}

interface UseDeleteProductOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useDeleteProduct(options: UseDeleteProductOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await productApi.delete(id);
      // console.log("Deletion response:", response);
      return response;
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<ResponseList<Product>>(productKeys.lists(), (oldData) => {
        if (!oldData) return oldData;
        const newData = {
          ...oldData,
          items: oldData.items.filter((item) => item.id !== deletedId),
          total: oldData.total - 1,
        };
        // console.log("newData", newData);
        return newData;
      });
      // queryClient.invalidateQueries({ queryKey: productKeys.lists() });
      onSuccess?.();
    },
    onError,
  });
}

export function usePriceGroups() {
  return useQuery({
    queryKey: QUERY_KEYS.PRICE_GROUPS,
    queryFn: async () => {
      const response = await priceGroupApi.list();
      return response;
    },
  });
}

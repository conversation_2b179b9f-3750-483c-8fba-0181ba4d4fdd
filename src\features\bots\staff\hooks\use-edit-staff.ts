import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { uploadImage } from "@/lib/apis/images";

import {
  getDefaultFormValues,
  mapStaffToFormValues,
  staffFormSchema,
  StaffFormValues,
} from "./form";
import { useStaffDetail, useUpdateStaff } from "./staff";

// Helper function to check if an object path is dirty
const isPathDirty = (dirtyFields: object, path: string): boolean => {
  return Object.keys(dirtyFields).some((field) => field.startsWith(path));
};

// Helper function to check if any personality fields are dirty
const hasAnyDirtyField = (personalityDirty: {
  tone?: boolean;
  language?: boolean;
  personal_trait?: {
    formality?: boolean;
    detailed?: boolean;
    creativity?: boolean;
  };
}): boolean => {
  return !!(
    personalityDirty.tone ||
    personalityDirty.language ||
    personalityDirty.personal_trait?.formality ||
    personalityDirty.personal_trait?.detailed ||
    personalityDirty.personal_trait?.creativity
  );
};

export const useEditStaff = (staffId: string) => {
  const router = useRouter();
  const { data: staff, isLoading, refetch } = useStaffDetail(staffId);
  const { t } = useTranslation();
  const [isImageUploading, setIsImageUploading] = useState(false);

  const updateStaffMutation = useUpdateStaff({
    onSuccess: async (data) => {
      toast.success(t("common.staffUpdated"));
      // Refetch staff data to reset form state
      await refetch();
    },
    onError: () => {
      toast.error(t("common.updateStaffError"));
    },
  });

  const form = useForm<StaffFormValues>({
    resolver: zodResolver(staffFormSchema),
    defaultValues: staff ? mapStaffToFormValues(staff!) : getDefaultFormValues(),
  });

  const onSubmit = async (data: StaffFormValues) => {
    console.log(data);
    try {
      const dirtyFields = form.formState.dirtyFields;
      const payload: any = {};

      // Handle image upload if image is dirty and is a base64 string
      if (dirtyFields.staffInfo?.image && data.staffInfo.image) {
        const imageData = data.staffInfo.image;
        if (imageData.image.startsWith("data:")) {
          try {
            setIsImageUploading(true);
            // Extract the filename if present in the base64 string
            const nameMatch = imageData.image.match(/;name=(.*?)(;|$)/);
            const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : imageData.name;

            // Upload to API
            const response = await uploadImage({
              prefix: "media",
              name: filename,
              image: imageData.image,
            });

            // Update the image data with the uploaded URL
            imageData.image = response.url;
            imageData.name = filename;
          } catch (error) {
            console.error("Error uploading staff image:", error);
            toast.error(t("common.imageUploadError") || "Failed to upload image");
            setIsImageUploading(false);
            return;
          } finally {
            setIsImageUploading(false);
          }
        }
      }

      // Only include STYLES if personality fields are dirty
      if (isPathDirty(dirtyFields, "personality")) {
        const personalityDirty = {
          tone: dirtyFields.personality?.tone,
          language: dirtyFields.personality?.language,
          personal_trait: dirtyFields.personality?.personal_trait && {
            formality: dirtyFields.personality?.personal_trait?.formality,
            detailed: dirtyFields.personality?.personal_trait?.detailed,
            creativity: dirtyFields.personality?.personal_trait?.creativity,
          },
        };

        if (hasAnyDirtyField(personalityDirty)) {
          payload.STYLES = {
            configuration: {
              personality: {
                ...(dirtyFields.personality?.tone && { tone: data.personality.tone }),
                ...(dirtyFields.personality?.language && { language: data.personality.language }),
                ...(dirtyFields.personality?.personal_trait && {
                  personal_trait: {
                    ...(dirtyFields.personality?.personal_trait?.formality && {
                      formality: data.personality.personal_trait.formality,
                    }),
                    ...(dirtyFields.personality?.personal_trait?.detailed && {
                      detailed: data.personality.personal_trait.detailed,
                    }),
                    ...(dirtyFields.personality?.personal_trait?.creativity && {
                      creativity: data.personality.personal_trait.creativity,
                    }),
                  },
                }),
              },
            },
          };
        }
      }

      // Only include KNOWLEDGE if knowledge fields are dirty
      if (isPathDirty(dirtyFields, "knowledge_base")) {
        payload.KNOWLEDGE = {
          configuration: {
            knowledge_base: {
              ...(dirtyFields.knowledge_base?.knowledge_list && {
                knowledge_ids: data.knowledge_base.knowledge_list.map((k) => k.id),
              }),
              ...(dirtyFields.knowledge_base?.domain_expertise_ids && {
                domain_expertise_ids: data.knowledge_base.domain_expertise_ids,
              }),
            },
          },
        };
      }

      // Only include TASK if conversation tasks are dirty
      if (isPathDirty(dirtyFields, "conversation.tasks")) {
        payload.TASK = {
          task_ids: data.conversation.tasks.map((task) => task.id.toString()),
        };
      }

      // Only include SKILL if staffInfo fields are dirty
      if (isPathDirty(dirtyFields, "staffInfo")) {
        payload.INFORMATION = {
          ...(dirtyFields.staffInfo?.name && { name: data.staffInfo.name }),
          ...(dirtyFields.staffInfo?.image &&
            data.staffInfo.image && {
              image: {
                name: data.staffInfo.image.name,
                image: data.staffInfo.image.image,
              },
            }),
          ...(dirtyFields.staffInfo?.role && { role: data.staffInfo.role }),
          ...(dirtyFields.staffInfo?.department && { department_id: data.staffInfo.department }),
        };
      }

      // Only submit if there are dirty fields
      if (Object.keys(payload).length > 0) {
        await updateStaffMutation.mutateAsync({ id: staffId, data: payload });
      } else {
        toast.info("No changes to save");
      }
    } catch (error) {
      console.error("Error saving staff:", error);
      toast.error("An error occurred while saving staff");
    }
  };

  const handleCancel = () => {
    router.push(`/staff/${staffId}/interact`);
  };

  const isTabDirty = (tabId: string) => {
    const dirtyFields = form.formState.dirtyFields;
    // console.log(dirtyFields?.knowledge_base?.knowledge_list === (true as any));
    switch (tabId) {
      case "staff-info":
        return (
          Object.keys(dirtyFields).some((field) => field.startsWith("staffInfo")) ||
          dirtyFields?.knowledge_base?.domain_expertise_ids === (true as any)
        );
      case "interaction-style":
        return Object.keys(dirtyFields).some((field) => field.startsWith("personality"));
      case "knowledge":
        return dirtyFields?.knowledge_base?.knowledge_list === (true as any);
      case "skills":
        return Object.keys(dirtyFields).some((field) => field.startsWith("tools"));
      case "task":
        return Object.keys(dirtyFields).some((field) => field.startsWith("conversation"));
      default:
        return false;
    }
  };

  return {
    form,
    staff,
    isLoading,
    onSubmit,
    handleCancel,
    isTabDirty,
    isUpdating: updateStaffMutation.isPending || isImageUploading,
  };
};

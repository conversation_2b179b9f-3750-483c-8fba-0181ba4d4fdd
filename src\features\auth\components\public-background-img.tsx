interface BackgroundImageProps {
  className?: string;
  src: string;
  position?: string;
  opacity?: number;
}

export const BackgroundImage = ({
  className,
  src,
  position = "center",
  opacity = 1,
}: BackgroundImageProps) => {
  return (
    <div
      className={`absolute inset-y-0 right-0 z-0 w-1/2 ${className}`}
      style={{
        backgroundImage: `url('${src}')`,
        backgroundSize: "contain",
        backgroundPosition: position,
        backgroundRepeat: "no-repeat",
        opacity,
      }}
    />
  );
};

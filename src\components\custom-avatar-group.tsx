import React from "react";

import { Avatar, AvatarImage } from "@/components/ui/avatar";

interface CustomAvatarGroupProps {
  images: (string | undefined)[];
  size?: "sm" | "md" | "lg";
  ariaLabel?: string;
}

const sizeClasses = {
  sm: "w-6 h-6 text-xs",
  md: "w-6 h-6 text-xs",
  lg: "w-6 h-6 text-xs",
};

/**
 * CustomAvatarGroup
 * A reusable component that displays a group of avatars with overlap effect
 */
const CustomAvatarGroup: React.FC<CustomAvatarGroupProps> = ({
  images,
  size = "md",
  ariaLabel = "Avatar group",
}) => {
  return (
    <div className="flex -space-x-2.5" role="group" aria-label={ariaLabel}>
      {images.slice(0, 3).map((image, index) => (
        <div key={index} className="relative overflow-hidden" style={{ zIndex: index + 1 }}>
          <Avatar
            className={`${sizeClasses[size]} rounded-full border-2 border-bg-primary bg-muted`}>
            <AvatarImage src={image || undefined} className="rounded-full" />
          </Avatar>
          {/* Overlay +N on the third avatar if needed */}
          {index === 2 && images.length > 3 && (
            <span
              className={`absolute inset-0 flex items-center justify-center rounded-full text-xs font-normal text-white ${sizeClasses[size]}`}
              style={{ background: "rgba(0,0,0,0.35)" }}>
              +{images.length - 2}
            </span>
          )}
        </div>
      ))}
    </div>
  );
};

export default CustomAvatarGroup;

import * as z from "zod";

// Define the order form schema
export const orderFormSchema = z.object({
  customer: z.any().nullable(),
  billing_address: z.any().nullable(),
  location: z.any().nullable(),
  staff: z.any().nullable(),
  source: z.any().nullable(),
  shipping_address: z.any().nullable(),
  order_line_items: z.array(z.any()),
  note: z.string().default(""),
  tags: z.string().default(""),
  total: z.number().default(0),
  sub_total: z.number().default(0),
  discount: z.number().default(0),
  payments: z.any().nullable(),
  other_fees: z.any().nullable(),
  shipping_fee: z.number().default(0),
  tax: z.number().default(0),
  discount_id: z.string().nullable(),
  discount_by_customer_group: z.number().nullable(),
  redeem_point: z.any().nullable(),
  reward_program: z.any().nullable(),
});

export type OrderFormValues = z.infer<typeof orderFormSchema>;

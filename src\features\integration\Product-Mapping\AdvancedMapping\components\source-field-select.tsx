import { useTranslation } from "react-i18next";

import { Combobox } from "@/components/ui/combobox";

import { SOURCE_FIELDS } from "../hooks/types";

interface SourceFieldSelectProps {
  value: string;
  onChange: (value: string) => void;
  sourceData?: any;
  sourceFields?: string[];
}

export function SourceFieldSelect({
  value,
  onChange,
  sourceData,
  sourceFields = [],
}: SourceFieldSelectProps) {
  const { t } = useTranslation();

  // Start with default SOURCE_FIELDS
  let sourceFieldItems = SOURCE_FIELDS.map((field: { id: string; name: string }) => ({
    id: field.id,
    name: field.name,
  }));

  // If explicit sourceFields are provided (from the API), use those instead
  if (sourceFields && sourceFields.length > 0) {
    // Create field items from the API fields
    const apiFields = sourceFields.map((field) => ({
      id: field,
      name: field.charAt(0).toUpperCase() + field.slice(1), // Capitalize first letter
    }));

    // Replace default fields with API fields
    sourceFieldItems = apiFields;
  }
  // Fallback: If no sourceFields but we have sourceData, try to extract fields from it
  else if (sourceData) {
    // Based on the screenshot, standard_source_data has direct properties
    if (typeof sourceData === "object" && sourceData !== null) {
      const fields = Object.keys(sourceData).filter((key) => key !== "variants");

      if (fields.length > 0) {
        const extractedFields = fields.map((field) => ({
          id: field,
          name: field.charAt(0).toUpperCase() + field.slice(1), // Capitalize first letter
        }));

        sourceFieldItems = extractedFields;
      }
    }
  }
  return (
    <div className="flex flex-col gap-2">
      <label className="text-sm font-medium">
        {t("pages.productMapping.advancedMapping.sourceField")}
      </label>
      <Combobox
        value={value}
        onValueChange={onChange}
        items={sourceFieldItems}
        placeholder={t("pages.productMapping.advancedMapping.selectFieldsPlaceholder")}
        searchPlaceholder={t("pages.productMapping.advancedMapping.searchFieldsPlaceholder")}
      />
    </div>
  );
}

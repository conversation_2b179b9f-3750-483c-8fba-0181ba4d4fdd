import { Skeleton } from "@/components/ui";

export default function KnowledgePanelSkeleton() {
  return (
    <div className=" rounded-md flex-1  space-y-3">
      <Skeleton className="h-5 w-1/4" />
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex items-center gap-2">
          <Skeleton className="h-6 w-6 flex-none rounded" />
          <Skeleton className="h-6 flex-auto" />
          <Skeleton className="h-6 w-12 rounded" />
          <Skeleton className="h-6 w-8 rounded" />
        </div>
      ))}
    </div>
  );
}

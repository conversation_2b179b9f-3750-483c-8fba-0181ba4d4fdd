import Link from "next/link";
import { Row } from "@tanstack/react-table";
import { MapPin, Phone, User } from "lucide-react";

import { useOrderDetail } from "@/features/orders/hooks/order";

import { Card } from "@/components/ui";
import { CustomImage } from "@/components/ui/image";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Order } from "../hooks/types";

const formatNumber = (value: number): string => {
  return value.toLocaleString("en-US", { maximumFractionDigits: 0 });
};

const getFullAddress = (address: any) => {
  if (!address) return "";
  const parts = [
    address.address1,
    address.ward,
    address.district,
    address.city,
    address.province,
  ].filter(Boolean);
  return parts.join(", ");
};

export default function OrderRowExpand({ row }: { row: Row<Order> }) {
  const order = row.original;
  const { order: orderDetail } = useOrderDetail(order.id);

  return (
    <div className="w-full">
      <div className="grid gap-2 md:grid-cols-[300px_1fr]">
        {/* Left Side - Customer Information */}
        <div className="space-y-2">
          <Card className="px-4 py-2">
            <div className="space-y-2">
              <div className="flex items-center gap-2 font-medium">
                <User className="size-4" />
                <Link href={`/customers/${order.customer.id}`}>
                  <span className="cursor-pointer text-sm text-primary hover:underline">
                    {order.customer.first_name}
                  </span>
                </Link>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Phone className="size-4" />
                {order.customer.phone}
              </div>
              <div className="flex items-start gap-2">
                <MapPin className="mt-1 size-4" />
                <div className="line-clamp-2 text-sm">
                  {orderDetail?.shipping_address
                    ? getFullAddress(orderDetail.shipping_address)
                    : "--"}
                </div>
              </div>
            </div>
          </Card>

          <Card className="px-4 py-2">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-muted-foreground">Total amount (₫)</div>
              <div className="text-sm font-semibold">{formatNumber(parseFloat(order.total))}</div>
            </div>
          </Card>

          <Card className="p-0">
            <div className="px-4 py-2">
              <div className="font-medium text-muted-foreground">Notes</div>
            </div>
            <div className="max-h-[110px] overflow-y-auto">
              <div className="px-4 pb-2">
                <div className="text-sm">{order.note || "--"}</div>
              </div>
            </div>
          </Card>
        </div>

        {/* Right Side - Product List */}
        <Card className="p-0">
          <div className="max-h-[290px] overflow-y-auto rounded-lg border-t">
            <Table>
              <TableHeader className="sticky top-0 border-b bg-card">
                <TableRow>
                  <TableHead className="w-1/2">Product Information</TableHead>
                  <TableHead className="w-[10%] text-center">Qty</TableHead>
                  <TableHead className="w-1/5 text-right">Price (₫)</TableHead>
                  <TableHead className="w-1/5 text-right">Total Amount (₫)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.order_line_items.map((product) => (
                  <TableRow key={product.sku}>
                    <TableCell className="w-2/5">
                      <div className="flex items-center gap-3">
                        <div className="size-10 shrink-0 overflow-hidden rounded bg-muted">
                          <CustomImage
                            src={product.image_url || ""}
                            alt={product.id}
                            width={40}
                            height={40}
                            className="size-10 object-cover"
                          />
                        </div>
                        <div className="min-w-0 flex-1">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-default truncate font-medium">
                                  {product.name}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top" className="max-w-[300px]">
                                <p>{product.name}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-default truncate text-sm text-muted-foreground">
                                  SKU: {product.sku}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p>SKU: {product.sku}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="w-1/5 text-center">{product.quantity}</TableCell>
                    <TableCell className="w-1/5 text-right">
                      <div>{formatNumber(parseFloat(product.unit_price))}</div>
                      {parseFloat(product.discount) > 0 && (
                        <div className="text-sm text-muted-foreground line-through">
                          {formatNumber(
                            parseFloat(product.unit_price) + parseFloat(product.discount)
                          )}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="w-1/5 text-right">
                      {formatNumber(parseFloat(product.unit_price) * product.quantity)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </Card>
      </div>
    </div>
  );
}

import React from "react";
import { useTranslation } from "react-i18next";

import { Card, CardTitle, Separator } from "@/components/ui";
import { FetchEvent } from "@/lib/apis/fetch-event";
import { dateFormatter } from "@/utils/helpers/date-formater";

import EventTabs from "./event-tabs";

interface InfoRowProps {
  label: string;
  value: string | number;
  isLast?: boolean;
}

const InfoRow: React.FC<InfoRowProps> = ({ label, value, isLast }) => {
  return (
    <div
      className={`grid grid-cols-2 gap-4 ${label !== "Status" && !isLast ? "border-b" : ""} py-2`}>
      <div className="text-base font-normal leading-6">{label}:</div>
      <div className="text-base font-medium leading-6">{value || "---"}</div>
    </div>
  );
};

interface EventItemProps {
  data: FetchEvent;
  index: number;
}

export default function EventItem({ data, index }: EventItemProps) {
  const { t } = useTranslation();

  const firstColumnData = [
    { label: t("pages.fetchEventDetail.actionGroup"), value: data?.action_group || "N/A" },
    { label: t("pages.fetchEventDetail.connectionId"), value: data?.connection_id || "N/A" },
    { label: t("pages.fetchEventDetail.actionType"), value: data?.action_type || "N/A" },
    { label: t("pages.fetchEventDetail.eventSource"), value: data?.event_source || "N/A" },
    { label: t("pages.fetchEventDetail.retryCount"), value: data?.retry_count ?? 0 },
  ];

  const secondColumnData = [
    { label: t("pages.fetchEventDetail.status"), value: data?.status || "---" },
    {
      label: t("pages.fetchEventDetail.continuationToken"),
      value: data?.continuation_token || "N/A",
    },
    { label: t("pages.fetchEventDetail.objectId"), value: data?.object_id || "N/A" },
    {
      label: t("pages.fetchEventDetail.eventTime"),
      value: data?.event_time ? dateFormatter(data.event_time) : "N/A",
    },
    {
      label: t("pages.fetchEventDetail.createdAt"),
      value: data?.created_at ? dateFormatter(data.created_at) : "N/A",
    },
    {
      label: t("pages.fetchEventDetail.updatedAt"),
      value: data?.updated_at ? dateFormatter(data.updated_at) : "N/A",
    },
  ];

  function getFlexibleFields(event: Record<string, unknown>): Record<string, unknown> | null {
    const definedKeys = new Set<keyof FetchEvent>([
      "id",
      "company_id",
      "action_type",
      "created_at",
      "updated_at",
      "event_source",
      "channel",
      "continuation_token",
      "is_batch",
      "action_group",
      "connection_id",
      "event_time",
      "batch_id",
      "object_id",
      "retry_count",
    ]);
    if (!event) return null;
    const flexibleFields = Object.keys(event)
      .filter((key) => !definedKeys.has(key as keyof FetchEvent))
      .reduce(
        (acc, key) => {
          acc[key] = event[key];
          return acc;
        },
        {} as Record<string, unknown>
      );
    const filteredFields = Object.fromEntries(
      Object.entries(flexibleFields).filter(([_, value]) => value !== null)
    );

    return Object.keys(filteredFields).length > 0 ? filteredFields : null;
  }

  const extraFields = getFlexibleFields(data as unknown as Record<string, unknown>);

  return (
    <Card className="flex max-h-full w-full flex-col">
      <div className="flex flex-none flex-col gap-4  p-4">
        <CardTitle className="text-sm font-medium">
          {t("pages.fetchEventDetail.eventNumber", { number: index + 1 })}
        </CardTitle>

        <div className="grid grid-cols-1 md:grid-cols-2 md:gap-4">
          <div className="flex flex-col px-4">
            {firstColumnData.map((item, index) => (
              <InfoRow isLast={index === firstColumnData.length - 1} key={item.label} {...item} />
            ))}
          </div>
          <div className="flex flex-col px-4">
            {secondColumnData.map((item, index) => (
              <InfoRow key={item.label} {...item} isLast={index === secondColumnData.length - 1} />
            ))}
          </div>
        </div>
      </div>
      <Separator className="!m-0" />
      <EventTabs extraFields={extraFields} />
    </Card>
  );
}

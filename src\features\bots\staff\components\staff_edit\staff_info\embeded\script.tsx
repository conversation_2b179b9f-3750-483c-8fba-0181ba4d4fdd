import React, { useEffect, useState } from "react";
import { Copy } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

interface EmbedScriptProps {
  staffId: string;
  themeColor?: string;
  baseUrl?: string;
  staffImage?: string;
}

export function EmbedScript({
  staffId,
  themeColor = "#FF9500",
  baseUrl: propBaseUrl,
  staffImage,
}: EmbedScriptProps) {
  const { t } = useTranslation();
  const [copied, setCopied] = React.useState(false);
  const [currentBaseUrl, setCurrentBaseUrl] = useState(
    propBaseUrl || "https://dev.optiwarehouse.com"
  );

  // Get the current URL on client-side
  useEffect(() => {
    // Only run on client-side
    if (typeof window !== "undefined" && !propBaseUrl) {
      const { protocol, host } = window.location;
      setCurrentBaseUrl(`${protocol}//${host}`);
    }
  }, [propBaseUrl]);

  // Generate the embed script code
  const scriptCode = `<!-- Virtual Staff Widget Script -->
<script src="${currentBaseUrl}/xbot-embed.js" data-bot-id="${staffId}" data-theme-color="${themeColor}" data-image="${staffImage}"></script>`;

  // Generate the container code
  const containerCode = `<!-- Virtual Staff Widget Container -->
<div id="xbot-container"></div>`;

  // Combined code for copy
  const combinedCode = `${containerCode}\n\n${scriptCode}`;

  const handleCopy = () => {
    navigator.clipboard.writeText(combinedCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div>
      <div className="flex items-center justify-between bg-muted p-3">
        <h3 className="text-sm font-medium">{t("pages.staff.embed.script.title")}</h3>
        <Button variant="outline" size="sm" onClick={handleCopy} className="h-8">
          <Copy className="mr-1 size-3.5" />
          {copied ? t("pages.staff.embed.script.copied") : t("pages.staff.embed.script.copy")}
        </Button>
      </div>
      <ScrollArea className="h-[200px] rounded-b-md border-border bg-card p-3">
        <div className="space-y-4">
          <div className="space-y-2">
            <p className="text-xs font-medium text-muted-foreground">
              {t("pages.staff.embed.script.containerInstructions")}
            </p>
            <pre className="overflow-x-auto whitespace-pre-wrap break-all rounded-md bg-muted p-3 text-xs">
              {containerCode}
            </pre>
          </div>
          <div className="space-y-2">
            <p className="text-xs font-medium text-muted-foreground">
              {t("pages.staff.embed.script.scriptInstructions")}
            </p>
            <pre className="overflow-x-auto whitespace-pre-wrap break-all rounded-md bg-muted p-3 text-xs">
              {scriptCode}
            </pre>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}

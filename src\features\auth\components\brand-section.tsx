"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";

import bg from "@/assets/images/bg.png";
import BrandLogo from "@/assets/images/brand-logo.svg";

export function BrandSection() {
  const router = useRouter();

  return (
    <div className="relative hidden overflow-hidden lg:block">
      {/* Background image with blue overlay */}
      <div className="absolute inset-0">
        <Image
          src={bg}
          alt="Warehouse Background"
          className="object-cover blur-[1px]"
          fill
          priority
        />
        {/* Blue overlay */}
        <div className="absolute inset-0 bg-primary/20" />
      </div>

      {/* Logo centered */}
      <div className="relative flex h-full items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <BrandLogo
            alt="Opti Warehouse"
            className="cursor-pointer"
            draggable={false}
            priority
            onClick={() => router.push("/login")}
          />
        </div>
      </div>
    </div>
  );
}

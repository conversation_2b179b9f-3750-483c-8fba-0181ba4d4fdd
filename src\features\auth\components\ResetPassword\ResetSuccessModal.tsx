import { useRouter } from "next/navigation";
import { BadgeCheck } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";

export const ResetSuccessModal = () => {
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
      <div className="w-full max-w-[400px] space-y-6 rounded-lg border bg-card p-6 shadow-lg">
        <div className="flex flex-col items-center justify-center space-y-2 text-center">
          <div className="rounded-full bg-muted p-3 text-success">
            <BadgeCheck className="size-[100px]" />
          </div>
          <h2 className="text-lg font-medium">{t("auth.resetPasswordSuccess")}</h2>
          <p className="text-sm text-muted-foreground">
            {t("auth.resetPasswordSuccessDescription")}
          </p>
        </div>

        <Button className="w-full" onClick={() => router.push("/login")}>
          {t("auth.backToLogin")}
        </Button>
      </div>
    </div>
  );
};

"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary-hover",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive-hover",
        outline: "border border-border hover:bg-neutral-300 hover:text-accent-foreground",
        secondary: "bg-neutral-200 text-secondary-foreground hover:bg-neutral-300",
        ghost: "hover:bg-neutral-300 hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        icon: "hover:bg-neutral-300 hover:text-accent-foreground",
        outlineDestructive:
          "border border-border-destructive-50 text-destructive hover:bg-destructive/10 hover:text-red-500",
        outline_primary: "bg-transparent border-2 border-primary hover:bg-primary",
        outline_default_primary: "hover:bg-primary",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        md: "h-10 rounded-md px-4",
        lg: "h-11 rounded-md px-8",
        icon: "size-10",
        xs: "h-8 px-2",
      },
      isFullWidth: {
        true: "w-full",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      isFullWidth: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  uploading?: boolean;
  loadingShowText?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      isFullWidth,
      asChild = false,
      loading = false,
      uploading = false,
      loadingShowText = true,
      leftIcon,
      rightIcon,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";
    const isIconButton = size === "icon";
    const { t } = useTranslation();

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, isFullWidth, className }))}
        ref={ref}
        disabled={disabled || loading || uploading}
        {...props}>
        {loading ? (
          <>
            <Loader2 className="size-4 animate-spin" />
            {!isIconButton && loadingShowText && children}
          </>
        ) : uploading ? (
          <>
            <Loader2 className="size-4 animate-spin" />
            {!isIconButton && t("common.uploading")}
          </>
        ) : (
          <>
            {leftIcon}
            {children}
            {rightIcon}
          </>
        )}
      </Comp>
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };

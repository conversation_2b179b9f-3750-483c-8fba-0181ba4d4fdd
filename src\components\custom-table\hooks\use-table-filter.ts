import { useCallback, useEffect, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import { DateRange } from "react-day-picker";
import { toast } from "sonner";

import { EFilterType, FilterTableProps } from "@/components/data-table/types";
import { saveFilterApi, SaveFilterItem } from "@/lib/apis/save-filter";
import { formatDateToString } from "@/lib/utils/date";
import {
  convertDateRangeToString,
  convertDateStringToDateRange,
  determineDateOption,
} from "@/utils/helpers/date-formater";

import useDebounce from "../../../hooks/use-debounce";
import { useSavedFilters } from "./use-saved-filters";

export interface SavedFilter {
  id: string;
  name: string;
  type: string;
  filters: Record<string, unknown>;
}

export interface DateFilter {
  option: string | null;
  range: DateRange | undefined;
}

export interface UseTableFilterProps extends FilterTableProps {
  handleParamSearch: (payload: Record<string, unknown>) => void;
  defaultPayload?: Record<string, unknown>;
}

export const useTableFilter = ({
  filterType,
  initialValues = {},
  defaultPayload = {},
  listFilter = [],
  handleParamSearch = () => {},
}: UseTableFilterProps) => {
  const searchParams = useSearchParams();
  const filterTabId = searchParams?.get("filter_tab") || null;
  const [searchValue, setSearchValue] = useState<string>(
    (listFilter.find((filter) => filter.id === "query")?.defaultValue as string) || ""
  );
  const debouncedSearchValue = useDebounce(searchValue, 500);
  const [loading, setLoading] = useState(false);
  const [payload, setPayload] = useState<Record<string, unknown>>({
    ...defaultPayload,
    ...initialValues,
  });
  const { data: savedFilters } = useSavedFilters({ filterType: filterType as string });
  const [mainFilters, setMainFilters] = useState<Record<string, string[]>>(() => {
    const filters: Record<string, string[]> = {};
    listFilter
      .filter((filter) => filter.type !== EFilterType.DATE)
      .forEach((filter) => {
        const value = initialValues[filter.id] || filter.defaultValue;
        filters[filter.id] = value ? String(value).split(",") : [];
      });
    return filters;
  });

  const [dateFilters, setDateFilters] = useState<Record<string, DateFilter>>(() => {
    const filters: Record<string, DateFilter> = {};
    listFilter
      .filter((filter) => filter.type === "date")
      .forEach((filter) => {
        const fromDate = initialValues[`${filter.id}_from`];
        const toDate = initialValues[`${filter.id}_to`];

        if (fromDate && toDate) {
          const dateOption = determineDateOption(fromDate as string, toDate as string);
          filters[filter.id] = {
            option: dateOption,
            range: {
              from: new Date(fromDate as string),
              to: new Date(toDate as string),
            },
          };
        } else {
          filters[filter.id] = {
            option: "all",
            range: undefined,
          };
        }
      });
    return filters;
  });
  const prevSearchValueRef = useRef<string>("");
  const [otherFilters, setOtherFilters] = useState<Record<string, string[]>>(() => {
    const filters: Record<string, string[]> = {};
    listFilter
      .filter((filter) => filter.type !== EFilterType.DATE)
      .forEach((filter) => {
        const value = initialValues[filter.id] || filter.defaultValue;
        filters[filter.id] = value ? String(value).split(",") : ["all"];
      });
    return filters;
  });
  const [otherDateFilters, setOtherDateFilters] = useState<Record<string, DateFilter>>(() => {
    const filters: Record<string, DateFilter> = {};
    listFilter
      .filter((filter) => filter.type === "date")
      .forEach((filter) => {
        const fromDate = initialValues[`${filter.id}_from`];
        const toDate = initialValues[`${filter.id}_to`];

        if (fromDate && toDate) {
          const dateOption = determineDateOption(fromDate as string, toDate as string);
          filters[filter.id] = {
            option: dateOption,
            range: {
              from: new Date(fromDate as string),
              to: new Date(toDate as string),
            },
          };
        } else {
          filters[filter.id] = {
            option: "all",
            range: undefined,
          };
        }
      });
    return filters;
  });
  const queryClient = useQueryClient();

  // Create debounced timer refs
  const mainFilterTimerRef = useRef<NodeJS.Timeout>();
  const dateOptionTimerRef = useRef<NodeJS.Timeout>();
  const dateRangeTimerRef = useRef<NodeJS.Timeout>();

  const findMissingNumber = useCallback(() => {
    const listFilterNameNumbers = savedFilters
      .map((filter) => Number(filter.name.toLowerCase().split(`filter ${filterType} `)[1]))
      .filter((num) => !isNaN(num))
      .sort((a, b) => a - b);

    let missingNumber = 1;
    for (const num of listFilterNameNumbers) {
      if (num === missingNumber) {
        missingNumber++;
      } else if (num > missingNumber) {
        break;
      }
    }

    return missingNumber;
  }, [savedFilters, filterType]);

  const handleMainFilterChange = useCallback(
    (filterId: string, values: string[]) => {
      // Immediately update the UI
      setMainFilters((prev) => ({
        ...prev,
        [filterId]: values,
      }));
      setOtherFilters((prev) => ({
        ...prev,
        [filterId]: values,
      }));

      // Clear any existing timer
      if (mainFilterTimerRef.current) {
        clearTimeout(mainFilterTimerRef.current);
      }

      // Set new timer for API call
      mainFilterTimerRef.current = setTimeout(() => {
        const newPayload = { ...payload };
        if (values?.length > 0) {
          newPayload[filterId] = values.join(",");
        } else {
          delete newPayload[filterId];
        }
        handleParamSearch({ ...newPayload, page: 0 });
      }, 500);
    },
    [handleParamSearch, payload]
  );

  const handleDateOptionSelect = useCallback(
    (filterId: string, value: string, type: "main" | "other") => {
      const dateRange = convertDateStringToDateRange(value);

      if (type === "main") {
        // Immediately update the UI
        setDateFilters((prev) => ({
          ...prev,
          [filterId]: {
            option: value,
            range: dateRange,
          },
        }));

        // Clear any existing timer
        if (dateOptionTimerRef.current) {
          clearTimeout(dateOptionTimerRef.current);
        }

        // Set new timer for API call
        dateOptionTimerRef.current = setTimeout(() => {
          const newPayload = { ...payload };
          if (dateRange?.from && dateRange?.to) {
            newPayload[`${filterId}_from`] = formatDateToString(dateRange.from);
            newPayload[`${filterId}_to`] = formatDateToString(dateRange.to);
          } else {
            delete newPayload[`${filterId}_from`];
            delete newPayload[`${filterId}_to`];
          }
          handleParamSearch({ ...newPayload, page: 0 });
        }, 500);
      } else {
        setOtherDateFilters((prev) => ({
          ...prev,
          [filterId]: {
            option: value,
            range: dateRange,
          },
        }));
      }
    },
    [handleParamSearch, payload]
  );

  const handleDateRangeChange = useCallback(
    (filterId: string, type: "main" | "other", range?: DateRange) => {
      if (type === "main") {
        // Immediately update the UI
        setDateFilters((prev) => ({
          ...prev,
          [filterId]: {
            ...prev[filterId],
            option: "customize",
            range,
          },
        }));

        // Clear any existing timer
        if (dateRangeTimerRef.current) {
          clearTimeout(dateRangeTimerRef.current);
        }

        // Set new timer for API call
        dateRangeTimerRef.current = setTimeout(() => {
          const newPayload = { ...payload };
          if (range?.from && range?.to) {
            newPayload[`${filterId}_from`] = formatDateToString(range.from);
            newPayload[`${filterId}_to`] = formatDateToString(range.to);
          } else {
            delete newPayload[`${filterId}_from`];
            delete newPayload[`${filterId}_to`];
          }
          handleParamSearch({ ...newPayload, page: 0 });
        }, 500);
      } else {
        setOtherDateFilters((prev) => ({
          ...prev,
          [filterId]: {
            ...prev[filterId],
            range,
          },
        }));
      }
    },
    [handleParamSearch, payload]
  );

  // Clean up timers on unmount
  useEffect(() => {
    return () => {
      if (mainFilterTimerRef.current) {
        clearTimeout(mainFilterTimerRef.current);
      }
      if (dateOptionTimerRef.current) {
        clearTimeout(dateOptionTimerRef.current);
      }
      if (dateRangeTimerRef.current) {
        clearTimeout(dateRangeTimerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const normalizedSearchValue = debouncedSearchValue || "";
    const normalizedPrevSearch = prevSearchValueRef.current || "";
    const isSearchValueChanged = normalizedSearchValue !== normalizedPrevSearch;

    if (isSearchValueChanged) {
      const newPayload = { ...payload };
      if (normalizedSearchValue) {
        newPayload.query = normalizedSearchValue;
      } else {
        delete newPayload.query;
      }
      handleParamSearch({
        ...newPayload,
        page: 0,
      });
      prevSearchValueRef.current = normalizedSearchValue;
    }
  }, [debouncedSearchValue, handleParamSearch, payload]);

  useEffect(() => {
    setPayload({
      ...defaultPayload,
      ...initialValues,
    });
    setSearchValue(initialValues["query"] as string);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(initialValues)]);

  useEffect(() => {
    const mainFiltersList: Record<string, string[]> = {};
    listFilter
      .filter((filter) => filter.type !== "date")
      .forEach((filter) => {
        const value = initialValues[filter.id];
        mainFiltersList[filter.id] = value ? String(value).split(",") : [];
      });
    setMainFilters(mainFiltersList);

    // Set other filters (remaining items after numberOfFilters)
    const otherFiltersList: Record<string, string[]> = {};
    listFilter
      .filter((filter) => filter.type !== "date")
      .forEach((filter) => {
        const value = initialValues[filter.id];
        otherFiltersList[filter.id] = value ? String(value).split(",") : ["all"];
      });
    setOtherFilters(otherFiltersList);

    // Set date filters (unchanged)
    const dateFiltersList: Record<string, DateFilter> = {};
    listFilter
      .filter((filter) => filter.type === "date")
      .forEach((filter) => {
        const fromDate = initialValues[`${filter.id}_from`];
        const toDate = initialValues[`${filter.id}_to`];
        dateFiltersList[filter.id] = {
          option: convertDateRangeToString(fromDate as string, toDate as string),
          range:
            fromDate && toDate
              ? {
                  from: new Date(fromDate as string),
                  to: new Date(toDate as string),
                }
              : undefined,
        };
      });
    setDateFilters(dateFiltersList);

    // Set other date filters
    const otherDateFiltersList: Record<string, DateFilter> = {};
    listFilter
      .filter((filter) => filter.type === "date")
      .forEach((filter) => {
        const fromDate = initialValues[`${filter.id}_from`];
        const toDate = initialValues[`${filter.id}_to`];
        otherDateFiltersList[filter.id] = {
          option: convertDateRangeToString(fromDate as string, toDate as string),
          range:
            fromDate && toDate
              ? {
                  from: new Date(fromDate as string),
                  to: new Date(toDate as string),
                }
              : undefined,
        };
      });
    setOtherDateFilters(otherDateFiltersList);
    // Set search value
    setSearchValue(initialValues["query"] as string);
  }, [initialValues, listFilter]);

  const handleOtherFilterChange = (filterId: string, values: string[]) => {
    const currentValues = otherFilters[filterId] || [];
    if (currentValues.includes("all") && values.some((v) => v !== "all")) {
      setOtherFilters((prev) => ({
        ...prev,
        [filterId]: values.filter((v) => v !== "all"),
      }));
      return;
    }

    if (values.includes("all")) {
      setOtherFilters((prev) => ({
        ...prev,
        [filterId]: ["all"],
      }));
      return;
    }
    if (values.length === 0) {
      setOtherFilters((prev) => ({
        ...prev,
        [filterId]: ["all"],
      }));
      return;
    }

    setOtherFilters((prev) => ({
      ...prev,
      [filterId]: values,
    }));
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };
  const handleSaveFilter = useCallback(async () => {
    if (!filterType) {
      toast.error("Filter type is required");
      return;
    }
    // Check if filters only contain page and pageSize defaults
    const hasOnlyDefaultPagination = (filters: Record<string, unknown>) => {
      const { page, limit, ...otherFilters } = filters;
      return (
        Object.keys(otherFilters).length == 0 && page == 0 && (limit == 20 || limit == undefined)
      );
    };

    try {
      setLoading(true);
      // Process otherFilters to remove "all" values
      const processedOtherFilters = Object.entries(otherFilters).reduce(
        (acc, [key, values]) => {
          // If "all" is selected or no values are selected, skip this filter
          if (!values.includes("all") && values.length > 0) {
            acc[key] = values.join(",");
          }
          return acc;
        },
        {} as Record<string, string>
      );
      const filterData = {
        filters: {
          page: 0,
          limit: 20,
          ...(searchValue && { query: searchValue }),
          ...processedOtherFilters,
          ...Object.entries(otherDateFilters).reduce(
            (acc, [key, filterData]) => {
              if (filterData.range?.from && filterData.range?.to) {
                acc[`${key}_from`] = formatDateToString(filterData.range.from);
                acc[`${key}_to`] = formatDateToString(filterData.range.to);
              }
              return acc;
            },
            {} as Record<string, string>
          ),
        } as Record<string, unknown>,
      };

      if (hasOnlyDefaultPagination(filterData.filters)) {
        toast.error("No filters to save");
        return;
      }

      const currentFilter = filterTabId ? savedFilters?.find((f) => f.id === filterTabId) : null;
      let res: any;

      if (filterTabId && currentFilter) {
        // Update existing filter
        const updatedFilter: SaveFilterItem = {
          id: currentFilter.id,
          name: currentFilter.name,
          type: currentFilter.type,
          filters: filterData.filters,
          company_id: (currentFilter as SaveFilterItem).company_id || "",
          user_id: (currentFilter as SaveFilterItem).user_id || "",
          created_at: (currentFilter as SaveFilterItem).created_at || new Date().toISOString(),
          updated_at: (currentFilter as SaveFilterItem).updated_at || new Date().toISOString(),
          icon: currentFilter.icon,
        };
        res = await saveFilterApi.update([updatedFilter], filterType);
        // Update query cache
        queryClient.setQueryData(["saveFilter", filterType], (oldData: any) => ({
          ...oldData,
          items: oldData.items.map((filter: SavedFilter) =>
            filter.id === currentFilter.id ? res[0] : filter
          ),
        }));

        const newPayload = {
          ...filterData.filters,
          page: 0,
          limit: 20,
          filter_tab: currentFilter.id,
        };
        setPayload(newPayload);
        handleParamSearch(newPayload);

        toast.success("Filter updated successfully!");
      } else {
        // Create new filter
        const missingNumber = findMissingNumber();

        res = await saveFilterApi.create({
          name: `Filter ${filterType} ${missingNumber}`,
          type: filterType,
          ...filterData,
        });
        // Update query cache
        queryClient.setQueryData(["saveFilter", filterType], (oldData: any) => ({
          ...oldData,
          items: [res, ...(oldData?.items || [])],
        }));
        const newPayload = { ...filterData.filters, page: 0, limit: 20, filter_tab: res.id };
        setPayload(newPayload);
        handleParamSearch(newPayload);

        toast.success("Filter saved successfully!");
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to save filter");
    } finally {
      setLoading(false);
    }
  }, [
    filterTabId,
    filterType,
    findMissingNumber,
    handleParamSearch,
    otherDateFilters,
    otherFilters,
    queryClient,
    savedFilters,
    searchValue,
  ]);

  const handleApplyFilters = useCallback(
    async ({ isSaveFilter = false }: { isSaveFilter?: boolean } = {}) => {
      if (isSaveFilter) {
        await handleSaveFilter();
      }
      // Convert otherFilters to mainFilters format
      else {
        const processedMainFilters = Object.entries(otherFilters).reduce(
          (acc, [key, values]) => {
            // If "all" is selected or no values are selected, use empty array
            acc[key] = values.includes("all") || values.length === 0 ? [] : values;
            return acc;
          },
          {} as Record<string, string[]>
        );

        // Prepare the new payload
        const newPayload = { ...payload };

        // Add main filters to payload
        Object.entries(processedMainFilters).forEach(([key, values]) => {
          if (values.length > 0) {
            newPayload[key] = values.join(",");
          } else {
            delete newPayload[key];
          }
        });

        // Add date filters to payload
        Object.entries(otherDateFilters).forEach(([key, filterData]) => {
          if (filterData.range?.from && filterData.range?.to) {
            newPayload[`${key}_from`] = formatDateToString(filterData.range.from);
            newPayload[`${key}_to`] = formatDateToString(filterData.range.to);
          } else {
            delete newPayload[`${key}_from`];
            delete newPayload[`${key}_to`];
          }
        });

        newPayload.page = 0;
        setPayload(newPayload);
        handleParamSearch(newPayload);
      }
    },
    [handleParamSearch, handleSaveFilter, otherDateFilters, otherFilters, payload]
  );

  const handleResetFilters = () => {
    const filter_tab = payload.filter_tab;

    // Reset main filters to empty arrays
    const resetMainFilters: Record<string, string[]> = {};
    listFilter
      .filter((filter) => filter.type !== EFilterType.DATE)
      .forEach((filter) => {
        resetMainFilters[filter.id] = [];
      });
    setMainFilters(resetMainFilters);

    // Reset date filters to default state
    const resetDateFilters: Record<string, DateFilter> = {};
    listFilter
      .filter((filter) => filter.type === "date")
      .forEach((filter) => {
        resetDateFilters[filter.id] = {
          option: "all",
          range: undefined,
        };
      });
    setDateFilters(resetDateFilters);

    // Reset other filters
    const resetOtherFilters: Record<string, string[]> = {};
    listFilter
      .filter((filter) => filter.type !== EFilterType.DATE)
      .forEach((filter) => {
        resetOtherFilters[filter.id] = ["all"];
      });
    setOtherFilters(resetOtherFilters);

    // Reset other date filters
    const resetOtherDateFilters: Record<string, DateFilter> = {};
    listFilter
      .filter((filter) => filter.type === "date")
      .forEach((filter) => {
        resetOtherDateFilters[filter.id] = {
          option: "all",
          range: undefined,
        };
      });
    setOtherDateFilters(resetOtherDateFilters);

    // Reset search value
    setSearchValue("");
    const newPayload = {
      page: 0,
      limit: 20,
      ...(filter_tab ? { filter_tab } : {}),
    };
    // Reset payload to initial state
    setPayload(newPayload);
    handleParamSearch(newPayload);
  };

  return {
    searchValue,
    loading,
    mainFilters,
    dateFilters,
    savedFilters,
    otherFilters,
    otherDateFilters,
    handleSearch,
    handleMainFilterChange,
    handleDateOptionSelect,
    handleDateRangeChange,
    setMainFilters,
    setOtherFilters,
    setDateFilters,
    setOtherDateFilters,
    setSearchValue,
    handleApplyFilters,
    handleResetFilters,
    handleSaveFilter,
    handleOtherFilterChange,
  };
};

import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { customerApi, customerGroupApi } from "@/lib/apis/customer";
import { ResponseList } from "@/lib/apis/types/common";

import { CUSTOMER_KEYS, QUERY_KEYS } from "./keys";
import type { Customer, CustomerGroup, UseCustomersOptions } from "./type";

export { type Customer };

export const useCustomers = ({ query = "", enabled = true }: UseCustomersOptions = {}) => {
  const result = useInfiniteQuery({
    queryKey: CUSTOMER_KEYS.list({ query, limit: 20 }),
    queryFn: ({ pageParam = 0 }) => {
      // First page of search: only use query
      if (query && pageParam === 0) {
        return customerApi.list({ query });
      }

      // Subsequent pages or non-search: use all params
      return customerApi.list({
        limit: 20,
        page: pageParam + 1,
        ...(query ? { query } : {}),
      });
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage || lastPage.items.length === 0) return undefined;
      const nextPage = Number(lastPage.page) + 1;
      const totalPages = Math.ceil(lastPage.total / 20);
      return nextPage <= totalPages ? nextPage : undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const customers = result.data?.pages.flatMap((page) => page.items) ?? [];

  return {
    ...result,
    customers,
  };
};

interface UseCustomerListOptions {
  limit?: number;
  query?: string;
  enabled?: boolean;
  page?: number;
  [key: string]: any;
}

export function useCustomerList(options: UseCustomerListOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();
  const query = useInfiniteQuery({
    queryKey: CUSTOMER_KEYS.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      customerApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const customers = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteCustomerMutation = useMutation({
    mutationFn: async (id: string) => {
      return customerApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Customer>>>(
        CUSTOMER_KEYS.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
    },
    onError: () => {
      toast.error("Customer deletion failed");
    },
  });

  const useUpdateCustomerMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Customer> }) => {
      return customerApi.update(id, data);
    },
    onSuccess: (updatedCustomer) => {
      // Update all customer queries
      queryClient
        .getQueriesData<InfiniteData<ResponseList<Customer>>>({
          queryKey: CUSTOMER_KEYS.lists(),
        })
        .forEach(([queryKey, oldData]) => {
          if (oldData && oldData.pages.length > 0) {
            queryClient.setQueryData(
              queryKey,
              (old: InfiniteData<ResponseList<Customer>> | undefined) => {
                if (!old) return old;

                const newPages = old.pages.map((page) => ({
                  ...page,
                  items: page.items.map((item) =>
                    item.id === updatedCustomer.id ? updatedCustomer : item
                  ),
                }));

                return {
                  ...old,
                  pages: newPages,
                };
              }
            );
          }
        });

      toast.success("Customer updated successfully");
    },
    onError: () => {
      toast.error("Customer update failed");
    },
  });

  return {
    ...query,
    customers,
    total,
    useUpdateCustomerMutation,
    useDeleteCustomerMutation,
  };
}

export function useCustomerDetails(id: string) {
  const { data, isLoading, refetch, error } = useQuery<Customer>({
    queryKey: QUERY_KEYS.CUSTOMER_DETAILS(id),
    queryFn: async () => {
      try {
        const response = await customerApi.getById(id);
        return response;
      } catch (error) {
        // Show toast error
        toast.error("Failed to load customer details");
        throw error; // Re-throw to let React Query handle the error state
      }
    },
    enabled: !!id,
  });

  return {
    customer: data,
    isLoading,
    refetch,
    error,
  };
}

export function useCustomerGroups(enabled: boolean = false) {
  const { data, isLoading } = useQuery<ResponseList<CustomerGroup>>({
    queryKey: QUERY_KEYS.CUSTOMER_GROUPS,
    queryFn: async () => {
      const response = await customerGroupApi.list({
        limit: 20,
      });
      return response;
    },
    enabled,
  });

  return {
    data: data?.items ?? [],
    isLoading,
  };
}
export function useAddCustomer() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (customerData: Partial<Customer>) => {
      try {
        const response = await customerApi.create(customerData);
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CUSTOMER_DETAILS(response.id) });
          queryClient.invalidateQueries({ queryKey: CUSTOMER_KEYS.lists() });
        }, 3000);
        return response;
      } catch (error: any) {
        console.log("error", error);
        // Handle specific error for duplicate phone number
        if (error === "Customer phone already existed") {
          throw error;
        }
        throw error;
      }
    },
  });
}

export function useUpdateCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (customerData: Partial<Customer> & { id: string }) => {
      // Log the payload being sent to the API
      console.log("Updating customer with data:", customerData);

      try {
        // Ensure we're sending customerData with the full customer_group object
        const response = await customerApi.update(customerData.id, customerData);
        return response;
      } catch (error: any) {
        // Handle specific error for duplicate phone number
        if (error.response?.data?.error === "error_customer_phone_existed") {
          throw error.response.data;
        }
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CUSTOMER_DETAILS(variables.id) });
        queryClient.invalidateQueries({ queryKey: CUSTOMER_KEYS.lists() });
      }, 3000);
    },
  });
}

export function useDeleteCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return customerApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      // Update all customer list queries
      queryClient
        .getQueriesData<InfiniteData<ResponseList<Customer>>>({
          queryKey: CUSTOMER_KEYS.lists(),
        })
        .forEach(([queryKey, oldData]) => {
          if (oldData && oldData.pages.length > 0) {
            queryClient.setQueryData(
              queryKey,
              (old: InfiniteData<ResponseList<Customer>> | undefined) => {
                if (!old) return old;

                const newPages = old.pages.map((page) => ({
                  ...page,
                  items: page.items.filter((item) => item.id !== deletedId),
                  total: page.total - 1,
                }));

                return {
                  ...old,
                  pages: newPages,
                };
              }
            );
          }
        });

      toast.success("Customer deleted successfully");
    },
    onError: () => {
      toast.error("Customer deletion failed");
    },
  });
}

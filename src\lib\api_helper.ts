import axios, {
  AxiosDefaults,
  AxiosHeaderV<PERSON>ue,
  AxiosInterceptorManager,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  InternalAxiosRequestConfig,
} from "axios";

import config from "@/config";

import { authApi } from "./apis/auth";
import { clearAuth, getAuthToken, setAuthToken } from "./auth";

interface CustomAxios {
  defaults: AxiosDefaults;
  interceptors: {
    request: AxiosInterceptorManager<InternalAxiosRequestConfig>;
    response: AxiosInterceptorManager<AxiosResponse>;
  };
  getUri(config?: AxiosRequestConfig): string;
  getUri(config?: AxiosRequestConfig): string;
  request<T = any, R = T, D = any>(config: AxiosRequestConfig<D>): Promise<R>;
  get<T = any, R = T, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
  delete<T = void, R = T, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
  head<T = any, R = T, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
  options<T = any, R = T, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;
  post<T = any, R = T, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
  put<T = any, R = T, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
  patch<T = any, R = T, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R>;
  postForm<T = any, R = T, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D>
  ): Promise<R>;
  putForm<T = any, R = T, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D>
  ): Promise<R>;
  patchForm<T = any, R = T, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D>
  ): Promise<R>;
}

export interface AxiosInstance extends CustomAxios {
  <T = any, R = T, D = any>(config: AxiosRequestConfig<D>): Promise<R>;
  <T = any, R = T, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R>;

  defaults: Omit<AxiosDefaults, "headers"> & {
    headers: HeadersDefaults & {
      [key: string]: AxiosHeaderValue;
    };
  };
}

// Public API instance (không cần token)
const publicApi = axios.create({
  baseURL: config.API_URL,
  headers: {
    "Content-Type": "application/json",
  },
}) as AxiosInstance;

// Private API instance (cần token)
const privateApi = axios.create({
  baseURL: config.API_URL,
  headers: {
    "Content-Type": "application/json",
  },
}) as AxiosInstance;

// Stream API instance (for streaming responses)
const streamApi = axios.create({
  baseURL: config.API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  responseType: "stream",
}) as AxiosInstance;

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

// Xử lý response cho public API
publicApi.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  async (error) => {
    const errorMessage = error.response?.data?.message || error.message;
    return Promise.reject(errorMessage);
  }
);

// Interceptor để thêm token vào private requests
privateApi.interceptors.request.use((config) => {
  const token = getAuthToken();
  const access_token = token?.Token?.AccessToken;
  const id_token = token?.Token?.IdToken;
  if (id_token) {
    config.headers.Authorization = `Bearer ${id_token}`;
  }
  if (access_token) {
    config.params = { ...config.params, access_token };
  }

  if (config.url === "auth/refresh") {
    delete config.params;
    delete config.headers["Authorization"];
  }
  return config;
});

// Interceptor để xử lý response và refresh token cho private API
privateApi.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  async (error) => {
    const originalRequest = error.config;
    const errorMessage = error.response?.data?.message || error.message;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return privateApi(originalRequest);
          })
          .catch((err) => Promise.reject(err));
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const authData = getAuthToken();
      if (!authData?.Token?.RefreshToken) {
        clearAuth();
        window.location.href = "/login";
        return Promise.reject(errorMessage);
      }

      try {
        const tokenData = await authApi.refreshToken(authData.Token.RefreshToken);

        // Cập nhật token mới vào auth_user hiện tại
        const newAuthData = {
          ...authData,
          Token: { ...authData.Token, ...tokenData },
        };
        setAuthToken(newAuthData);

        // Cập nhật header cho request tiếp theo
        privateApi.defaults.headers.common.Authorization = `Bearer ${tokenData.IdToken}`;
        originalRequest.headers.Authorization = `Bearer ${tokenData.IdToken}`;

        processQueue(null, tokenData.IdToken);
        return privateApi(originalRequest);
      } catch (err) {
        processQueue(err, null);
        clearAuth();
        window.location.href = "/login";
        return Promise.reject(errorMessage);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(errorMessage);
  }
);

// Add the same interceptors as privateApi for authentication
streamApi.interceptors.request.use((config) => {
  const token = getAuthToken();
  const access_token = token?.Token?.AccessToken;
  const id_token = token?.Token?.IdToken;
  if (id_token) {
    config.headers.Authorization = `Bearer ${id_token}`;
  }
  if (access_token) {
    config.params = { ...config.params, access_token };
  }

  if (config.url === "auth/refresh") {
    delete config.params;
    delete config.headers["Authorization"];
  }
  return config;
});

export { publicApi, privateApi, streamApi };

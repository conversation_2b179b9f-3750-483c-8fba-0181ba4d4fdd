import { FileType, Trash } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Form, FormControl, FormField, FormItem, Input, Label } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

import { CreateKnowledgeByTextPayload } from "../../types";

const TextUploader = ({
  form,
  isUploading,
  listText,
  setListText,
}: {
  form: any;
  isUploading: boolean;
  listText: CreateKnowledgeByTextPayload[];
  setListText: (listText: CreateKnowledgeByTextPayload[]) => void;
}) => {
  const { t } = useTranslation();

  const handleAddKnowledge = () => {
    const { knowledge_name, content } = form.getValues();
    if (knowledge_name && content) {
      setListText([...listText, { knowledge_name, content }]);
      form.reset();
    }
  };
  const handleRemoveTextKnowledge = (index: number) => {
    setListText(listText.filter((_, i) => i !== index));
  };

  return (
    <div className="flex flex-auto flex-col gap-4 overflow-hidden  p-1">
      <div className="flex flex-none flex-col gap-4">
        <Form {...form}>
          <FormField
            control={form.control}
            name="knowledge_name"
            render={({ field, fieldState }) => (
              <FormItem>
                <Label className="mb-2  font-semibold text-gray-700">
                  {t("pages.knowledge.upload.textTitle") || "Title"}
                </Label>
                <FormControl>
                  <Input
                    placeholder={
                      t("pages.knowledge.upload.textTitlePlaceholder") || "Enter knowledge title"
                    }
                    {...field}
                    disabled={isUploading}
                    error={fieldState.error?.message}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="content"
            render={({ field, fieldState }) => (
              <FormItem>
                <Label className="mb-2 flex items-center justify-between gap-2 font-semibold text-gray-700">
                  {t("pages.knowledge.upload.textInput") || "Direct Text Input"}
                  <div className="text-xs font-semibold text-gray-700">
                    {field.value.length}/20,000 {t("common.words")}
                  </div>
                </Label>
                <FormControl>
                  <Textarea
                    placeholder={
                      t("pages.knowledge.upload.textInputPlaceholder") ||
                      "Paste your knowledge base text here..."
                    }
                    {...field}
                    maxLength={20000}
                    disabled={isUploading}
                    error={fieldState.error?.message}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </Form>
        <Button className="w-fit self-end" onClick={handleAddKnowledge} disabled={isUploading}>
          {t("common.add")}
        </Button>
      </div>
      <div className="flex flex-auto flex-col gap-2 overflow-y-auto">
        {listText.map((knowledge, index) => (
          <div
            key={index}
            className="mb-2 flex items-center justify-between rounded border bg-card px-3 py-2">
            <div className="flex items-center gap-2">
              <FileType className="size-4 text-gray-500" />
              <span className="text-sm">{knowledge.knowledge_name}</span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="ghost"
                size="xs"
                onClick={() => handleRemoveTextKnowledge(index)}>
                <Trash className="size-4 text-destructive" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TextUploader;

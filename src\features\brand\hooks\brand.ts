import { InfiniteData, useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { brandApi } from "@/lib/apis/brand";
import { ResponseList } from "@/lib/apis/types/common";

import { brandKeys, IGetBrandParams } from "./key";
import { Brand, BrandPayload } from "./types";

interface UseBrandsOptions extends Partial<IGetBrandParams> {
  enabled?: boolean;
}

export function useBrands(options: UseBrandsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();
  const query = useInfiniteQuery({
    queryKey: brandKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      brandApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const brands = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteBrandMutation = useMutation({
    mutationFn: async (id: string) => {
      return brandApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Brand>>>(
        brandKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Brand deleted successfully");
    },
    onError: () => {
      toast.error("Brand deletion failed");
    },
  });

  const useUpdateBrandMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: BrandPayload }) => {
      return brandApi.update(id, data as unknown as Brand);
    },
    onSuccess: (updatedBrand) => {
      // Update all brand queries
      queryClient
        .getQueriesData<InfiniteData<ResponseList<Brand>>>({
          queryKey: brandKeys.lists(),
        })
        .forEach(([queryKey, oldData]) => {
          if (oldData && oldData.pages.length > 0) {
            queryClient.setQueryData(
              queryKey,
              (old: InfiniteData<ResponseList<Brand>> | undefined) => {
                if (!old) return old;

                const newPages = old.pages.map((page) => ({
                  ...page,
                  items: page.items.map((item) =>
                    item.id === updatedBrand.id ? updatedBrand : item
                  ),
                }));

                return {
                  ...old,
                  pages: newPages,
                };
              }
            );
          }
        });

      toast.success("Brand updated successfully");
    },
    onError: () => {
      toast.error("Brand update failed");
    },
  });

  return {
    ...query,
    brands,
    total,
    useDeleteBrandMutation,
    useUpdateBrandMutation,
  };
}

interface UseCreateBrandOptions {
  onSuccess?: (data: BrandPayload) => void;
  onError?: (error: Error) => void;
}

export function useCreateBrand(options: UseCreateBrandOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BrandPayload) => {
      return brandApi.create(data);
    },
    onSuccess: (data) => {
      // Update all brand list queries
      // queryClient.invalidateQueries({ queryKey: brandKeys.lists() });

      // Also directly update any brand list queries with the new brand
      queryClient
        .getQueriesData<InfiniteData<ResponseList<Brand>>>({
          queryKey: brandKeys.lists(),
        })
        .forEach(([queryKey, oldData]) => {
          if (oldData && oldData.pages.length > 0) {
            queryClient.setQueryData(
              queryKey,
              (old: InfiniteData<ResponseList<Brand>> | undefined) => {
                if (!old) return old;

                const newPages = [...old.pages];
                if (newPages.length > 0) {
                  // Add new brand to the first page
                  newPages[0] = {
                    ...newPages[0],
                    items: [data, ...newPages[0].items],
                    total: newPages[0].total + 1,
                  };
                }

                return {
                  ...old,
                  pages: newPages,
                };
              }
            );
          }
        });

      toast.success("Brand created successfully");
      onSuccess?.(data);
    },
    onError: (error) => {
      toast.error("Failed to create brand");
      onError?.(error as Error);
    },
  });
}

interface UseUpdateBrandOptions {
  onSuccess?: (data: Brand) => void;
  onError?: (error: Error) => void;
}

export function useUpdateBrand(options: UseUpdateBrandOptions = {}) {
  const { onSuccess, onError } = options;
  const { useUpdateBrandMutation } = useBrands({ enabled: false });
  const queryClient = useQueryClient();

  return {
    ...useUpdateBrandMutation,
    mutateAsync: async ({ id, data }: { id: string; data: BrandPayload }) => {
      try {
        const result = await useUpdateBrandMutation.mutateAsync({ id, data });
        onSuccess?.(result);
        return result;
      } catch (error) {
        onError?.(error as Error);
        throw error;
      }
    },
  };
}

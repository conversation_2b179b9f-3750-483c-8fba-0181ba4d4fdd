interface PaginatedResponse<T> {
  total: number;
  page: number;
  limit: number;
  items: T[];
}

export interface InventoryItem {
  id: string;
  incoming: number;
  cost: number;
  company_id: string;
  available: number;
  created_at: string;
  packing: number;
  location_id: string;
  min_value: number | null;
  location_name: string;
  variant_id: string;
  shipping: number;
  updated_at: string;
  product_id: string;
  on_hand: number;
  sku: string;
  max_value: number | null;
}

export interface InventoryTransaction {
  id: string;
  cost: number;
  quantity: number;
  company_id: string;
  change: number;
  created_at: string;
  transaction_type: string;
  location_id: string;
  reference: string;
  updated_at: string;
  inventory_item_id: string;
  staff_id: string;
}

export type InventoryItemsResponse = PaginatedResponse<InventoryItem>;
export type InventoryTransactionsResponse = PaginatedResponse<InventoryTransaction>;

"use client";

import { useCallback, useEffect, useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTheme } from "next-themes";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { DATA_DEFAULT } from "@/features/settings/theme-settings/color-settings/constants";
import { ThemeColor, ThemeColors } from "@/features/settings/theme-settings/color-settings/types";

import { settingApi } from "@/lib/apis/setting";
import { useVersion, Version, versionApi } from "@/lib/apis/version";

// Convert hex to RGB
const hexToRgb = (hex: string) => {
  hex = hex.replace(/^#/, "");
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
};

const rgbToHsl = (r: number, g: number, b: number) => {
  r /= 255;
  g /= 255;
  b /= 255;
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }
  const hDeg = Math.round(h * 360);
  const sPct = Math.round(s * 100);
  const lPct = Math.round(l * 100);
  return `${hDeg} ${sPct}% ${lPct}%`;
};

// Convert hex to HSL string
const hexToHsl = (hex: string) => {
  const rgb = hexToRgb(hex);
  return rgbToHsl(rgb.r, rgb.g, rgb.b);
};

const handleMapNewColor = (dataDefault: ThemeColor[], dataCallApi: Record<string, string>) => {
  return dataDefault.map((item) => ({
    ...item,
    value: dataCallApi[item.key] || item.value,
    ...(item?.subColor && {
      subColor: item.subColor.map((i) => ({ ...i, value: dataCallApi[i.key] || i.value })),
    }),
  }));
};

export const useApplyThemeColors = () => {
  const { theme, systemTheme } = useTheme();
  const { getSettingValue, isLoading } = useVersion();

  useEffect(() => {
    if (typeof window === "undefined" || isLoading) return;
    const applyColors = () => {
      try {
        const root = document.documentElement;
        // Determine current mode
        const currentMode =
          theme === "system"
            ? systemTheme === "dark"
              ? "dark"
              : "light"
            : theme === "dark"
              ? "dark"
              : "light";

        const colors = getSettingValue<Record<string, Record<string, string>>>("color");
        if (!colors?.[currentMode]) return;

        Object.entries(colors[currentMode]).forEach(([key, value]) => {
          if (!value) return;

          try {
            const hslValue = hexToHsl(value);
            root.style.setProperty(`--${key}`, hslValue);
          } catch (error) {
            console.error(`Error applying color ${key}:`, error);
          }
        });
      } catch (error) {
        console.error("Error applying theme colors:", error);
      }
    };

    applyColors();
  }, [theme, systemTheme, getSettingValue, isLoading]);

  return { isLoading };
};

export function useColorSetting() {
  const { getSettingValue, isLoading } = useVersion();
  const { t } = useTranslation();

  const colors = getSettingValue<Record<string, Record<string, string>>>("color");
  const [localColors, setLocalColors] = useState<ThemeColors>(() => {
    const colors = getSettingValue<Record<string, Record<string, string>>>("color");
    if (!colors) return DATA_DEFAULT;
    return {
      light: handleMapNewColor(DATA_DEFAULT.light, colors.light || {}),
      dark: handleMapNewColor(DATA_DEFAULT.dark, colors.dark || {}),
    };
  });

  useEffect(() => {
    if (!colors || isLoading) return;
    setLocalColors({
      light: handleMapNewColor(DATA_DEFAULT.light, colors.light || {}),
      dark: handleMapNewColor(DATA_DEFAULT.dark, colors.dark || {}),
    });
  }, [colors, isLoading]);

  const handleColorChange = useCallback((mode: "light" | "dark", key: string, color: string) => {
    setLocalColors((prev) => {
      const newColors = { ...prev };
      const colorIndex = newColors[mode].findIndex((c) => c.key === key);
      if (colorIndex > -1) {
        newColors[mode] = [...newColors[mode]];
        newColors[mode][colorIndex] = {
          ...newColors[mode][colorIndex],
          value: color,
        };
      }
      return newColors;
    });
  }, []);

  const handleReset = useCallback(() => {
    const colors = getSettingValue<Record<string, Record<string, string>>>("color");
    if (!colors || isLoading) return;

    setLocalColors({
      light: handleMapNewColor(DATA_DEFAULT.light, colors.light || {}),
      dark: handleMapNewColor(DATA_DEFAULT.dark, colors.dark || {}),
    });

    toast.success(t("common.success"), {
      description: t("pages.settings.color.resetSuccess"),
    });
  }, [getSettingValue, isLoading, t]);

  return {
    localColors,
    setLocalColors,
    handleColorChange,
    handleReset,
    isLoading,
  };
}

export const useUpdateColor = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const updateColorMutation = useMutation({
    mutationFn: async (newColors: ThemeColors) => {
      setIsLoading(true);
      const newColorSettings = {
        light: newColors.light.reduce((acc, { key, value }) => ({ ...acc, [key]: value }), {}),
        dark: newColors.dark.reduce((acc, { key, value }) => ({ ...acc, [key]: value }), {}),
      };

      await settingApi.updateColorSettings(newColorSettings);
      const version = await versionApi.getVersion();
      return {
        ...version,
        data: {
          ...version.data,
          dict: {
            ...version.data.dict,
            color: { ...version.data.dict.color, setting_value: newColorSettings },
          },
        },
      };
    },
    onSuccess: (updatedVersion: Version) => {
      queryClient.setQueryData(["version"], updatedVersion);

      toast.success(t("common.success"), {
        description: t("pages.settings.color.saveSuccess"),
      });
      setIsLoading(false);
    },
    onError: (error: Error) => {
      console.log(error);
      toast.error(t("common.error"), {
        description: t("pages.settings.color.saveError"),
      });
      setIsLoading(false);
    },
  });

  return {
    updateColorMutation: updateColorMutation.mutateAsync,
    isLoading,
  };
};

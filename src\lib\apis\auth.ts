import {
  ChangePasswordPayload,
  ForgotPasswordPayload,
  LoginPayload,
  ResendCodePayload,
  ResetPasswordPayload,
  VerifyCodePayload,
} from "@/features/auth/types";
import { SignupFormValues } from "@/features/auth/utils/validators/signup";

import { AUTH_ENDPOINTS, ENDPOINTS } from "@/constants/endpoints";

import { privateApi, publicApi } from "../api_helper";
import { TokenData, UserInfoResponse } from "./types/auth";
import { MessageResponse, SessionResponse } from "./types/common";

export const authApi = {
  login: (data: LoginPayload) => {
    return publicApi.post<TokenData>(AUTH_ENDPOINTS.LOGIN, data);
  },

  changePassword: async (payload: ChangePasswordPayload) => {
    return privateApi.post<MessageResponse>(ENDPOINTS.AUTH.CHANGE_PASSWORD, payload);
  },

  refreshToken: async (refresh_token: string) => {
    return publicApi.post<TokenData>(ENDPOINTS.AUTH.REFRESH_TOKEN, {
      refresh_token,
    });
  },

  forgot: async (payload: ForgotPasswordPayload) => {
    return publicApi.post<MessageResponse>(ENDPOINTS.AUTH.FORGOT, payload);
  },

  confirm: async (username: string, code: string, newPassword: string) => {
    return publicApi.post<MessageResponse>(ENDPOINTS.AUTH.CONFIRM, {
      username,
      code,
      newPassword,
    });
  },

  newPassword: async (payload: ResetPasswordPayload) => {
    return publicApi.post<MessageResponse>(ENDPOINTS.AUTH.NEW_PASSWORD, payload);
  },

  getInfo: async (access_token: string, token: string, username: string) => {
    return privateApi.get<UserInfoResponse>(ENDPOINTS.AUTH.GET_INFO, {
      params: { access_token, token, username },
    });
  },

  resendConfirmationCode: async (payload: ResendCodePayload) => {
    return publicApi.post<MessageResponse>(ENDPOINTS.AUTH.RESEND_CODE, payload);
  },

  verifyCode: async (payload: VerifyCodePayload) => {
    return publicApi.post<MessageResponse>(ENDPOINTS.AUTH.VERIFY_CODE, payload);
  },

  signup: async (data: SignupFormValues) => {
    return publicApi.post<MessageResponse>(AUTH_ENDPOINTS.REGISTER, data);
  },

  verifyCodeReset: async (payload: VerifyCodePayload) => {
    return publicApi.post<SessionResponse>(ENDPOINTS.AUTH.VERIFY_CODE_RESET, payload);
  },
};

import { useInfiniteQuery } from "@tanstack/react-query";

import { addressDistrictApi, addressProvinceApi, addressWard<PERSON>pi } from "@/lib/apis/address";
import { ResponseList } from "@/lib/apis/types/common";

import { QUERY_KEYS } from "./keys";
import { District, Province, Ward } from "./type";

// Define a response type that explicitly includes the page property
interface PaginatedResponse<T> extends ResponseList<T> {
  page: number;
}

const PAGE_SIZE = 20;

export const useProvinces = (query: string = "") => {
  // Format query for API: convert to lowercase, remove accents, replace spaces with dashes
  const formattedQuery = query ? formatSearchQuery(query) : "";

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery<
    PaginatedResponse<Province>
  >({
    queryKey: QUERY_KEYS.ADDRESS_PROVINCES(formattedQuery),
    queryFn: ({ pageParam = 0 }) => {
      const params: any = {
        limit: PAGE_SIZE,
        page: pageParam,
      };
      if (formattedQuery) {
        params.query = formattedQuery;
      }
      return addressProvinceApi.list(params) as Promise<PaginatedResponse<Province>>;
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage || lastPage.items.length < PAGE_SIZE) return undefined;
      return Number(lastPage.page) + 1;
    },
    initialPageParam: 0,
  });

  // Flatten all pages of data
  const provinces = data?.pages.flatMap((page) => page.items) || [];

  return {
    provinces,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
};

export const useDistricts = (provinceCode?: string, query: string = "") => {
  // Format query for API: convert to lowercase, remove accents, replace spaces with dashes
  const formattedQuery = query ? formatSearchQuery(query) : "";

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery<
    PaginatedResponse<District>
  >({
    queryKey: QUERY_KEYS.ADDRESS_DISTRICTS(provinceCode, formattedQuery),
    queryFn: ({ pageParam = 0 }) => {
      // Cast to any to avoid TypeScript errors with dynamic properties
      const params: any = {
        limit: PAGE_SIZE,
        page: pageParam,
      };
      if (formattedQuery) {
        params.query = formattedQuery;
      }
      if (provinceCode) {
        params["province.code"] = provinceCode;
      }
      return addressDistrictApi.list(params) as Promise<PaginatedResponse<District>>;
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage || lastPage.items.length < PAGE_SIZE) return undefined;
      return Number(lastPage.page) + 1;
    },
    initialPageParam: 0,
    enabled: !!provinceCode,
  });

  // Flatten all pages of data
  const districts = data?.pages.flatMap((page) => page.items) || [];

  return {
    districts,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
};

export const useWards = (provinceCode?: string, districtCode?: string, query: string = "") => {
  // Format query for API: convert to lowercase, remove accents, replace spaces with dashes
  const formattedQuery = query ? formatSearchQuery(query) : "";

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery<
    PaginatedResponse<Ward>
  >({
    queryKey: QUERY_KEYS.ADDRESS_WARDS(provinceCode, districtCode, formattedQuery),
    queryFn: ({ pageParam = 0 }) => {
      // Cast to any to avoid TypeScript errors with dynamic properties
      const params: any = {
        limit: PAGE_SIZE,
        page: pageParam,
      };
      if (formattedQuery) {
        params.query = formattedQuery;
      }
      if (provinceCode) {
        params["province.code"] = provinceCode;
      }
      if (districtCode) {
        params["district.code"] = districtCode;
      }
      return addressWardApi.list(params) as Promise<PaginatedResponse<Ward>>;
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage || lastPage.items.length < PAGE_SIZE) return undefined;
      return Number(lastPage.page) + 1;
    },
    initialPageParam: 0,
    enabled: !!provinceCode && !!districtCode,
  });

  // Flatten all pages of data
  const wards = data?.pages.flatMap((page) => page.items) || [];

  return {
    wards,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
};

// Helper function to format search queries
function formatSearchQuery(query: string): string {
  // Remove diacritics (accents)
  const withoutAccents = query.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
  // Convert to lowercase and replace spaces with dashes
  return withoutAccents.toLowerCase().trim().replace(/\s+/g, "-");
}

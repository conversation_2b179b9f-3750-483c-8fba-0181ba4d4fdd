import { useState } from "react";
import { ClipboardPaste } from "lucide-react";

import { Button } from "./ui/button";
import { Popover, PopoverContent } from "./ui/popover";

interface ClipboardCopyProps {
  text: string;
  variant?: "default" | "secondary" | "ghost";
  size?: "default" | "sm" | "lg" | "xs";
  className?: string;
  onCopy?: () => void;
}

export const ClipboardCopy = ({
  text,
  variant = "secondary",
  size = "xs",
  className,
  onCopy,
}: ClipboardCopyProps) => {
  const [showPopover, setShowPopover] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setShowPopover(true);
      onCopy?.();

      // Tự động ẩn popover sau 1.5 giây
      setTimeout(() => {
        setShowPopover(false);
      }, 1500);
    } catch (error) {
      console.error("Không thể sao chép vào clipboard:", error);
    }
  };

  return (
    <div className="relative">
      <Button variant={variant} size={size} className={className} onClick={handleCopy}>
        <ClipboardPaste className="size-4" />
      </Button>
      <Popover open={showPopover}>
        <PopoverContent className="w-auto p-2 text-xs">Đã sao chép</PopoverContent>
      </Popover>
    </div>
  );
};

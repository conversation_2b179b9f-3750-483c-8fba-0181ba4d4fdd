"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import Footer from "@/features/xbots/components/footer";
import Header from "@/features/xbots/components/header";
import MessageList from "@/features/xbots/components/message-list";
import { useCreateConversation } from "@/features/xbots/hooks/use-conversation";
import useEmbedded from "@/features/xbots/hooks/use-embed";
import { useMessage } from "@/features/xbots/hooks/use-message";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

import { usePublicStaffDetail, useStaffDetail } from "../bots/staff/hooks/staff";
import { VirtualStaffModel } from "../bots/staff/hooks/type";
import { MESSAGE } from "./hooks/types";

interface Bot {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  jobTitle?: string;
}

interface UserInfo {
  name: string;
  phoneNumber: string;
}
interface VirtualStaffProps {
  botId: string;
  isConversation?: boolean;
  roleBot?: string;
  listMessenger?: Array<MESSAGE>;
  selectedStaff?: VirtualStaffModel;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
  conversationId?: string;
  senderRoles?: string[];
  receiverRoles?: string[];
  isStream?: boolean;
}

export const VirtualStaff = ({
  botId,
  isConversation = false,
  roleBot = "",
  listMessenger,
  selectedStaff,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  conversationId: propConversationId,
  senderRoles = ["EXTERNAL_USER"],
  receiverRoles = ["VIRTUAL_STAFF"],
  isStream = true,
}: VirtualStaffProps) => {
  const searchParams = useSearchParams();
  const isEmbedded = searchParams.get("embedded") === "true";

  // Use the appropriate staff detail hook based on whether the component is embedded
  const { data: staffFromPrivateApi, isLoading: isPrivateStaffLoading } = useStaffDetail(
    isEmbedded ? "" : botId
  );
  const { data: staffFromPublicApi, isLoading: isPublicStaffLoading } = usePublicStaffDetail(
    isEmbedded ? botId : ""
  );

  // Use public API data when embedded, otherwise use private API data
  const staff = isEmbedded ? staffFromPublicApi : staffFromPrivateApi;
  const isStaffLoading = isEmbedded ? isPublicStaffLoading : isPrivateStaffLoading;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<UserInfo>({ name: "", phoneNumber: "" });
  const [showInformation, setShowInformation] = useState(true);
  // If conversationId prop is provided, use it; otherwise use internal state
  const [internalConversationId, setInternalConversationId] = useState<string | null>(null);
  // Use prop conversation ID if provided, otherwise use internal state
  const conversationId = propConversationId || internalConversationId;
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  // Initialize conversation creation hook
  const createConversationMutation = useCreateConversation();

  const {
    messages,
    input,
    isLoading: isMessageLoading,
    isStreaming,
    streamingMessage,
    isSending,
    setInput,
    handleKeyDown,
    sendMessage,
    handleInputChange,
    setWelcomeMessages,
  } = useMessage({
    staffId: botId,
    isStream: isStream,
    role: roleBot,
    listMessenger,
    conversationId,
    customer_name: userInfo.name,
    customer_phone_number: userInfo.phoneNumber,
    isEmbedded,
  });
  console.log("conversation:", isStream);
  // If not embedded, handle the embedded script setup
  const { isLoaded: embedLoaded, error: embedError } = useEmbedded({
    botId,
    containerSelector: "#chat-embed-container",
    defaultOpen: false,
  });

  // Add scroll handler to detect when to load more messages
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const container = e.currentTarget;
      if (!container || !hasNextPage || isFetchingNextPage) return;

      const { scrollTop } = container;
      const threshold = 100;

      if (scrollTop < threshold) {
        fetchNextPage?.();
      }
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage]
  );

  const handleUserInfoSubmit = (name: string, phoneNumber: string) => {
    setUserInfo({ name, phoneNumber });

    // Create a new conversation when user submits their info
    if (staff?.id) {
      createConversationMutation.mutate(
        {
          customer_name: `${name}`,
          customer_phone_number: `${phoneNumber}`,
          assignee_id: staff.id,
          staff_id: staff.id,
        },
        {
          onSuccess: (response) => {
            // Handle the created conversation - response is ResponseList<CONVERSATION>
            if (response.items && response.items.length > 0) {
              const createdConversation = response.items[0];
              setInternalConversationId(createdConversation.id);
              toast.success("Chat session started");
            }
            setShowInformation(false);
            // Set welcome messages
            setWelcomeMessages(staff.name, name);
          },
          onError: (error) => {
            toast.error("Failed to start chat session");
            console.error("Error creating conversation:", error);
            // Still continue even if there's an error
            setShowInformation(false);
            setWelcomeMessages(staff.name, name);
          },
        }
      );
    } else {
      setShowInformation(false);
      // Set welcome messages
      if (staff) {
        setWelcomeMessages(staff.name, name);
      }
    }
  };

  const handleSubmit = () => {
    if (showInformation) return;
    sendMessage();
  };

  const handleInfoKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (userInfo.name && userInfo.phoneNumber) {
        handleUserInfoSubmit(userInfo.name, userInfo.phoneNumber);
      }
    }
  };

  useEffect(() => {
    if (isConversation) {
      setShowInformation(false);
    }
  }, [isConversation]);

  // Get the correct staff data to display
  const displayStaff = selectedStaff || staff;

  // Handle error state
  if (error) {
    return (
      <div className="flex h-full flex-col-reverse items-center justify-center p-4">
        <div className="max-w-md rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
          <h2 className="mb-2 text-lg font-bold">Error</h2>
          <p>{error}</p>
          {embedError && <p className="mt-2">{embedError}</p>}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-auto flex-col overflow-hidden bg-card">
      {isStaffLoading && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="rounded-lg bg-white p-6 shadow-lg dark:bg-zinc-900">
            <div className="flex items-center space-x-3">
              <Loader2 className="size-6 animate-spin text-orange-500" />
              <span className="font-medium">Loading virtual staff...</span>
            </div>
          </div>
        </div>
      )}

      <div className="flex-none">
        <Header
          name={displayStaff?.name || "Virtual Assistant"}
          jobTitle={displayStaff?.role || "Virtual Assistant"}
          avatar={displayStaff?.image?.url || ""}
          status="online"
          botId={botId}
          isEmbedded={isEmbedded}
          isConversation={isConversation}
        />
      </div>

      <div
        ref={chatContainerRef}
        className="flex flex-auto flex-col-reverse overflow-y-auto"
        onScroll={handleScroll}>
        {!showInformation ? (
          <MessageList
            messages={[...(listMessenger ?? []), ...messages]}
            streamingMessage={streamingMessage}
            isStreaming={isStreaming}
            isStream={isStream}
            isSending={isSending}
            isFetchingNextPage={isFetchingNextPage}
            conversationId={conversationId}
            staffAvatar={staff?.image?.url}
            senderRoles={senderRoles}
            receiverRoles={receiverRoles}
          />
        ) : null}
      </div>

      {showInformation && (
        <div className="px-4">
          <Card className="rounded-2xl bg-bg-primary p-4">
            <div className="flex flex-col gap-4">
              <div>
                <label htmlFor="name" className="mb-2 block text-sm font-medium">
                  Name
                </label>
                <Input
                  id="name"
                  placeholder="How can i call you?"
                  value={userInfo.name}
                  onChange={(e) => setUserInfo((prev) => ({ ...prev, name: e.target.value }))}
                  onKeyDown={handleInfoKeyDown}
                />
              </div>
              <div>
                <label htmlFor="phoneNumber" className="mb-2 block text-sm font-medium">
                  Phone number
                </label>
                <Input
                  id="phoneNumber"
                  placeholder="Enter your phone number..."
                  value={userInfo.phoneNumber}
                  onChange={(e) =>
                    setUserInfo((prev) => ({ ...prev, phoneNumber: e.target.value }))
                  }
                  onKeyDown={handleInfoKeyDown}
                />
              </div>
              <Button
                className="w-full"
                onClick={() => handleUserInfoSubmit(userInfo.name, userInfo.phoneNumber)}
                loading={createConversationMutation.isPending}
                disabled={!userInfo.name || !userInfo.phoneNumber}>
                {t("common.start", "Start")}
              </Button>
            </div>
          </Card>
        </div>
      )}

      <Footer
        input={input}
        isLoading={isMessageLoading || isStaffLoading || createConversationMutation.isPending}
        handleInputChange={handleInputChange}
        handleSubmit={handleSubmit}
        showInformation={showInformation}
        userInfo={userInfo}
        handleKeyDown={handleKeyDown}
        onUserInfoSubmit={() => handleUserInfoSubmit(userInfo.name, userInfo.phoneNumber)}
      />
    </div>
  );
};

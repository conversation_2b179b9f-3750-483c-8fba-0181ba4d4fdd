export const authKeys = {
  all: ["auth"] as const,
  login: () => [...authKeys.all, "login"] as const,
  signup: () => [...authKeys.all, "signup"] as const,
  register: () => [...authKeys.all, "register"] as const,
  forgotPassword: (email: string) => [...authKeys.all, "forgot-password", email] as const,
  resetPassword: (email: string) => [...authKeys.all, "reset-password", email] as const,
  changePassword: () => [...authKeys.all, "change-password"] as const,
  verifyCode: (username: string) => [...authKeys.all, "verify-code", username] as const,
  resendCode: (username: string) => [...authKeys.all, "resend-code", username] as const,
  verifyCodeReset: (username: string) => [...authKeys.all, "verify-code-reset", username] as const,
  userInfo: () => [...authKeys.all, "user-info"] as const,
} as const;

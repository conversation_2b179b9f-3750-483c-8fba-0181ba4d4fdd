import Link from "next/link";
import { Row } from "@tanstack/react-table";
import { capitalize } from "lodash";

import { Order } from "@/features/orders/hooks/types";

import ChannelLogo, {
  DateColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import { CustomColumn } from "@/components/data-table/data-table";
import { Badge } from "@/components/ui/badge";
import { ORDER_STATUS } from "@/constants/order_status";

export const customerOrderColumns = (t: any): CustomColumn<Order>[] => [
  {
    id: "order",
    accessorKey: "order",
    header: t("nav.order"),
    sorter: true,
    sortKey: "number",
    cell: ({ row }: { row: Row<Order> }) => {
      const order = row.original;
      return (
        <Link href={`/orders/${order.id}`}>
          <div className="flex cursor-pointer items-center gap-4 text-primary hover:underline">
            <TextColumn text={`${order?.number}`} />
          </div>
        </Link>
      );
    },
  },
  {
    id: "source",
    accessorKey: "source",
    header: t("pages.products.source"),
    sorter: true,
    sortKey: "source.id",
    cell: ({ row }: { row: Row<Order> }) => (
      <ChannelLogo channelKey={row?.original?.source?.channel_name || ""} href={""} />
    ),
  },
  {
    id: "order_status",
    accessorKey: "status",
    header: t("pages.orders.filters.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<Order> }) => {
      const status = row?.original?.status;
      const statusConfig = ORDER_STATUS[status as keyof typeof ORDER_STATUS] || {
        variant: "default",
      };

      return (
        <span className="text-sm">
          <Badge variant={statusConfig.variant}>{capitalize(status)}</Badge>
        </span>
      );
    },
  },
  {
    id: "payment_status",
    accessorKey: "payment_status",
    header: t("pages.orders.filters.paymentStatus"),
    sorter: true,
    sortKey: "payment_status",
    cell: ({ row }: { row: Row<Order> }) => {
      const paymentStatus = row?.original?.payment_status;

      // Determine variant based on payment status
      const variant =
        paymentStatus === "UNPAID" ? "destructive" : paymentStatus === "PAID" ? "blue" : "default";

      return (
        <span className="text-sm">
          <Badge variant={variant}>{capitalize(paymentStatus)}</Badge>
        </span>
      );
    },
  },
  {
    id: "amount",
    accessorKey: "amount",
    header: t("pages.orders.amount"),
    sorter: true,
    sortKey: "amount",
    cell: ({ row }: { row: Row<Order> }) => {
      // Use type casting for properties that might not be defined in the type
      const amount = (row.original as any)?.amount || 0;
      return <TextColumn text={`${amount}`} className="text-right" />;
    },
  },
  {
    id: "redeem_points",
    accessorKey: "redeem_points",
    header: t("pages.orders.redeemPoints"),
    sorter: true,
    sortKey: "redeem_points",
    cell: ({ row }: { row: Row<Order> }) => {
      // Use a fallback for redeem_points if it doesn't exist on the type
      const points = (row.original as any)?.redeem_points;
      return <TextColumn text={points?.toString() || "—"} className="text-center" />;
    },
  },
  {
    id: "loyal_points",
    accessorKey: "loyal_points",
    header: t("pages.orders.loyalPoints"),
    sorter: true,
    sortKey: "loyal_points",
    cell: ({ row }: { row: Row<Order> }) => {
      // Use a fallback for loyal_points if it doesn't exist on the type
      const points = (row.original as any)?.loyal_points;
      return <TextColumn text={points?.toString() || "—"} className="text-center" />;
    },
  },
  {
    id: "staff",
    accessorKey: "staff",
    header: t("pages.products.inventory.staff"),
    sorter: true,
    sortKey: "staff",
    cell: ({ row }: { row: Row<Order> }) => <TextColumn text={row?.original?.staff?.name || ""} />,
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.orders.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Order> }) => <DateColumn date={row?.original?.updated_at} />,
  },
];

"use client";

import { useState } from "react";
import { AlertTriangle, File, FileText, Globe, Trash, Upload } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import AddKnowledgeDialog from "@/features/bots/knowledge/components/knowledge-dialog/add-knowledge";
import { Knowledge } from "@/features/bots/knowledge/types";

import { Button } from "@/components/ui";
import { Badge } from "@/components/ui/badge";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";

import EditStaffCard from "../edit_staff_card";

interface KnowledgeTabProps {
  form: UseFormReturn<any>;
}

export default function KnowledgeTab({ form }: KnowledgeTabProps) {
  const [openUploadDialog, setOpenUploadDialog] = useState(false);

  const { t } = useTranslation();
  const knowledgeBase = form.watch("knowledge_base.knowledge_list") || [];

  const handleSave = (selectedKnowledgeItems: Knowledge[]) => {
    form.setValue("knowledge_base.knowledge_list", selectedKnowledgeItems, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const renderStatus = (status: string) => {
    let variant = "";
    const label = t(`pages.knowledge.status.${status.toLowerCase()}`);
    switch (status.toLowerCase()) {
      case "pending":
        variant = "sematic_pending";
        break;
      case "processing":
        variant = "sematic_info";
        break;
      case "ready":
        variant = "sematic_info";
        break;
      case "error":
        variant = "sematic_error";
        break;
      default:
        variant = "sematic_default";
        break;
    }
    return (
      <Badge
        variant={variant as any}
        className="flex-none text-xs font-semibold hover:no-underline">
        {label}
      </Badge>
    );
  };

  const handleDelete = (id: string) => {
    const currentKnowledge = form.getValues("knowledge_base.knowledge_list");
    form.setValue(
      "knowledge_base.knowledge_list",
      currentKnowledge.filter((knowledge: Knowledge) => knowledge?.id !== id),
      {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      }
    );
  };

  return (
    <Form {...form}>
      <EditStaffCard
        title={t("pages.staff.knowledge.tab")}
        headerButton={
          <Button type="button" size="sm" onClick={() => setOpenUploadDialog(true)}>
            <Upload className="size-4" />
            {t("common.upload")}
          </Button>
        }>
        <div className="flex h-full flex-col gap-4">
          <p className="flex gap-1 text-sm text-sematic-warning">
            <AlertTriangle className="size-4 shrink-0" />
            <span>{t("pages.staff.knowledgeWarning")}</span>
          </p>
          <FormField
            control={form.control}
            name="knowledge_base.knowledge_list"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col gap-2">
                    {knowledgeBase?.map((knowledge: Knowledge, idx: number) => (
                      <div
                        key={idx}
                        className="mb-2 flex items-center justify-between rounded border px-3 py-2">
                        <div className="flex max-w-[300px] items-center gap-2 xl:max-w-full">
                          {knowledge.source === "URL" ? (
                            <Globe className="size-4 shrink-0 text-gray-500" />
                          ) : knowledge.source === "FILE" ? (
                            <File className="size-4 shrink-0 text-gray-500" />
                          ) : (
                            <FileText className="size-4 shrink-0 text-gray-500" />
                          )}
                          <span className="text-sm">{knowledge.name}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          {renderStatus(knowledge.status)}
                          <Button
                            type="button"
                            variant="ghost"
                            size="xs"
                            onClick={() => handleDelete(knowledge.id)}>
                            <Trash className="size-4 text-destructive" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        {openUploadDialog && (
          <AddKnowledgeDialog
            open={openUploadDialog}
            onOpenChange={setOpenUploadDialog}
            onClose={() => setOpenUploadDialog(false)}
            showKnowledgeSection={true}
            selectedKnowledge={knowledgeBase}
            onSave={handleSave}
          />
        )}
      </EditStaffCard>
    </Form>
  );
}

"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { useProductDetail } from "@/features/integration/Product-Mapping/hooks/product-mapping";
import { MappingDetailSkeleton } from "@/features/integration/Product-Mapping/ProductMappingDetail/components/mapping-detail-skeletion";
import { MappingStatus } from "@/features/integration/Product-Mapping/ProductMappingDetail/mapping-status";
import { ProductMapping } from "@/features/integration/Product-Mapping/ProductMappingDetail/product-mapping";
import { Variant } from "@/features/integration/Product-Mapping/ProductMappingDetail/variant";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

export default function ProductMappingIdPage() {
  const params = useParams();
  const productId = params.id as string;
  const router = useRouter();
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [selectedConnectionId, setSelectedConnectionId] = useState<string>("");
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const {
    productDetail,
    sourceVariants,
    destinationVariants,
    mappedVariants,
    isLoading,
    isError,
    error,
    refetch,
  } = useProductDetail(productId);

  useEffect(() => {
    // If we have a productId, fetch the details
    if (productId) {
      refetch();
    }
  }, [productId, refetch]);

  // Auto-retry logic when there's an error
  useEffect(() => {
    if (isError && retryCount < 3 && !isRetrying) {
      setIsRetrying(true);
      const timer = setTimeout(() => {
        refetch();
        setRetryCount((prev) => prev + 1);
        setIsRetrying(false);
      }, 3000); // Retry after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, isRetrying, refetch]);

  const handleCancel = () => {
    setShowCancelDialog(true);
  };

  const handleConfirmedCancel = () => {
    setShowCancelDialog(false);
    toast.info(t("productMapping.cancelledMessage"));
    router.push("/product-mapping");
  };

  const handleEdit = async () => {
    // Use selected connection ID or fallback to the first item
    const connectionId = selectedConnectionId || productDetail?.data?.[0]?.connection_id;

    // Navigate to product attribute page with connection_id as query parameter
    if (connectionId) {
      router.push(
        `/product-mapping/${productId}/product-attribute?connection_id=${connectionId}&source_data=true`
      );
    } else {
      router.push(`/product-mapping/${productId}/product-attribute`);
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  const handlePlatformChange = (connectionId: string) => {
    setSelectedConnectionId(connectionId);
  };

  if (isLoading || isRetrying) {
    return <MappingDetailSkeleton />;
  }

  if (isError || !productDetail) {
    return (
      <div className="flex h-64 flex-col items-center justify-center gap-4">
        <p className="text-xl font-medium text-destructive">{t("productMapping.errorLoading")}</p>
        <div className="flex items-center gap-2">
          {/* <span>Retrying</span>
          <LoadingThreeDotsJumping /> */}
        </div>
        {retryCount >= 3 && (
          <Button
            onClick={() => {
              setRetryCount(0);
              refetch();
            }}
            className="mt-2">
            {t("productMapping.manualRetry")}
          </Button>
        )}
      </div>
    );
  }

  // Get the source data which should be consistent across all items
  const getSourceData = () => {
    // Find first item with valid source data
    const itemWithSourceData = productDetail.data?.find(
      (item: any) => item.standard_source_data && Object.keys(item.standard_source_data).length > 0
    );

    return itemWithSourceData?.standard_source_data || null;
  };

  // Find the current destination data based on selected connection ID
  const getDestinationData = () => {
    if (!productDetail?.data || productDetail.data.length === 0) {
      return null;
    }

    // If no connection ID is selected, prefer to return one with destination data
    if (!selectedConnectionId) {
      const itemWithDestData = productDetail.data.find(
        (item: any) =>
          item.standard_destination_data && Object.keys(item.standard_destination_data).length > 0
      );

      return itemWithDestData?.standard_destination_data || null;
    }

    // Find the item with the selected connection ID
    const selectedItem = productDetail.data.find(
      (item: any) => item.connection_id === selectedConnectionId
    );

    return selectedItem?.standard_destination_data || null;
  };

  // Get variants and mapping data
  const getSourceVariants = () => {
    // Source variants should be the same regardless of selected platform
    const sourceData = getSourceData();
    return sourceData?.variants || sourceVariants || [];
  };

  const getDestinationVariants = () => {
    const destinationData = getDestinationData();
    return destinationData?.variants || [];
  };

  const getMappedVariants = () => {
    // Get mapping data for the current connection
    if (!selectedConnectionId && productDetail?.data?.length > 0) {
      // If no connection selected, try to find one with mapping data
      const itemWithMapping = productDetail.data.find(
        (item: any) => item.other_mappings?.mapping_data
      );
      return itemWithMapping?.other_mappings?.mapping_data || mappedVariants;
    }

    // Find the item with the selected connection ID
    const selectedItem = productDetail.data.find(
      (item: any) => item.connection_id === selectedConnectionId
    );

    return selectedItem?.other_mappings?.mapping_data || mappedVariants;
  };

  // Prepare the data for the ProductMapping component
  const prepareMappingData = () => {
    const sourceData = getSourceData();
    const destinationData = getDestinationData();

    // Find the item with the selected connection ID
    const selectedItem = selectedConnectionId
      ? productDetail.data.find((item: any) => item.connection_id === selectedConnectionId)
      : productDetail.data[0];

    // Create a new object with the source and destination data
    return {
      ...selectedItem,
      standard_source_data: sourceData,
      standard_destination_data: destinationData,
    };
  };

  return (
    <div className="px-4 pb-4">
      <Card className="border-none">
        <CardContent className="p-0">
          {/* Mapping Status */}
          <div className="p-4">
            <Label>{t("productMapping.mappingStatus")}</Label>
            <MappingStatus
              productMappingDetail={productDetail}
              onRefresh={handleRefresh}
              onPlatformChange={handlePlatformChange}
            />
          </div>

          <Separator />

          {/* Product Comparison */}
          <div className="px-4">
            <ProductMapping productMappingDetail={prepareMappingData()} />
          </div>

          {/* Variants */}
          <div className="px-4 pt-4">
            <Label>{t("productMapping.variant")}</Label>
            <Variant
              sourceVariants={getSourceVariants()}
              destinationVariants={getDestinationVariants()}
              mappedVariants={getMappedVariants()}
            />
          </div>
        </CardContent>
      </Card>
      <div className="h-20"></div>

      {/* Footer with action buttons */}
      <div className="fixed inset-x-0 bottom-0 border-t bg-card px-4 py-3 shadow-2xl">
        <div className="relative flex">
          {/* Right column - Buttons */}
          <div className="ml-auto flex items-center justify-end">
            <div className="flex gap-2">
              <Button
                className="inline-flex w-24 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}>
                {t("common.back")}
              </Button>
              <Button
                className="inline-flex w-40 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
                type="button"
                disabled={isSubmitting}
                onClick={handleEdit}>
                {isSubmitting ? (
                  <div className="flex items-center">
                    <Loader2 className="size-4 animate-spin" />
                  </div>
                ) : (
                  t("common.updateAttribute")
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Add Cancel Confirmation Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("common.areYouSure")}</DialogTitle>
            <DialogDescription>{t("common.leaveDesc")}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowCancelDialog(false)}>
              {t("common.cancel")}
            </Button>
            <Button type="button" variant="default" onClick={handleConfirmedCancel}>
              {t("common.confirm")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

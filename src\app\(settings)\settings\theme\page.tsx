"use client";

import { useTranslation } from "react-i18next";

import ColorSetting from "@/features/settings/theme-settings/color-settings/color-setting";
import LogoSetting from "@/features/settings/theme-settings/logo-settings/logo-settings";

import { Card, CardTitle } from "@/components/ui";

export default function ThemeSettingPage() {
  const { t } = useTranslation();

  return (
    <div>
      <Card className="m-4 space-y-6 p-4">
        <CardTitle>{t("pages.settings.logoSetting")}</CardTitle>
        <LogoSetting />
      </Card>
      <Card className="m-4 space-y-6 p-4">
        <CardTitle>{t("pages.settings.colorSetting")}</CardTitle>
        <ColorSetting />
      </Card>
    </div>
  );
}

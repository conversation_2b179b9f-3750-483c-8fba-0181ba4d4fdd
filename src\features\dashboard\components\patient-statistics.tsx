"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Card } from "@/components/ui/card";
import { Tabs, <PERSON>bs<PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface PatientData {
  name: string;
  value: number;
}

interface PatientBarData {
  name: string;
  admitted: number;
  discharged: number;
  transferred: number;
}

interface PatientStatisticsProps {
  data: {
    outpatient: PatientData[];
    inpatient: PatientBarData[];
  };
}

type PatientType = "outpatient" | "inpatient";

export function PatientStatistics({ data }: PatientStatisticsProps) {
  const { t } = useTranslation();
  const [activeType, setActiveType] = useState<PatientType>("outpatient");

  return (
    <Card className="p-6">
      <div className="mb-6 flex flex-1 items-center justify-between">
        <h2 className="text-lg font-semibold">{t("pages.overview.patientStats.title")}</h2>
        <Tabs
          defaultValue="outpatient"
          value={activeType}
          onValueChange={(value) => setActiveType(value as PatientType)}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="outpatient">
              <span className="truncate">{t("pages.overview.patientStats.outpatient")}</span>
            </TabsTrigger>
            <TabsTrigger value="inpatient">
              <span className="truncate">{t("pages.overview.patientStats.inpatient")}</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        {activeType === "outpatient" ? (
          <AreaChart data={data.outpatient}>
            <defs>
              <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--data-1))" stopOpacity={0.8} />
                <stop offset="95%" stopColor="hsl(var(--data-1))" stopOpacity={0} />
              </linearGradient>
            </defs>
            <Area
              type="monotone"
              dataKey="value"
              stroke="hsl(var(--data-1))"
              fillOpacity={1}
              fill="url(#colorValue)"
            />
            <XAxis
              tickLine={false}
              axisLine={false}
              dataKey="name"
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "var(--radius)",
              }}
              labelStyle={{
                color: "hsl(var(--foreground))",
              }}
            />
          </AreaChart>
        ) : (
          <BarChart data={data.inpatient}>
            <XAxis
              tickLine={false}
              axisLine={false}
              dataKey="name"
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              stroke="hsl(var(--muted-foreground))"
              fontSize={12}
              tickFormatter={(value) => `$${value}`}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "var(--radius)",
              }}
              labelStyle={{
                color: "hsl(var(--foreground))",
              }}
            />
            <Bar dataKey="admitted" fill="hsl(var(--data-1))" radius={4} />
            <Bar dataKey="discharged" fill="hsl(var(--data-2))" radius={4} />
            <Bar dataKey="transferred" fill="hsl(var(--data-3))" radius={4} />
          </BarChart>
        )}
      </ResponsiveContainer>
    </Card>
  );
}

"use client";

import { Plus, Trash } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui";

import EditStaffCard from "../edit_staff_card";

interface TaskTabProps {
  form: UseFormReturn<any>;
}

export default function TaskTab({ form }: TaskTabProps) {
  const { t } = useTranslation();
  const tasks = form.watch("task.tasks") || [];

  const handleAddTask = () => {
    const currentTasks = form.getValues("task.tasks") || [];
    form.setValue("task.tasks", [
      ...currentTasks,
      { id: Date.now(), text: "", created: new Date().toISOString() },
    ]);
  };

  const handleDeleteTask = (taskId: number) => {
    const currentTasks = form.getValues("task.tasks");
    form.setValue(
      "task.tasks",
      currentTasks.filter((task: any) => task.id !== taskId)
    );
  };

  const handleTaskChange = (taskId: number, text: string) => {
    const currentTasks = form.getValues("task.tasks");
    form.setValue(
      "task.tasks",
      currentTasks.map((task: any) => (task.id === taskId ? { ...task, text } : task))
    );
  };

  return (
    <EditStaffCard
      title={t("pages.staff.task.tab")}
      headerButton={
        <Button leftIcon={<Plus size={16} />} size={"sm"} onClick={handleAddTask}>
          {t("common.add")}
        </Button>
      }>
      <div className="divide-y">
        {tasks.map((task: any) => (
          <div key={task.id} className="flex items-center justify-between py-3">
            <div>
              <input
                type="text"
                value={task.text}
                onChange={(e) => handleTaskChange(task.id, e.target.value)}
                placeholder={t("pages.staff.task.enterTask")}
                className="w-full border-none bg-transparent p-0 text-sm focus:outline-none"
              />
            </div>
            <div className="flex w-40 items-center justify-between px-4">
              <p className="text-sm text-muted-foreground">
                {new Date(task.created).toLocaleDateString()}
              </p>
              <button
                className="text-destructive hover:text-destructive-hover"
                onClick={() => handleDeleteTask(task.id)}>
                <Trash size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </EditStaffCard>
  );
}

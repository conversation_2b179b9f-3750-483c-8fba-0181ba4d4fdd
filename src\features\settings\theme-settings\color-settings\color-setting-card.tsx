import { ColorSettingItem } from "./color-setting-item";
import { ThemeColor } from "./types";

interface ColorSettingCardProps {
  mode: "light" | "dark";
  colors: ThemeColor[];
  onColorChange: (mode: "light" | "dark", key: string, color: string) => void;
}

export function ColorSettingCard({ mode, colors, onColorChange }: ColorSettingCardProps) {
  return (
    <div className="grid grid-cols-1 gap-4  xl:grid-cols-2">
      {colors.map((color) => (
        <ColorSettingItem
          key={color.key}
          keyColor={color.key}
          value={color.value}
          description={color.description}
          onChange={(key, newColor) => onColorChange(mode, key, newColor)}
          subColors={color.subColor}
          mode={mode}
        />
      ))}
    </div>
  );
}

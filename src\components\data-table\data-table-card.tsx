import { PropsWithChildren } from "react";

import { cn } from "@/lib/utils";

import { Card } from "../ui";

interface TableCardProps extends PropsWithChildren {
  className?: string;
}

export default function TableCard({ children, className }: TableCardProps) {
  return (
    <Card
      className={cn(
        "m-4 mt-0 flex h-fit max-h-[calc(100vh-84px)] flex-col space-y-0 overflow-hidden p-4",
        className
      )}>
      {children}
    </Card>
  );
}

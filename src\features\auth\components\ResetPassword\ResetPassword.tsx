"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { BrandSection } from "@/features/auth/components/brand-section";
import { Footer } from "@/features/auth/components/public-footer";
import { useResetPassword } from "@/features/auth/hooks/useResetPassword";
import {
  ResetPasswordFormValues,
  resetPasswordSchema,
} from "@/features/auth/utils/validators/resetPass";

import { Button } from "@/components/ui/button";
import { Form, FormField } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { createI18nResolver } from "@/lib/utils";

import { ResetSuccessModal } from "./ResetSuccessModal";

export const ResetPassword = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const session = searchParams?.get("session");
  const { t } = useTranslation();
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const form = useForm<ResetPasswordFormValues>({
    resolver: createI18nResolver(resetPasswordSchema, t),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    if (!session) {
      router.replace("/forgot-password");
    }
  }, [session, router]);

  const { onSubmit: onResetPassword, loading } = useResetPassword({
    onSuccess: () => {
      setShowSuccessModal(true);
    },
  });

  const onSubmit = (data: ResetPasswordFormValues) => {
    onResetPassword({ formData: data, session: session || "" });
  };

  return (
    <div className="min-h-screen w-screen">
      <div className="grid min-h-screen w-screen lg:grid-cols-[1fr_2fr]">
        <BrandSection />
        <div className="relative flex flex-col justify-between p-8">
          <div className="flex flex-1 items-center justify-center">
            <div className="relative z-10 w-full max-w-[400px] space-y-6">
              <div className="space-y-1.5">
                <h1 className="text-2xl font-semibold tracking-tight">{t("auth.resetPassword")}</h1>
                <p className="text-sm text-muted-foreground">{t("auth.resetPasswordSubtitle")}</p>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="newPassword"
                      render={({ field, fieldState }) => (
                        <Input
                          {...field}
                          label={t("auth.newPassword")}
                          type="password"
                          placeholder={t("auth.passwordPlaceholder")}
                          disabled={loading}
                          error={fieldState.error?.message}
                        />
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field, fieldState }) => (
                        <Input
                          {...field}
                          label={t("auth.confirmPassword")}
                          type="password"
                          placeholder={t("auth.confirmPasswordPlaceholder")}
                          disabled={loading}
                          error={fieldState.error?.message}
                        />
                      )}
                    />
                  </div>

                  <Button type="submit" disabled={loading} loading={loading} className="w-full">
                    {loading ? t("auth.resetting") : t("auth.resetPassword")}
                  </Button>
                </form>
              </Form>
            </div>
          </div>

          <Footer />
        </div>
      </div>

      {showSuccessModal && <ResetSuccessModal />}
    </div>
  );
};

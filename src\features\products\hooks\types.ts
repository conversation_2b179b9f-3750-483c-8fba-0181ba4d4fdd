export interface CreateImage {
  name: string;
  image: string; // base64 or file data
}

export interface Image {
  id: string;
  name: string;
  url: string; // Changed from 'image' to 'url' to better reflect its purpose
}

export interface Brand {
  image?: string | null;
  name: string;
  id: string;
}

export interface Category {
  id: string;
  name: string;
  image?: string | null;
  parent_category_id?: string | null;
  has_children?: boolean;
  meta_data?: any | null;
}

export interface Price {
  price: number;
  price_group: {
    name: string;
    id: string;
  };
}

export interface Measurements {
  height_unit: string;
  weight_unit: string;
  length_value: number;
  weight_value: number;
  height_value: number;
  width_unit: string;
  width_value: number;
  length_unit: string;
}

export interface Variant {
  id: string;
  name: string;
  sku: string;
  option1: string | null;
  option2: string | null;
  option3: string | null;
  images?: Image[] | null;
  prices: Price[];
  measurements: Measurements;
  company_id?: string;
  created_at?: string;
  updated_at?: string;
  product_id?: string;
  barcode?: string | null;
  brand?: Brand | null;
  category?: Category | null;
  inventories?: any | null;
  user?: any | null;
  title?: string;
}

export interface Product {
  company_id: string;
  created_at: string;
  tags: string | null;
  updated_at: string;
  publish: boolean;
  name: string;
  id: string;
  sku: string;
  barcode: string | null;
  brand: Brand | null;
  category: Category | null;
  images: Image[]; // This uses the response/display Image interface
  prices: Price[] | null;
  variants: Variant[];
  options: any | null;
  description: string;
  shortDescription: string;
  source: Source | null;
  title?: string;
}

export interface Source {
  id: string;
  name: string;
  channel_name: string;
  logo: string;
  origin: string;
  origin_id: string;
}

export interface ProductOptionValue {
  id: string;
  value: string;
  image?: string;
}

export interface ProductOption {
  name: string;
  values: ProductOptionValue[];
}

export interface ProductUnit {
  id: string;
  variant: string;
  variant_name?: string;
  unit: string;
  unit_name?: string;
  child_variant_id?: string;
  child_variant_ids?: string[];
}

export interface PriceVariant {
  id: string;
  name: string;
  sku: string;
  option1: string | null;
  option2: string | null;
  option3: string | null;
  prices: Price[];
  images?: { image: string; name: string }[];
  original_sku?: string;
  unit?: {
    id: string;
    name: string;
    ratio: number;
  };
  parent_variant_id?: string;
  measurements: Measurements;
}

export interface PriceGroup {
  id: string;
  name: string;
  company_id: string;
  created_at: string;
  updated_at: string;
}

export interface PriceGroupResponse {
  total: number;
  page: number;
  limit: string;
  items: PriceGroup[];
}

export interface BrandResponse {
  total: number;
  page: number;
  limit: number;
  items: BrandItem[];
}

export interface BrandItem {
  id: string;
  name: string;
  image: string | null;
  company_id: string;
  created_at: string;
  updated_at: string;
  product_count: number;
}

export interface CategoryResponse {
  total: number;
  page: number;
  limit: number;
  items: CategoryItem[];
}

export interface CategoryItem {
  id: string;
  name: string;
  image: string | null;
  company_id: string;
  created_at: string;
  updated_at: string;
  product_count: number;
}

export interface BasicInformationValues {
  images: CreateImage[];
  name: string;
  description: string;
  shortDescription: string;
  brand: { id: string; name: string } | string;
  category: { id: string; name: string } | string;
  sku: string;
  tags: string | null;
  _touched?: {
    name: boolean;
    sku: boolean;
  };
}

export interface UnitItem {
  id: string;
  name: string;
  ratio: number;
  company_id: string;
  created_at: string;
  updated_at: string;
}

export interface UnitResponse {
  total: number;
  page: number;
  limit: string;
  items: UnitItem[];
}

export interface CreateVariant {
  id: string;
  name: string;
  sku: string;
  option1: string | null;
  option2: string | null;
  option3: string | null;
  images?: CreateImage[] | null;
  prices: Price[];
  measurements: Measurements;
  original_sku?: string;
  parent_variant_id?: string;
  unit?: {
    id: string;
    name: string;
    ratio: number;
  };
  company_id?: string;
  created_at?: string;
  updated_at?: string;
  product_id?: string;
  barcode?: string | null;
  brand?: Brand | null;
  category?: Category | null;
  inventories?: any | null;
  user?: any | null;
}

// New interface specifically for creating products
export interface CreateProduct {
  name: string;
  sku: string;
  tags: string | null;
  description: string;
  shortDescription: string;
  brand: { id: string; name: string } | null;
  category: { id: string; name: string } | null;
  images: CreateImage[];
  options: ProductOption[];
  variants: CreateVariant[];
  measurements: Measurements;
}

export interface InventoryData {
  branch: string;
  available: number;
  incoming: number;
  onHand: number;
  packing: number;
  shipping: number;
  minValue: number;
  maxValue: number;
}

export interface ProductOptionValue {
  id: string;
  value: string;
  image?: string;
}

export interface ProductOption {
  name: string;
  values: ProductOptionValue[];
}

export interface ProductUnit {
  id: string;
  variant: string;
  variant_name?: string;
  unit: string;
  unit_name?: string;
  child_variant_id?: string;
  child_variant_ids?: string[];
}

export interface PriceVariant {
  id: string;
  name: string;
  sku: string;
  option1: string | null;
  option2: string | null;
  option3: string | null;
  prices: Price[];
  images?: { image: string; name: string }[];
  original_sku?: string;
  unit?: {
    id: string;
    name: string;
    ratio: number;
  };
  parent_variant_id?: string;
  measurements: Measurements;
}

export interface PriceGroup {
  id: string;
  name: string;
  company_id: string;
  created_at: string;
  updated_at: string;
}

export interface PriceGroupResponse {
  total: number;
  page: number;
  limit: string;
  items: PriceGroup[];
}

export interface BrandResponse {
  total: number;
  page: number;
  limit: number;
  items: BrandItem[];
}

export interface BrandItem {
  id: string;
  name: string;
  image: string | null;
  company_id: string;
  created_at: string;
  updated_at: string;
  product_count: number;
}

export interface CategoryResponse {
  total: number;
  page: number;
  limit: number;
  items: CategoryItem[];
}

export interface CategoryItem {
  id: string;
  name: string;
  image: string | null;
  company_id: string;
  created_at: string;
  updated_at: string;
  product_count: number;
}

export interface BasicInformationValues {
  images: CreateImage[];
  name: string;
  description: string;
  shortDescription: string;
  brand: { id: string; name: string } | string;
  category: { id: string; name: string } | string;
  sku: string;
  tags: string | null;
  _touched?: {
    name: boolean;
    sku: boolean;
  };
}

export interface UnitItem {
  id: string;
  name: string;
  ratio: number;
  company_id: string;
  created_at: string;
  updated_at: string;
}

export interface UnitResponse {
  total: number;
  page: number;
  limit: string;
  items: UnitItem[];
}

export interface CreateVariant {
  id: string;
  name: string;
  sku: string;
  option1: string | null;
  option2: string | null;
  option3: string | null;
  images?: CreateImage[] | null;
  prices: Price[];
  measurements: Measurements;
  original_sku?: string;
  parent_variant_id?: string;
  unit?: {
    id: string;
    name: string;
    ratio: number;
  };
  company_id?: string;
  created_at?: string;
  updated_at?: string;
  product_id?: string;
  barcode?: string | null;
  brand?: Brand | null;
  category?: Category | null;
  inventories?: any | null;
  user?: any | null;
}

// New interface specifically for creating products
export interface CreateProduct {
  name: string;
  sku: string;
  tags: string | null;
  description: string;
  shortDescription: string;
  brand: { id: string; name: string } | null;
  category: { id: string; name: string } | null;
  images: CreateImage[];
  options: ProductOption[];
  prices: Price[];
  variants: CreateVariant[];
  measurements: Measurements;
  publish: boolean;
}

export interface InventoryData {
  branch: string;
  available: number;
  incoming: number;
  onHand: number;
  packing: number;
  shipping: number;
  minValue: number;
  maxValue: number;
}

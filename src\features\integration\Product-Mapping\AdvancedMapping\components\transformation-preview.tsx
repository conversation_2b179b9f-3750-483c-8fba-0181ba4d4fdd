import { useTranslation } from "react-i18next";

import { Card } from "@/components/ui";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

import { Transformation } from "../hooks/types";
import { usePreviewTransformation } from "../hooks/use-preview-transformation";
import { TransformationChain } from "./transformation-chain";
import { TransformationRuleChain } from "./transformation-rule-chain";

interface TransformationPreviewProps {
  activeTab: "output" | "rule";
  onTabChange: (value: "output" | "rule") => void;
  sourceValue: string;
  transformedValue: string;
  sourceField: string;
  transformations: Transformation[];
  syncRecordId?: string;
  connectionId?: string;
  sourceData?: any;
}

export function TransformationPreview({
  activeTab,
  onTabChange,
  sourceValue,
  transformedValue,
  sourceField,
  transformations,
  syncRecordId = "",
  connectionId = "",
  sourceData,
}: TransformationPreviewProps) {
  const { t } = useTranslation();

  // Use our custom hook to handle transformation logic
  const { isLoading, getChainOutput, getPreviousOutput, getFinalOutput } = usePreviewTransformation(
    {
      sourceValue,
      transformedValue,
      sourceField,
      transformations,
      syncRecordId,
      connectionId,
      activeTab,
    }
  );

  return (
    <div className="flex h-fit flex-col gap-4">
      <Card className="border-none p-4">
        <Tabs value={activeTab} onValueChange={(value) => onTabChange(value as any)}>
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              {t("pages.productMapping.advancedMapping.preview")}
            </h3>
            <TabsList className="w-fit justify-start">
              <TabsTrigger value="output">
                {t("pages.productMapping.advancedMapping.output")}
              </TabsTrigger>
              <TabsTrigger value="rule">
                {t("pages.productMapping.advancedMapping.ruleConfiguration")}
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="output" className="mt-4 space-y-6">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            ) : (
              transformations.map((transformation, index) => (
                <TransformationChain
                  key={transformation.id || `transformation-${index}`}
                  transformation={transformation}
                  index={index}
                  sourceValue={sourceValue}
                  transformedValue={getChainOutput(transformation, index)}
                  isLastTransformation={index === transformations.length - 1}
                  previousOutput={getPreviousOutput(index)}
                />
              ))
            )}
          </TabsContent>

          <TabsContent value="rule" className="mt-4 space-y-6">
            {transformations.map((transformation, index) => (
              <TransformationRuleChain
                key={transformation.id || `rule-${index}`}
                transformation={transformation}
                index={index}
                sourceField={sourceField}
                sourceData={sourceData}
              />
            ))}
          </TabsContent>
        </Tabs>
      </Card>

      <Card className="space-y-2 border-none p-4 text-sm">
        <h4 className="font-medium">{t("pages.productMapping.advancedMapping.finalOutput")}</h4>
        <div className="break-all rounded-md border border-border bg-muted p-2">
          {isLoading ? <Skeleton className="h-6 w-full" /> : getFinalOutput()}
        </div>
      </Card>
    </div>
  );
}

import { useEffect, useMemo, useRef, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ArrowRightFromLine, ImageIcon, MoreVertical, Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { QUERY_KEYS } from "@/features/products/hooks/keys";
import type { PriceVariant, ProductOption } from "@/features/products/hooks/types";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { CustomImage } from "@/components/ui/image";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { priceGroupApi } from "@/lib/apis/product";
import { cn } from "@/lib/utils";
import { formatPrice, parsePrice } from "@/utils/helpers/price-formater";

interface ProductPricesProps {
  variants: PriceVariant[];
  options: ProductOption[];
  onChange: (groupId: string, variants: PriceVariant[]) => void;
  showValidation?: boolean;
  onValidationChange?: (hasErrors: boolean) => void;
  onSectionFocus?: () => void;
}

export function ProductPrices({
  variants,
  options,
  onChange,
  showValidation = false,
  onValidationChange,
  onSectionFocus,
}: ProductPricesProps) {
  // console.log("ProductPrices - Received:", {
  //   variants,
  //   options,
  //   variantsWithUnits: variants.filter((v) => v.unit?.id),
  //   variantsWithoutUnits: variants.filter((v) => !v.unit?.id),
  // });

  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [activeGroup, setActiveGroup] = useState<string>("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newGroup, setNewGroup] = useState<{ name: string }>({ name: "" });
  const [applyPrice, setApplyPrice] = useState("");

  // Add ref for the tabs container
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  // Add this state at the top of the component
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Add state for touched fields
  const [touchedFields, setTouchedFields] = useState<{
    [key: string]: boolean;
  }>({});

  // Fetch price groups
  const { data: priceGroupsData } = useQuery({
    queryKey: QUERY_KEYS.PRICE_GROUPS,
    queryFn: async () => {
      const response = await priceGroupApi.list();
      return response;
    },
  });

  // Create price group mutation
  const createPriceGroupMutation = useMutation({
    mutationFn: (name: string) => priceGroupApi.create(name),
    onSuccess: async (response) => {
      await queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.PRICE_GROUPS,
      });
      setIsDialogOpen(false);
      setNewGroup({ name: "" });

      // Wait for the price groups to update
      setTimeout(() => {
        const newGroupId = response.id;
        handleTabChange(newGroupId);
      }, 100);
    },
  });

  // Set initial active group
  useEffect(() => {
    if (priceGroupsData?.items.length && !activeGroup) {
      setActiveGroup(priceGroupsData.items[0].id);
    }
  }, [priceGroupsData, activeGroup]);

  // Update validation effect
  useEffect(() => {
    if (showValidation) {
      let hasErrors = false;
      variants.forEach((variant) => {
        // Ensure prices is an array
        const prices = Array.isArray(variant.prices) ? variant.prices : [];
        if (!prices.some((p) => p.price_group.id === activeGroup && p.price)) {
          hasErrors = true;
        }
      });
      onValidationChange?.(hasErrors);
    }
  }, [showValidation, variants, activeGroup, onValidationChange]);

  const handleTabChange = (groupId: string) => {
    setActiveGroup(groupId);

    // Wait for state update and DOM to reflect changes
    setTimeout(() => {
      // Find the active tab and scrollable container
      const scrollContainer = document.querySelector(".flex.overflow-x-auto") as HTMLElement;
      const activeTab = scrollContainer?.querySelector(
        `[role="tab"][data-state="active"]`
      ) as HTMLElement;

      if (scrollContainer && activeTab) {
        const scrollLeft =
          activeTab.offsetLeft - scrollContainer.offsetWidth / 2 + activeTab.offsetWidth / 2;

        scrollContainer.scrollTo({
          left: scrollLeft,
          behavior: "smooth",
        });
      }
    }, 0);
  };

  // Add this for handling direct tab clicks
  const handleTabClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const clickedTab = (e.target as HTMLElement).closest('[role="tab"]') as HTMLElement;
    if (!clickedTab) return;

    const scrollContainer = clickedTab.closest(".overflow-x-auto") as HTMLElement;
    if (!scrollContainer) return;

    const scrollLeft =
      clickedTab.offsetLeft - scrollContainer.offsetWidth / 2 + clickedTab.offsetWidth / 2;

    scrollContainer.scrollTo({
      left: scrollLeft,
      behavior: "smooth",
    });
  };

  const handleAddPriceGroup = () => {
    if (!newGroup.name.trim()) return;
    createPriceGroupMutation.mutate(newGroup.name);
  };

  // Update ensurePricesArray to clean up the prices object
  const ensurePricesArray = (prices: any) => {
    if (!prices) return [];

    // If it's an object, convert to clean array format
    if (typeof prices === "object" && !Array.isArray(prices)) {
      const cleanPrices = [];
      // Only take numeric keys (like "0", "1", etc)
      for (const key in prices) {
        if (/^\d+$/.test(key) && prices[key].price_group) {
          cleanPrices.push(prices[key]);
        }
      }
      return cleanPrices;
    }

    // If it's already an array, return as is
    if (Array.isArray(prices)) {
      return prices;
    }

    return [];
  };

  // Update handlePriceChange
  const handlePriceChange = (variantId: string, value: string) => {
    const updatedVariants = variants.map((variant) => {
      if (variant.id === variantId) {
        const currentPrices = ensurePricesArray(variant.prices);
        const priceIndex = currentPrices.findIndex((p) => p.price_group?.id === activeGroup);

        const newPrice = {
          price: parsePrice(value),
          price_group: {
            id: activeGroup,
            name: priceGroupsData?.items.find((g) => g.id === activeGroup)?.name || "",
          },
        };

        return {
          ...variant,
          prices:
            priceIndex >= 0
              ? currentPrices.map((p, index) => (index === priceIndex ? newPrice : p))
              : [...currentPrices, newPrice],
        };
      }
      return variant;
    });

    onChange(activeGroup, updatedVariants);
  };

  // Update handleApplyAll
  const handleApplyAll = () => {
    if (!applyPrice || !activeGroup) return;

    const updatedVariants = variants.map((variant) => {
      const currentPrices = ensurePricesArray(variant.prices);
      const priceIndex = currentPrices.findIndex((p) => p.price_group?.id === activeGroup);

      const newPrice = {
        price: parsePrice(applyPrice),
        price_group: {
          id: activeGroup,
          name: priceGroupsData?.items.find((g) => g.id === activeGroup)?.name || "",
        },
      };

      const updatedPrices =
        priceIndex >= 0
          ? currentPrices.map((p, index) => (index === priceIndex ? newPrice : p))
          : [...currentPrices, newPrice];

      return {
        ...variant,
        prices: updatedPrices,
      };
    });

    onChange(activeGroup, updatedVariants);
    setApplyPrice("");
  };

  // Group variants by parent-child relationship
  const validVariants = useMemo(() => {
    // Step 1: Create a map to organize variants by SKU
    const skuMap = new Map<string, PriceVariant>();

    // First pass: index all variants by their SKU
    variants.forEach((variant) => {
      skuMap.set(variant.sku, variant);
    });

    // Step 2: Identify parent variants and child variants
    const parentVariants: PriceVariant[] = [];
    const childVariantMap = new Map<string, PriceVariant[]>();

    variants.forEach((variant) => {
      // If variant has an original_sku that is different from its SKU, it's a child
      if (variant.original_sku && variant.original_sku !== variant.sku) {
        // Add to the appropriate parent's children array
        if (!childVariantMap.has(variant.original_sku)) {
          childVariantMap.set(variant.original_sku, []);
        }
        childVariantMap.get(variant.original_sku)?.push(variant);
      } else {
        // It's a parent variant (or standalone)
        parentVariants.push(variant);
      }
    });

    // Step 3: Create the final ordered array with parents followed by their children
    const result: PriceVariant[] = [];

    parentVariants.forEach((parent) => {
      // Add the parent first
      result.push(parent);

      // Add any children that belong to this parent
      const children = childVariantMap.get(parent.sku) || [];
      result.push(...children);
    });

    return result;
  }, [variants]);

  // Add these handlers
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    setStartX(e.pageX - e.currentTarget.offsetLeft);
    setScrollLeft(e.currentTarget.scrollLeft);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - e.currentTarget.offsetLeft;
    const walk = (x - startX) * 2;
    e.currentTarget.scrollLeft = scrollLeft - walk;
  };

  // Update input handler to include blur event
  // const handlePriceBlur = (variantId: string, field: string) => {
  //   setTouchedFields((prev) => ({
  //     ...prev,
  //     [`${variantId}-${field}`]: true,
  //   }));
  // };

  return (
    <div onClick={() => onSectionFocus?.()} onFocus={() => onSectionFocus?.()}>
      {/* <div className="text-sm text-muted-foreground pb-4">Variant prices.</div> */}

      {/* Price Group Tabs */}
      <div className="relative mx-2 flex items-center">
        {/* Left shadow gradient */}
        <div />

        {/* Scrollable tabs */}
        <div
          className="hide-scrollbar flex overflow-x-auto"
          onMouseDown={handleMouseDown}
          onMouseLeave={handleMouseLeave}
          onMouseUp={handleMouseUp}
          onMouseMove={handleMouseMove}
          onClick={handleTabClick}>
          <Tabs value={activeGroup} onValueChange={handleTabChange} className="relative">
            <TabsList
              className="hide-scrollbar flex h-auto w-full gap-2 overflow-x-auto rounded-none bg-transparent p-0"
              ref={tabsContainerRef}>
              {priceGroupsData?.items.map((group) => (
                <TabsTrigger
                  key={group.id}
                  value={group.id}
                  className={cn(
                    "rounded-none border-b-2 flex-shrink-0 hide-scrollbar pb-4 font-semibold",
                    "data-[state=active]:shadow-none",
                    "data-[state=active]:bg-transparent data-[state=active]:border-primary",
                    "data-[state=inactive]:border-transparent",
                    "data-[state=active]:h-full"
                  )}>
                  <div className="whitespace-nowrap">{group.name}</div>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>

        {/* Tab List Popover */}
        <div className="shrink-0 border-l">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical size={16} />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-48 p-0" align="end">
              <div className="max-h-[300px] overflow-y-auto py-2">
                {priceGroupsData?.items.map((group) => (
                  <div className="flex" key={group.id}>
                    <Button
                      variant="ghost"
                      className={cn(
                        "w-full justify-start rounded-none px-3 py-2 text-left font-normal flex flex-col items-start",
                        activeGroup === group.id && "bg-muted"
                      )}
                      onClick={() => handleTabChange(group.id)}>
                      {group.name}
                    </Button>
                  </div>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Fixed Add Button */}
        <div className="relative z-10 ml-2 shrink-0 bg-background">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="icon">
                <Plus size={16} />
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[400px]">
              <DialogHeader>
                <DialogTitle>{t("pages.products.addManual.prices.addGroup")}</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">{t("pages.products.name")}</label>
                    <Input
                      placeholder="Enter price group name"
                      value={newGroup.name}
                      onChange={(e) =>
                        setNewGroup((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  {t("common.cancel")}
                </Button>
                <Button onClick={handleAddPriceGroup}>{t("common.add")}</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Variants Table */}
      <div className="relative overflow-hidden rounded-lg border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className={options.length > 0 ? "" : "border-none"}>
                {["Color", null, null].map((fallback, i) =>
                  options[i]?.name || fallback ? (
                    <TableHead key={i} className="w-[30px] min-w-[30px]">
                      {options[i]?.name || fallback}
                    </TableHead>
                  ) : null
                )}
                <TableHead className="w-[30px] min-w-[30px]">SKU</TableHead>
                <TableHead className="w-[30px] min-w-[30px] text-right">
                  <div className="flex items-center justify-end gap-2">
                    <div className="flex items-center">
                      <Input
                        type="text"
                        min="0"
                        placeholder="Price"
                        value={formatPrice(applyPrice)}
                        onChange={(e) => {
                          setApplyPrice(e.target.value);
                        }}
                        suffix="đ"
                        className="w-[110px]"
                      />
                    </div>
                    <Button onClick={handleApplyAll} disabled={!applyPrice || !activeGroup}>
                      {t("pages.products.addManual.sections.apply")}
                    </Button>
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {options.length > 0 &&
                validVariants.map((variant) => (
                  <TableRow key={variant.id}>
                    <TableCell className="w-[30px] min-w-[30px]">
                      <div className="flex items-center gap-4">
                        {variant.images?.[0] ? (
                          <div className="relative aspect-square w-[50px]">
                            <CustomImage
                              src={variant.images[0].image}
                              alt={variant.option1 || ""}
                              width={100}
                              height={100}
                              className="size-full rounded-md border object-cover"
                            />
                          </div>
                        ) : (
                          <div className="flex aspect-square w-[50px] items-center justify-center rounded-md border bg-muted">
                            <ImageIcon className="size-4 text-muted-foreground" />
                          </div>
                        )}
                        <div className="flex items-center gap-2">
                          {/* Only show arrow for variants where SKU differs from original_sku */}
                          {variant.original_sku && variant.original_sku !== variant.sku && (
                            <ArrowRightFromLine className="size-4 text-muted-foreground" />
                          )}
                          <span
                            className="inline-block max-w-[150px]"
                            title={variant.option1 || ""}>
                            {variant.option1 && variant.option1.length > 20
                              ? variant.option1.slice(0, 20) + "..."
                              : variant.option1}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    {options[1] && (
                      <TableCell className="w-[30px] min-w-[30px]">
                        <span className="inline-block max-w-[150px]" title={variant.option2 || ""}>
                          {variant.option2 && variant.option2.length > 20
                            ? variant.option2.slice(0, 20) + "..."
                            : variant.option2}
                        </span>
                      </TableCell>
                    )}
                    {options[2] && (
                      <TableCell className="w-[30px] min-w-[30px]">
                        <span className="inline-block max-w-[150px]" title={variant.option3 || ""}>
                          {variant.option3 && variant.option3.length > 20
                            ? variant.option3.slice(0, 20) + "..."
                            : variant.option3}
                        </span>
                      </TableCell>
                    )}
                    <TableCell className="w-[30px] min-w-[30px]">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="inline-block max-w-[150px] truncate">
                              {variant.sku}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{variant.sku}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell className="w-[30px] min-w-[30px] text-right">
                      <div className="flex items-center justify-end gap-2">
                        <div className="flex items-center gap-2">
                          <Input
                            type="text"
                            placeholder="Enter price"
                            value={formatPrice(
                              ensurePricesArray(variant.prices)
                                ?.find((p) => p.price_group?.id === activeGroup)
                                ?.price?.toString() || "0"
                            )}
                            onChange={(e) => {
                              handlePriceChange(variant.id, e.target.value);
                            }}
                            // onBlur={() => handlePriceBlur(variant.id, "price")}
                            className={cn(
                              "w-[190px]",
                              touchedFields[`${variant.id}-price`] &&
                                !ensurePricesArray(variant.prices).find(
                                  (p) => p.price_group?.id === activeGroup
                                )?.price &&
                                "border-destructive focus-visible:ring-destructive"
                            )}
                            suffix="đ"
                            defaultValue={0}
                          />
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}

export function validatePrices(variants: PriceVariant[], activeGroup: number) {
  const errors: { field: string; variantId: string; message: string }[] = [];

  if (!activeGroup) return errors; // Don't validate if no group selected

  variants.forEach((variant) => {
    if (!variant.prices?.[activeGroup]?.price) {
      errors.push({ field: "price", variantId: variant.id, message: "Price is required" });
    }
  });

  return errors;
}

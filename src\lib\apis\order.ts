import { Order } from "@/features/orders/hooks/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

export const orderApi = {
  list: async (params?: Record<string, unknown>) => {
    const response = await privateApi.get<ResponseList<Order>>(ENDPOINTS.ORDER.LIST, {
      params,
    });
    return response;
  },

  create: async (data: Partial<Order>) => {
    const response = await privateApi.post<Order>(ENDPOINTS.ORDER.CREATE, data);
    return response;
  },

  update: async (id: string, data: Partial<Order>) => {
    const response = await privateApi.put<Order>(ENDPOINTS.ORDER.UPDATE.replace(":id", id), data);
    return response;
  },

  getById: async (id: string) => {
    const response = await privateApi.get<Order>(ENDPOINTS.ORDER.GET_BY_ID.replace(":id", id));
    return response;
  },
};

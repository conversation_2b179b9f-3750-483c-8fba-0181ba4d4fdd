export interface IGetTasksParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}

export const taskKeys = {
  all: () => ["task"] as const,
  lists: () => [...taskKeys.all(), "list"] as const,
  list: (params: IGetTasksParams) => [...taskKeys.lists(), params] as const,
  details: () => [...taskKeys.all(), "detail"] as const,
  detail: (id: string) => [...taskKeys.details(), id] as const,
  update: (id: string) => [...taskKeys.all(), "update", id] as const,
  delete: (id: string) => [...taskKeys.all(), "delete", id] as const,
};

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { CreateImage } from "@/features/products/hooks/types";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ImageUpload } from "@/components/ui/image-upload";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { STAFF_AVATARS } from "./staff-avatar";

// Predefined staff avatars

interface StaffAvatarFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  value: CreateImage | null;
  onChange: (value: CreateImage | null) => void;
  onSave?: (value: CreateImage | null) => void;
  onCancel?: () => void;
}

export function StaffAvatarForm({
  open,
  onOpenChange,
  value,
  onChange,
  onSave,
  onCancel,
}: StaffAvatarFormProps) {
  const { t } = useTranslation();
  const [selectedAvatar, setSelectedAvatar] = useState<CreateImage | null>(value);
  const [uploadedImage, setUploadedImage] = useState<CreateImage | null>(null);

  const handleAvatarSelect = (image: { src: string }) => {
    const newAvatar = {
      name: `avatar_${Date.now()}.png`,
      image: image.src,
    };
    setSelectedAvatar(newAvatar);
    onChange(newAvatar);
  };

  const handleImageUpload = async (base64: string | null) => {
    if (!base64) {
      setUploadedImage(null);
      onChange(null);
      return;
    }
    try {
      const nameMatch = base64.match(/;name=(.*?)(;|$)/);
      const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : `avatar_${Date.now()}.png`;

      const newImage = {
        name: filename,
        image: base64,
      };

      setUploadedImage(newImage);
      setSelectedAvatar(newImage);
      onChange(newImage);
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error(t("common.error"), {
        description: t("common.uploadError"),
      });
    }
  };

  const handleSave = () => {
    if (selectedAvatar) {
      const nameMatch = selectedAvatar.name.match(/;name=(.*?)(;|$)/);
      const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : `avatar_${Date.now()}.png`;

      if (onSave) {
        onSave({
          name: filename,
          image: selectedAvatar.image,
        });
      }
      onChange({
        name: filename,
        image: selectedAvatar.image,
      });
    }
    onOpenChange(false);
  };

  // Cancel action
  const handleCancel = () => {
    if (onCancel) onCancel();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-auto max-w-md p-0">
        <DialogHeader className="p-6 pb-2">
          <DialogTitle className="text-sm font-medium">{t("pages.staff.avatar.title")}</DialogTitle>
        </DialogHeader>
        <div className="px-6">
          <Tabs className="w-full space-y-4">
            <TabsList className="w-full p-1">
              <TabsTrigger value="xbot" className="flex-1 px-3 py-1.5">
                {t("pages.staff.avatar.xbotAvatar")}
              </TabsTrigger>
              <TabsTrigger value="image" className="flex-1 px-3 py-1.5">
                {t("pages.staff.avatar.image")}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="xbot" className="mt-0">
              <div className="flex flex-col items-center">
                <div className="flex flex-col items-center py-4">
                  <Avatar className="mb-2 size-20">
                    <AvatarImage
                      src={selectedAvatar?.image || STAFF_AVATARS[0].image.src}
                      className="object-contain"
                      alt={t("pages.staff.avatar.selectedAvatar")}
                    />
                    <AvatarFallback>X</AvatarFallback>
                  </Avatar>
                </div>
                <div className="mb-2 w-full pl-1 text-left text-base font-medium text-foreground">
                  {t("pages.staff.avatar.avatar")}
                </div>
                <div className="grid w-auto grid-cols-6 gap-2 p-0">
                  {STAFF_AVATARS.map((avatar) => (
                    <Button
                      key={avatar.id}
                      type="button"
                      variant={
                        selectedAvatar?.image === avatar.image.src
                          ? "outline_primary"
                          : "outline_default_primary"
                      }
                      className="m-0 flex size-10 items-center justify-center rounded-full shadow-sm transition-all"
                      onClick={() => handleAvatarSelect(avatar.image)}>
                      <Avatar className="size-8">
                        <AvatarImage
                          src={avatar.image.src}
                          className="object-contain"
                          alt={t("pages.staff.avatar.avatar")}
                        />
                        <AvatarFallback>X</AvatarFallback>
                      </Avatar>
                    </Button>
                  ))}
                </div>
              </div>
            </TabsContent>
            <TabsContent value="image">
              <ImageUpload
                value={uploadedImage?.image || null}
                onChange={handleImageUpload}
                multiple={false}
                maxTotalSize={5 * 1024 * 1024}
                isSquare={true}
                className="w-full min-w-[280px]"
              />
            </TabsContent>
          </Tabs>
        </div>
        <div className="flex h-16 items-center justify-between border-t bg-card px-6">
          <div className="flex items-center gap-2">
            {/* Add any left side content here if needed */}
          </div>
          <div className="flex gap-2">
            <Button
              className="inline-flex min-w-20 items-center rounded-lg border bg-primary-foreground px-3 text-sm font-medium text-foreground hover:bg-foreground/10 disabled:cursor-not-allowed disabled:opacity-50"
              type="button"
              variant="outline"
              onClick={handleCancel}>
              {t("common.cancel")}
            </Button>
            <Button
              className="inline-flex min-w-20 items-center rounded-lg bg-primary px-3 text-sm font-medium text-primary-foreground hover:bg-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
              type="button"
              variant="default"
              onClick={handleSave}
              disabled={!selectedAvatar}>
              {t("common.save")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

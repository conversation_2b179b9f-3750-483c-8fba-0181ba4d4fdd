"use client";

import { memo, useState } from "react";
import { t } from "i18next";
import { Search, X } from "lucide-react";

import useDatatable from "@/components/custom-table/hooks/use-data-table";
import { useTableFilter } from "@/components/custom-table/hooks/use-table-filter";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import DataTableFacetedDateFilter from "../../data-table/data-table-faceted-date-filter";
import { DataTableFacetedFilter } from "../../data-table/data-table-faceted-filter";
import { NUMBER_OF_FILTERS } from "../constant";
import TableFilterSheet from "./other-filter-sheet";
import SingleFilterGroup from "./single-filter-group";

export interface TableFilterProps extends FilterTableProps {
  defaultPayload?: Record<string, unknown>;
  children?: React.ReactNode;
}

export default memo(function TableFilter({
  showSearch = true,
  searchPlaceHolder = "",
  listFilter = [],
  handleParamSearch = () => {},
  defaultPayload = {},
  initialValues = {},
  isOtherFilters = true,
  isClear = true,
  filterType,
  children,
}: TableFilterProps) {
  const [filters, setFilters] = useState<FilterType[]>(listFilter);
  const {
    searchValue,
    loading,
    mainFilters,
    dateFilters,
    otherFilters,
    otherDateFilters,
    handleSearch,
    handleMainFilterChange,
    handleDateOptionSelect,
    handleDateRangeChange,
    handleApplyFilters,
    handleResetFilters,
    handleOtherFilterChange,
    setMainFilters,
  } = useTableFilter({
    filterType,
    initialValues,
    defaultPayload,
    listFilter: filters,
    handleParamSearch,
  });
  const { handleExport } = useDatatable();

  const handleReorderFilters = (newOrder: FilterType[]) => {
    setFilters(newOrder);
  };

  // Function to handle single filter selection with noFilter support
  const handleSingleFilterChange = (filterId: string, values: string[]) => {
    // Check if the selected option has noFilter property
    const filter = filters.find((f) => f.id === filterId);
    const selectedValue = values[0]; // For single filter, we only have one value

    if (filter?.dataOption) {
      const selectedOption = filter.dataOption.find(
        (option) => String(option.value) === selectedValue
      );

      if (selectedOption?.noFilter) {
        // If noFilter is true, we want to clear this filter
        // But we still need to update the UI state to show ALL as selected

        // Update UI state to show ALL as selected
        setMainFilters((prev) => ({
          ...prev,
          [filterId]: [selectedValue],
        }));

        // Create a new payload without this filter
        const newPayload = { ...defaultPayload };
        delete newPayload[filterId];
        newPayload.page = 0;

        // Directly call handleParamSearch to trigger API request
        handleParamSearch(newPayload);
        return;
      }
    }

    // If no noFilter or noFilter is false, proceed with normal filtering
    handleMainFilterChange(filterId, values);
  };

  const renderFilter = (filter: FilterType) => {
    switch (filter.type) {
      case EFilterType.DATE:
        return (
          <DataTableFacetedDateFilter
            key={filter.id}
            title={filter.title}
            dateSelectedOption={dateFilters[filter.id]?.option}
            handleApplyFilters={handleApplyFilters}
            onDateOptionSelect={(value: string) => {
              handleDateOptionSelect(filter.id, value, "main");
            }}
            onDateRangeChange={(range) => {
              handleDateRangeChange(filter.id, "main", range);
            }}
            dateRange={dateFilters[filter.id]?.range}
          />
        );
      case EFilterType.SELECT_BOX:
        return (
          <DataTableFacetedFilter
            key={filter.id}
            title={filter.title}
            options={
              filter.dataOption?.map((opt) => ({
                id: String(opt.value),
                name: opt.label,
                label: opt.label,
                value: String(opt.value),
                count: opt.count,
                noFilter: opt.noFilter,
              })) || []
            }
            isChannelFetch={filter.isChannelFetch}
            value={mainFilters[filter.id] || []}
            onChange={(value) => handleMainFilterChange(filter.id, value)}
            remote={filter.remote}
            pathUrlLoad={filter.pathUrlLoad}
            defaultValue={mainFilters[filter.id]}
          />
        );
      case EFilterType.SINGLE:
        return (
          <SingleFilterGroup
            options={
              filter.dataOption?.map((opt) => ({
                id: String(opt.value),
                name: opt.label,
                label: opt.label,
                value: String(opt.value),
                count: opt.count,
                noFilter: opt.noFilter,
              })) || []
            }
            selectedOption={mainFilters[filter.id]?.[0] || filter.defaultValue?.toString() || ""}
            onSelect={(value) => handleSingleFilterChange(filter.id, [value])}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-1 flex-col items-center gap-2 md:flex-row md:flex-wrap">
      {showSearch && (
        <div className="w-full min-w-[300px] flex-1 xl:max-w-[300px]">
          <Input
            className="w-full"
            placeholder={searchPlaceHolder}
            value={searchValue || ""}
            onChange={(e) => handleSearch(e.target.value)}
            leftIcon={<Search size={16} />}
          />
        </div>
      )}

      <div className="flex w-full flex-1 flex-wrap justify-between gap-2 sm:flex-nowrap">
        <div className="flex flex-1 items-center gap-2">
          {filters.slice(0, NUMBER_OF_FILTERS).map((filter) => (
            <div key={filter.id}>{renderFilter(filter)}</div>
          ))}
          {isOtherFilters && (
            <TableFilterSheet
              listFilter={filters}
              dateFilters={otherDateFilters}
              handleDateOptionSelect={handleDateOptionSelect}
              handleDateRangeChange={handleDateRangeChange}
              handleApplyFilters={handleApplyFilters}
              handleOtherFilterChange={handleOtherFilterChange}
              otherFilters={otherFilters}
              onReorderFilters={handleReorderFilters}
              isSavedFilterLoading={loading}
            />
          )}
          {isClear && (
            <Button
              variant="link"
              size="default"
              className="w-fit p-0 text-muted-foreground"
              leftIcon={<X size={16} />}
              onClick={handleResetFilters}>
              {t("common.clear")}
            </Button>
          )}
        </div>
        {children}
      </div>
    </div>
  );
});

export interface MappingAttribute {
  connection_id: string;
  channel: string;
  type: string;
  source_attributes?: Record<string, AttributeDetails>;
  destination_attributes?: Record<string, AttributeDetails>;
  mappings?: Mapping[];
  master_data?: Record<string, any>;
}

export interface AttributeDetails {
  label: string;
  description: string;
  type: string;
  is_required: boolean;
  properties?: Record<string, AttributeDetails>;
  validate?: Record<string, number>;
}

export interface Mapping {
  source_field: string;
  destination_field: string;
  enabled: boolean;
  error_message?: string;
  transformations?: Transformation[];
}

export interface Transformation {
  type: string;
  config?: Record<string, any>;
}

export interface MappingPayload {
  mappings: Mapping[];
}

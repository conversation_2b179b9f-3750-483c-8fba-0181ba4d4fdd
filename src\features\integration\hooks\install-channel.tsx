import { useMemo } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";

import { channelApi } from "@/lib/apis/channel";
import { convertToPascalCase, snakeToCapitalizedWords } from "@/utils/helpers/text-formater";

export interface SchemaProperty {
  title: string;
  type: string | string[];
  format?: string;
  enum?: string[];
  default?: any;
  additionalProperties?: boolean | object;
  required?: string[];
  properties?: Record<string, SchemaProperty>;
  $ref?: string;
}

export interface SetupFieldsResponse {
  setup_fields: {
    $schema: string;
    definitions: Record<string, SchemaProperty>;
    $ref: string;
  };
}

export interface FormField {
  name: string;
  label: string | JSX.Element;
  type: string;
  placeholder: string;
}

interface UseInstallChannelReturn {
  formFields: FormField[];
  initialValues: Record<string, any>;
  validationSchema: z.ZodObject<any>;
  isLoading: boolean;
  error: Error | null;
  handleSubmit: (data: any) => Promise<void>;
}

export function useInstallChannel(channelKey: string): UseInstallChannelReturn {
  const router = useRouter();

  const {
    data: schema,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["channel", channelKey, "setup_fields"],
    queryFn: async () => {
      const response = await channelApi.getConnectionSetupFields(channelKey);
      return response.setup_fields;
    },
    enabled: !!channelKey,
  });

  const settingSchema = useMemo(() => {
    return (schema?.definitions?.[`${convertToPascalCase(channelKey)}SettingsSchema`] ||
      {}) as SchemaProperty;
  }, [schema, channelKey]);

  const formFields = useMemo(() => {
    return Object.entries(settingSchema.properties || {}).map(([key, value]) => ({
      name: key,
      label: settingSchema.required?.includes(key) ? (
        <>
          {snakeToCapitalizedWords(key)} <span style={{ color: "red" }}>*</span>
        </>
      ) : (
        snakeToCapitalizedWords(key)
      ),
      type: Array.isArray(value.type)
        ? (value.type[0] as string)
        : typeof value.type === "string"
          ? key.toLowerCase().includes("password")
            ? "password"
            : "text"
          : "text",
      placeholder: `Enter ${snakeToCapitalizedWords(key)}`,
    }));
  }, [settingSchema]);

  const initialValues = useMemo(() => {
    return Object.keys(settingSchema.properties || {}).reduce(
      (acc, field) => {
        acc[field] = settingSchema.properties?.[field]?.default ?? "";
        return acc;
      },
      {} as Record<string, any>
    );
  }, [settingSchema]);

  const validationSchema = useMemo(() => {
    const shape = Object.keys(settingSchema.properties || {}).reduce(
      (acc, key) => {
        const field = settingSchema.properties?.[key];
        let fieldSchema: z.ZodType;

        if (field?.type === "string") {
          fieldSchema = z.string();
        } else if (field?.type === "number") {
          fieldSchema = z.number();
        } else if (field?.type === "boolean") {
          fieldSchema = z.boolean();
        } else {
          fieldSchema = z.any();
        }

        if (settingSchema.required?.includes(key)) {
          acc[key] = fieldSchema.pipe(
            z.string().min(1, `${snakeToCapitalizedWords(key)} is required`)
          );
        } else {
          acc[key] = fieldSchema.optional();
        }

        return acc;
      },
      {} as Record<string, z.ZodType>
    );

    return z.object(shape);
  }, [settingSchema]);

  const handleSubmit = async (data: any) => {
    try {
      const response = await channelApi.createConnection({
        connection_type: channelKey,
        settings: data,
      });

      if (response?.authorization_url) {
        window.open(response.authorization_url, "_blank");
      }

      if (response?.connection_id) {
        router.push(`/channels/connections/${response.connection_id}`);
      }

      if (response?.message) {
        toast.error(response.message);
      }
    } catch (error) {
      console.log(error);
      toast.error(error as string);
    }
  };

  return {
    formFields,
    initialValues,
    validationSchema,
    isLoading,
    error: error as Error | null,
    handleSubmit,
  };
}

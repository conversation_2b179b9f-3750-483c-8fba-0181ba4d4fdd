import { ThemeColors } from "./types";

export const DATA_DEFAULT: ThemeColors = {
  dark: [
    {
      value: "#FF910B",
      description: "Primary",
      key: "primary",
    },
    {
      value: "#FFA73C",
      description: "Secondary",
      key: "secondary",
    },
    {
      value: "#09090B",
      description: "BG Primary",
      key: "bg-primary",
    },
    {
      value: "#09090B",
      description: "BG Secondary",
      key: "bg-secondary",
    },
    {
      value: "#18181B",
      description: "Card",
      key: "card",
    },
    {
      value: "#3F3F46",
      description: "Border",
      key: "border",
    },
    {
      value: "#71717A",
      description: "Muted Foreground",
      key: "muted-foreground",
    },
    {
      value: "#FFFFFF",
      description: "Foreground",
      key: "foreground",
    },
  ],
  light: [
    {
      value: "#FF910B",
      description: "Primary",
      key: "primary",
    },
    {
      value: "#FF910B",
      description: "Secondary",
      key: "secondary",
    },
    {
      value: "#F4F4F5",
      description: "BG Primary",
      key: "bg-primary",
    },
    {
      value: "#FFFFFF",
      description: "BG Secondary",
      key: "bg-secondary",
    },
    {
      value: "#FFFFFF",
      description: "Card",
      key: "card",
    },
    {
      value: "#D4D4D8",
      description: "Border",
      key: "border",
    },
    {
      value: "#71717A",
      description: "Muted Foreground",
      key: "muted-foreground",
    },
    {
      value: "#000000",
      description: "Foreground",
      key: "foreground",
    },
  ],
};

export const COLOR_FORMATS = [
  { value: "hex", label: "HEX" },
  { value: "rgb", label: "RGB" },
  { value: "hsb", label: "HSB" },
] as const;

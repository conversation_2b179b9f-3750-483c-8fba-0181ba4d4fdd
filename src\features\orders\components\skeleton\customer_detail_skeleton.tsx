import { Skeleton } from "@/components/ui/skeleton";

export function CustomerDetailSkeleton() {
  return (
    <div className="pt-4">
      <div className="space-y-4">
        {/* Customer Name */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="size-4" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        {/* Points and Group Name */}
        <div className="space-y-2 rounded-md border border-dashed border-border p-3">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-12" />
          </div>
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
        </div>

        {/* Phone Number */}
        <div className="flex items-center gap-2">
          <Skeleton className="size-4" />
          <Skeleton className="h-5 w-32" />
        </div>

        {/* Email */}
        <div className="flex items-center gap-2">
          <Skeleton className="size-4" />
          <Skeleton className="h-5 w-40" />
        </div>

        {/* Shipping Address */}
        <div className="space-y-1 border-t pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="size-4" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
          <div className="pl-6">
            <Skeleton className="h-5 max-w-40" />
          </div>
        </div>

        {/* Billing Address */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="size-4" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
          <div className="pl-6">
            <Skeleton className="h-5 max-w-40" />
          </div>
        </div>
      </div>
    </div>
  );
}

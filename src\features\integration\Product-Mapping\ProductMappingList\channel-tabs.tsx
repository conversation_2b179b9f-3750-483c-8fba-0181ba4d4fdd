"use client";

import { useEffect, useMemo, useState } from "react";
import { MoreVertical } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useConnection } from "@/features/integration/hooks/connection";

import { Tabs, Ta<PERSON>List, TabsTrigger } from "@/components/ui";
import { Combobox } from "@/components/ui/combobox";
import { CustomImage } from "@/components/ui/image";
import { LoadingDotPulse } from "@/components/ui/loading-dot-pulse";
import { ConnectionStatus } from "@/lib/apis/connection";

interface ChannelTabProps {
  selectedConnectionId: string;
  onConnectionChange: (connectionId: string) => void;
}

// Maximum number of tabs to display before showing the rest in dropdown
const MAX_VISIBLE_TABS = 4;

export default function ChannelTabs({ selectedConnectionId, onConnectionChange }: ChannelTabProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const { t } = useTranslation();

  // Use the actual connection hook to fetch data
  const { connections, total, isLoading, hasNextPage, fetchNextPage, isFetchingNextPage } =
    useConnection({
      status: ConnectionStatus.ACTIVE,
      limit: 20,
      search: searchQuery,
      enabled: true, // Always fetch connections for tabs
    });

  // Set first connection as selected on initial load
  useEffect(() => {
    if (isFirstLoad && connections.length > 0 && !selectedConnectionId) {
      onConnectionChange(connections[0].id);
      setIsFirstLoad(false);
    }
  }, [connections, selectedConnectionId, onConnectionChange, isFirstLoad]);

  // Determine which connections to display as tabs
  const visibleConnections = useMemo(() => {
    if (connections.length <= MAX_VISIBLE_TABS) {
      // If we have fewer connections than the limit, show all of them
      return connections;
    }

    // Check if the selected connection is in the first MAX_VISIBLE_TABS
    const firstFewConnections = connections.slice(0, MAX_VISIBLE_TABS);
    const selectedInFirstFew = firstFewConnections.some((conn) => conn.id === selectedConnectionId);

    if (selectedInFirstFew) {
      // If selected connection is already in the first few, just show them
      return firstFewConnections;
    } else {
      // If selected connection is not in the first few, replace the last one
      const selectedConnection = connections.find((conn) => conn.id === selectedConnectionId);
      if (selectedConnection) {
        // Replace the last connection with the selected one
        return [...firstFewConnections.slice(0, MAX_VISIBLE_TABS - 1), selectedConnection];
      }
      return firstFewConnections;
    }
  }, [connections, selectedConnectionId]);

  // Format connections for the combobox
  const comboboxItems = connections.map((connection) => ({
    id: connection.id,
    name: connection.name,
    displayValue: connection.name,
  }));

  // Handle load more - will be triggered by scroll event inside Combobox
  const handleLoadMore = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };

  const handleConnectionSelection = (connectionId: string) => {
    onConnectionChange(connectionId);
  };

  if (connections.length === 0 && !isLoading) {
    return <div className="py-4 text-muted-foreground">No active connections available</div>;
  }

  const hasMoreConnections = connections.length > MAX_VISIBLE_TABS;

  return (
    <div className="flex w-full items-end">
      <Tabs
        defaultValue={connections[0]?.id}
        value={selectedConnectionId}
        onValueChange={onConnectionChange}
        className="flex w-full items-end">
        <TabsList className="h-11 w-full items-end rounded-none border-b border-transparent bg-transparent p-0">
          {isLoading && connections.length === 0 ? (
            <div className="flex h-11 items-center px-4">
              <LoadingDotPulse />
            </div>
          ) : (
            <>
              {/* Display visible tabs */}
              {visibleConnections.map((connection) => (
                <TabsTrigger
                  key={connection.id}
                  value={connection.id}
                  className="group relative !h-11 flex-1 items-center rounded-none border-b-2 border-transparent px-5 text-sm font-semibold text-muted-foreground data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-foreground data-[state=active]:shadow-none">
                  <div className="flex items-center justify-center gap-2 truncate">
                    {connection.image && (
                      <CustomImage
                        src={connection.image}
                        alt={connection.image}
                        width={30}
                        height={30}
                        className="rounded-sm"
                      />
                    )}
                    <span className="text-sm">{connection.name}</span>
                  </div>
                </TabsTrigger>
              ))}

              {/* Combobox dropdown for search and load more */}
              <div className="my-2 flex items-center">
                <Combobox
                  value=""
                  onValueChange={handleConnectionSelection}
                  items={comboboxItems}
                  placeholder={
                    hasMoreConnections ? `+${connections.length - MAX_VISIBLE_TABS} more` : ""
                  }
                  searchPlaceholder={t("common.search")}
                  emptyText={t("common.noResultsFound")}
                  isLoading={isLoading}
                  hasNextPage={hasNextPage}
                  isLoadingMore={isFetchingNextPage}
                  onLoadMore={handleLoadMore}
                  isShowChevronsUpDown={false}
                  variantButton="icon"
                  middleIcon={MoreVertical}
                  popoverWidth="280px"
                  popoverAlign="end"
                  buttonSize="icon"
                />
              </div>
            </>
          )}
        </TabsList>
      </Tabs>
    </div>
  );
}

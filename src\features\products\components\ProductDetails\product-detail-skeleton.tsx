import { Skeleton } from "@/components/ui/skeleton";

export const ProductDetailSkeleton = () => {
  return (
    <div className="container mx-auto px-6">
      {/* Product Info Section Skeleton */}
      <div className="mb-8 space-y-6 rounded-lg bg-card p-6 shadow-sm">
        {/* Header */}
        <div className="space-y-2">
          <Skeleton className="h-8 w-2/3" />
          <Skeleton className="h-4 w-1/2" />
        </div>

        {/* Main Content */}
        <div className="flex flex-col gap-6 lg:flex-row">
          {/* Left Column - Image Gallery */}
          <div className="w-full lg:w-1/3">
            <div className="flex h-full flex-col rounded-lg bg-card">
              {/* Main Image */}
              <Skeleton className="aspect-square w-full rounded-xl" />
              {/* Thumbnail Strip */}
              <div className="mt-4 flex gap-2">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="aspect-square w-16 rounded-xl" />
                ))}
              </div>
            </div>
          </div>

          {/* Right Column - Product Details */}
          <div className="w-full lg:w-2/3">
            <div className="space-y-4 rounded-lg bg-card p-4">
              {/* Metadata Grid */}
              {[...Array(6)].map((_, i) => (
                <div key={i} className="flex items-center gap-2 py-3">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-5 flex-1" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Variants and Details Section Skeleton */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Left Column - Variants List */}
        <div className="sticky top-6 h-fit rounded-lg bg-card p-4">
          <Skeleton className="mb-4 h-6 w-1/3" />
          {[...Array(3)].map((_, i) => (
            <div key={i} className="mb-2">
              <Skeleton className="h-16 w-full rounded-lg" />
            </div>
          ))}
        </div>

        {/* Right Column - Stacked Cards */}
        <div className="space-y-6 lg:col-span-2">
          {/* Variant Details Card */}
          <div className="rounded-lg bg-card p-4">
            <Skeleton className="mb-4 h-6 w-1/4" />
            <div className="flex gap-4">
              <Skeleton className="size-32 rounded-lg" />
              <div className="flex-1 space-y-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="flex items-center gap-2">
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-5 flex-1" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Price Card */}
          <div className="rounded-lg bg-card p-4">
            <Skeleton className="mb-4 h-6 w-1/4" />
            {[...Array(3)].map((_, i) => (
              <div key={i} className="mb-3">
                <Skeleton className="h-12 w-full rounded-lg" />
              </div>
            ))}
          </div>

          {/* Inventory Card */}
          <div className="rounded-lg bg-card p-4">
            <Skeleton className="mb-4 h-6 w-1/4" />
            {[...Array(3)].map((_, i) => (
              <div key={i} className="mb-3">
                <Skeleton className="h-12 w-full rounded-lg" />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer Actions Skeleton */}
      <div className="fixed inset-x-0 bottom-0 border-t bg-card">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-end gap-2">
            <Skeleton className="h-9 w-20 rounded-lg" />
            <Skeleton className="h-9 w-20 rounded-lg" />
          </div>
        </div>
      </div>

      {/* Add padding to prevent content from being hidden behind fixed footer */}
      <div className="h-20" />
    </div>
  );
};

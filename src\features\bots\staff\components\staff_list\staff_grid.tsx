import Link from "next/link";
import { MessagesSquare } from "lucide-react";
import { useTranslation } from "react-i18next";

import { VirtualStaffModel } from "@/features/bots/staff/hooks/type";

import defaultAvatar from "@/assets/images/staff/default_avatar.png";
import { CustomPagination } from "@/components/custom-pagination";
import { TextColumn } from "@/components/custom-table/container/common-column";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import { Card, Separator } from "@/components/ui";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CustomImage } from "@/components/ui/image";
import { authProtectedPaths } from "@/constants/paths";
import toNavUrl from "@/utils/helpers/nav-url-formater";

import StaffGridSkeleton from "./staff_grid_skeleton";

interface StaffGridProps {
  staff: VirtualStaffModel[];
  loading?: boolean;
  total: number;
  pageSize: number;
  currentPage: number;
}

function StaffProgress({
  label,
  icon,
  score,
  current,
  total,
  percent,
  secondaryPercent = 0,
  secondaryColor = "bg-sematic-info",
  color = "bg-sematic-success",
}: {
  label: string;
  icon?: React.ReactNode;
  score: number;
  current: number;
  total: number;
  percent: number;
  secondaryPercent?: number;
  secondaryColor?: string;
  color?: string;
}) {
  return (
    <div>
      <div className="mb-1 flex items-center justify-between text-xs">
        <div className="flex items-center gap-1">
          <span className="text-sm text-muted-foreground">{label}</span>
          <Badge variant={"sematic_success"} className="border-none gap-1">
            {icon}
            {score}
          </Badge>
        </div>
        <span className="text-sm text-muted-foreground">
          {current}/{total}
        </span>
      </div>
      <div className="flex w-full items-center gap-[6px]">
        <div className="flex h-3 flex-1 items-center gap-[2px] overflow-hidden rounded-full bg-bg-secondary p-[2px] pl-[2px]">
          <div className={`h-2 rounded-l-full ${color}`} style={{ width: `${percent}%` }} />
          {secondaryPercent > 0 && (
            <div className={`h-2 ${secondaryColor}`} style={{ width: `${secondaryPercent}%` }} />
          )}
        </div>
        <div className="flex-none">
          <span className="text-sm font-normal text-muted-foreground">{percent}%</span>
        </div>
      </div>
    </div>
  );
}

function StaffCard({ staff }: { staff: VirtualStaffModel }) {
  const { t } = useTranslation();

  // Combine skills and domain_expertises, filter out falsy values
  const allSkills = [...(staff?.domain_expertise || [])].filter(Boolean);
  const maxBadges = 3;
  const visibleSkills = allSkills.slice(0, maxBadges);
  const extraCount = allSkills.length - maxBadges;
  return (
    <Card className="flex flex-col justify-between gap-2 p-4">
      <div className="flex items-start gap-2">
        <CustomImage
          src={staff?.image?.url || defaultAvatar.src}
          alt={staff.name}
          width={40}
          height={40}
          className="size-10 rounded-full object-cover ring-1 ring-border"
        />
        <div className="flex-1 space-y-1">
          <div className="flex items-start justify-between gap-2">
            <Link href={`/staff/${staff.id}`} className="text-sm font-semibold">
              {staff.name}
            </Link>
            <Badge variant={"sematic_success"} className="border-none">
              {t("pages.staff.online")}
            </Badge>
          </div>
          <div className=" text-sm text-muted-foreground">{staff.role}</div>
          <div className="flex flex-wrap gap-2">
            {visibleSkills.map((skill) => (
              <Badge
                key={skill}
                variant="secondary"
                className="max-w-24 px-[10px] py-[2px] text-xs font-semibold">
                <TextColumn text={skill} />
              </Badge>
            ))}
            {extraCount > 0 && (
              <Badge variant="secondary" className="py-[2px] text-xs font-semibold">
                +{extraCount}
              </Badge>
            )}
          </div>
        </div>
      </div>
      <Separator />
      <div className="mt-2 space-y-2">
        <StaffProgress
          label="Tasks"
          icon={
            <span role="img" aria-label="tasks">
              🚀
            </span>
          }
          score={95}
          current={21}
          total={30}
          percent={70}
          secondaryPercent={5}
        />
        <StaffProgress
          label="Conversation"
          icon={
            <span role="img" aria-label="conversation">
              🚀
            </span>
          }
          score={85}
          current={22}
          total={30}
          percent={60}
          secondaryPercent={12}
        />
      </div>
      <Link href={toNavUrl(authProtectedPaths.INTERACT.replace(":id", staff.id))}>
        <Button className=" w-full font-semibold" variant="secondary">
          <MessagesSquare size={16} />
          {t("pages.staff.interact")}
        </Button>
      </Link>
    </Card>
  );
}

export default function StaffGrid({
  staff,
  loading,
  total,
  pageSize,
  currentPage,
}: StaffGridProps) {
  const { t } = useTranslation();
  const { handlePageChange, handlePageSizeChange } = useDatatable();
  if (loading) {
    return <StaffGridSkeleton />;
  }
  if (!staff?.length) {
    return <div className="p-8 text-center text-muted-foreground">{t("pages.staff.noStaff")}</div>;
  }
  return (
    <div className="flex flex-col gap-6 overflow-y-hidden">
      <div className="overflow-y-auto">
        <div className="grid grid-cols-1 gap-4 overflow-y-auto sm:grid-cols-2 lg:grid-cols-3">
          {staff.map((s) => (
            <StaffCard key={s.id} staff={s} />
          ))}
        </div>
      </div>
      <CustomPagination
        total={total}
        pageSize={pageSize}
        currentPage={currentPage + 1}
        onPageSizeChange={handlePageSizeChange}
        onPageChange={(page) => handlePageChange(page - 1)}
      />
    </div>
  );
}

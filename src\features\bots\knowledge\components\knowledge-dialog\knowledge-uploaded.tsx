"use client";

import { use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useState } from "react";
import {
  ArrowDownWideNarrow,
  ArrowUpWideNarrow,
  FileText,
  FileType,
  Globe,
  Loader2,
  Search,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import ConfirmDialog from "@/components/confirm-dialog";
import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import useDebounce from "@/hooks/use-debounce";
import { timeAgo } from "@/utils/helpers/date-formater";

import { useKnowledge } from "../../hooks/knowledge";
import { Knowledge } from "../../types";

interface KnowledgePage {
  items: Knowledge[];
  total: number;
  page: number;
  limit: number;
}

interface KnowledgeUploadedTabProps {
  onClose?: () => void;
  selectedKnowledge?: Knowledge[];
  onSelectedKnowledgeChange: (knowledge: Knowledge[]) => void;
  isSaving?: boolean;
  setIsSaving?: (isSaving: boolean) => void;
  lastDeletedId?: string;
  isStaffContext?: boolean;
}

const KnowledgeUploadedTab = ({
  onClose,
  selectedKnowledge = [],
  onSelectedKnowledgeChange,
  isSaving = false,
  setIsSaving,
  lastDeletedId,
  isStaffContext = false,
}: KnowledgeUploadedTabProps) => {
  const { t } = useTranslation();
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebounce(search, 500);
  const [sort, setSort] = useState<SortDirection>(SortDirection.DESC);

  const options = useMemo(
    () => ({
      limit: 20,
      enabled: true,
      query: debouncedSearch,
      sort_updated_at: sort,
      status: "READY",
    }),
    [debouncedSearch, sort]
  );

  const isKnowledgeSelected = useCallback(
    (knowledgeItem: Knowledge) => {
      if (!isStaffContext) return false;
      return selectedKnowledge.some((k) => k.id === knowledgeItem.id);
    },
    [selectedKnowledge, isStaffContext]
  );

  const handleKnowledgeSelect = useCallback(
    (knowledgeItem: Knowledge, checked: boolean) => {
      if (!isStaffContext) return;
      if (lastDeletedId && knowledgeItem.id === lastDeletedId) return;

      if (checked) {
        onSelectedKnowledgeChange([...selectedKnowledge, knowledgeItem]);
      } else {
        onSelectedKnowledgeChange(selectedKnowledge.filter((k) => k.id !== knowledgeItem.id));
      }
    },
    [selectedKnowledge, onSelectedKnowledgeChange, lastDeletedId, isStaffContext]
  );

  const {
    knowledge,
    useDeleteKnowledgeMutation,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useKnowledge(options);

  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showLeaveDialog, setShowLeaveDialog] = useState(false);

  const handleDelete = useCallback(
    async (knowledgeId: string) => {
      return useDeleteKnowledgeMutation.mutateAsync(knowledgeId);
    },
    [useDeleteKnowledgeMutation]
  );

  const handleLeave = () => {
    setShowLeaveDialog(false);
    onClose?.();
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    const threshold = 20;
    const isNearBottom = target.scrollHeight - (target.scrollTop + target.clientHeight) < threshold;

    if (isNearBottom && !isLoading && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  return (
    <div className="relative flex flex-auto flex-col overflow-y-hidden p-1">
      {/* Centered loading overlay */}
      {isSaving && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="size-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">{t("common.saving") || "Saving..."}</p>
          </div>
        </div>
      )}

      <div className="flex-none">
        <div className="flex items-center justify-between gap-2">
          <Input
            className="w-full"
            placeholder={t("pages.knowledge.upload.search")}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            leftIcon={<Search size={16} />}
            disabled={isSaving}
          />
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
            onClick={() =>
              setSort(sort === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC)
            }
            disabled={isSaving}>
            <span className="text-xs font-medium">
              {sort === SortDirection.DESC
                ? t("pages.knowledge.upload.newest")
                : t("pages.knowledge.upload.oldest")}
            </span>
            {sort === SortDirection.DESC ? (
              <ArrowDownWideNarrow className="size-4" />
            ) : (
              <ArrowUpWideNarrow className="size-4" />
            )}
          </Button>
        </div>
      </div>
      <div className="mt-4 min-h-0 flex-1 overflow-y-auto" onScroll={handleScroll}>
        {isLoading ? (
          <div className="flex justify-center py-2">
            <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        ) : knowledge.length === 0 ? (
          <div className="flex justify-center py-2">
            <span className="text-sm text-gray-500">{t("pages.knowledge.upload.noKnowledge")}</span>
          </div>
        ) : (
          knowledge.map((knowledgeItem: Knowledge) => (
            <div
              key={knowledgeItem.id}
              className="mb-2 flex items-center justify-between rounded border px-3 py-2">
              <div className="flex items-center gap-2">
                {knowledgeItem.source === "URL" ? (
                  <Globe className="size-4 text-gray-500" />
                ) : knowledgeItem.source === "FILE" ? (
                  <FileText className="size-4 text-gray-500" />
                ) : (
                  <FileType className="size-4 text-gray-500" />
                )}
                <span className="text-sm">{knowledgeItem.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-gray-500">{timeAgo(knowledgeItem.updated_at)}</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={knowledgeItem.id}
                    checked={isKnowledgeSelected(knowledgeItem)}
                    onCheckedChange={(checked) =>
                      handleKnowledgeSelect(knowledgeItem, checked as boolean)
                    }
                    disabled={isSaving}
                  />
                </div>
              </div>
            </div>
          ))
        )}
        {isFetchingNextPage && (
          <div className="flex justify-center py-2">
            <div className="size-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        )}
      </div>
      <ConfirmDialog
        open={showLeaveDialog}
        title={t("common.unsavedChanges")}
        description={t("common.leaveWithoutSavingDescription")}
        confirmText={t("common.leaveWithoutSaving")}
        cancelText={t("common.stay")}
        onConfirm={() => {
          setShowLeaveDialog(false);
          onClose?.();
        }}
        onCancel={() => setShowLeaveDialog(false)}
      />
      <ConfirmDialog
        open={showDeleteDialog}
        title={t("common.areYouSure")}
        description={t("common.areYouSureDescription")}
        confirmText={t("common.confirm")}
        cancelText={t("common.cancel")}
        onConfirm={() => handleDelete(knowledge[0]?.id)}
        onCancel={() => setShowDeleteDialog(false)}
      />
      <AlertDialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("common.unsavedChanges")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("common.leaveWithoutSavingDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowLeaveDialog(false)}>
              {t("common.stay")}
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleLeave}>{t("common.leave")}</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("common.areYouSure")}</AlertDialogTitle>
            <AlertDialogDescription>{t("common.areYouSureDescription")}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDeleteDialog(false)}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction onClick={() => handleDelete(knowledge[0]?.id)}>
              {t("common.confirm")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default KnowledgeUploadedTab;

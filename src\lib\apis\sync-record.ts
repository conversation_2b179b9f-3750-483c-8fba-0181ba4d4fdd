import { SyncRecordDetail } from "@/features/integration/SyncRecords/hooks/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseList } from "./types/common";

export interface SyncRecord {
  id: string;
  company_id: string;
  channel: string;
  record_type: string;
  connection_id: string;
  fetch_event_id: string;
  fetched_at: string;
  finished_at: string;
  created_at: string;
  updated_at: string;
  published_at: string;
  source?: string | null;
  transformed_at: string;
  destinations?: string | null;
  raw_record_version: string;
  transformed_record_id: string;
  transformed_record_version: string;
  status: "FETCHED" | "TRANSFORMED" | "PUBLISHED" | "ERROR" | "SKIPPED" | "COMPLETED";
}

export const syncRecordApi = {
  list: async (params?: Record<string, unknown>) => {
    return privateApi.get<ResponseList<SyncRecord>>(
      ENDPOINTS.INTEGRATION_ENDPOINTS.SYNC_RECORD_LIST,
      { params }
    );
  },
  detail: async (sync_record_id: string) => {
    const url = `${ENDPOINTS.INTEGRATION_ENDPOINTS.SYNC_RECORD_DETAIL}`;
    return privateApi.post<SyncRecordDetail>(url, {
      sync_record_id,
      show_details: true,
    });
  },
};

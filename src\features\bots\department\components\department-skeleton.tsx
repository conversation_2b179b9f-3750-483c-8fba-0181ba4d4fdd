import { Skeleton } from "@/components/ui/skeleton";

export function DepartmentSkeleton() {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="h-full rounded-lg bg-bg-primary p-4">
          <div className="flex h-full flex-col justify-between gap-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-32" />
              <div className="flex -space-x-2">
                {Array.from({ length: 3 }).map((_, j) => (
                  <Skeleton key={j} className="size-8 rounded-full border-2 border-background" />
                ))}
              </div>
            </div>

            <Skeleton className="h-4 w-3/4" />

            <div className="flex justify-end pt-2">
              <Skeleton className="h-8 w-24" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

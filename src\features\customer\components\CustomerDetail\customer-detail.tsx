import React from "react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";

interface CustomerDetailProps {
  customer: {
    name: string;
    birthday: string;
    gender: string;
    phone: string;
    email: string;
    shippingAddress: string;
    billingAddress: string;
    groupName: string;
    loyalPoints: number;
    redeemPoints: number;
    tags: string[];
  };
}

export const CustomerDetail: React.FC<CustomerDetailProps> = ({ customer }) => {
  const { t } = useTranslation();

  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card className="md:col-span-2">
        <CardHeader className="p-4">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {t("pages.customer.details.customerDetails")}
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <Table>
                <TableBody>
                  <TableRow className="border-b">
                    <TableCell className="py-3 pr-4 text-sm font-medium">
                      {t("pages.customer.details.name")}:
                    </TableCell>
                    <TableCell className="py-3 text-sm">{customer.name}</TableCell>
                  </TableRow>
                  <TableRow className="border-b">
                    <TableCell className="py-3 pr-4 text-sm font-medium">
                      {t("pages.customer.details.birthday")}:
                    </TableCell>
                    <TableCell className="py-3 text-sm">{customer.birthday}</TableCell>
                  </TableRow>
                  <TableRow className="border-b">
                    <TableCell className="py-3 pr-4 text-sm font-medium">
                      {t("pages.customer.details.gender")}:
                    </TableCell>
                    <TableCell className="py-3 text-sm">{customer.gender}</TableCell>
                  </TableRow>
                  <TableRow className="border-b">
                    <TableCell className="py-3 pr-4 text-sm font-medium">
                      {t("pages.customer.details.phone")}:
                    </TableCell>
                    <TableCell className="py-3 text-sm">{customer.phone}</TableCell>
                  </TableRow>
                  <TableRow className="border-b">
                    <TableCell className="py-3 pr-4 text-sm font-medium">
                      {t("pages.customer.details.email")}:
                    </TableCell>
                    <TableCell className="py-3 text-sm">{customer.email}</TableCell>
                  </TableRow>
                  <TableRow className="border-b">
                    <TableCell className="py-3 pr-4 text-sm font-medium">
                      {t("pages.customer.details.shippingAddress")}:
                    </TableCell>
                    <TableCell className="py-3 text-sm">{customer.shippingAddress}</TableCell>
                  </TableRow>
                  <TableRow className="border-b">
                    <TableCell className="py-3 pr-4 text-sm font-medium">
                      {t("pages.customer.details.billingAddress")}:
                    </TableCell>
                    <TableCell className="py-3 text-sm">{customer.billingAddress}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <div className="flex flex-col">
              <div className="rounded-md border border-dashed p-4">
                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    {t("pages.customer.details.groupName")}
                  </h3>
                  <Badge variant="yellow">{customer.groupName}</Badge>
                </div>

                <div className="mb-4 flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    {t("pages.customer.details.totalLoyalPoints")}
                  </h3>
                  <span className="text-sm">{customer.loyalPoints}</span>
                </div>

                <Separator className="my-4" />

                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    {t("pages.customer.details.totalRedeemPoints")}
                  </h3>
                  <span className="text-sm">{customer.redeemPoints}</span>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">{t("pages.customer.details.tags")}</h3>
                  <div className="flex flex-wrap justify-end gap-1">
                    {customer.tags && customer.tags.length > 0 ? (
                      customer.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary">
                          {tag}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted-foreground">
                        {t("pages.customer.details.noTags")}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomerDetail;

import { useState } from "react";
import Image from "next/image";

import Staff from "@/assets/images/staff.png";

interface AvatarStatusProps {
  src?: string | null;
  alt: string;
  fill?: boolean;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  status?: "online" | "offline" | "away" | "busy" | null;
  statusColor?: string;
  statusSize?: number;
}

export const AvatarStatus = ({
  src,
  alt,
  fill = false,
  width,
  height,
  className = "",
  priority = false,
  status = "online",
  statusColor = "#10b981", // Default green color for online
  statusSize = 16,
}: AvatarStatusProps) => {
  const [error, setError] = useState(false);

  const handleError = () => {
    setError(true);
  };

  const imageProps = {
    src: !src || error ? Staff : src,
    className: `${className} transition-opacity duration-300 object-cover`,
    onError: handleError,
    priority,
  };

  const getStatusColor = () => {
    if (statusColor) return statusColor;

    switch (status) {
      case "online":
        return "#10b981"; // green-500
      case "busy":
        return "#ef4444"; // red-500
      case "away":
        return "#f59e0b"; // amber-500
      case "offline":
        return "#6b7280"; // gray-500
      default:
        return "#10b981";
    }
  };

  return (
    <div className="relative size-full">
      <div className="size-full overflow-hidden rounded-lg">
        {fill ? (
          <div className="relative size-full">
            <Image fill sizes="100%" alt={alt} {...imageProps} />
          </div>
        ) : (
          <Image width={width || 80} height={height || 80} alt={alt} {...imageProps} />
        )}
      </div>

      {status && (
        <div
          className="absolute -bottom-1.5 -right-1.5 flex size-4 items-center justify-center rounded-full border-4 border-white dark:border-muted"
          style={{
            backgroundColor: getStatusColor(),
            width: statusSize,
            height: statusSize,
          }}
        />
      )}
    </div>
  );
};

import React from "react";
import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface SaleSuggestionProps {
  defaultPriceGroup: string;
  defaultPaymentMethod: string;
  discountPercent: string | number;
}

export const SaleSuggestion: React.FC<SaleSuggestionProps> = ({
  defaultPriceGroup,
  defaultPaymentMethod,
  discountPercent,
}) => {
  const { t } = useTranslation();

  return (
    <Card className="w-full">
      <CardHeader className="p-4">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {t("pages.customer.sales.suggestionInfo")}
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4">
        <div className="w-full">
          <div className="flex border-b py-2">
            <div className="text-sm font-medium">{t("pages.customer.sales.defaultPriceGroup")}</div>
            <div className="ml-auto text-sm">{defaultPriceGroup || "--"}</div>
          </div>

          <div className="flex border-b py-2">
            <div className="text-sm font-medium">
              {t("pages.customer.sales.defaultPaymentMethod")}
            </div>
            <div className="ml-auto text-sm">{defaultPaymentMethod || "--"}</div>
          </div>

          <div className="flex border-b py-2">
            <div className="text-sm font-medium">{t("pages.customer.sales.discountPercent")}</div>
            <div className="ml-auto text-sm">{discountPercent || "--"}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SaleSuggestion;

"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

import { getAuthToken } from "@/lib/auth";

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    const token = getAuthToken();
    if (token?.Token?.AccessToken) {
      router.replace("/dashboard");
    } else {
      router.replace("/login");
    }
  }, [router]);

  return null; // Không render gì cả vì trang này chỉ để redirect
}

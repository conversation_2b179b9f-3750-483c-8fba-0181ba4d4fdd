import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authApi } from "@/lib/apis/auth";

import { authKeys } from "./keys";

export const useVerifyCodeReset = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    isSuccess: success,
    reset: resetMutation,
  } = useMutation({
    mutationKey: authKeys.verifyCodeReset(""),
    mutationFn: async ({ username, code }: { username: string; code: string }) => {
      if (!username || !code) {
        throw new Error(t("auth.verificationCodeRequired"));
      }

      return authApi.verifyCodeReset({ username, code });
    },
    onSuccess: (data) => {
      toast.success(t("auth.verificationCodeSuccess"));
      router.push(`/reset-password?session=${data.session}`);
    },
  });

  return {
    onSubmit,
    loading,
    error,
    success,
    reset: resetMutation,
  };
};

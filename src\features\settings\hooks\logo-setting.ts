import { useEffect, useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { t } from "i18next";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { mediaApi } from "@/lib/apis/media";
import { LogoSettings, settingApi } from "@/lib/apis/setting";
import { useVersion, Version, versionApi } from "@/lib/apis/version";

// Form type that uses strings for display
type LogoSettingsForm = {
  lightLogo: string | null;
  darkLogo: string | null;
  lightIcon: string | null;
  darkIcon: string | null;
  favicon: string | null;
};

const defaultLogoSettings: LogoSettings = {
  lightLogo: null,
  darkLogo: null,
  lightIcon: null,
  darkIcon: null,
  favicon: null,
};

export const useLogoSetting = () => {
  const { getSettingValue, isLoading } = useVersion();
  const [isLoadingState, setIsLoadingState] = useState(isLoading);
  useEffect(() => {
    setIsLoadingState(isLoading);
  }, [isLoading]);
  const shopInfo = getSettingValue<Record<string, any>>("shop_info");

  const form = useForm<LogoSettingsForm>({
    defaultValues: {
      lightLogo: null,
      darkLogo: null,
      lightIcon: null,
      darkIcon: null,
      favicon: null,
    },
  });

  useEffect(() => {
    if (!shopInfo || isLoading) return;

    const formData: LogoSettingsForm = {
      lightLogo: shopInfo.lightLogo?.url || null,
      darkLogo: shopInfo.darkLogo?.url || null,
      lightIcon: shopInfo.lightIcon?.url || null,
      darkIcon: shopInfo.darkIcon?.url || null,
      favicon: shopInfo.favicon?.url || null,
    };

    form.reset(formData);
  }, [shopInfo, form, isLoading]);

  const handleImageUpload = async (
    type: keyof LogoSettings,
    imageContent: string,
    fileName: string
  ) => {
    try {
      const prefix = "media";
      const res = await mediaApi.uploadImage({
        prefix,
        name: fileName,
        image: imageContent,
      });

      if (res?.url) {
        // Update the form with just the URL for display
        form.setValue(type, res.url);

        // Return the full image data for API
        return {
          id: res.id,
          name: fileName,
          url: res.url,
        };
      }
      return null;
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error(t("common.error"));
      return null;
    }
  };

  const queryClient = useQueryClient();

  const updateLogoMutation = useMutation({
    mutationFn: async (logoSettings: LogoSettings) => {
      // Get current shop_info settings
      const currentShopInfo = getSettingValue<Record<string, any>>("shop_info") || {};

      // Update only logo-related fields while preserving other fields
      const updatedShopInfo = {
        ...currentShopInfo,
        ...logoSettings,
      };
      await settingApi.updateLogoSetting("shop_info", updatedShopInfo);
      const version = await versionApi.getVersion();
      return {
        ...version,
        data: {
          ...version.data,
          dict: {
            ...version.data.dict,
            shop_info: { ...version.data.dict.shop_info, setting_value: updatedShopInfo },
          },
        },
      };
    },
    onSuccess: (updatedVersion: Version) => {
      queryClient.setQueryData(["version"], updatedVersion);
      toast.success(t("common.success"), {
        description: t("pages.settings.logo.saveSuccess"),
      });
    },
    onError: (error: Error) => {
      // console.log(error);
      toast.error(t("common.error"), {
        description: t("pages.settings.logo.saveError"),
      });
    },
  });

  const onSubmit = async (data: LogoSettingsForm) => {
    try {
      // First, upload all images that are base64 strings
      setIsLoadingState(true);

      const logoSettings: LogoSettings = { ...defaultLogoSettings };

      for (const [key, value] of Object.entries(data)) {
        if (value && typeof value === "string" && value.startsWith("data:image")) {
          // Extract filename from the data URL if available, otherwise use a default
          const fileName = value.includes(";name=")
            ? value.split(";name=")[1].split(";")[0]
            : `${key}-${Date.now()}.png`;

          const imageData = await handleImageUpload(key as keyof LogoSettings, value, fileName);
          if (imageData) {
            logoSettings[key as keyof LogoSettings] = imageData;
          }
        } else if (value && typeof value === "string") {
          // Keep existing values for unchanged logos
          logoSettings[key as keyof LogoSettings] = {
            id: key,
            name: key,
            url: value,
          };
        }
      }

      // Update all logo settings in a single call
      if (Object.keys(logoSettings).length > 0) {
        await updateLogoMutation.mutateAsync(logoSettings);
      } else {
        toast.info(t("common.info"), {
          description: t("pages.settings.logo.noChangesToSave"),
        });
      }
      setIsLoadingState(false);
    } catch (error) {
      console.error("Error saving logo settings:", error);
      toast.error(t("common.error"), {
        description: t("pages.settings.logo.saveError"),
      });
      setIsLoadingState(false);
    }
  };

  const handleReset = () => {
    const shopInfo = getSettingValue<Record<string, any>>("shop_info");
    if (!shopInfo || isLoading) return;

    // Extract only logo-related fields for form display
    const formData: LogoSettingsForm = {
      lightLogo: shopInfo.lightLogo?.url || null,
      darkLogo: shopInfo.darkLogo?.url || null,
      lightIcon: shopInfo.lightIcon?.url || null,
      darkIcon: shopInfo.darkIcon?.url || null,
      favicon: shopInfo.favicon?.url || null,
    };

    form.reset(formData);
    toast.success(t("common.success"), {
      description: t("pages.settings.logo.resetSuccess"),
    });
  };

  return {
    form,
    handleImageUpload,
    onSubmit,
    handleReset,
    isLoading: isLoadingState,
  };
};

export const useUpdateFavicon = () => {
  const { getSettingValue, isLoading } = useVersion();

  useEffect(() => {
    if (isLoading) return;

    const shopInfo = getSettingValue<Record<string, any>>("shop_info");
    if (!shopInfo?.favicon?.url) return;
    const favicon = shopInfo.favicon.url;
    const faviconLink = document.querySelector("link[rel='icon']") as HTMLLinkElement;

    if (faviconLink) {
      faviconLink.href = favicon;
    } else {
      const newFavicon = document.createElement("link");
      newFavicon.rel = "icon";
      newFavicon.href = favicon;
      document.head.appendChild(newFavicon);
    }
  }, [getSettingValue, isLoading]);
};

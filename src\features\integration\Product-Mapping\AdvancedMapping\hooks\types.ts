export type Direction = {
  title: string;
  content: string;
  example: string;
  value_type: string;
};

export interface Transformation {
  id?: string;
  type?: string;
  label?: string;
  description?: string;
  config?: Record<string, any>;
  config_fields?: Array<{
    key: string;
    label: string;
    type: string;
    placeholder?: string;
    description: string;
    options?: Array<{ value: string; label: string } | string>;
  }>;
  multiple_fields?: boolean;
  direction?: Direction;
}

export interface TransformationPayload {
  sync_record_id: string;
  connection_id: string;
  source_field: string;
  transformations: Array<{
    type: string;
    config: Record<string, any>;
  }>;
  transformedValue?: string;
  // API response fields
  outputs?: string[];
  success?: boolean;
  message?: string;
}

export const SOURCE_FIELDS = [
  { id: "name", name: "Name" },
  { id: "description", name: "Description" },
  { id: "price", name: "Price" },
  { id: "sku", name: "SKU" },
  { id: "category", name: "Category" },
  { id: "brand", name: "Brand" },
  { id: "weight", name: "Weight" },
  { id: "dimensions", name: "Dimensions" },
  { id: "color", name: "Color" },
  { id: "material", name: "Material" },
] as const;

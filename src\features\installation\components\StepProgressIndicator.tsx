import React from "react";

interface StepProgressIndicatorProps {
  steps: string[];
  activeStep: number;
}

const StepProgressIndicator: React.FC<StepProgressIndicatorProps> = ({ steps, activeStep }) => {
  // If more than one step is active or steps is not an array, return empty fragment
  if (!Array.isArray(steps) || steps.length === 0 || activeStep >= steps.length) {
    return null;
  }

  return (
    <>
      <div
        className={`gap-10 px-4 sm:grid`}
        style={{
          gridTemplateColumns: `repeat(${steps.length}, minmax(0, 1fr))`,
        }}>
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <div className="col-span-1">
              <div
                className={` ${index <= activeStep ? "bg-primary opacity-50" : "bg-muted"} h-[4px] w-full rounded-3xl`}></div>
              <div className="mt-3 w-full">
                <div className="inline-flex items-center justify-start">
                  <div className="relative flex items-center justify-center">
                    {/* <div className="absolute size-10 rounded-full bg-[#E1E1FE]"></div> */}
                    <div
                      className={`relative z-10 flex size-8 items-center justify-center rounded-full ring-4  ${index <= activeStep ? "bg-primary text-primary-foreground ring-[#E1E1FE]" : "bg-transparent text-muted-foreground ring-muted"} text-sm font-semibold`}>
                      {index + 1}
                    </div>
                  </div>
                  <div className="ml-[16px]">
                    <div className="font-medium text-muted-foreground">Step {index + 1}</div>
                    <div className="font-bold text-accent-foreground">{step}</div>
                  </div>
                </div>
              </div>
            </div>
          </React.Fragment>
        ))}
      </div>
    </>
  );
};

export default StepProgressIndicator;

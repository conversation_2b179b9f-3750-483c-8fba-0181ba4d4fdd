"use client";

import { StaticImageData } from "next/image";

import { Button } from "@/components/ui/button";
import { CustomImage } from "@/components/ui/image";
import { Switch } from "@/components/ui/switch";

export interface SocialPlatformProps {
  name: string;
  logo: string | StaticImageData;
  enabled: boolean;
  onToggle: (enabled: boolean) => void;
  onAuthorize: () => void;
}

export function SocialPlatform({
  name,
  logo,
  enabled,
  onToggle,
  onAuthorize,
}: SocialPlatformProps) {
  return (
    <div className="rounded-md border border-border p-4">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex size-8 items-center justify-center rounded-md">
              <CustomImage src={logo} alt={name} width={24} height={24} className="size-6" />
            </div>
            <span className="font-medium">{name}</span>
          </div>
          <Switch checked={enabled} onCheckedChange={onToggle} />
        </div>
        {enabled && (
          <div className="flex justify-end">
            <Button variant="secondary" size="sm" onClick={onAuthorize} type="button">
              Authorize
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

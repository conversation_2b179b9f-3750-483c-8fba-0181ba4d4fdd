"use client";

import { useState } from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

import { AddVoucherDialog } from "./dialog/add_voucher";

interface PaymentProps {
  subtotal: number;
  totalItems?: number;
  disabled?: boolean;
  readOnly?: boolean;
  isPaid?: boolean;
  discount?: string;
  tax?: string;
  shippingFee?: string;
  voucherTotal?: string;
  total?: string;
  otherFees?: string | null;
  onApplyVoucher?: (code: string) => void;
}

export function Payment({
  subtotal,
  totalItems = 0,
  disabled,
  readOnly = false,
  isPaid = false,
  discount = "0",
  tax = "0",
  shippingFee = "0",
  voucherTotal = "0",
  total = "0",
  otherFees = null,
  onApplyVoucher,
}: PaymentProps) {
  const { t } = useTranslation();
  const [showVoucherDialog, setShowVoucherDialog] = useState(false);

  // Function to parse string amounts to numbers
  const parseAmount = (amount: string) => {
    return parseInt(amount) || 0;
  };

  // Function to format currency
  const formatCurrency = (value: number | string) => {
    const numValue = typeof value === "string" ? parseAmount(value) : value;
    return `${numValue.toLocaleString()} đ`;
  };

  const paymentItems = [
    { label: "pages.orders.subtotal", value: readOnly ? subtotal : parseAmount(String(subtotal)) },
    {
      label: "pages.orders.discount",
      value: parseAmount(discount),
      isDiscount: true,
      shortcut: "F6",
    },
    {
      label: "pages.orders.voucher",
      value: parseAmount(voucherTotal),
      showAddButton: !readOnly && !disabled,
    },
    { label: "pages.orders.tax", value: parseAmount(tax) },
    { label: "pages.orders.shipping", value: parseAmount(shippingFee) },
    // { label: "pages.orders.fees", value: parseAmount(otherFees || "0") },
    {
      label: "pages.orders.total",
      value: readOnly ? parseAmount(total) : parseAmount(String(subtotal)),
      isTotal: true,
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-sm font-medium">{t("pages.orders.payment")}</h2>
        {readOnly && (
          <Badge variant={isPaid ? "green" : "destructive"}>{isPaid ? "Paid" : "Unpaid"}</Badge>
        )}
      </div>

      <div className="space-y-2">
        {/* First section with total items */}
        <div className="grid grid-cols-3 items-center py-1">
          <div className="text-sm">{t("pages.orders.subtotal")}</div>
          <div className="text-center text-sm text-muted-foreground">
            {totalItems} {t("pages.orders.products")}
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="truncate text-right text-sm">
                {formatCurrency(paymentItems[0].value)}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">{formatCurrency(paymentItems[0].value)}</div>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Remaining items */}
        {paymentItems.slice(1).map((item) => (
          <div
            key={item.label}
            className={cn(
              "flex items-center justify-between py-1 text-sm",
              item.isTotal && "border-t pt-2 font-medium truncate"
            )}>
            <div className="flex items-center justify-between gap-2">
              <span className="text-sm">{t(item.label)}</span>
              {item.shortcut && (
                <span className="ml-1 text-sm text-muted-foreground">({item.shortcut})</span>
              )}
            </div>
            {item.showAddButton && item.value === 0 ? (
              <AddVoucherDialog
                open={showVoucherDialog}
                onOpenChange={setShowVoucherDialog}
                onApplyVoucher={(code) => onApplyVoucher?.(code)}
                trigger={
                  <Button
                    type="button"
                    variant="link"
                    size="sm"
                    className="h-6 justify-end p-0 text-right text-primary hover:bg-transparent"
                    disabled={disabled}>
                    <Plus className="mr-1 size-4" />
                    {t("pages.orders.addVoucher")}
                  </Button>
                }
              />
            ) : (
              <span className={cn(item.isDiscount && item.value > 0 && "text-destructive")}>
                {formatCurrency(item.value)}
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

import { FilterOption } from "@/components/data-table/types";

export const OrderStatusFilterOptions: FilterOption[] = [
  { value: "DRAFT", label: "Draft" },
  { value: "SHIPPING", label: "Shipping" },
  { value: "RETURNED", label: "Returned" },
  { value: "COMPLETED", label: "Completed" },
  { value: "CANCELLED", label: "Cancelled" },
  { value: "DELIVERED", label: "Delivered" },
  { value: "PENDING", label: "Pending" },
  { value: "CONFIRMED", label: "Confirmed" },
];

export const PaymentStatusFilterOptions: FilterOption[] = [
  { value: "UNPAID", label: "Unpaid" },
  { value: "PAID", label: "Paid" },
];

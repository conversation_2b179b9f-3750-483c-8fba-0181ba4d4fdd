// import { TransformationType } from "../hooks/types";

// export interface TransformationSample {
//   type: "standard" | "filter";
//   description: string;
//   example: string;
//   examples?: {
//     operators?: string[];
//     logical?: string[];
//     string?: string[];
//     primitives?: string[];
//   };
// }

// export const TRANSFORMATION_SAMPLES: Partial<Record<TransformationType, TransformationSample>> = {
//   direct: {
//     type: "standard",
//     description: "Maps a field directly without changes",
//     example: 'Title → "Blue Denim Jacket"',
//   },
//   uppercase: {
//     type: "standard",
//     description: "Converts text to uppercase",
//     example: '"Blue Denim" → "BLUE DENIM"',
//   },
//   lowercase: {
//     type: "standard",
//     description: "Converts text to lowercase",
//     example: '"Blue Denim" → "blue denim"',
//   },
//   titlecase: {
//     type: "standard",
//     description: "Converts text to title case",
//     example: '"blue denim jacket" → "Blue Denim Jacket"',
//   },
//   trim: {
//     type: "standard",
//     description: "Removes whitespace from text",
//     example: '"  Blue Denim  " → "Blue Denim"',
//   },
//   replace: {
//     type: "standard",
//     description: "Replaces text occurrences",
//     example: 'Replace "Blue" with "Black" → "Black Denim Jacket"',
//   },
//   concat: {
//     type: "standard",
//     description: "Combines multiple fields",
//     example: 'Brand + Size → "Fashion Brand - M"',
//   },
//   split: {
//     type: "standard",
//     description: "Splits text into array",
//     example: 'Dimensions "10x20x5" → ["10", "20", "5"]',
//   },
//   prefix: {
//     type: "standard",
//     description: "Adds text at start",
//     example: 'Size "M" → "Size: M"',
//   },
//   postfix: {
//     type: "standard",
//     description: "Adds text at end",
//     example: 'Price "49.99" → "49.99 USD"',
//   },
//   substring: {
//     type: "standard",
//     description: "Extracts text portion",
//     example: '"Blue Denim" from 0 to 4 → "Blue"',
//   },
//   filter: {
//     type: "filter",
//     description: "Filters array elements",
//     example: 'Filter tags containing "blue" → ["Blue Denim", "Blue Jeans"]',
//     examples: {
//       operators: [
//         "item.price > 100",
//         "item.stock <= 5",
//         'item.size === "medium"',
//         'item.color !== "red"',
//       ],
//       logical: [
//         "item.price > 100 && item.stock > 0",
//         'item.size === "medium" || item.size === "large"',
//         '!(item.color === "red")',
//         'item.price > 50 && (item.size === "medium" || item.size === "large")',
//       ],
//       string: [
//         'item.name.includes("blue")',
//         'item.description.startsWith("Classic")',
//         'item.brand.endsWith("Brand")',
//         'item.title.toLowerCase().includes("denim")',
//       ],
//       primitives: ["value > 100", "value <= 5", 'value === "medium"', 'value !== "red"'],
//     },
//   },
// };

export const config = {
  API_URL: process.env.NEXT_PUBLIC_API_URL || "https://api-staging.optiwarehouse.com",
  API_URL_ONEX: process.env.NEXT_PUBLIC_API_URL_ONEX || "https://api-staging.onexapis.com",
  API_URL_ONEXBOTS: process.env.NEXT_PUBLIC_API_URL_ONEXBOTS || "https://dev-agent.onexbots.com",
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || "https://dev.optiwarehouse.com",
  PRODUCT: process.env.NEXT_PUBLIC_PRODUCT || "onexbots.com",
  WS_URL:
    process.env.NEXT_PUBLIC_WS_URL ||
    "wss://i308mln9pe.execute-api.ap-southeast-1.amazonaws.com/dev",
} as const;

export default config;

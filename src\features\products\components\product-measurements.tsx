import { useEffect, useMemo, useState } from "react";
import { ArrowR<PERSON><PERSON>romLine, ImageIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { CustomImage } from "@/components/ui/image";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

import type { Measurements, PriceVariant, ProductOption } from "../hooks/types";

interface ProductMeasurementsProps {
  variants: PriceVariant[];
  options: ProductOption[];
  onChange: (variants: PriceVariant[]) => void;
  showValidation?: boolean;
  onSectionFocus?: () => void;
}

export function ProductMeasurements({
  variants = [],
  options = [],
  onChange,
  showValidation = false,
  onSectionFocus,
}: ProductMeasurementsProps & { showValidation?: boolean }) {
  const { t } = useTranslation();
  const [applyValues, setApplyValues] = useState({
    weight_value: "",
    weight_unit: "g",
    height_value: "",
    height_unit: "cm",
    width_value: "",
    width_unit: "cm",
    length_value: "",
    length_unit: "cm",
  });

  // Organize variants with parents followed by their children
  const validVariants = useMemo(() => {
    // Step 1: Create a map to organize variants by SKU
    const skuMap = new Map<string, PriceVariant>();

    // First pass: index all variants by their SKU
    variants.forEach((variant) => {
      skuMap.set(variant.sku, variant);
    });

    // Step 2: Identify parent variants and child variants
    const parentVariants: PriceVariant[] = [];
    const childVariantMap = new Map<string, PriceVariant[]>();

    variants.forEach((variant) => {
      // If variant has an original_sku that is different from its SKU, it's a child
      if (variant.original_sku && variant.original_sku !== variant.sku) {
        // Add to the appropriate parent's children array
        if (!childVariantMap.has(variant.original_sku)) {
          childVariantMap.set(variant.original_sku, []);
        }
        childVariantMap.get(variant.original_sku)?.push(variant);
      } else {
        // It's a parent variant (or standalone)
        parentVariants.push(variant);
      }
    });

    // Step 3: Create the final ordered array with parents followed by their children
    const result: PriceVariant[] = [];

    parentVariants.forEach((parent) => {
      // Add the parent first
      result.push(parent);

      // Add any children that belong to this parent
      const children = childVariantMap.get(parent.sku) || [];
      result.push(...children);
    });

    return result.filter((variant) => {
      // Filter out incomplete variants
      return (
        !variant.sku.endsWith("-") &&
        (!variant.unit?.id || (variant.unit?.id && variant.unit?.ratio))
      );
    });
  }, [variants]);

  const handleMeasurementChange = (variantId: string, field: keyof Measurements, value: string) => {
    const updatedVariants = variants.map((variant) =>
      variant.id === variantId
        ? {
            ...variant,
            measurements: {
              ...(variant.measurements || {
                weight_value: 0,
                weight_unit: "g",
                height_value: 0,
                height_unit: "cm",
                width_value: 0,
                width_unit: "cm",
                length_value: 0,
                length_unit: "cm",
              }),
              [field]: field.endsWith("_value") ? Number(value) : value,
            },
          }
        : variant
    );
    onChange(updatedVariants);
  };

  const handleApplyAll = () => {
    const updatedVariants = variants.map((variant) => ({
      ...variant,
      measurements: {
        weight_value: applyValues.weight_value
          ? Number(applyValues.weight_value)
          : variant.measurements?.weight_value || 0,
        weight_unit: applyValues.weight_unit,
        height_value: applyValues.height_value
          ? Number(applyValues.height_value)
          : variant.measurements?.height_value || 0,
        height_unit: applyValues.height_unit,
        width_value: applyValues.width_value
          ? Number(applyValues.width_value)
          : variant.measurements?.width_value || 0,
        width_unit: applyValues.width_unit,
        length_value: applyValues.length_value
          ? Number(applyValues.length_value)
          : variant.measurements?.length_value || 0,
        length_unit: applyValues.length_unit,
      },
    }));
    onChange(updatedVariants);
    // Reset apply values after applying
    setApplyValues({
      weight_value: "",
      weight_unit: "g",
      height_value: "",
      height_unit: "cm",
      width_value: "",
      width_unit: "cm",
      length_value: "",
      length_unit: "cm",
    });
  };

  // Add state for touched fields
  const [touchedFields, setTouchedFields] = useState<{
    [key: string]: boolean;
  }>({});

  // Add blur handler
  const handleBlur = (field: string) => {
    setTouchedFields((prev) => ({
      ...prev,
      [field]: true,
    }));
  };

  // Update useEffect to handle showValidation
  useEffect(() => {
    if (showValidation) {
      // Mark all fields as touched when validation is triggered
      const newTouchedFields: { [key: string]: boolean } = {};
      variants.forEach((variant) => {
        newTouchedFields[`${variant.id}-weight_value`] = true;
        newTouchedFields[`${variant.id}-height_value`] = true;
      });
      setTouchedFields(newTouchedFields);
    }
  }, [showValidation, variants]);

  return (
    <div
      className="space-y-4"
      onClick={() => onSectionFocus?.()}
      onFocus={() => onSectionFocus?.()}>
      {/* Apply All Section */}
      <div className="relative flex justify-center p-2">
        <div className="flex items-center gap-4">
          {/* Weight Apply */}
          <div className="flex items-center gap-2">
            <div className="relative">
              <Input
                type="text"
                min="0"
                placeholder="Weight"
                value={applyValues.weight_value}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^[0-9]*$/.test(value) || value === "") {
                    setApplyValues((prev) => ({ ...prev, weight_value: value }));
                  }
                }}
                className="w-full pr-8"
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                g
              </span>
            </div>
          </div>

          {/* Height Apply */}
          <div className="flex items-center gap-2">
            <div className="relative">
              <Input
                type="text"
                min="0"
                placeholder="Height"
                value={applyValues.height_value}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^[0-9]*$/.test(value) || value === "") {
                    setApplyValues((prev) => ({ ...prev, height_value: value }));
                  }
                }}
                className="w-full pr-8"
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                cm
              </span>
            </div>
          </div>

          {/* Width Apply */}
          <div className="flex items-center gap-2">
            <div className="relative">
              <Input
                type="text"
                min="0"
                placeholder="Width"
                value={applyValues.width_value}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^[0-9]*$/.test(value) || value === "") {
                    setApplyValues((prev) => ({ ...prev, width_value: value }));
                  }
                }}
                className="w-full pr-8"
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                cm
              </span>
            </div>
          </div>

          {/* Length Apply */}
          <div className="flex items-center gap-2">
            <div className="relative">
              <Input
                type="text"
                min="0"
                placeholder="Length"
                value={applyValues.length_value}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^[0-9]*$/.test(value) || value === "") {
                    setApplyValues((prev) => ({ ...prev, length_value: value }));
                  }
                }}
                className="w-full pr-8"
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                cm
              </span>
            </div>
          </div>

          <Button
            onClick={handleApplyAll}
            disabled={!Object.values(applyValues).some((v) => v !== "" && v !== "g" && v !== "cm")}>
            {t("pages.products.addManual.sections.apply")}
          </Button>
        </div>
      </div>

      {/* Variants Table */}
      <div className="rounded-lg border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className={options.length > 0 ? "" : "border-none"}>
                <TableHead className="min-w-[112px]">{options[0]?.name || "Color"}</TableHead>
                {options[1] && <TableHead className="min-w-[120px]">{options[1].name}</TableHead>}
                {options[2] && <TableHead className="min-w-[120px]">{options[2].name}</TableHead>}
                <TableHead className="min-w-[100px]">SKU</TableHead>
                <TableHead className="min-w-[120px] text-right">Weight (g)</TableHead>
                <TableHead className="min-w-[120px] text-right">Height (cm)</TableHead>
                <TableHead className="min-w-[120px] text-right">Width (cm)</TableHead>
                <TableHead className="min-w-[120px] text-right">Length (cm)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {options.length > 0 &&
                validVariants.map((variant) => (
                  <TableRow key={variant.id}>
                    <TableCell className="min-w-[112px] truncate">
                      <div className="flex items-center gap-4">
                        {variant.images?.[0] ? (
                          <div className="relative aspect-square w-[50px] shrink-0">
                            <CustomImage
                              src={variant.images[0].image}
                              alt={variant.option1 || ""}
                              width={100}
                              height={100}
                              className="size-full shrink-0 rounded-md border object-cover"
                            />
                          </div>
                        ) : (
                          <div className="flex aspect-square w-[50px] items-center justify-center rounded-md border bg-muted">
                            <ImageIcon className="size-4 text-muted-foreground" />
                          </div>
                        )}
                        <div className="flex items-center gap-2">
                          {/* Only show arrow for variants where SKU differs from original_sku */}
                          {variant.original_sku && variant.original_sku !== variant.sku && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <ArrowRightFromLine className="size-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent side="right">
                                  Parent SKU: {variant.original_sku}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                          <span
                            className="inline-block max-w-[150px]"
                            title={variant.option1 || ""}>
                            {variant.option1 && variant.option1.length > 20
                              ? variant.option1.slice(0, 20) + "..."
                              : variant.option1}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    {options[1] && (
                      <TableCell className="min-w-[120px] truncate">{variant.option2}</TableCell>
                    )}
                    {options[2] && (
                      <TableCell className="min-w-[100px]">{variant.option3}</TableCell>
                    )}
                    <TableCell className="min-w-[100px]">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="inline-block max-w-[150px] truncate pt-2">
                              {variant.sku}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{variant.sku}</p>
                            {variant.original_sku && variant.original_sku !== variant.sku && (
                              <p>Parent SKU: {variant.original_sku}</p>
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    {/* Weight */}
                    <TableCell className="items-center justify-end gap-2">
                      <div className="relative">
                        <Input
                          type="text"
                          min="0"
                          value={variant.measurements?.weight_value}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^[0-9]*$/.test(value) || value === "") {
                              handleMeasurementChange(variant.id, "weight_value", value);
                            }
                          }}
                          onBlur={() => handleBlur(variant.id)}
                          className={cn(
                            "pr-8",
                            (touchedFields[`${variant.id}-weight_value`] || showValidation) &&
                              !variant.measurements?.weight_value &&
                              "border-destructive focus-visible:ring-destructive"
                          )}
                        />
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                          g
                        </span>
                      </div>
                      {(touchedFields[`${variant.id}-weight_value`] || showValidation) &&
                        !variant.measurements?.weight_value && (
                          <p className="text-xs text-destructive">Weight is required</p>
                        )}
                    </TableCell>

                    {/* Height */}
                    <TableCell className="items-center justify-end gap-2">
                      <div className="relative">
                        <Input
                          type="text"
                          min="0"
                          value={variant.measurements?.height_value}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^[0-9]*$/.test(value) || value === "") {
                              handleMeasurementChange(variant.id, "height_value", value);
                            }
                          }}
                          onBlur={() => handleBlur(variant.id)}
                          className={cn(
                            "pr-8",
                            (touchedFields[`${variant.id}-height_value`] || showValidation) &&
                              !variant.measurements?.height_value &&
                              "border-destructive focus-visible:ring-destructive"
                          )}
                        />
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                          cm
                        </span>
                      </div>
                      {(touchedFields[`${variant.id}-height_value`] || showValidation) &&
                        !variant.measurements?.height_value && (
                          <p className="text-xs text-destructive">Height is required</p>
                        )}
                    </TableCell>

                    {/* Width */}
                    <TableCell className="items-center justify-end gap-2">
                      <div className="relative">
                        <Input
                          type="text"
                          min="0"
                          value={variant.measurements?.width_value}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^[0-9]*$/.test(value) || value === "") {
                              handleMeasurementChange(variant.id, "width_value", value);
                            }
                          }}
                          onBlur={() => handleBlur(variant.id)}
                          className={cn(
                            "pr-8",
                            (touchedFields[`${variant.id}-width_value`] || showValidation) &&
                              !variant.measurements?.width_value &&
                              "border-destructive focus-visible:ring-destructive"
                          )}
                        />
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                          cm
                        </span>
                      </div>
                      {(touchedFields[`${variant.id}-width_value`] || showValidation) &&
                        !variant.measurements?.width_value && (
                          <p className="text-xs text-destructive">Width is required</p>
                        )}
                    </TableCell>

                    {/* Length */}
                    <TableCell className="items-center justify-end gap-2">
                      <div className="relative">
                        <Input
                          type="text"
                          min="0"
                          value={variant.measurements?.length_value}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^[0-9]*$/.test(value) || value === "") {
                              handleMeasurementChange(variant.id, "length_value", value);
                            }
                          }}
                          onBlur={() => handleBlur(variant.id)}
                          className={cn(
                            "pr-8",
                            (touchedFields[`${variant.id}-length_value`] || showValidation) &&
                              !variant.measurements?.length_value &&
                              "border-destructive focus-visible:ring-destructive"
                          )}
                        />
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                          cm
                        </span>
                      </div>
                      {(touchedFields[`${variant.id}-length_value`] || showValidation) &&
                        !variant.measurements?.length_value && (
                          <p className="text-xs text-destructive">Length is required</p>
                        )}
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}

export function validateMeasurements(variants: PriceVariant[]) {
  const errors: { field: string; variantId: string; message: string }[] = [];

  variants.forEach((variant) => {
    if (!variant.measurements?.weight_value) {
      errors.push({ field: "weight", variantId: variant.id, message: "Weight is required" });
    }
    if (!variant.measurements?.height_value) {
      errors.push({ field: "height", variantId: variant.id, message: "Height is required" });
    }
  });

  return errors;
}

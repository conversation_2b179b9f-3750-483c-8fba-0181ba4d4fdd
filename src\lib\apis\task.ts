import { Task } from "@/features/bots/task/hooks/type";

import { TASK_ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseAxiosDetail } from "./types/common";

export const taskApi = {
  list: async (params?: Record<string, unknown>) => {
    const response = await privateApi.get<ResponseAxiosDetail<Task>>(TASK_ENDPOINTS.LIST, {
      params,
    });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await privateApi.get(TASK_ENDPOINTS.GET_BY_ID.replace(":id", id));
    return response.data as Task;
  },

  create: async (data: Partial<Task>) => {
    const response = await privateApi.post<Task>(TASK_ENDPOINTS.CREATE, data);
    return response as Task;
  },

  update: async (id: string, data: Partial<Task>) => {
    const response = await privateApi.put<Task>(TASK_ENDPOINTS.UPDATE.replace(":id", id), data);
    return response as Task;
  },

  delete: async (id: string) => {
    const response = await privateApi.delete(TASK_ENDPOINTS.DELETE.replace(":id", id));
    return response;
  },
};

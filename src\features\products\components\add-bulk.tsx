"use client";

import React from "react";
import { AlertTriangle } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

interface AddBulkProductProps {
  onClose: () => void;
}

export function AddBulkProduct({ onClose }: AddBulkProductProps) {
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // TODO: Implement bulk product submission logic
    console.log("Submitting bulk products");
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // TODO: Implement file upload logic
      console.log("File uploaded:", file);
    }
  };

  const handleTemplateDownload = () => {
    // TODO: Implement template download logic
    console.log("Downloading template");
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-2xl rounded-lg bg-white p-6">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-xl font-semibold">Add Bulk Product</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            ×
          </button>
        </div>

        <div className="mb-6">
          <div className="flex items-start gap-2">
            <AlertTriangle className="mt-1 size-5 shrink-0 text-red-500" />
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-red-600">Notice:</h3>
              <ul className="list-disc space-y-1 pl-4 text-sm">
                <li>
                  Please import the file according to the template to avoid data discrepancies.
                </li>
                <li>
                  Download our template{" "}
                  <button
                    onClick={handleTemplateDownload}
                    className="text-blue-600 hover:underline">
                    here
                  </button>
                  .
                </li>
                <li>
                  Please import the file according to the template to avoid data discrepancies.
                </li>
              </ul>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label>Image</Label>
            <div className="rounded-lg border-2 border-dashed p-8 text-center">
              <input
                type="file"
                accept=".csv,.xlsx,.xls"
                className="hidden"
                id="file-upload"
                onChange={handleFileUpload}
              />
              <label
                htmlFor="file-upload"
                className="flex cursor-pointer flex-col items-center justify-center gap-2">
                <div className="size-8">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    className="text-gray-400">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">
                  Drag files here or <span className="text-blue-600">Upload</span>
                </p>
              </label>
            </div>
          </div>

          <div className="flex justify-end pt-4">
            <Button type="submit" className="w-20">
              Add
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

export interface Customer {
  id: string;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  company_id?: string;
  company_name?: string;
  customer_code?: string;
  customer_group?: CustomerGroup;
  gender?: string;
  birthday?: string;
  created_at?: string;
  updated_at?: string;
  addresses?: CustomerAddress[];
  billing_address?: CustomerAddress;
  loyal_customer?: {
    point: number;
    used_point: number;
  };
  shipping_address?: CustomerAddress;
  selected_billing_address?: CustomerAddress;
  selected_shipping_address?: CustomerAddress;
  source?: {
    id: string;
    channel_name: string;
  };
}

export interface CustomerAddress {
  id: number;
  first_name?: string;
  last_name?: string;
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  province_code?: string;
  district?: string;
  district_code?: string;
  ward?: string;
  ward_code?: string;
  country?: string;
  country_code?: string;
  zip?: string;
  phone?: string;
  name?: string;
  default_shipping?: boolean;
  default_billing?: boolean;
}

export interface UseCustomersOptions {
  query?: string;
  enabled?: boolean;
}

export interface CustomerGroup {
  id: string;
  name: string;
  min_order_amount: number | null;
  max_discount_amount_per_order: number | null;
  min_purchased_amount: number;
  discount_percent: number;
  default_price_group: string | null;
  default_payment_method: string | null;
  next_group: string | null;
  image: any | null;
}

export interface Province {
  code: string;
  name_with_type: string;
  name: string;
  id: number;
  slug: string;
}

export interface District {
  code: string;
  name: string;
  id: number;
  slug: string;
  path_with_type: string;
  name_with_type: string;
  province: Province;
}

export interface Ward {
  code: string;
  name: string;
  id: number;
  slug: string;
  path_with_type: string;
  name_with_type: string;
  district: District;
  province: Province;
}

"use client";

import { UrlObject } from "url";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight } from "lucide-react";
import { useTranslation } from "react-i18next";

import { data } from "./breadcrumb-data";

interface BreadcrumbItem {
  title: string;
  endpoint: string | null;
}

function toNavUrl(url: string): UrlObject {
  return { pathname: url };
}

export function CustomBreadcrumb() {
  const { t } = useTranslation();
  const pathname = usePathname();

  // Function to find data entry by endpoint
  const findByEndpoint = (endpoint: string) => {
    // Handle dynamic routes
    const exactMatch = Object.entries(data).find(([, item]) => item.endpoint === endpoint);
    if (exactMatch) return exactMatch;
    const pathSegments = endpoint.split("/");
    const matches = Object.entries(data).filter(([, item]) => {
      if (!item.endpoint) return false;
      const endpointSegments = item.endpoint.split("/");
      if (pathSegments.length !== endpointSegments.length) return false;
      return pathSegments.every((segment, index) => {
        if (segment === endpointSegments[index]) return true;
        if (endpointSegments[index].startsWith(":")) return true;
        return false;
      });
    });

    if (matches.length > 1) {
      return matches.reduce((mostSpecific, current) => {
        const currentDynamicSegments =
          current[1].endpoint?.split("/").filter((s) => s.startsWith(":")).length || 0;
        const mostSpecificDynamicSegments =
          mostSpecific[1].endpoint?.split("/").filter((s) => s.startsWith(":")).length || 0;
        return currentDynamicSegments < mostSpecificDynamicSegments ? current : mostSpecific;
      });
    }

    return matches[0];
  };

  // Function to get breadcrumb chain
  const getBreadcrumbChain = (pathname: string): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [];

    // Find the current page in data
    const currentPage = findByEndpoint(pathname);
    if (!currentPage) return breadcrumbs;
    const [, currentItem] = currentPage;
    let current = currentItem;

    // Add current page
    breadcrumbs.push({
      title: current.title,
      endpoint: current.endpoint,
    });

    // Add all parents in reverse order
    while (current.parent) {
      const parent = data[current.parent as keyof typeof data];
      if (!parent) break;

      breadcrumbs.unshift({
        title: parent.title,
        endpoint: parent.endpoint,
      });
      current = parent;
    }

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbChain(pathname || "");
  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
      {breadcrumbs.map((item, index) => {
        const isLast = index === breadcrumbs.length - 1;
        return (
          <div key={item.endpoint} className="flex items-center">
            {index > 0 && <ChevronRight className="mx-1 size-4 text-muted-foreground/50" />}
            {isLast ? (
              <span className="leading-5 text-foreground">{t(item.title)}</span>
            ) : (
              <Link
                href={toNavUrl(item.endpoint || "/")}
                className="hover:text-foreground hover:underline">
                {t(item.title)}
              </Link>
            )}
          </div>
        );
      })}
    </nav>
  );
}

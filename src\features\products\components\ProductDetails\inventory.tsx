import { useMemo, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { ChevronDown, ChevronUp, Loader2, PackageOpen } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useInventoryItems, useInventoryTransactions } from "@/features/inventory/hooks/inventory";
import { InventoryItem, InventoryTransaction } from "@/features/inventory/hooks/types";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { locationApi } from "@/lib/apis/location";

interface InventoryProps {
  sku: string;
}

export const Inventory = ({ sku }: InventoryProps) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<"inventory" | "history">("inventory");
  const [selectedBranch, setSelectedBranch] = useState<string>("all");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  // Fetch locations data
  const { data: locationsData } = useQuery({
    queryKey: ["locations"],
    queryFn: async () => {
      const response = await locationApi.list({ limit: 20 });
      return response;
    },
  });

  const locations = useMemo(() => locationsData?.items || [], [locationsData?.items]);

  // Include "All Branches" option
  const branches = useMemo(
    () => [
      { id: "all", name: t("pages.products.inventory.allBranches") },
      ...(locations.map((loc) => ({ id: loc.id, name: loc.name })) || []),
    ],
    [locations, t]
  );

  // Fetch inventory data using hooks
  const { data: inventoryResponse, isLoading: isLoadingInventory } = useInventoryItems(sku);
  const { data: historyResponse, isLoading: isLoadingHistory } = useInventoryTransactions(sku);

  const inventoryData = useMemo(() => inventoryResponse?.items || [], [inventoryResponse?.items]);
  const historyData = useMemo(() => historyResponse?.items || [], [historyResponse?.items]);

  // Filter and sort history data
  const filteredHistoryData = useMemo(() => {
    return historyData
      .filter(
        (item: InventoryTransaction) =>
          selectedBranch === "all" || item.location_id === selectedBranch
      )
      .sort((a: InventoryTransaction, b: InventoryTransaction) => {
        if (!sortDirection) return 0;
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return sortDirection === "asc" ? dateA - dateB : dateB - dateA;
      });
  }, [historyData, selectedBranch, sortDirection]);

  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center py-4">
      <PackageOpen className="size-12 text-gray-400" />
      <p className="mt-1 text-sm text-gray-500">{t("pages.products.inventory.noMatchResult")}</p>
    </div>
  );

  const renderLoading = () => (
    <div className="flex items-center justify-center py-8">
      <Loader2 className="size-8 animate-spin text-muted-foreground" />
    </div>
  );

  const toggleSort = () => {
    setSortDirection((prev) => (prev === "desc" ? "asc" : "desc"));
  };

  return (
    <div className="space-y-4">
      {/* Tabs and Dropdown */}
      <div className="flex items-center justify-between border-b">
        <div className="flex gap-4">
          <button
            className={`px-1 pb-2 font-medium ${
              activeTab === "inventory"
                ? "border-b-2 border-foreground text-foreground"
                : "text-foreground"
            }`}
            onClick={() => setActiveTab("inventory")}>
            {t("pages.products.inventory.inventory")}
          </button>
          <button
            className={`px-1 pb-2 font-medium ${
              activeTab === "history"
                ? "border-b-2 border-foreground text-foreground"
                : "text-foreground"
            }`}
            onClick={() => setActiveTab("history")}>
            {t("pages.products.inventory.history")}
          </button>
        </div>

        {/* Branch Dropdown - Only show in History tab */}
        {activeTab === "history" && (
          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="mb-2 flex items-center gap-2">
                {branches.find((b) => b.id === selectedBranch)?.name ||
                  t("pages.products.inventory.allBranches")}
                <ChevronDown className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {branches.map((branch) => (
                <DropdownMenuItem
                  key={branch.id}
                  onClick={() => setSelectedBranch(branch.id)}
                  className={selectedBranch === branch.id ? "bg-accent" : ""}>
                  {branch.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Inventory Table */}
      {activeTab === "inventory" && (
        <>
          {isLoadingInventory ? (
            renderLoading()
          ) : inventoryData.length > 0 ? (
            <div className="relative w-full overflow-auto rounded-lg border">
              <div className="max-h-[600px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.branch")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.available")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.incoming")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.onHand")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.packing")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.shipping")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.minValue")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.maxValue")}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {inventoryData.map((item: InventoryItem) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.location_name}</TableCell>
                        <TableCell>{item.available}</TableCell>
                        <TableCell>{item.incoming}</TableCell>
                        <TableCell>{item.on_hand}</TableCell>
                        <TableCell>{item.packing}</TableCell>
                        <TableCell>{item.shipping}</TableCell>
                        <TableCell>{item.min_value || 0}</TableCell>
                        <TableCell>{item.max_value || 0}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          ) : (
            renderEmptyState()
          )}
        </>
      )}

      {/* History Tab Content */}
      {activeTab === "history" && (
        <>
          {isLoadingHistory ? (
            renderLoading()
          ) : filteredHistoryData.length > 0 ? (
            <div className="relative w-full overflow-auto rounded-lg border">
              <div className="max-h-[600px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="sticky top-0">
                        <Button
                          variant="ghost"
                          className="flex items-center gap-1 p-0 hover:bg-transparent"
                          onClick={toggleSort}>
                          Được tạo vào
                          {sortDirection === "asc" ? (
                            <ChevronUp className="size-4" />
                          ) : (
                            <ChevronDown className="size-4" />
                          )}
                        </Button>
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.staff")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.transactionType")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.change")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.quantity")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.reference")}
                      </TableHead>
                      <TableHead className="sticky top-0">
                        {t("pages.products.inventory.branch")}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredHistoryData.map((item: InventoryTransaction) => (
                      <TableRow key={item.id}>
                        <TableCell>{new Date(item.created_at).toLocaleString()}</TableCell>
                        <TableCell>{item.staff_id}</TableCell>
                        <TableCell>{item.transaction_type}</TableCell>
                        <TableCell>{item.change}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>{item.reference}</TableCell>
                        <TableCell>
                          {locations.find((loc) => loc.id === item.location_id)?.name || "Default"}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          ) : (
            renderEmptyState()
          )}
        </>
      )}
    </div>
  );
};

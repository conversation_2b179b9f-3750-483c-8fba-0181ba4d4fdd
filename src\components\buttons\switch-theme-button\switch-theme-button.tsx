import { useState } from "react";
import { motion } from "framer-motion";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

export const SwitchThemeButton = () => {
  const { theme, setTheme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  // Determine which icon to show based on current theme and hover state
  const showDarkIcon = theme === "light" ? isHovered : !isHovered;
  const showLightIcon = theme === "dark" ? isHovered : !isHovered;

  return (
    <div
      className="relative flex cursor-pointer items-center gap-2"
      onClick={(e: React.MouseEvent<HTMLDivElement>) => {
        e.preventDefault();
        setTheme(theme === "dark" ? "light" : "dark");
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}>
      <div className="relative size-4 overflow-hidden">
        <motion.div
          animate={{
            y: showDarkIcon ? 0 : -16,
            opacity: showDarkIcon ? 1 : 0,
          }}
          transition={{ duration: 0.15 }}
          className="absolute left-0 top-0">
          <Moon size={16} />
        </motion.div>
        <motion.div
          animate={{
            y: showLightIcon ? 0 : 16,
            opacity: showLightIcon ? 1 : 0,
          }}
          transition={{ duration: 0.15 }}
          className="absolute left-0 top-0">
          <Sun size={16} />
        </motion.div>
      </div>
    </div>
  );
};

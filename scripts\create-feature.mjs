import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get arguments
const featureName = process.argv[2];
const componentName = process.argv[3];

if (!featureName) {
  console.error('Please provide a feature name');
  process.exit(1);
}

if (!componentName) {
  console.error('Please provide a component name');
  process.exit(1);
}

// Define paths
const baseDir = path.join(__dirname, '..', 'src');
const featureDir = path.join(baseDir, 'features', featureName);
const templateDir = path.join(__dirname, '..', '.templates', 'ComponentTemplate');

// Check if feature exists
const featureExists = fs.existsSync(featureDir);

// Create feature structure
const directories = [
  path.join(featureDir, 'components'),
  path.join(featureDir, 'components', componentName),
  path.join(featureDir, 'components', componentName, 'designs'),
  path.join(featureDir, 'hooks'),
  path.join(featureDir, 'store'),
  path.join(featureDir, 'store', 'actions'),
  path.join(featureDir, 'store', 'reducer'),
  path.join(featureDir, 'store', 'saga'),
  path.join(featureDir, 'store', 'types'),
  path.join(featureDir, 'utils'),
  path.join(featureDir, 'utils', 'validators'),
];

// Create all directories
directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Create component files from template
const targetDir = path.join(featureDir, 'components', componentName);
const templateFiles = fs.readdirSync(templateDir);

// File name mapping
const fileNameMap = {
  'Component.tsx': `${componentName}.tsx`,
  'Component.test.tsx': `${componentName}.test.tsx`,
  'README.md': `${componentName}.md`,
  'ai-prompts.md': 'ai-prompts.md'
};

templateFiles.forEach(file => {
  // Skip ai-prompts.md
  if (file === 'ai-prompts.md') {
    return;
  }

  const sourcePath = path.join(templateDir, file);
  const targetPath = path.join(targetDir, fileNameMap[file] || file);

  // Read the template file
  let content = fs.readFileSync(sourcePath, 'utf8');

  // Replace placeholder with actual component name
  content = content.replace(/ComponentTemplate/g, componentName);

  // Write the file
  fs.writeFileSync(targetPath, content);
});

// Create hook file
const hookContent = `import { useState } from "react";

export const use${componentName} = () => {
  const [loading, setLoading] = useState(false);

  const handleAction = async () => {
    try {
      setLoading(true);
      // Add your logic here
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    handleAction
  };
};`;

fs.writeFileSync(path.join(featureDir, 'hooks', `use${componentName}.ts`), hookContent);

// Create validator file
const validatorContent = `import { z } from "zod";

export const ${componentName.toLowerCase()}Schema = z.object({
  // Add your schema here
});

export type ${componentName}FormValues = z.infer<typeof ${componentName.toLowerCase()}Schema>;`;

const validatorDir = path.join(featureDir, 'utils', 'validators');
if (!fs.existsSync(validatorDir)) {
  fs.mkdirSync(validatorDir, { recursive: true });
}
fs.writeFileSync(path.join(validatorDir, `${componentName.toLowerCase()}.ts`), validatorContent);

// Create or update index.ts
const indexPath = path.join(featureDir, 'index.ts');
const indexContent = `export * from './components/${componentName}/${componentName}';\n`;

if (fs.existsSync(indexPath)) {
  const currentContent = fs.readFileSync(indexPath, 'utf8');
  if (!currentContent.includes(indexContent)) {
    fs.appendFileSync(indexPath, indexContent);
  }
} else {
  fs.writeFileSync(indexPath, indexContent);
}

console.log(`
✨ ${featureExists ? 'Added new component to' : 'Created new'} feature: ${featureName}

Structure created:
/src
  /features
    /${featureName}
      /components
        /${componentName}
          /${componentName}.tsx
          /${componentName}.test.tsx
          /${componentName}.md
          /designs/
      /hooks
        /use${componentName}.ts
      /store
        /actions
          /index.ts
        /reducer
          /index.ts
        /saga
          /index.ts
        /types
          /index.ts
      /utils
        /validators
          /${componentName.toLowerCase()}.ts
      /index.ts

Next steps:
1. Implement your business logic in hooks/use${componentName}.ts
2. Add your validations in utils/validators/${componentName.toLowerCase()}.ts
3. Configure your store in store/
4. Update translations for "${featureName}.${componentName.toLowerCase()}"
5. Add your design files to: src/features/${featureName}/components/${componentName}/designs/

Run tests with: npm test ${componentName}
`); 
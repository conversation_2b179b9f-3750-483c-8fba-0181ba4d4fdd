import { useState } from "react";
import { Check, Clipboard, MoreVertical } from "lucide-react";
import <PERSON><PERSON><PERSON><PERSON>ie<PERSON> from "react18-json-view";

import { <PERSON><PERSON>, Card, Popover, PopoverContent, PopoverTrigger } from "@/components/ui";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { convertSnakeToCamel } from "@/utils/helpers/text-formater";

import "react18-json-view/src/style.css";

interface EventTabsProps {
  extraFields: Record<string, unknown> | null;
}

export default function EventTabs({ extraFields }: EventTabsProps) {
  const tabKeys = Object.keys(extraFields || {});
  const [activeTab, setActiveTab] = useState(tabKeys[0] || "");
  const [copied, setCopied] = useState(false);
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };
  const copyToClipboard = async () => {
    if (!extraFields || !activeTab) return;

    try {
      const content = extraFields[activeTab];
      await navigator.clipboard.writeText(JSON.stringify(content, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 30000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  if (!extraFields || tabKeys.length === 0) {
    return <div className="p-4 text-gray-500">No extra fields available</div>;
  }

  return (
    <div className="flex w-full flex-auto overflow-hidden p-4">
      <Card className="flex w-full overflow-hidden bg-muted-50 p-4">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="flex w-full flex-col overflow-hidden">
          <div className="flex w-full items-center justify-between gap-4 overflow-hidden border-b">
            <div className="flex flex-auto items-center  overflow-x-auto">
              <div className="hide-scrollbar flex  items-center gap-2">
                <TabsList className="justify-start rounded-none border-b bg-transparent p-0">
                  {tabKeys.map((key) => (
                    <TabsTrigger
                      key={key}
                      value={key}
                      className="group relative h-9 rounded-none border-b-2 border-transparent text-sm font-medium 
                         data-[state=active]:border-primary data-[state=active]:bg-transparent 
                         data-[state=active]:text-foreground data-[state=active]:shadow-none">
                      {convertSnakeToCamel(key)}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>
              <div className="shrink-0 border-l">
                <Popover>
                  <PopoverTrigger asChild>
                    <button className="p-2">
                      <MoreVertical size={16} />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48 p-2" align="end">
                    <div className="max-h-[300px] space-y-px overflow-y-auto">
                      {tabKeys.map((key) => (
                        <button
                          key={key}
                          className={cn(
                            "flex w-full items-center justify-between rounded-sm px-2 py-1.5 text-sm hover:bg-muted",
                            activeTab === key && "bg-muted"
                          )}
                          onClick={() => handleTabChange(key)}>
                          {convertSnakeToCamel(key)}
                          {activeTab === key && <Check size={16} className="text-primary" />}
                        </button>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <Button
              title={copied ? "Copied" : "Copy to clipboard"}
              size="sm"
              variant="ghost"
              className="flex-none"
              onClick={copyToClipboard}>
              <Button size="sm" variant="ghost" onClick={copyToClipboard}>
                {copied ? <Check size={16} className="text-primary" /> : <Clipboard size={16} />}
              </Button>
            </Button>
          </div>
          <div className="flex-auto overflow-y-auto">
            {Object.entries(extraFields).map(([key, value]) => (
              <TabsContent key={key} value={key}>
                <JSONView src={value} collapsed={false} enableClipboard={false} />
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </Card>
    </div>
  );
}
